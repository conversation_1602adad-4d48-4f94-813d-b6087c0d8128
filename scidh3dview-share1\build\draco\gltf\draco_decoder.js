
var DracoDecoderModule = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(DracoDecoderModule = {})  {

var Module=typeof DracoDecoderModule!="undefined"?DracoDecoderModule:{};var readyPromiseResolve,readyPromiseReject;Module["ready"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var isRuntimeInitialized=false;var isModuleParsed=false;Module["onRuntimeInitialized"]=function(){isRuntimeInitialized=true;if(isModuleParsed){if(typeof Module["onModuleLoaded"]==="function"){Module["onModuleLoaded"](Module)}}};Module["onModuleParsed"]=function(){isModuleParsed=true;if(isRuntimeInitialized){if(typeof Module["onModuleLoaded"]==="function"){Module["onModuleLoaded"](Module)}}};function isVersionSupported(versionString){if(typeof versionString!=="string")return false;const version=versionString.split(".");if(version.length<2||version.length>3)return false;if(version[0]==1&&version[1]>=0&&version[1]<=5)return true;if(version[0]!=0||version[1]>10)return false;return true}Module["isVersionSupported"]=isVersionSupported;var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof importScripts=="function";var ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string";var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;function logExceptionOnExit(e){if(e instanceof ExitStatus)return;let toLog=e;err("exiting due to exception: "+toLog)}if(ENVIRONMENT_IS_NODE){var fs=require("fs");var nodePath=require("path");if(ENVIRONMENT_IS_WORKER){scriptDirectory=nodePath.dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=(filename,binary)=>{var ret=tryParseAsDataURI(filename);if(ret){return binary?ret:ret.toString()}filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);return fs.readFileSync(filename,binary?undefined:"utf8")};readBinary=filename=>{var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}return ret};readAsync=(filename,onload,onerror)=>{var ret=tryParseAsDataURI(filename);if(ret){onload(ret)}filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);fs.readFile(filename,function(err,data){if(err)onerror(err);else onload(data.buffer)})};if(process["argv"].length>1){thisProgram=process["argv"][1].replace(/\\/g,"/")}arguments_=process["argv"].slice(2);quit_=(status,toThrow)=>{if(keepRuntimeAlive()){process["exitCode"]=status;throw toThrow}logExceptionOnExit(toThrow);process["exit"](status)};Module["inspect"]=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=url=>{try{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText}catch(err){var data=tryParseAsDataURI(url);if(data){return intArrayToString(data)}throw err}};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{try{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}catch(err){var data=tryParseAsDataURI(url);if(data){return data}throw err}}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}var data=tryParseAsDataURI(url);if(data){onload(data.buffer);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=title=>document.title=title}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;var WebAssembly={Memory:function(opts){this.buffer=new ArrayBuffer(opts["initial"]*65536)},Module:function(binary){},Instance:function(module,info){this.exports=(
// EMSCRIPTEN_START_ASM
function instantiate(ia){function c(d){d.set=function(a,b){this[a]=b};d.get=function(a){return this[a]};return d}var e;var f=new Uint8Array(123);for(var a=25;a>=0;--a){f[48+a]=52+a;f[65+a]=a;f[97+a]=26+a}f[43]=62;f[47]=63;function l(m,n,o){var g,h,a=0,i=n,j=o.length,k=n+(j*3>>2)-(o[j-2]=="=")-(o[j-1]=="=");for(;a<j;a+=4){g=f[o.charCodeAt(a+1)];h=f[o.charCodeAt(a+2)];m[i++]=f[o.charCodeAt(a)]<<2|g>>4;if(i<k)m[i++]=g<<4|h>>2;if(i<k)m[i++]=h<<6|f[o.charCodeAt(a+3)]}}function p(q){l(e,1028,"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");l(e,8900,"AQAAAAMAAAAFAAAABwAAAAAAAACYIwAA+QAAAPoAAAD7AAAA/AAAAE41ZHJhY28yMk1lc2hUcmF2ZXJzYWxTZXF1ZW5jZXJJTlNfMjhNYXhQcmVkaWN0aW9uRGVncmVlVHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzJfRUVFRUVFAE41ZHJhY28xNVBvaW50c1NlcXVlbmNlckUAAAAAsCsAAHMjAADYKwAA7CIAAJAjAAD/////AAAAAIgkAAD9AAAA/gAAAP8AAABONWRyYWNvMjhNYXhQcmVkaWN0aW9uRGVncmVlVHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQBONWRyYWNvMTNUcmF2ZXJzZXJCYXNlSU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQAAsCsAACUkAADYKwAAvCMAAIAkAAAAAAAAgCQAAAABAAABAQAA/wAAAAAAAABAJQAAAgEAAAMBAAAEAQAABQEAAE41ZHJhY28yMk1lc2hUcmF2ZXJzYWxTZXF1ZW5jZXJJTlNfMTlEZXB0aEZpcnN0VHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzJfRUVFRUVFAAAA2CsAAMAkAACQIwAAAAAAAMAlAAAGAQAABwEAAP8AAABONWRyYWNvMTlEZXB0aEZpcnN0VHJhdmVyc2VySU5TXzExQ29ybmVyVGFibGVFTlNfMzZNZXNoQXR0cmlidXRlSW5kaWNlc0VuY29kaW5nT2JzZXJ2ZXJJUzFfRUVFRQDYKwAAYCUAAIAkAAAAAAAAcCYAAAgBAAAJAQAACgEAAAsBAABONWRyYWNvMjJNZXNoVHJhdmVyc2FsU2VxdWVuY2VySU5TXzE5RGVwdGhGaXJzdFRyYXZlcnNlcklOU18yNE1lc2hBdHRyaWJ1dGVDb3JuZXJUYWJsZUVOU18zNk1lc2hBdHRyaWJ1dGVJbmRpY2VzRW5jb2RpbmdPYnNlcnZlcklTMl9FRUVFRUUAANgrAADkJQAAkCM=");l(e,9860,"3CYAAMwAAAAMAQAAzgAAAM8AAAANAQAA0AAAANEAAADSAAAA0wAAANQAAADVAAAA1gAAAA4BAABONWRyYWNvMjFNZXNoU2VxdWVudGlhbERlY29kZXJFANgrAAC8JgAA3B8AAAAAAAAcJwAADwEAABABAAARAQAAEgEAAE41ZHJhY28xNUxpbmVhclNlcXVlbmNlckUAAADYKwAAACcAAJAjAAAAAAAAnCcAAMwAAAATAQAAFAEAAM8AAAAjAAAAFQEAANEAAADSAAAA0w==");l(e,10080,"RmFpbGVkIHRvIHBhcnNlIERyYWNvIGhlYWRlci4ATjVkcmFjbzE3UG9pbnRDbG91ZERlY29kZXJFAAAAsCsAAH4nAAABAAAAAQAAAAIAAAACAAAABAAAAAQAAAAIAAAACAAAAAQAAAAIAAAAAQ==");l(e,10211,"wAAAAMAAAADAAAAAwP//////////AAAAACAoAAAWAQAAFwEAABgBAAAZAQAATjVkcmFjbzRNZXNoRQAAANgrAAAQKAAAZCgAAP////8AAAAAAAAAAGQoAAAaAQAAGwEAABwBAAAdAQAATjVkcmFjbzEwUG9pbnRDbG91ZEUAAAAAsCsAAEwo");l(e,10356,"AgAAAAMAAAAFAAAABwAAAAsAAAANAAAAEQAAABMAAAAXAAAAHQAAAB8AAAAlAAAAKQAAACsAAAAvAAAANQAAADsAAAA9AAAAQwAAAEcAAABJAAAATwAAAFMAAABZAAAAYQAAAGUAAABnAAAAawAAAG0AAABxAAAAfwAAAIMAAACJAAAAiwAAAJUAAACXAAAAnQAAAKMAAACnAAAArQAAALMAAAC1AAAAvwAAAMEAAADFAAAAxwAAANMAAAABAAAACwAAAA0AAAARAAAAEwAAABcAAAAdAAAAHwAAACUAAAApAAAAKwAAAC8AAAA1AAAAOwAAAD0AAABDAAAARwAAAEkAAABPAAAAUwAAAFkAAABhAAAAZQAAAGcAAABrAAAAbQAAAHEAAAB5AAAAfwAAAIMAAACJAAAAiwAAAI8AAACVAAAAlwAAAJ0AAACjAAAApwAAAKkAAACtAAAAswAAALUAAAC7AAAAvwAAAMEAAADFAAAAxwAAANEAAAAAAAAACgAAAGQAAADoAwAAECcAAKCGAQBAQg8AgJaYAADh9QUAypo7AAAAAAAAAAAwMDAxMDIwMzA0MDUwNjA3MDgwOTEwMTExMjEzMTQxNTE2MTcxODE5MjAyMTIyMjMyNDI1MjYyNzI4MjkzMDMxMzIzMzM0MzUzNjM3MzgzOTQwNDE0MjQzNDQ0NTQ2NDc0ODQ5NTA1MTUyNTM1NDU1NTY1NzU4NTk2MDYxNjI2MzY0NjU2NjY3Njg2OTcwNzE3MjczNzQ3NTc2Nzc3ODc5ODA4MTgyODM4NDg1ODY4Nzg4ODk5MDkxOTI5Mzk0OTU5Njk3OTg5OU4xMF9fY3h4YWJpdjExNl9fc2hpbV90eXBlX2luZm9FAAAAANgrAADoKgAAbC0AAE4xMF9fY3h4YWJpdjExN19fY2xhc3NfdHlwZV9pbmZvRQAAANgrAAAYKwAADCsAAE4xMF9fY3h4YWJpdjExN19fcGJhc2VfdHlwZV9pbmZvRQAAANgrAABIKwAADCsAAE4xMF9fY3h4YWJpdjExOV9fcG9pbnRlcl90eXBlX2luZm9FANgrAAB4KwAAbCsAAAAAAAA8KwAAHgEAAB8BAAAgAQAAIQEAACIBAAAjAQAAJAEAACUBAAAAAAAAICwAAB4BAAAmAQAAIAEAACEBAAAiAQAAJwEAACgBAAApAQAATjEwX19jeHhhYml2MTIwX19zaV9jbGFzc190eXBlX2luZm9FAAAAANgrAAD4KwAAPCsAAAAAAACQLAAADgAAACoBAAArAQAAAAAAALgsAAAOAAAALAEAAC0BAAAAAAAAeCwAAA4AAAAuAQAALwEAAFN0OWV4Y2VwdGlvbgAAAACwKwAAaCwAAFN0OWJhZF9hbGxvYwAAAADYKwAAgCwAAHgsAABTdDIwYmFkX2FycmF5X25ld19sZW5ndGgAAAAA2CsAAJwsAACQLAAAAAAAAOgsAAABAAAAMAEAADEBAABTdDExbG9naWNfZXJyb3IA2CsAANgsAAB4LAAAAAAAABwtAAABAAAAMgEAADEBAABTdDEybGVuZ3RoX2Vycm9yAAAAANgrAAAILQAA6CwAAAAAAABQLQAAAQAAADMBAAAxAQAAU3QxMm91dF9vZl9yYW5nZQAAAADYKwAAPC0AAOgsAABTdDl0eXBlX2luZm8AAAAAsCsAAFwt");l(e,11636,"8C8B")}var r=new ArrayBuffer(16);var s=new Int32Array(r);var t=new Float32Array(r);var u=new Float64Array(r);function v(){throw new Error("abort")}function w(x){t[2]=x}function y(z){return s[z]}function ha(q){var A=q.a;var B=A.a;var C=B.buffer;B.grow=fa;var D=new Int8Array(C);var E=new Int16Array(C);var F=new Int32Array(C);var G=new Uint8Array(C);var H=new Uint16Array(C);var I=new Uint32Array(C);var J=new Float32Array(C);var K=new Float64Array(C);var L=Math.imul;var M=Math.fround;var N=Math.abs;var O=Math.clz32;var P=Math.min;var Q=Math.max;var R=Math.floor;var S=Math.ceil;var T=Math.trunc;var U=Math.sqrt;var V=A.b;var W=A.c;var X=A.d;var Y=A.e;var Z=77808;var _=0;
// EMSCRIPTEN_START_FUNCS
function mc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0,z=0;if(!a){return 1}e=F[c+20>>2];o=F[c+12>>2];g=F[c+16>>2];a:{if((e|0)>=(o|0)&g>>>0>=I[c+8>>2]|(e|0)>(o|0)){break a}o=G[g+F[c>>2]|0];g=g+1|0;e=g?e:e+1|0;F[c+16>>2]=g;F[c+20>>2]=e;b:{switch(o|0){case 0:e=a;f=b;g=d;a=0;d=0;j=Z+-64|0;Z=j;F[j+56>>2]=0;F[j+48>>2]=0;F[j+52>>2]=0;F[j+40>>2]=0;F[j+44>>2]=0;F[j+32>>2]=0;F[j+36>>2]=0;F[j+24>>2]=0;F[j+28>>2]=0;F[j+16>>2]=0;F[j+20>>2]=0;F[j+8>>2]=0;F[j+12>>2]=0;c:{if(!Nd(j+8|0,c)){break c}if(!Md(j+8|0,c)|(F[j+20>>2]?0:e)){break c}hc(c,0,0);if(e){s=f<<2;t=F[j+36>>2];u=F[j+48>>2];x=F[j+24>>2];l=F[j+56>>2];i=F[j+52>>2];while(1){d:{if(l>>>0>16383){break d}while(1){if((i|0)<=0){break d}i=i-1|0;F[j+52>>2]=i;l=G[i+u|0]|l<<8;F[j+56>>2]=l;if(l>>>0<16384){continue}break}}a=l&4095;r=F[(a<<2)+x>>2];b=(r<<3)+t|0;l=(L(F[b>>2],l>>>12|0)+a|0)-F[b+4>>2]|0;F[j+56>>2]=l;if((f|0)>0){a=0;if(!G[c+36|0]|r>>>0>32){break c}o=d+f|0;e:{if(!r){ma(g+(d<<2)|0,0,s);break e}y=r&-2;z=r&1;b=F[c+32>>2];h=F[c+28>>2];q=F[c+24>>2];while(1){k=0;a=b;m=0;n=0;if((r|0)!=1){while(1){p=q+(a>>>3|0)|0;f:{if(p>>>0>=h>>>0){p=0;break f}p=G[p|0];b=a+1|0;F[c+32>>2]=b;p=p>>>(a&7)&1;a=b}p=p<<k|m;m=0;v=q+(a>>>3|0)|0;if(v>>>0<h>>>0){m=G[v|0];b=a+1|0;F[c+32>>2]=b;m=m>>>(a&7)&1;a=b}v=k|1;k=k+2|0;m=p|m<<v;n=n+2|0;if((y|0)!=(n|0)){continue}break}}n=g+(d<<2)|0;if(z){p=q+(a>>>3|0)|0;if(p>>>0<h>>>0){p=G[p|0];b=a+1|0;F[c+32>>2]=b;a=p>>>(a&7)&1}else{a=0}m=a<<k|m}F[n>>2]=m;d=d+1|0;if((o|0)!=(d|0)){continue}break}}d=o}w=f+w|0;if(e>>>0>w>>>0){continue}break}}D[c+36|0]=0;f=F[c+20>>2];a=0;m=F[c+32>>2]+7|0;a=m>>>0<7?1:a;m=a<<29|m>>>3;b=m+F[c+16>>2]|0;a=(a>>>3|0)+f|0;F[c+16>>2]=b;F[c+20>>2]=b>>>0<m>>>0?a+1|0:a;a=1}b=F[j+36>>2];if(b){F[j+40>>2]=b;ja(b)}b=F[j+24>>2];if(b){F[j+28>>2]=b;ja(b)}b=F[j+8>>2];if(b){F[j+12>>2]=b;ja(b)}Z=j- -64|0;return a;case 1:break b;default:break a}}b=0;e=F[c+20>>2];o=F[c+12>>2];g=F[c+16>>2];g:{if((e|0)>=(o|0)&g>>>0>=I[c+8>>2]|(e|0)>(o|0)){break g}o=G[g+F[c>>2]|0];g=g+1|0;e=g?e:e+1|0;F[c+16>>2]=g;F[c+20>>2]=e;h:{switch(o-1|0){case 8:o=a;r=d;e=Z+-64|0;Z=e;F[e+56>>2]=0;F[e+48>>2]=0;F[e+52>>2]=0;F[e+40>>2]=0;F[e+44>>2]=0;F[e+32>>2]=0;F[e+36>>2]=0;F[e+24>>2]=0;F[e+28>>2]=0;F[e+16>>2]=0;F[e+20>>2]=0;F[e+8>>2]=0;F[e+12>>2]=0;h=e+8|0;i:{j:{if(!H[c+38>>1]){break j}if(!Ta(1,h+12|0,c)){break j}b=F[c+8>>2];d=F[c+16>>2];f=b-d|0;i=F[h+12>>2];b=F[c+12>>2]-(F[c+20>>2]+(b>>>0<d>>>0)|0)|0;if(f>>>0<i>>>6>>>0&(b|0)<=0|(b|0)<0){break j}b=F[h>>2];a=F[h+4>>2]-b>>2;k:{if(a>>>0<i>>>0){qa(h,i-a|0);i=F[h+12>>2];break k}if(a>>>0<=i>>>0){break k}F[h+4>>2]=b+(i<<2)}g=1;if(!i){break i}f=F[c+16>>2];d=F[c+20>>2];s=F[h>>2];j=F[c+8>>2];n=F[c+12>>2];b=0;while(1){g=0;if((d|0)>=(n|0)&f>>>0>=j>>>0|(d|0)>(n|0)){break i}g=F[c>>2];p=G[g+f|0];a=d;f=f+1|0;a=f?a:a+1|0;F[c+16>>2]=f;d=a;F[c+20>>2]=a;a=p>>>2|0;l=0;l:{m:{n:{o:{t=p&3;switch(t|0){case 0:break m;case 3:break o;default:break n}}a=a+b|0;g=0;if(a>>>0>=i>>>0){break i}ma(s+(b<<2)|0,0,(p&252)+4|0);b=a;break l}while(1){if((f|0)==(j|0)&(d|0)==(n|0)){break j}i=G[f+g|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;a=i<<(l<<3|6)|a;l=l+1|0;if((t|0)!=(l|0)){continue}break}}F[s+(b<<2)>>2]=a}b=b+1|0;i=F[h+12>>2];if(b>>>0<i>>>0){continue}break}a=h+16|0;n=F[h>>2];d=F[h+16>>2];b=F[h+20>>2]-d|0;p:{if(b>>>0<=32767){qa(a,8192-(b>>>2|0)|0);break p}if((b|0)==32768){break p}F[h+20>>2]=d+32768}d=h+28|0;b=F[d>>2];f=F[h+32>>2]-b>>3;q:{if(f>>>0<i>>>0){_a(d,i-f|0);b=F[d>>2];break q}if(f>>>0>i>>>0){F[h+32>>2]=(i<<3)+b}if(!i){break j}}j=F[a>>2];f=0;d=0;while(1){g=n+(f<<2)|0;l=F[g>>2];h=(f<<3)+b|0;a=d;F[h+4>>2]=a;F[h>>2]=l;g=F[g>>2];d=g+a|0;if(d>>>0>8192){break j}r:{if(a>>>0>=d>>>0){break r}l=0;h=g&7;if(h){while(1){F[j+(a<<2)>>2]=f;a=a+1|0;l=l+1|0;if((h|0)!=(l|0)){continue}break}}if(g-1>>>0<=6){break r}while(1){g=j+(a<<2)|0;F[g>>2]=f;F[g+28>>2]=f;F[g+24>>2]=f;F[g+20>>2]=f;F[g+16>>2]=f;F[g+12>>2]=f;F[g+8>>2]=f;F[g+4>>2]=f;a=a+8|0;if((d|0)!=(a|0)){continue}break}}f=f+1|0;if((i|0)!=(f|0)){continue}break}k=(d|0)==8192}g=k}s:{if(!g|(F[e+20>>2]?0:o)){break s}d=0;k=Z-16|0;Z=k;t:{if(!Sa(1,k+8|0,c)){break t}a=F[c+8>>2];f=F[c+16>>2];g=a-f|0;j=F[k+12>>2];i=F[c+20>>2];a=F[c+12>>2]-(i+(a>>>0<f>>>0)|0)|0;b=F[k+8>>2];if((j|0)==(a|0)&g>>>0<b>>>0|a>>>0<j>>>0){break t}a=i+j|0;g=b+f|0;a=g>>>0<f>>>0?a+1|0:a;F[c+16>>2]=g;F[c+20>>2]=a;if((b|0)<=0){break t}a=f+F[c>>2]|0;F[e+48>>2]=a;c=b-1|0;f=c+a|0;g=G[f|0];u:{if(g>>>0<=63){F[e+52>>2]=c;a=G[f|0]&63;break u}v:{switch((g>>>6|0)-1|0){case 0:if(b>>>0<2){break t}b=b-2|0;F[e+52>>2]=b;a=a+b|0;a=G[a+1|0]<<8&16128|G[a|0];break u;case 1:if(b>>>0<3){break t}b=b-3|0;F[e+52>>2]=b;a=a+b|0;a=G[a+1|0]<<8|G[a+2|0]<<16&4128768|G[a|0];break u;default:break v}}b=b-4|0;F[e+52>>2]=b;a=a+b|0;a=(G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24))&1073741823}F[e+56>>2]=a+32768;d=a>>>0<8355840}Z=k+16|0;if(!d){break s}if(!o){m=1;break s}b=F[e+52>>2];a=F[e+56>>2];c=F[e+36>>2];d=F[e+48>>2];f=F[e+24>>2];while(1){w:{if(a>>>0>32767){break w}while(1){if((b|0)<=0){break w}b=b-1|0;F[e+52>>2]=b;a=G[b+d|0]|a<<8;F[e+56>>2]=a;if(a>>>0<32768){continue}break}}m=a&8191;k=F[f+(m<<2)>>2];g=c+(k<<3)|0;a=(L(F[g>>2],a>>>13|0)+m|0)-F[g+4>>2]|0;F[e+56>>2]=a;F[r+(q<<2)>>2]=k;m=1;q=q+1|0;if((o|0)!=(q|0)){continue}break}}a=F[e+36>>2];if(a){F[e+40>>2]=a;ja(a)}a=F[e+24>>2];if(a){F[e+28>>2]=a;ja(a)}a=F[e+8>>2];if(a){F[e+12>>2]=a;ja(a)}Z=e- -64|0;b=m;break g;case 9:o=a;r=d;g=Z+-64|0;Z=g;F[g+56>>2]=0;F[g+48>>2]=0;F[g+52>>2]=0;F[g+40>>2]=0;F[g+44>>2]=0;F[g+32>>2]=0;F[g+36>>2]=0;F[g+24>>2]=0;F[g+28>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;F[g+8>>2]=0;F[g+12>>2]=0;h=g+8|0;x:{y:{if(!H[c+38>>1]){break y}if(!Ta(1,h+12|0,c)){break y}b=F[c+8>>2];d=F[c+16>>2];f=b-d|0;i=F[h+12>>2];b=F[c+12>>2]-(F[c+20>>2]+(b>>>0<d>>>0)|0)|0;if(f>>>0<i>>>6>>>0&(b|0)<=0|(b|0)<0){break y}b=F[h>>2];a=F[h+4>>2]-b>>2;z:{if(a>>>0<i>>>0){qa(h,i-a|0);i=F[h+12>>2];break z}if(a>>>0<=i>>>0){break z}F[h+4>>2]=b+(i<<2)}e=1;if(!i){break x}f=F[c+16>>2];d=F[c+20>>2];s=F[h>>2];j=F[c+8>>2];n=F[c+12>>2];b=0;while(1){e=0;if((d|0)>=(n|0)&f>>>0>=j>>>0|(d|0)>(n|0)){break x}t=F[c>>2];p=G[t+f|0];e=d;f=f+1|0;e=f?e:e+1|0;F[c+16>>2]=f;d=e;F[c+20>>2]=e;a=p>>>2|0;l=0;A:{B:{C:{D:{e=p&3;switch(e|0){case 0:break B;case 3:break D;default:break C}}a=a+b|0;e=0;if(a>>>0>=i>>>0){break x}ma(s+(b<<2)|0,0,(p&252)+4|0);b=a;break A}while(1){if((f|0)==(j|0)&(d|0)==(n|0)){break y}i=G[f+t|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;a=i<<(l<<3|6)|a;l=l+1|0;if((e|0)!=(l|0)){continue}break}}F[s+(b<<2)>>2]=a}b=b+1|0;i=F[h+12>>2];if(b>>>0<i>>>0){continue}break}a=h+16|0;n=F[h>>2];d=F[h+16>>2];b=F[h+20>>2]-d|0;E:{if(b>>>0<=131071){qa(a,32768-(b>>>2|0)|0);break E}if((b|0)==131072){break E}F[h+20>>2]=d+131072}d=h+28|0;b=F[d>>2];f=F[h+32>>2]-b>>3;F:{if(f>>>0<i>>>0){_a(d,i-f|0);b=F[d>>2];break F}if(f>>>0>i>>>0){F[h+32>>2]=(i<<3)+b}if(!i){break y}}j=F[a>>2];f=0;d=0;while(1){e=n+(f<<2)|0;l=F[e>>2];h=(f<<3)+b|0;a=d;F[h+4>>2]=a;F[h>>2]=l;e=F[e>>2];d=e+a|0;if(d>>>0>32768){break y}G:{if(a>>>0>=d>>>0){break G}l=0;h=e&7;if(h){while(1){F[j+(a<<2)>>2]=f;a=a+1|0;l=l+1|0;if((h|0)!=(l|0)){continue}break}}if(e-1>>>0<=6){break G}while(1){e=j+(a<<2)|0;F[e>>2]=f;F[e+28>>2]=f;F[e+24>>2]=f;F[e+20>>2]=f;F[e+16>>2]=f;F[e+12>>2]=f;F[e+8>>2]=f;F[e+4>>2]=f;a=a+8|0;if((d|0)!=(a|0)){continue}break}}f=f+1|0;if((i|0)!=(f|0)){continue}break}k=(d|0)==32768}e=k}H:{if(!e|(F[g+20>>2]?0:o)){break H}d=0;f=Z-16|0;Z=f;I:{if(!Sa(1,f+8|0,c)){break I}e=F[c+8>>2];b=F[c+16>>2];k=e-b|0;j=F[f+12>>2];i=F[c+20>>2];e=F[c+12>>2]-(i+(b>>>0>e>>>0)|0)|0;a=F[f+8>>2];if((j|0)==(e|0)&k>>>0<a>>>0|e>>>0<j>>>0){break I}e=i+j|0;k=a+b|0;e=k>>>0<b>>>0?e+1|0:e;F[c+16>>2]=k;F[c+20>>2]=e;if((a|0)<=0){break I}b=b+F[c>>2]|0;F[g+48>>2]=b;c=a-1|0;e=c+b|0;k=G[e|0];J:{if(k>>>0<=63){F[g+52>>2]=c;a=G[e|0]&63;break J}K:{switch((k>>>6|0)-1|0){case 0:if(a>>>0<2){break I}a=a-2|0;F[g+52>>2]=a;a=a+b|0;a=G[a+1|0]<<8&16128|G[a|0];break J;case 1:if(a>>>0<3){break I}a=a-3|0;F[g+52>>2]=a;a=a+b|0;a=G[a+1|0]<<8|G[a+2|0]<<16&4128768|G[a|0];break J;default:break K}}a=a-4|0;F[g+52>>2]=a;a=a+b|0;a=(G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24))&1073741823}F[g+56>>2]=a+131072;d=a>>>0<33423360}Z=f+16|0;if(!d){break H}if(!o){m=1;break H}b=F[g+52>>2];a=F[g+56>>2];c=F[g+36>>2];d=F[g+48>>2];f=F[g+24>>2];while(1){L:{if(a>>>0>131071){break L}while(1){if((b|0)<=0){break L}b=b-1|0;F[g+52>>2]=b;a=G[b+d|0]|a<<8;F[g+56>>2]=a;if(a>>>0<131072){continue}break}}m=a&32767;e=F[f+(m<<2)>>2];k=c+(e<<3)|0;a=(L(F[k>>2],a>>>15|0)+m|0)-F[k+4>>2]|0;F[g+56>>2]=a;F[r+(q<<2)>>2]=e;m=1;q=q+1|0;if((o|0)!=(q|0)){continue}break}}a=F[g+36>>2];if(a){F[g+40>>2]=a;ja(a)}a=F[g+24>>2];if(a){F[g+28>>2]=a;ja(a)}a=F[g+8>>2];if(a){F[g+12>>2]=a;ja(a)}Z=g- -64|0;b=m;break g;case 10:o=a;r=d;g=Z+-64|0;Z=g;F[g+56>>2]=0;F[g+48>>2]=0;F[g+52>>2]=0;F[g+40>>2]=0;F[g+44>>2]=0;F[g+32>>2]=0;F[g+36>>2]=0;F[g+24>>2]=0;F[g+28>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;F[g+8>>2]=0;F[g+12>>2]=0;h=g+8|0;M:{N:{if(!H[c+38>>1]){break N}if(!Ta(1,h+12|0,c)){break N}b=F[c+8>>2];d=F[c+16>>2];f=b-d|0;i=F[h+12>>2];b=F[c+12>>2]-(F[c+20>>2]+(b>>>0<d>>>0)|0)|0;if(f>>>0<i>>>6>>>0&(b|0)<=0|(b|0)<0){break N}b=F[h>>2];a=F[h+4>>2]-b>>2;O:{if(a>>>0<i>>>0){qa(h,i-a|0);i=F[h+12>>2];break O}if(a>>>0<=i>>>0){break O}F[h+4>>2]=b+(i<<2)}e=1;if(!i){break M}f=F[c+16>>2];d=F[c+20>>2];s=F[h>>2];j=F[c+8>>2];n=F[c+12>>2];b=0;while(1){e=0;if((d|0)>=(n|0)&f>>>0>=j>>>0|(d|0)>(n|0)){break M}t=F[c>>2];p=G[t+f|0];a=d;f=f+1|0;a=f?a:a+1|0;F[c+16>>2]=f;d=a;F[c+20>>2]=a;a=p>>>2|0;l=0;P:{Q:{R:{S:{u=p&3;switch(u|0){case 0:break Q;case 3:break S;default:break R}}a=a+b|0;e=0;if(a>>>0>=i>>>0){break M}ma(s+(b<<2)|0,0,(p&252)+4|0);b=a;break P}while(1){if((f|0)==(j|0)&(d|0)==(n|0)){break N}i=G[f+t|0];e=d;f=f+1|0;e=f?e:e+1|0;F[c+16>>2]=f;d=e;F[c+20>>2]=e;a=i<<(l<<3|6)|a;l=l+1|0;if((u|0)!=(l|0)){continue}break}}F[s+(b<<2)>>2]=a}b=b+1|0;i=F[h+12>>2];if(b>>>0<i>>>0){continue}break}a=h+16|0;n=F[h>>2];d=F[h+16>>2];b=F[h+20>>2]-d|0;T:{if(b>>>0<=262143){qa(a,65536-(b>>>2|0)|0);break T}if((b|0)==262144){break T}F[h+20>>2]=d+262144}d=h+28|0;b=F[d>>2];f=F[h+32>>2]-b>>3;U:{if(f>>>0<i>>>0){_a(d,i-f|0);b=F[d>>2];break U}if(f>>>0>i>>>0){F[h+32>>2]=(i<<3)+b}if(!i){break N}}j=F[a>>2];f=0;d=0;while(1){e=n+(f<<2)|0;l=F[e>>2];h=(f<<3)+b|0;a=d;F[h+4>>2]=a;F[h>>2]=l;e=F[e>>2];d=e+a|0;if(d>>>0>65536){break N}V:{if(a>>>0>=d>>>0){break V}l=0;h=e&7;if(h){while(1){F[j+(a<<2)>>2]=f;a=a+1|0;l=l+1|0;if((h|0)!=(l|0)){continue}break}}if(e-1>>>0<=6){break V}while(1){e=j+(a<<2)|0;F[e>>2]=f;F[e+28>>2]=f;F[e+24>>2]=f;F[e+20>>2]=f;F[e+16>>2]=f;F[e+12>>2]=f;F[e+8>>2]=f;F[e+4>>2]=f;a=a+8|0;if((d|0)!=(a|0)){continue}break}}f=f+1|0;if((i|0)!=(f|0)){continue}break}k=(d|0)==65536}e=k}W:{if(!e|(F[g+20>>2]?0:o)){break W}d=0;e=Z-16|0;Z=e;X:{if(!Sa(1,e+8|0,c)){break X}a=F[c+8>>2];f=F[c+16>>2];k=a-f|0;j=F[e+12>>2];i=F[c+20>>2];a=F[c+12>>2]-(i+(a>>>0<f>>>0)|0)|0;b=F[e+8>>2];if((j|0)==(a|0)&k>>>0<b>>>0|a>>>0<j>>>0){break X}a=i+j|0;k=b+f|0;a=k>>>0<f>>>0?a+1|0:a;F[c+16>>2]=k;F[c+20>>2]=a;if((b|0)<=0){break X}a=f+F[c>>2]|0;F[g+48>>2]=a;c=b-1|0;f=c+a|0;k=G[f|0];Y:{if(k>>>0<=63){F[g+52>>2]=c;a=G[f|0]&63;break Y}Z:{switch((k>>>6|0)-1|0){case 0:if(b>>>0<2){break X}b=b-2|0;F[g+52>>2]=b;a=a+b|0;a=G[a+1|0]<<8&16128|G[a|0];break Y;case 1:if(b>>>0<3){break X}b=b-3|0;F[g+52>>2]=b;a=a+b|0;a=G[a+1|0]<<8|G[a+2|0]<<16&4128768|G[a|0];break Y;default:break Z}}b=b-4|0;F[g+52>>2]=b;a=a+b|0;a=(G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24))&1073741823}F[g+56>>2]=a+262144;d=a>>>0<66846720}Z=e+16|0;if(!d){break W}if(!o){m=1;break W}b=F[g+52>>2];a=F[g+56>>2];c=F[g+36>>2];d=F[g+48>>2];f=F[g+24>>2];while(1){_:{if(a>>>0>262143){break _}while(1){if((b|0)<=0){break _}b=b-1|0;F[g+52>>2]=b;a=G[b+d|0]|a<<8;F[g+56>>2]=a;if(a>>>0<262144){continue}break}}m=a&65535;e=F[f+(m<<2)>>2];k=c+(e<<3)|0;a=(L(F[k>>2],a>>>16|0)+m|0)-F[k+4>>2]|0;F[g+56>>2]=a;F[r+(q<<2)>>2]=e;m=1;q=q+1|0;if((o|0)!=(q|0)){continue}break}}a=F[g+36>>2];if(a){F[g+40>>2]=a;ja(a)}a=F[g+24>>2];if(a){F[g+28>>2]=a;ja(a)}a=F[g+8>>2];if(a){F[g+12>>2]=a;ja(a)}Z=g- -64|0;b=m;break g;case 11:o=a;r=d;g=Z+-64|0;Z=g;F[g+56>>2]=0;F[g+48>>2]=0;F[g+52>>2]=0;F[g+40>>2]=0;F[g+44>>2]=0;F[g+32>>2]=0;F[g+36>>2]=0;F[g+24>>2]=0;F[g+28>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;F[g+8>>2]=0;F[g+12>>2]=0;h=g+8|0;$:{aa:{if(!H[c+38>>1]){break aa}if(!Ta(1,h+12|0,c)){break aa}b=F[c+8>>2];d=F[c+16>>2];f=b-d|0;i=F[h+12>>2];b=F[c+12>>2]-(F[c+20>>2]+(b>>>0<d>>>0)|0)|0;if(f>>>0<i>>>6>>>0&(b|0)<=0|(b|0)<0){break aa}b=F[h>>2];a=F[h+4>>2]-b>>2;ba:{if(a>>>0<i>>>0){qa(h,i-a|0);i=F[h+12>>2];break ba}if(a>>>0<=i>>>0){break ba}F[h+4>>2]=b+(i<<2)}e=1;if(!i){break $}f=F[c+16>>2];d=F[c+20>>2];s=F[h>>2];j=F[c+8>>2];n=F[c+12>>2];b=0;while(1){e=0;if((d|0)>=(n|0)&f>>>0>=j>>>0|(d|0)>(n|0)){break $}t=F[c>>2];p=G[t+f|0];e=d;f=f+1|0;e=f?e:e+1|0;F[c+16>>2]=f;d=e;F[c+20>>2]=e;a=p>>>2|0;l=0;ca:{da:{ea:{fa:{e=p&3;switch(e|0){case 0:break da;case 3:break fa;default:break ea}}a=a+b|0;e=0;if(a>>>0>=i>>>0){break $}ma(s+(b<<2)|0,0,(p&252)+4|0);b=a;break ca}while(1){if((f|0)==(j|0)&(d|0)==(n|0)){break aa}i=G[f+t|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;a=i<<(l<<3|6)|a;l=l+1|0;if((e|0)!=(l|0)){continue}break}}F[s+(b<<2)>>2]=a}b=b+1|0;i=F[h+12>>2];if(b>>>0<i>>>0){continue}break}a=h+16|0;n=F[h>>2];d=F[h+16>>2];b=F[h+20>>2]-d|0;ga:{if(b>>>0<=1048575){qa(a,262144-(b>>>2|0)|0);break ga}if((b|0)==1048576){break ga}F[h+20>>2]=d- -1048576}d=h+28|0;b=F[d>>2];f=F[h+32>>2]-b>>3;ha:{if(f>>>0<i>>>0){_a(d,i-f|0);b=F[d>>2];break ha}if(f>>>0>i>>>0){F[h+32>>2]=(i<<3)+b}if(!i){break aa}}j=F[a>>2];f=0;d=0;while(1){e=n+(f<<2)|0;l=F[e>>2];h=(f<<3)+b|0;a=d;F[h+4>>2]=a;F[h>>2]=l;e=F[e>>2];d=e+a|0;if(d>>>0>262144){break aa}ia:{if(a>>>0>=d>>>0){break ia}l=0;h=e&7;if(h){while(1){F[j+(a<<2)>>2]=f;a=a+1|0;l=l+1|0;if((h|0)!=(l|0)){continue}break}}if(e-1>>>0<=6){break ia}while(1){e=j+(a<<2)|0;F[e>>2]=f;F[e+28>>2]=f;F[e+24>>2]=f;F[e+20>>2]=f;F[e+16>>2]=f;F[e+12>>2]=f;F[e+8>>2]=f;F[e+4>>2]=f;a=a+8|0;if((d|0)!=(a|0)){continue}break}}f=f+1|0;if((i|0)!=(f|0)){continue}break}k=(d|0)==262144}e=k}ja:{if(!e|(F[g+20>>2]?0:o)){break ja}d=0;f=Z-16|0;Z=f;ka:{if(!Sa(1,f+8|0,c)){break ka}e=F[c+8>>2];b=F[c+16>>2];k=e-b|0;j=F[f+12>>2];i=F[c+20>>2];e=F[c+12>>2]-(i+(b>>>0>e>>>0)|0)|0;a=F[f+8>>2];if((j|0)==(e|0)&k>>>0<a>>>0|e>>>0<j>>>0){break ka}e=i+j|0;k=a+b|0;e=k>>>0<b>>>0?e+1|0:e;F[c+16>>2]=k;F[c+20>>2]=e;if((a|0)<=0){break ka}b=b+F[c>>2]|0;F[g+48>>2]=b;c=a-1|0;e=c+b|0;k=G[e|0];la:{if(k>>>0<=63){F[g+52>>2]=c;a=G[e|0]&63;break la}ma:{switch((k>>>6|0)-1|0){case 0:if(a>>>0<2){break ka}a=a-2|0;F[g+52>>2]=a;a=a+b|0;a=G[a+1|0]<<8&16128|G[a|0];break la;case 1:if(a>>>0<3){break ka}a=a-3|0;F[g+52>>2]=a;a=a+b|0;a=G[a+1|0]<<8|G[a+2|0]<<16&4128768|G[a|0];break la;default:break ma}}a=a-4|0;F[g+52>>2]=a;a=a+b|0;a=(G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24))&1073741823}F[g+56>>2]=a- -1048576;d=a>>>0<267386880}Z=f+16|0;if(!d){break ja}if(!o){m=1;break ja}b=F[g+52>>2];a=F[g+56>>2];c=F[g+36>>2];d=F[g+48>>2];f=F[g+24>>2];while(1){na:{if(a>>>0>1048575){break na}while(1){if((b|0)<=0){break na}b=b-1|0;F[g+52>>2]=b;a=G[b+d|0]|a<<8;F[g+56>>2]=a;if(a>>>0<1048576){continue}break}}m=a&262143;e=F[f+(m<<2)>>2];k=c+(e<<3)|0;a=(L(F[k>>2],a>>>18|0)+m|0)-F[k+4>>2]|0;F[g+56>>2]=a;F[r+(q<<2)>>2]=e;m=1;q=q+1|0;if((o|0)!=(q|0)){continue}break}}a=F[g+36>>2];if(a){F[g+40>>2]=a;ja(a)}a=F[g+24>>2];if(a){F[g+28>>2]=a;ja(a)}a=F[g+8>>2];if(a){F[g+12>>2]=a;ja(a)}Z=g- -64|0;b=m;break g;case 12:o=a;r=d;e=Z+-64|0;Z=e;F[e+56>>2]=0;F[e+48>>2]=0;F[e+52>>2]=0;F[e+40>>2]=0;F[e+44>>2]=0;F[e+32>>2]=0;F[e+36>>2]=0;F[e+24>>2]=0;F[e+28>>2]=0;F[e+16>>2]=0;F[e+20>>2]=0;F[e+8>>2]=0;F[e+12>>2]=0;h=e+8|0;oa:{pa:{if(!H[c+38>>1]){break pa}if(!Ta(1,h+12|0,c)){break pa}b=F[c+8>>2];d=F[c+16>>2];f=b-d|0;i=F[h+12>>2];b=F[c+12>>2]-(F[c+20>>2]+(b>>>0<d>>>0)|0)|0;if(f>>>0<i>>>6>>>0&(b|0)<=0|(b|0)<0){break pa}b=F[h>>2];a=F[h+4>>2]-b>>2;qa:{if(a>>>0<i>>>0){qa(h,i-a|0);i=F[h+12>>2];break qa}if(a>>>0<=i>>>0){break qa}F[h+4>>2]=b+(i<<2)}g=1;if(!i){break oa}f=F[c+16>>2];d=F[c+20>>2];s=F[h>>2];j=F[c+8>>2];n=F[c+12>>2];b=0;while(1){g=0;if((d|0)>=(n|0)&f>>>0>=j>>>0|(d|0)>(n|0)){break oa}g=F[c>>2];p=G[g+f|0];a=d;f=f+1|0;a=f?a:a+1|0;F[c+16>>2]=f;d=a;F[c+20>>2]=a;a=p>>>2|0;l=0;ra:{sa:{ta:{ua:{t=p&3;switch(t|0){case 0:break sa;case 3:break ua;default:break ta}}a=a+b|0;g=0;if(a>>>0>=i>>>0){break oa}ma(s+(b<<2)|0,0,(p&252)+4|0);b=a;break ra}while(1){if((f|0)==(j|0)&(d|0)==(n|0)){break pa}i=G[f+g|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;a=i<<(l<<3|6)|a;l=l+1|0;if((t|0)!=(l|0)){continue}break}}F[s+(b<<2)>>2]=a}b=b+1|0;i=F[h+12>>2];if(b>>>0<i>>>0){continue}break}a=h+16|0;n=F[h>>2];d=F[h+16>>2];b=F[h+20>>2]-d|0;va:{if(b>>>0<=2097151){qa(a,524288-(b>>>2|0)|0);break va}if((b|0)==2097152){break va}F[h+20>>2]=d+2097152}d=h+28|0;b=F[d>>2];f=F[h+32>>2]-b>>3;wa:{if(f>>>0<i>>>0){_a(d,i-f|0);b=F[d>>2];break wa}if(f>>>0>i>>>0){F[h+32>>2]=(i<<3)+b}if(!i){break pa}}j=F[a>>2];f=0;d=0;while(1){g=n+(f<<2)|0;l=F[g>>2];h=(f<<3)+b|0;a=d;F[h+4>>2]=a;F[h>>2]=l;g=F[g>>2];d=g+a|0;if(d>>>0>524288){break pa}xa:{if(a>>>0>=d>>>0){break xa}l=0;h=g&7;if(h){while(1){F[j+(a<<2)>>2]=f;a=a+1|0;l=l+1|0;if((h|0)!=(l|0)){continue}break}}if(g-1>>>0<=6){break xa}while(1){g=j+(a<<2)|0;F[g>>2]=f;F[g+28>>2]=f;F[g+24>>2]=f;F[g+20>>2]=f;F[g+16>>2]=f;F[g+12>>2]=f;F[g+8>>2]=f;F[g+4>>2]=f;a=a+8|0;if((d|0)!=(a|0)){continue}break}}f=f+1|0;if((i|0)!=(f|0)){continue}break}k=(d|0)==524288}g=k}ya:{if(!g|(F[e+20>>2]?0:o)){break ya}d=0;k=Z-16|0;Z=k;za:{if(!Sa(1,k+8|0,c)){break za}a=F[c+8>>2];f=F[c+16>>2];g=a-f|0;j=F[k+12>>2];i=F[c+20>>2];a=F[c+12>>2]-(i+(a>>>0<f>>>0)|0)|0;b=F[k+8>>2];if((j|0)==(a|0)&g>>>0<b>>>0|a>>>0<j>>>0){break za}a=i+j|0;g=b+f|0;a=g>>>0<f>>>0?a+1|0:a;F[c+16>>2]=g;F[c+20>>2]=a;if((b|0)<=0){break za}a=f+F[c>>2]|0;F[e+48>>2]=a;c=b-1|0;f=c+a|0;g=G[f|0];Aa:{if(g>>>0<=63){F[e+52>>2]=c;a=G[f|0]&63;break Aa}Ba:{switch((g>>>6|0)-1|0){case 0:if(b>>>0<2){break za}b=b-2|0;F[e+52>>2]=b;a=a+b|0;a=G[a+1|0]<<8&16128|G[a|0];break Aa;case 1:if(b>>>0<3){break za}b=b-3|0;F[e+52>>2]=b;a=a+b|0;a=G[a+1|0]<<8|G[a+2|0]<<16&4128768|G[a|0];break Aa;default:break Ba}}b=b-4|0;F[e+52>>2]=b;a=a+b|0;a=(G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24))&1073741823}F[e+56>>2]=a+2097152;d=a>>>0<534773760}Z=k+16|0;if(!d){break ya}if(!o){m=1;break ya}b=F[e+52>>2];a=F[e+56>>2];c=F[e+36>>2];d=F[e+48>>2];f=F[e+24>>2];while(1){Ca:{if(a>>>0>2097151){break Ca}while(1){if((b|0)<=0){break Ca}b=b-1|0;F[e+52>>2]=b;a=G[b+d|0]|a<<8;F[e+56>>2]=a;if(a>>>0<2097152){continue}break}}m=a&524287;k=F[f+(m<<2)>>2];g=c+(k<<3)|0;a=(L(F[g>>2],a>>>19|0)+m|0)-F[g+4>>2]|0;F[e+56>>2]=a;F[r+(q<<2)>>2]=k;m=1;q=q+1|0;if((o|0)!=(q|0)){continue}break}}a=F[e+36>>2];if(a){F[e+40>>2]=a;ja(a)}a=F[e+24>>2];if(a){F[e+28>>2]=a;ja(a)}a=F[e+8>>2];if(a){F[e+12>>2]=a;ja(a)}Z=e- -64|0;b=m;break g;case 17:b=Ld(a,c,d);break g;case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:b=Z+-64|0;Z=b;F[b+56>>2]=0;F[b+48>>2]=0;F[b+52>>2]=0;F[b+40>>2]=0;F[b+44>>2]=0;F[b+32>>2]=0;F[b+36>>2]=0;F[b+24>>2]=0;F[b+28>>2]=0;F[b+16>>2]=0;F[b+20>>2]=0;F[b+8>>2]=0;F[b+12>>2]=0;Da:{if(!Nd(b+8|0,c)|(F[b+20>>2]?0:a)){break Da}if(!Md(b+8|0,c)){break Da}if(!a){f=1;break Da}m=F[b+52>>2];c=F[b+56>>2];e=F[b+36>>2];g=F[b+48>>2];o=F[b+24>>2];while(1){Ea:{if(c>>>0>16383){break Ea}while(1){if((m|0)<=0){break Ea}m=m-1|0;F[b+52>>2]=m;c=G[g+m|0]|c<<8;F[b+56>>2]=c;if(c>>>0<16384){continue}break}}f=c&4095;j=F[o+(f<<2)>>2];r=e+(j<<3)|0;c=(L(F[r>>2],c>>>12|0)+f|0)-F[r+4>>2]|0;F[b+56>>2]=c;F[(k<<2)+d>>2]=j;f=1;k=k+1|0;if((k|0)!=(a|0)){continue}break}}a=F[b+36>>2];if(a){F[b+40>>2]=a;ja(a)}a=F[b+24>>2];if(a){F[b+28>>2]=a;ja(a)}a=F[b+8>>2];if(a){F[b+12>>2]=a;ja(a)}Z=b- -64|0;b=f;break g;case 13:case 14:case 15:case 16:break h;default:break g}}b=Ld(a,c,d)}f=b}return f}function ih(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,J=0,K=0,M=0,N=0,O=0,P=0;u=Z+-64|0;Z=u;F[a+132>>2]=0;if(F[a+148>>2]){b=F[a+144>>2];if(b){while(1){f=F[b>>2];ja(b);b=f;if(b){continue}break}}b=0;F[a+144>>2]=0;f=F[a+140>>2];a:{if(!f){break a}if(f>>>0>=4){c=f&-4;while(1){e=b<<2;F[e+F[a+136>>2]>>2]=0;F[F[a+136>>2]+(e|4)>>2]=0;F[F[a+136>>2]+(e|8)>>2]=0;F[F[a+136>>2]+(e|12)>>2]=0;b=b+4|0;d=d+4|0;if((c|0)!=(d|0)){continue}break}}f=f&3;if(!f){break a}d=0;while(1){F[F[a+136>>2]+(b<<2)>>2]=0;b=b+1|0;d=d+1|0;if((f|0)!=(d|0)){continue}break}}F[a+148>>2]=0}b:{c:{if(!Da(1,u+60|0,F[F[a+4>>2]+32>>2])){break c}F[a+156>>2]=F[u+60>>2];if(!Da(1,u+56|0,F[F[a+4>>2]+32>>2])){break c}e=F[u+56>>2];if(e>>>0>1431655765|I[a+156>>2]>L(e,3)>>>0){break c}b=F[F[a+4>>2]+32>>2];c=F[b+8>>2];k=F[b+12>>2];d=F[b+20>>2];f=F[b+16>>2];if((k|0)<=(d|0)&f>>>0>=c>>>0|(d|0)>(k|0)){break c}k=G[f+F[b>>2]|0];f=f+1|0;d=f?d:d+1|0;F[b+16>>2]=f;F[b+20>>2]=d;if(!Da(1,u+52|0,b)){break c}q=F[u+52>>2];if(q>>>0>e>>>0|e>>>0>q+((q>>>0)/3|0)>>>0){break c}if(!Da(1,u+48|0,F[F[a+4>>2]+32>>2])){break c}d=F[u+48>>2];if(d>>>0>q>>>0){break c}F[a+28>>2]=F[a+24>>2];f=Zb(ka(88));b=F[a+8>>2];F[a+8>>2]=f;if(b){Za(b);if(!F[a+8>>2]){break c}}F[a+164>>2]=F[a+160>>2];Ib(a+160|0,e);F[a+176>>2]=F[a+172>>2];Ib(a+172|0,e);F[a- -64>>2]=0;F[a+92>>2]=-1;F[a+84>>2]=-1;F[a+88>>2]=-1;F[a+40>>2]=F[a+36>>2];F[a+52>>2]=F[a+48>>2];F[a+76>>2]=F[a+72>>2];A=a+216|0;Dd(A);Cd(A,k);if(!_c(F[a+8>>2],e,d+F[a+156>>2]|0)){break c}b=F[a+156>>2];D[u+8|0]=1;Ea(a+120|0,b+d|0,u+8|0);if((Bd(a,F[F[a+4>>2]+32>>2])|0)==-1){break c}c=a+232|0;b=c;F[b+144>>2]=a;f=F[($[F[F[a>>2]+32>>2]](a)|0)+32>>2];f=F[f>>2]+F[f+16>>2]|0;e=F[($[F[F[a>>2]+32>>2]](a)|0)+32>>2];e=F[e+8>>2]-F[e+16>>2]|0;O=b,P=H[F[($[F[F[a>>2]+32>>2]](a)|0)+32>>2]+38>>1],E[O+38>>1]=P;F[b>>2]=f;F[b+16>>2]=0;F[b+20>>2]=0;F[b+8>>2]=e;F[b+12>>2]=0;O=b,P=$[F[F[a>>2]+36>>2]](a)|0,F[O+148>>2]=P;F[a+372>>2]=k;F[a+384>>2]=d+F[a+156>>2];K=Ja(u+8|0);k=K;f=0;j=Z-16|0;Z=j;d:{if(!Aa(b+80|0,b)){break d}if(!yd(c)){break d}b=F[c+4>>2];F[k>>2]=F[c>>2];F[k+4>>2]=b;b=F[c+36>>2];F[k+32>>2]=F[c+32>>2];F[k+36>>2]=b;b=F[c+28>>2];F[k+24>>2]=F[c+24>>2];F[k+28>>2]=b;b=F[c+20>>2];F[k+16>>2]=F[c+16>>2];F[k+20>>2]=b;b=F[c+12>>2];F[k+8>>2]=F[c+8>>2];F[k+12>>2]=b;F[c+176>>2]=2;F[c+180>>2]=7;b=F[c+152>>2];if((b|0)<0){break d}F[j+12>>2]=0;f=2;h=F[c+156>>2];e=F[c+160>>2]-h>>2;e:{if(e>>>0<b>>>0){Fa(c+156|0,b-e|0,j+12|0);f=F[c+176>>2];d=F[c+180>>2];break e}d=7;if(b>>>0>=e>>>0){break e}F[c+160>>2]=h+(b<<2)}e=c+184|0;d=(d-f|0)+1|0;b=F[c+188>>2];f=F[c+184>>2];s=(b-f|0)/12|0;f:{if(d>>>0>s>>>0){h=0;b=d-s|0;o=F[e+8>>2];f=F[e+4>>2];g:{if(b>>>0<=(o-f|0)/12>>>0){if(b){b=L(b,12)-12|0;b=(b-((b>>>0)%12|0)|0)+12|0;f=ma(f,0,b)+b|0}F[e+4>>2]=f;break g}h:{i:{j:{s=F[e>>2];g=(f-s|0)/12|0;d=g+b|0;if(d>>>0<357913942){o=(o-s|0)/12|0;i=o<<1;o=o>>>0>=178956970?357913941:d>>>0<i>>>0?i:d;if(o){if(o>>>0>=357913942){break j}h=ka(L(o,12))}d=L(g,12)+h|0;b=L(b,12)-12|0;g=(b-((b>>>0)%12|0)|0)+12|0;b=ma(d,0,g);g=b+g|0;h=L(o,12)+h|0;if((f|0)==(s|0)){break i}while(1){d=d-12|0;f=f-12|0;F[d>>2]=F[f>>2];F[d+4>>2]=F[f+4>>2];F[d+8>>2]=F[f+8>>2];F[f+8>>2]=0;F[f>>2]=0;F[f+4>>2]=0;if((f|0)!=(s|0)){continue}break}F[e+8>>2]=h;b=F[e+4>>2];F[e+4>>2]=g;f=F[e>>2];F[e>>2]=d;if((b|0)==(f|0)){break h}while(1){d=b-12|0;h=F[d>>2];if(h){F[b-8>>2]=h;ja(h)}b=d;if((f|0)!=(b|0)){continue}break}break h}break b}oa();v()}F[e+8>>2]=h;F[e+4>>2]=g;F[e>>2]=b}if(f){ja(f)}}d=F[c+188>>2];break f}if(d>>>0>=s>>>0){d=b;break f}d=f+L(d,12)|0;if((d|0)!=(b|0)){while(1){f=b-12|0;h=F[f>>2];if(h){F[b-8>>2]=h;ja(h)}b=f;if((d|0)!=(b|0)){continue}break}}F[c+188>>2]=d}s=c+196|0;f=F[c+184>>2];b=(d-f|0)/12|0;o=F[c+196>>2];h=F[c+200>>2]-o>>2;k:{if(b>>>0>h>>>0){qa(s,b-h|0);f=F[c+184>>2];d=F[c+188>>2];break k}if(b>>>0>=h>>>0){break k}F[c+200>>2]=o+(b<<2)}if((d|0)==(f|0)){f=1;break d}b=0;while(1){l:{if(!Da(1,j+8|0,k)){break l}f=F[j+8>>2];d=F[c+148>>2];if(f>>>0>(F[d+4>>2]-F[d>>2]>>2>>>0)/3>>>0){break l}if(f){g=L(b,12);h=g+F[e>>2]|0;d=F[h>>2];o=F[h+4>>2]-d>>2;m:{if(o>>>0<f>>>0){qa(h,f-o|0);d=F[g+F[e>>2]>>2];break m}if(f>>>0>=o>>>0){break m}F[h+4>>2]=(f<<2)+d}mc(f,1,k,d);F[F[s>>2]+(b<<2)>>2]=f}f=1;b=b+1|0;if(b>>>0<(F[c+188>>2]-F[c+184>>2]|0)/12>>>0){continue}break d}break}f=0}Z=j+16|0;n:{if(!f){break n}e=0;c=0;d=0;b=0;k=0;f=0;s=0;o=0;l=Z-96|0;Z=l;F[l+72>>2]=0;F[l+64>>2]=0;F[l+68>>2]=0;F[l+48>>2]=0;F[l+52>>2]=0;F[l+40>>2]=0;F[l+44>>2]=0;F[l+56>>2]=1065353216;F[l+32>>2]=0;F[l+24>>2]=0;F[l+28>>2]=0;j=a;C=F[a+124>>2];o:{p:{q:{r:{s:{if((q|0)<=0){break s}J=j+232|0;M=F[j+216>>2]!=F[j+220>>2];B=1;t:{while(1){h=s;s=h+1|0;u:{v:{w:{g=F[j+404>>2];if((g|0)==-1){F[j+400>>2]=7;break w}a=-1;i=F[j+428>>2]+(g<<2)|0;m=F[i>>2];g=m-1|0;F[i>>2]=g;if((m|0)<=0){break r}g=F[F[F[j+416>>2]+L(F[j+404>>2],12)>>2]+(g<<2)>>2];if(g>>>0>4){break r}i=F[(g<<2)+8896>>2];F[j+400>>2]=i;if(!g){if((b|0)==(c|0)){break r}i=-1;m=F[j+8>>2];B=F[m+24>>2];t=c-4|0;e=F[t>>2];g=-1;x:{if((e|0)==-1){break x}p=e+1|0;p=(p>>>0)%3|0?p:e-2|0;g=-1;if((p|0)==-1){break x}g=F[F[m>>2]+(p<<2)>>2]}n=F[B+(g<<2)>>2];if((n|0)!=-1){i=n+1|0;i=(i>>>0)%3|0?i:n-2|0}if((e|0)!=-1&F[F[m+12>>2]+(e<<2)>>2]!=-1|(e|0)==(i|0)){break r}n=F[m+12>>2];if((i|0)!=-1&F[n+(i<<2)>>2]!=-1){break r}p=L(h,3);h=p+1|0;F[n+(e<<2)>>2]=h;x=h<<2;F[x+n>>2]=e;r=p+2|0;F[n+(i<<2)>>2]=r;w=r<<2;F[w+n>>2]=i;n=-1;h=-1;y:{if((e|0)==-1){break y}z:{if((e>>>0)%3|0){e=e-1|0;break z}e=e+2|0;h=-1;if((e|0)==-1){break y}}h=F[F[m>>2]+(e<<2)>>2]}e=h;A:{if((i|0)==-1){break A}h=i+1|0;h=(h>>>0)%3|0?h:i-2|0;if((h|0)==-1){break A}n=F[F[m>>2]+(h<<2)>>2]}if((e|0)==(g|0)|(g|0)==(n|0)){break r}a=F[m>>2];F[a+(p<<2)>>2]=g;F[a+x>>2]=n;F[a+w>>2]=e;if((e|0)!=-1){F[B+(e<<2)>>2]=r}a=F[j+120>>2]+(g>>>3&536870908)|0;e=F[a>>2];O=a,P=oi(g)&e,F[O>>2]=P;F[t>>2]=p;e=b;kc(J,p);break u}B:{switch(i-1|0){case 2:case 4:if((b|0)==(c|0)){break r}r=c-4|0;e=F[r>>2];i=F[j+8>>2];m=F[i+12>>2];if((e|0)!=-1&F[m+(e<<2)>>2]!=-1){break r}c=L(h,3);n=(g|0)==3;g=c+(n?2:1)|0;t=g<<2;F[t+m>>2]=e;F[m+(e<<2)>>2]=g;Ma(i+24|0,8324);p=F[j+8>>2];m=F[p+24>>2];if(F[p+28>>2]-m>>2>(C|0)){break r}a=F[p>>2];w=a+t|0;p=F[i+28>>2];i=F[i+24>>2];t=(p-i>>2)-1|0;F[w>>2]=t;if((i|0)!=(p|0)){F[m+(t<<2)>>2]=g}g=n?c:c+2|0;w=a+(c+n<<2)|0;C:{if((e|0)==-1){F[a+(g<<2)>>2]=-1;i=-1;break C}D:{E:{F:{if((e>>>0)%3|0){i=e-1|0;break F}i=e+2|0;if((i|0)==-1){break E}}i=F[a+(i<<2)>>2];F[a+(g<<2)>>2]=i;if((i|0)==-1){break D}F[m+(i<<2)>>2]=g;break D}F[a+(g<<2)>>2]=-1}i=e+1|0;e=(i>>>0)%3|0?i:e-2|0;i=-1;if((e|0)==-1){break C}i=F[a+(e<<2)>>2]}F[w>>2]=i;F[r>>2]=c;e=b;break v;case 6:break w;case 0:break B;default:break r}}if((e|0)==(c|0)){break r}f=c-4|0;m=F[f>>2];F[l+68>>2]=f;n=F[l+44>>2];G:{if(!n){break G}g=F[l+40>>2];p=ni(n)>>>0>1;a=h&n+2147483647;H:{if(!p){break H}a=h;if(a>>>0<n>>>0){break H}a=(h>>>0)%(n>>>0)|0}i=a;a=F[g+(i<<2)>>2];if(!a){break G}a=F[a>>2];if(!a){break G}I:{if(!p){g=n-1|0;while(1){n=F[a+4>>2];J:{if((n|0)!=(h|0)){if((i|0)==(g&n)){break J}break G}if((h|0)==F[a+8>>2]){break I}}a=F[a>>2];if(a){continue}break}break G}while(1){g=F[a+4>>2];K:{if((g|0)!=(h|0)){if(g>>>0>=n>>>0){g=(g>>>0)%(n>>>0)|0}if((g|0)==(i|0)){break K}break G}if((h|0)==F[a+8>>2]){break I}}a=F[a>>2];if(a){continue}break}break G}if((f|0)!=(k|0)){F[f>>2]=F[a+12>>2];F[l+68>>2]=c;f=c;break G}b=k-e|0;c=b>>2;d=c+1|0;if(d>>>0>=1073741824){break b}f=b>>>1|0;d=b>>>0>=2147483644?1073741823:d>>>0<f>>>0?f:d;if(d){if(d>>>0>=1073741824){break p}f=ka(d<<2)}else{f=0}b=f+(c<<2)|0;F[b>>2]=F[a+12>>2];d=f+(d<<2)|0;f=b+4|0;if((e|0)!=(k|0)){while(1){b=b-4|0;k=k-4|0;F[b>>2]=F[k>>2];if((e|0)!=(k|0)){continue}break}}F[l+72>>2]=d;F[l+68>>2]=f;F[l+64>>2]=b;if(e){ja(e)}e=b;k=d}if((e|0)==(f|0)){break t}x=f-4|0;a=F[x>>2];if((a|0)==(m|0)){break t}i=(a|0)==-1;g=F[j+8>>2];if(!i&F[F[g+12>>2]+(a<<2)>>2]!=-1){break t}n=F[g+12>>2];if((m|0)!=-1&F[n+(m<<2)>>2]!=-1){break t}p=L(h,3);t=p+2|0;F[n+(a<<2)>>2]=t;c=t<<2;F[c+n>>2]=a;h=p+1|0;F[n+(m<<2)>>2]=h;w=h<<2;F[w+n>>2]=m;L:{M:{N:{if(!i){if((a>>>0)%3|0){h=a-1|0;break N}h=a+2|0;if((h|0)!=-1){break N}i=F[g>>2];h=-1;break M}h=-1;i=F[g>>2];F[i+(p<<2)>>2]=-1;r=-1;break L}i=F[g>>2];h=F[i+(h<<2)>>2]}F[(p<<2)+i>>2]=h;r=a+1|0;a=(r>>>0)%3|0?r:a-2|0;r=-1;if((a|0)==-1){break L}r=F[(a<<2)+i>>2]}F[i+w>>2]=r;O:{if((m|0)==-1){F[c+i>>2]=-1;r=-1;c=-1;break O}P:{Q:{R:{if((m>>>0)%3|0){a=m-1|0;break R}a=m+2|0;if((a|0)==-1){break Q}}a=F[(a<<2)+i>>2];F[c+i>>2]=a;if((a|0)==-1){break P}F[F[g+24>>2]+(a<<2)>>2]=t;break P}F[c+i>>2]=-1}r=-1;a=m+1|0;a=(a>>>0)%3|0?a:m-2|0;c=-1;if((a|0)==-1){break O}r=F[(a<<2)+i>>2];c=a}a=F[j+388>>2];t=h<<2;m=a+t|0;w=a;a=r<<2;F[m>>2]=F[m>>2]+F[w+a>>2];w=a;a=F[g+24>>2];m=w+a|0;if((h|0)!=-1){F[a+t>>2]=F[m>>2]}a=c;while(1){if((a|0)!=-1){F[(a<<2)+i>>2]=h;t=a+1|0;a=(t>>>0)%3|0?t:a-2|0;g=-1;S:{if((a|0)==-1){break S}a=F[n+(a<<2)>>2];g=-1;if((a|0)==-1){break S}g=a+1|0;g=(g>>>0)%3|0?g:a-2|0}a=g;if((c|0)!=(a|0)){continue}break t}break}F[m>>2]=-1;T:{U:{if(M){break U}if((y|0)!=(z|0)){F[z>>2]=r;z=z+4|0;F[l+28>>2]=z;break U}a=y-o|0;g=a>>2;c=g+1|0;if(c>>>0>=1073741824){break T}h=a>>>1|0;h=a>>>0>=2147483644?1073741823:c>>>0<h>>>0?h:c;if(h){if(h>>>0>=1073741824){break p}c=ka(h<<2)}else{c=0}a=c+(g<<2)|0;F[a>>2]=r;z=a+4|0;if((o|0)!=(y|0)){while(1){a=a-4|0;y=y-4|0;F[a>>2]=F[y>>2];if((o|0)!=(y|0)){continue}break}}y=c+(h<<2)|0;F[l+32>>2]=y;F[l+28>>2]=z;F[l+24>>2]=a;if(o){ja(o)}o=a}F[x>>2]=p;c=f;kc(J,p);break u}break b}g=F[j+8>>2];Ma(g+24|0,8324);a=-1;k=F[j+8>>2];f=L(h,3);i=F[g+28>>2];m=F[g+24>>2];n=i-m|0;g=n>>2;p=g-1|0;F[F[k>>2]+(f<<2)>>2]=p;Ma(k+24|0,8324);r=f+1|0;F[F[k>>2]+(r<<2)>>2]=(F[k+28>>2]-F[k+24>>2]>>2)-1;k=F[j+8>>2];Ma(k+24|0,8324);t=f+2|0;F[F[k>>2]+(t<<2)>>2]=(F[k+28>>2]-F[k+24>>2]>>2)-1;x=F[j+8>>2];k=F[x+24>>2];if(F[x+28>>2]-k>>2>(C|0)){break r}V:{W:{if((i|0)!=(m|0)){F[k+(p<<2)>>2]=f;a=0;if((n|0)==-4){break W}}F[k+(g<<2)>>2]=r;a=g+1|0;if((a|0)==-1){break V}}F[k+(a<<2)>>2]=t}if((d|0)!=(c|0)){F[c>>2]=f;f=c+4|0;F[l+68>>2]=f;k=d;break v}a=d-b|0;k=a>>2;e=k+1|0;if(e>>>0>=1073741824){break b}c=a>>>1|0;a=a>>>0>=2147483644?1073741823:e>>>0<c>>>0?c:e;if(a){if(a>>>0>=1073741824){break p}c=ka(a<<2)}else{c=0}e=c+(k<<2)|0;F[e>>2]=f;k=c+(a<<2)|0;f=e+4|0;if((b|0)!=(d|0)){while(1){e=e-4|0;d=d-4|0;F[e>>2]=F[d>>2];if((b|0)!=(d|0)){continue}break}}F[l+72>>2]=k;F[l+68>>2]=f;F[l+64>>2]=e;if(b){ja(b)}d=k;b=e}kc(J,F[f-4>>2]);a=F[j+40>>2];X:{if((a|0)==F[j+36>>2]){break X}c=a-12|0;g=F[c+4>>2];h=q+(h^-1)|0;if(g>>>0>h>>>0){break t}if((g|0)!=(h|0)){break X}i=G[a-4|0];g=F[c>>2];F[j+40>>2]=c;if((g|0)<0){break t}m=f-4|0;a=F[m>>2];F[l+20>>2]=q+(g^-1);c=l+20|0;F[l+88>>2]=c;Fb(l,l+40|0,c,l+88|0);g=F[l>>2];Y:{if(i&1){c=-1;if((a|0)==-1){break Y}c=a+1|0;c=(c>>>0)%3|0?c:a-2|0;break Y}c=-1;if((a|0)==-1){break Y}c=a-1|0;if((a>>>0)%3|0){break Y}c=a+2|0}F[g+12>>2]=c;a=F[j+40>>2];if((a|0)==F[j+36>>2]){break X}while(1){c=a-12|0;g=F[c+4>>2];if(g>>>0>h>>>0){break t}if((g|0)!=(h|0)){break X}i=G[a-4|0];g=F[c>>2];F[j+40>>2]=c;if((g|0)<0){break t}a=F[m>>2];F[l+20>>2]=q+(g^-1);c=l+20|0;F[l+88>>2]=c;Fb(l,l+40|0,c,l+88|0);g=F[l>>2];Z:{if(i&1){c=-1;if((a|0)==-1){break Z}c=a+1|0;c=(c>>>0)%3|0?c:a-2|0;break Z}c=-1;if((a|0)==-1){break Z}c=a-1|0;if((a>>>0)%3|0){break Z}c=a+2|0}F[g+12>>2]=c;a=F[j+40>>2];if((a|0)!=F[j+36>>2]){continue}break}}c=f}B=(q|0)>(s|0);if((q|0)!=(s|0)){continue}break}s=q;break s}a=-1;if(B){break r}}a=-1;c=F[j+8>>2];if(F[c+28>>2]-F[c+24>>2]>>2>(C|0)){break r}if((b|0)!=(f|0)){m=j+72|0;k=j+60|0;n=j+312|0;while(1){f=f-4|0;q=F[f>>2];F[l+68>>2]=f;_:{if(wa(n)){g=F[j+8>>2];o=F[g>>2];if(((F[g+4>>2]-o>>2>>>0)/3|0)<=(s|0)){a=-1;break r}b=-1;i=F[g+24>>2];a=-1;$:{if((q|0)==-1){break $}e=q+1|0;e=(e>>>0)%3|0?e:q-2|0;a=-1;if((e|0)==-1){break $}a=F[o+(e<<2)>>2]}e=a;a=F[i+(e<<2)>>2];aa:{if((a|0)==-1){h=1;c=-1;break aa}h=1;c=-1;d=a+1|0;a=(d>>>0)%3|0?d:a-2|0;if((a|0)==-1){break aa}h=0;b=a+1|0;b=(b>>>0)%3|0?b:a-2|0;if((b|0)!=-1){c=F[o+(b<<2)>>2]}b=a}a=-1;d=-1;i=F[i+(c<<2)>>2];if((i|0)!=-1){d=i+1|0;d=(d>>>0)%3|0?d:i-2|0}if((b|0)==(q|0)|(d|0)==(q|0)|((q|0)!=-1&F[F[g+12>>2]+(q<<2)>>2]!=-1|(b|0)==(d|0))){break r}if(!h&F[F[g+12>>2]+(b<<2)>>2]!=-1){break r}h=-1;g=F[g+12>>2];i=-1;ba:{if((d|0)==-1){break ba}if(F[g+(d<<2)>>2]!=-1){break r}a=d+1|0;a=(a>>>0)%3|0?a:d-2|0;i=-1;if((a|0)==-1){break ba}i=F[o+(a<<2)>>2]}a=L(s,3);F[l>>2]=a;F[g+(a<<2)>>2]=q;F[g+(q<<2)>>2]=a;a=F[l>>2]+1|0;F[g+(a<<2)>>2]=b;F[g+(b<<2)>>2]=a;a=F[l>>2]+2|0;F[g+(a<<2)>>2]=d;F[g+(d<<2)>>2]=a;a=F[l>>2];F[o+(a<<2)>>2]=c;b=a+1|0;d=o+(b<<2)|0;F[d>>2]=i;q=a+2|0;o=o+(q<<2)|0;F[o>>2]=e;a=F[j+120>>2];e=b?c:-1;c=a+(e>>>3&536870908)|0;g=F[c>>2];O=c,P=oi(e)&g,F[O>>2]=P;h=(b|0)!=-1?F[d>>2]:h;b=a+(h>>>3&536870908)|0;d=F[b>>2];O=b,P=oi(h)&d,F[O>>2]=P;d=-1;d=(q|0)!=-1?F[o>>2]:d;a=a+(d>>>3&536870908)|0;b=F[a>>2];O=a,P=oi(d)&b,F[O>>2]=P;D[l+88|0]=1;wd(k,l+88|0);Ma(m,l);s=s+1|0;b=F[l+64>>2];break _}d=F[j+64>>2];a=F[j+68>>2];if((d|0)==a<<5){if((d+1|0)<0){break b}if(d>>>0<=1073741822){a=a<<6;d=(d&-32)+32|0;a=a>>>0>d>>>0?a:d}else{a=2147483647}$a(k,a);d=F[j+64>>2]}F[j+64>>2]=d+1;a=F[j+60>>2]+(d>>>3&536870908)|0;e=F[a>>2];O=a,P=oi(d)&e,F[O>>2]=P;d=F[j+76>>2];if((d|0)!=F[j+80>>2]){F[d>>2]=q;F[j+76>>2]=d+4;break _}c=F[m>>2];a=d-c|0;o=a>>2;e=o+1|0;if(e>>>0>=1073741824){break b}h=a>>>1|0;h=a>>>0>=2147483644?1073741823:e>>>0<h>>>0?h:e;if(h){if(h>>>0>=1073741824){break p}a=ka(h<<2)}else{a=0}e=a+(o<<2)|0;F[e>>2]=q;q=e+4|0;if((d|0)!=(c|0)){while(1){e=e-4|0;d=d-4|0;F[e>>2]=F[d>>2];if((d|0)!=(c|0)){continue}break}}F[j+80>>2]=a+(h<<2);F[j+76>>2]=q;F[j+72>>2]=e;if(!c){break _}ja(c)}if((b|0)!=(f|0)){continue}break}c=F[j+8>>2]}a=-1;if(((F[c+4>>2]-F[c>>2]>>2>>>0)/3|0)!=(s|0)){break r}a=F[c+28>>2]-F[c+24>>2]>>2;f=F[l+24>>2];h=F[l+28>>2];if((f|0)==(h|0)){break q}while(1){b=F[f>>2];k=F[c+24>>2];d=a-1|0;e=k+(d<<2)|0;if(F[e>>2]==-1){while(1){d=a-2|0;a=a-1|0;e=k+(d<<2)|0;if(F[e>>2]==-1){continue}break}}if(b>>>0<=d>>>0){F[l>>2]=c;e=F[e>>2];D[l+12|0]=1;F[l+8>>2]=e;F[l+4>>2]=e;if((e|0)!=-1){while(1){e=F[F[j+8>>2]>>2]+(e<<2)|0;if(F[e>>2]!=(d|0)){a=-1;break r}F[e>>2]=b;nc(l);e=F[l+8>>2];if((e|0)!=-1){continue}break}c=F[j+8>>2]}k=F[c+24>>2];e=k+(d<<2)|0;if((b|0)!=-1){F[k+(b<<2)>>2]=F[e>>2]}F[e>>2]=-1;e=1<<b;k=F[j+120>>2];b=k+(b>>>3&536870908)|0;k=k+(d>>>3&536870908)|0;d=1<<d;if(F[k>>2]&d){e=e|F[b>>2]}else{e=F[b>>2]&(e^-1)}F[b>>2]=e;F[k>>2]=F[k>>2]&(d^-1);a=a-1|0}f=f+4|0;if((h|0)!=(f|0)){continue}break}}f=F[l+24>>2]}if(f){ja(f)}b=F[l+48>>2];if(b){while(1){d=F[b>>2];ja(b);b=d;if(b){continue}break}}b=F[l+40>>2];F[l+40>>2]=0;if(b){ja(b)}b=F[l+64>>2];if(b){F[l+68>>2]=b;ja(b)}Z=l+96|0;break o}oa();v()}f=a;if((a|0)==-1){break n}a=K;b=F[a+16>>2];d=b+F[a>>2]|0;b=F[a+8>>2]-b|0;a=F[F[j+4>>2]+32>>2];E[a+38>>1]=H[a+38>>1];F[a>>2]=d;F[a+16>>2]=0;F[a+20>>2]=0;F[a+8>>2]=b;F[a+12>>2]=0;ca:{if(F[j+216>>2]==F[j+220>>2]){break ca}a=F[j+8>>2];if(F[a+4>>2]==F[a>>2]){break ca}b=0;while(1){if(Ad(j,b)){b=b+3|0;a=F[j+8>>2];if(b>>>0<F[a+4>>2]-F[a>>2]>>2>>>0){continue}break ca}break}break n}if(G[j+308|0]){D[j+308|0]=0;d=F[j+292>>2];a=0;e=F[j+304>>2]+7|0;a=e>>>0<7?1:a;e=a<<29|e>>>3;b=e+F[j+288>>2]|0;a=(a>>>3|0)+d|0;F[j+288>>2]=b;F[j+292>>2]=b>>>0<e>>>0?a+1|0:a}b=F[j+216>>2];if((b|0)!=F[j+220>>2]){a=0;while(1){e=L(a,144);Zc((e+b|0)+4|0,F[j+8>>2]);d=F[A>>2];c=d+e|0;b=F[c+132>>2];c=F[c+136>>2];if((b|0)!=(c|0)){while(1){Xc((e+F[A>>2]|0)+4|0,F[b>>2]);b=b+4|0;if((c|0)!=(b|0)){continue}break}d=F[A>>2]}if(!Yc((d+e|0)+4|0)){break n}a=a+1|0;b=F[j+216>>2];if(a>>>0<(F[j+220>>2]-b|0)/144>>>0){continue}break}}a=F[j+8>>2];Hb(j+184|0,F[a+28>>2]-F[a+24>>2]>>2);d=F[j+216>>2];if((d|0)!=F[j+220>>2]){b=0;while(1){a=L(b,144)+d|0;d=F[a+60>>2]-F[a+56>>2]>>2;c=a+104|0;a=F[j+8>>2];a=F[a+28>>2]-F[a+24>>2]>>2;Hb(c,(a|0)<(d|0)?d:a);b=b+1|0;d=F[j+216>>2];if(b>>>0<(F[j+220>>2]-d|0)/144>>>0){continue}break}}N=zd(j,f)}}Z=u- -64|0;return N|0}na();v()}function lh(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,J=0,K=0,M=0,N=0;t=Z+-64|0;Z=t;F[a+132>>2]=0;if(F[a+148>>2]){c=F[a+144>>2];if(c){while(1){b=F[c>>2];ja(c);c=b;if(b){continue}break}}c=0;F[a+144>>2]=0;b=F[a+140>>2];a:{if(!b){break a}if(b>>>0>=4){h=b&-4;while(1){e=c<<2;F[e+F[a+136>>2]>>2]=0;F[F[a+136>>2]+(e|4)>>2]=0;F[F[a+136>>2]+(e|8)>>2]=0;F[F[a+136>>2]+(e|12)>>2]=0;c=c+4|0;g=g+4|0;if((h|0)!=(g|0)){continue}break}}b=b&3;if(!b){break a}g=0;while(1){F[F[a+136>>2]+(c<<2)>>2]=0;c=c+1|0;g=g+1|0;if((b|0)!=(g|0)){continue}break}}F[a+148>>2]=0}b:{if(!Da(1,t+60|0,F[F[a+4>>2]+32>>2])){break b}F[a+156>>2]=F[t+60>>2];if(!Da(1,t+56|0,F[F[a+4>>2]+32>>2])){break b}e=F[t+56>>2];if(e>>>0>1431655765|I[a+156>>2]>L(e,3)>>>0){break b}c=F[F[a+4>>2]+32>>2];h=F[c+8>>2];m=F[c+12>>2];b=F[c+20>>2];g=F[c+16>>2];if((m|0)<=(b|0)&g>>>0>=h>>>0|(b|0)>(m|0)){break b}h=G[g+F[c>>2]|0];g=g+1|0;b=g?b:b+1|0;F[c+16>>2]=g;F[c+20>>2]=b;if(!Da(1,t+52|0,c)){break b}n=F[t+52>>2];if(n>>>0>e>>>0|e>>>0>n+((n>>>0)/3|0)>>>0){break b}if(!Da(1,t+48|0,F[F[a+4>>2]+32>>2])){break b}c=F[t+48>>2];if(c>>>0>n>>>0){break b}F[a+28>>2]=F[a+24>>2];g=Zb(ka(88));b=F[a+8>>2];F[a+8>>2]=g;if(b){Za(b);if(!F[a+8>>2]){break b}}F[a+164>>2]=F[a+160>>2];Ib(a+160|0,e);F[a+176>>2]=F[a+172>>2];Ib(a+172|0,e);F[a- -64>>2]=0;F[a+92>>2]=-1;F[a+84>>2]=-1;F[a+88>>2]=-1;F[a+40>>2]=F[a+36>>2];F[a+52>>2]=F[a+48>>2];F[a+76>>2]=F[a+72>>2];y=a+216|0;Dd(y);Cd(y,h);if(!_c(F[a+8>>2],e,c+F[a+156>>2]|0)){break b}b=F[a+156>>2];D[t+8|0]=1;Ea(a+120|0,b+c|0,t+8|0);if((Bd(a,F[F[a+4>>2]+32>>2])|0)==-1){break b}c=a+232|0;F[c+144>>2]=a;b=F[($[F[F[a>>2]+32>>2]](a)|0)+32>>2];b=F[b>>2]+F[b+16>>2]|0;g=F[($[F[F[a>>2]+32>>2]](a)|0)+32>>2];g=F[g+8>>2]-F[g+16>>2]|0;M=c,N=H[F[($[F[F[a>>2]+32>>2]](a)|0)+32>>2]+38>>1],E[M+38>>1]=N;F[c>>2]=b;F[c+16>>2]=0;F[c+20>>2]=0;F[c+8>>2]=g;F[c+12>>2]=0;F[a+372>>2]=h;C=Ja(t+8|0);h=C;m=0;d=Z-16|0;Z=d;b=F[c+4>>2];F[c+40>>2]=F[c>>2];F[c+44>>2]=b;b=F[c+36>>2];F[c+72>>2]=F[c+32>>2];F[c+76>>2]=b;g=F[c+28>>2];b=c- -64|0;F[b>>2]=F[c+24>>2];F[b+4>>2]=g;b=F[c+20>>2];F[c+56>>2]=F[c+16>>2];F[c+60>>2]=b;b=F[c+12>>2];F[c+48>>2]=F[c+8>>2];F[c+52>>2]=b;c:{d:{if(hc(c+40|0,1,d+8|0)){b=F[c+44>>2];F[c>>2]=F[c+40>>2];F[c+4>>2]=b;b=F[c+76>>2];F[c+32>>2]=F[c+72>>2];F[c+36>>2]=b;b=F[c+68>>2];F[c+24>>2]=F[c+64>>2];F[c+28>>2]=b;g=F[c+60>>2];f=g;b=F[c+56>>2];F[c+16>>2]=b;F[c+20>>2]=g;e=F[c+52>>2];g=F[c+48>>2];F[c+8>>2]=g;F[c+12>>2]=e;k=F[d+12>>2];i=e-((b>>>0>g>>>0)+f|0)|0;e=g-b|0;g=F[d+8>>2];if((k|0)==(i|0)&e>>>0>=g>>>0|i>>>0>k>>>0){break d}}break c}e=f+k|0;b=b+g|0;e=b>>>0<g>>>0?e+1|0:e;F[c+16>>2]=b;F[c+20>>2]=e;if(!Aa(c+80|0,c)){break c}if(!yd(c)){break c}b=F[c+4>>2];F[h>>2]=F[c>>2];F[h+4>>2]=b;b=F[c+36>>2];F[h+32>>2]=F[c+32>>2];F[h+36>>2]=b;b=F[c+28>>2];F[h+24>>2]=F[c+24>>2];F[h+28>>2]=b;b=F[c+20>>2];F[h+16>>2]=F[c+16>>2];F[h+20>>2]=b;b=F[c+12>>2];F[h+8>>2]=F[c+8>>2];F[h+12>>2]=b;m=1}Z=d+16|0;e:{if(!m){break e}b=0;c=0;g=0;m=0;j=Z-96|0;Z=j;F[j+72>>2]=0;F[j+64>>2]=0;F[j+68>>2]=0;F[j+48>>2]=0;F[j+52>>2]=0;F[j+40>>2]=0;F[j+44>>2]=0;F[j+56>>2]=1065353216;F[j+32>>2]=0;F[j+24>>2]=0;F[j+28>>2]=0;h=a;B=F[a+124>>2];f:{g:{h:{i:{j:{k:{l:{m:{if((n|0)<=0){break m}J=F[h+216>>2]!=F[h+220>>2];z=1;while(1){e=m;m=e+1|0;n:{o:{p:{q:{r:{s:{t:{u:{v:{w:{x:{y:{z:{A:{B:{if(!G[h+308|0]){break B}k=F[h+296>>2];d=F[h+304>>2];a=k+(d>>>3|0)|0;l=F[h+300>>2];if(a>>>0>=l>>>0){break B}f=G[a|0];a=d+1|0;F[h+304>>2]=a;p=f>>>(d&7)&1;if(!p){break B}i=0;f=a>>>3|0;r=k+f|0;C:{if(r>>>0>=l>>>0){d=a;a=0;break C}r=G[r|0];d=d+2|0;F[h+304>>2]=d;f=d>>>3|0;a=r>>>(a&7)&1}f=f+k|0;if(f>>>0<l>>>0){f=G[f|0];F[h+304>>2]=d+1;i=f>>>(d&7)<<1&2}f=-1;i=p|(a|i)<<1;switch(i-1|0){case 6:break y;case 0:break z;case 2:case 4:break A;default:break l}}if((c|0)==(g|0)){f=-1;break l}d=-1;i=F[h+8>>2];z=F[i+24>>2];r=c-4|0;b=F[r>>2];a=-1;D:{if((b|0)==-1){break D}k=b+1|0;k=(k>>>0)%3|0?k:b-2|0;a=-1;if((k|0)==-1){break D}a=F[F[i>>2]+(k<<2)>>2]}f=F[z+(a<<2)>>2];if((f|0)!=-1){d=f+1|0;d=(d>>>0)%3|0?d:f-2|0}if((b|0)==(d|0)){f=-1;break l}if((b|0)!=-1){f=-1;if(F[F[i+12>>2]+(b<<2)>>2]!=-1){break l}}k=F[i+12>>2];if((d|0)!=-1){f=-1;if(F[k+(d<<2)>>2]!=-1){break l}}l=L(e,3);e=l+1|0;F[k+(b<<2)>>2]=e;s=e<<2;F[s+k>>2]=b;p=l+2|0;F[k+(d<<2)>>2]=p;u=p<<2;F[u+k>>2]=d;k=-1;e=-1;E:{if((b|0)==-1){break E}F:{if((b>>>0)%3|0){b=b-1|0;break F}b=b+2|0;e=-1;if((b|0)==-1){break E}}e=F[F[i>>2]+(b<<2)>>2]}b=e;G:{if((d|0)==-1){break G}e=d+1|0;e=(e>>>0)%3|0?e:d-2|0;if((e|0)==-1){break G}k=F[F[i>>2]+(e<<2)>>2]}f=-1;if((a|0)==(b|0)|(a|0)==(k|0)){break l}e=F[i>>2];F[e+(l<<2)>>2]=a;F[e+s>>2]=k;F[e+u>>2]=b;if((b|0)!=-1){F[z+(b<<2)>>2]=p}b=F[h+120>>2]+(a>>>3&536870908)|0;e=F[b>>2];M=b,N=oi(a)&e,F[M>>2]=N;F[r>>2]=l;b=g;break n}if((c|0)==(g|0)){break l}r=c-4|0;b=F[r>>2];a=F[h+8>>2];d=F[a+12>>2];if((b|0)!=-1&F[d+(b<<2)>>2]!=-1){break l}l=(i|0)==5;i=L(e,3);p=(l?2:1)+i|0;s=p<<2;F[s+d>>2]=b;F[d+(b<<2)>>2]=p;Ma(a+24|0,8324);d=F[h+8>>2];k=F[d+24>>2];if(F[d+28>>2]-k>>2>(B|0)){break l}d=F[d>>2];u=d+s|0;f=F[a+28>>2];a=F[a+24>>2];s=(f-a>>2)-1|0;F[u>>2]=s;if((a|0)!=(f|0)){F[k+(s<<2)>>2]=p}f=l?i:i+2|0;l=d+(i+l<<2)|0;H:{if((b|0)==-1){F[d+(f<<2)>>2]=-1;a=-1;break H}I:{J:{K:{if((b>>>0)%3|0){a=b-1|0;break K}a=b+2|0;if((a|0)==-1){break J}}a=F[d+(a<<2)>>2];F[d+(f<<2)>>2]=a;if((a|0)==-1){break I}F[k+(a<<2)>>2]=f;break I}F[d+(f<<2)>>2]=-1}f=b+1|0;b=(f>>>0)%3|0?f:b-2|0;a=-1;if((b|0)==-1){break H}a=F[d+(b<<2)>>2]}F[l>>2]=a;F[r>>2]=i;b=g;break t}if((b|0)==(c|0)){break l}a=c-4|0;k=F[a>>2];F[j+68>>2]=a;l=F[j+44>>2];L:{if(!l){c=a;break L}f=F[j+40>>2];p=ni(l)>>>0>1;d=e&l+2147483647;M:{if(!p){break M}d=e;if(d>>>0<l>>>0){break M}d=(e>>>0)%(l>>>0)|0}i=d;d=F[f+(i<<2)>>2];if(!d){c=a;break L}d=F[d>>2];if(!d){c=a;break L}N:{if(!p){f=l-1|0;while(1){l=F[d+4>>2];O:{if((l|0)!=(e|0)){if((i|0)==(f&l)){break O}c=a;break L}if((e|0)==F[d+8>>2]){break N}}d=F[d>>2];if(d){continue}break}c=a;break L}while(1){f=F[d+4>>2];P:{if((f|0)!=(e|0)){if(f>>>0>=l>>>0){f=(f>>>0)%(l>>>0)|0}if((f|0)==(i|0)){break P}c=a;break L}if((e|0)==F[d+8>>2]){break N}}d=F[d>>2];if(d){continue}break}c=a;break L}if((a|0)!=(q|0)){F[a>>2]=F[d+12>>2];F[j+68>>2]=c;break L}a=q-b|0;g=a>>2;c=g+1|0;if(c>>>0>=1073741824){break x}f=a>>>1|0;f=a>>>0>=2147483644?1073741823:c>>>0<f>>>0?f:c;if(f){if(f>>>0>=1073741824){break j}a=ka(f<<2)}else{a=0}g=a+(g<<2)|0;F[g>>2]=F[d+12>>2];c=g+4|0;if((b|0)!=(q|0)){while(1){g=g-4|0;q=q-4|0;F[g>>2]=F[q>>2];if((b|0)!=(q|0)){continue}break}}q=a+(f<<2)|0;F[j+72>>2]=q;F[j+68>>2]=c;F[j+64>>2]=g;if(b){ja(b)}}if((c|0)==(g|0)){break p}s=c-4|0;b=F[s>>2];if((b|0)==(k|0)){break p}a=(b|0)==-1;f=F[h+8>>2];if(!a&F[F[f+12>>2]+(b<<2)>>2]!=-1){break p}l=F[f+12>>2];if((k|0)!=-1&F[l+(k<<2)>>2]!=-1){break p}p=L(e,3);r=p+2|0;F[l+(b<<2)>>2]=r;e=r<<2;F[e+l>>2]=b;d=p+1|0;F[l+(k<<2)>>2]=d;u=d<<2;F[u+l>>2]=k;if(a){break w}if((b>>>0)%3|0){d=b-1|0;break s}d=b+2|0;if((d|0)!=-1){break s}a=F[f>>2];d=-1;break r}i=F[h+8>>2];Ma(i+24|0,8324);d=F[h+8>>2];a=L(e,3);k=F[i+28>>2];l=F[i+24>>2];p=k-l|0;i=p>>2;r=i-1|0;F[F[d>>2]+(a<<2)>>2]=r;Ma(d+24|0,8324);s=a+1|0;F[F[d>>2]+(s<<2)>>2]=(F[d+28>>2]-F[d+24>>2]>>2)-1;d=F[h+8>>2];Ma(d+24|0,8324);u=a+2|0;F[F[d>>2]+(u<<2)>>2]=(F[d+28>>2]-F[d+24>>2]>>2)-1;A=F[h+8>>2];d=F[A+24>>2];if(F[A+28>>2]-d>>2>(B|0)){break l}Q:{R:{if((k|0)!=(l|0)){F[d+(r<<2)>>2]=a;f=0;if((p|0)==-4){break R}}F[d+(i<<2)>>2]=s;f=i+1|0;if((f|0)==-1){break Q}}F[d+(f<<2)>>2]=u}if((c|0)!=(q|0)){F[c>>2]=a;c=c+4|0;F[j+68>>2]=c;break t}g=c-b|0;i=g>>2;d=i+1|0;if(d>>>0>=1073741824){break v}f=g>>>1|0;d=g>>>0>=2147483644?1073741823:d>>>0<f>>>0?f:d;if(d){if(d>>>0>=1073741824){break j}f=ka(d<<2)}else{f=0}g=f+(i<<2)|0;F[g>>2]=a;q=f+(d<<2)|0;a=g+4|0;if((b|0)!=(c|0)){while(1){g=g-4|0;c=c-4|0;F[g>>2]=F[c>>2];if((b|0)!=(c|0)){continue}break}}F[j+72>>2]=q;F[j+68>>2]=a;F[j+64>>2]=g;if(!b){break u}ja(b);break u}na();v()}d=-1;a=F[f>>2];F[a+(p<<2)>>2]=-1;i=-1;break q}na();v()}c=a;b=g}a=F[h+40>>2];if((a|0)==F[h+36>>2]){break n}d=a-12|0;i=F[d+4>>2];f=n+(e^-1)|0;if(i>>>0>f>>>0){break p}if((f|0)!=(i|0)){break n}i=G[a-4|0];e=F[d>>2];F[h+40>>2]=d;if((e|0)<0){break p}k=c-4|0;a=F[k>>2];F[j+20>>2]=n+(e^-1);e=j+20|0;F[j+88>>2]=e;Fb(j,j+40|0,e,j+88|0);d=F[j>>2];S:{if(i&1){e=-1;if((a|0)==-1){break S}e=a+1|0;e=(e>>>0)%3|0?e:a-2|0;break S}e=-1;if((a|0)==-1){break S}e=a-1|0;if((a>>>0)%3|0){break S}e=a+2|0}F[d+12>>2]=e;d=F[h+40>>2];if((d|0)==F[h+36>>2]){break n}while(1){a=d-12|0;e=F[a+4>>2];if(e>>>0>f>>>0){break p}if((f|0)!=(e|0)){break n}d=G[d-4|0];e=F[a>>2];F[h+40>>2]=a;if((e|0)<0){break p}a=F[k>>2];F[j+20>>2]=n+(e^-1);e=j+20|0;F[j+88>>2]=e;Fb(j,j+40|0,e,j+88|0);i=F[j>>2];T:{if(d&1){e=-1;if((a|0)==-1){break T}e=a+1|0;e=(e>>>0)%3|0?e:a-2|0;break T}e=-1;if((a|0)==-1){break T}e=a-1|0;if((a>>>0)%3|0){break T}e=a+2|0}F[i+12>>2]=e;d=F[h+40>>2];if((d|0)!=F[h+36>>2]){continue}break}break n}a=F[f>>2];d=F[a+(d<<2)>>2]}F[(p<<2)+a>>2]=d;A=b+1|0;b=(A>>>0)%3|0?A:b-2|0;i=-1;if((b|0)==-1){break q}i=F[(b<<2)+a>>2]}F[a+u>>2]=i;U:{if((k|0)==-1){F[a+e>>2]=-1;i=-1;e=-1;break U}V:{W:{X:{if((k>>>0)%3|0){b=k-1|0;break X}b=k+2|0;if((b|0)==-1){break W}}b=F[(b<<2)+a>>2];F[a+e>>2]=b;if((b|0)==-1){break V}F[F[f+24>>2]+(b<<2)>>2]=r;break V}F[a+e>>2]=-1}i=-1;b=k+1|0;b=(b>>>0)%3|0?b:k-2|0;e=-1;if((b|0)==-1){break U}i=F[(b<<2)+a>>2];e=b}b=F[f+24>>2];k=b+(i<<2)|0;if((d|0)!=-1){F[b+(d<<2)>>2]=F[k>>2]}b=e;while(1){if((b|0)==-1){break o}F[(b<<2)+a>>2]=d;r=b+1|0;b=(r>>>0)%3|0?r:b-2|0;f=-1;Y:{if((b|0)==-1){break Y}b=F[l+(b<<2)>>2];f=-1;if((b|0)==-1){break Y}f=b+1|0;f=(f>>>0)%3|0?f:b-2|0}b=f;if((e|0)!=(b|0)){continue}break}}f=-1;if(!z){break m}break l}F[k>>2]=-1;Z:{if(J){break Z}if((w|0)!=(x|0)){F[x>>2]=i;x=x+4|0;F[j+28>>2]=x;break Z}a=w-o|0;d=a>>2;b=d+1|0;if(b>>>0>=1073741824){break i}e=a>>>1|0;e=a>>>0>=2147483644?1073741823:b>>>0<e>>>0?e:b;if(e){if(e>>>0>=1073741824){break j}a=ka(e<<2)}else{a=0}b=a+(d<<2)|0;F[b>>2]=i;x=b+4|0;if((o|0)!=(w|0)){while(1){b=b-4|0;w=w-4|0;F[b>>2]=F[w>>2];if((o|0)!=(w|0)){continue}break}}w=a+(e<<2)|0;F[j+32>>2]=w;F[j+28>>2]=x;F[j+24>>2]=b;if(o){ja(o)}o=b}F[s>>2]=p;b=g}z=(m|0)<(n|0);if((m|0)!=(n|0)){continue}break}m=n}f=-1;a=F[h+8>>2];if(F[a+28>>2]-F[a+24>>2]>>2>(B|0)){break l}if((c|0)!=(g|0)){l=h+72|0;e=h+60|0;w=h+312|0;while(1){c=c-4|0;i=F[c>>2];F[j+68>>2]=c;_:{if(wa(w)){q=F[h+8>>2];k=F[q>>2];if(((F[q+4>>2]-k>>2>>>0)/3|0)<=(m|0)){f=-1;break l}a=-1;f=-1;b=-1;x=F[q+24>>2];g=-1;$:{if((i|0)==-1){break $}n=i+1|0;n=(n>>>0)%3|0?n:i-2|0;g=-1;if((n|0)==-1){break $}g=F[k+(n<<2)>>2]}n=g;o=F[x+(n<<2)>>2];aa:{if((o|0)==-1){d=1;g=-1;break aa}d=1;p=o+1|0;o=(p>>>0)%3|0?p:o-2|0;g=-1;if((o|0)==-1){break aa}d=0;a=o;g=a+1|0;g=(g>>>0)%3|0?g:a-2|0;if((g|0)!=-1){g=F[k+(g<<2)>>2]}else{g=-1}}o=F[(g<<2)+x>>2];if((o|0)!=-1){b=o+1|0;b=(b>>>0)%3|0?b:o-2|0}if((a|0)==(i|0)|(b|0)==(i|0)|((i|0)!=-1&F[F[q+12>>2]+(i<<2)>>2]!=-1|(a|0)==(b|0))){break l}if(!d&F[F[q+12>>2]+(a<<2)>>2]!=-1){break l}d=-1;o=F[q+12>>2];q=-1;ba:{if((b|0)==-1){break ba}if(F[o+(b<<2)>>2]!=-1){break l}f=b+1|0;f=(f>>>0)%3|0?f:b-2|0;q=-1;if((f|0)==-1){break ba}q=F[k+(f<<2)>>2]}f=L(m,3);F[j>>2]=f;F[o+(f<<2)>>2]=i;F[o+(i<<2)>>2]=f;f=F[j>>2]+1|0;F[o+(f<<2)>>2]=a;F[o+(a<<2)>>2]=f;a=F[j>>2]+2|0;F[o+(a<<2)>>2]=b;F[o+(b<<2)>>2]=a;a=F[j>>2];F[k+(a<<2)>>2]=g;b=a+1|0;f=k+(b<<2)|0;F[f>>2]=q;o=a+2|0;i=k+(o<<2)|0;F[i>>2]=n;a=F[h+120>>2];g=b?g:-1;n=a+(g>>>3&536870908)|0;k=F[n>>2];M=n,N=oi(g)&k,F[M>>2]=N;d=(b|0)!=-1?F[f>>2]:d;b=a+(d>>>3&536870908)|0;g=F[b>>2];M=b,N=oi(d)&g,F[M>>2]=N;b=-1;b=(o|0)!=-1?F[i>>2]:b;a=a+(b>>>3&536870908)|0;g=F[a>>2];M=a,N=oi(b)&g,F[M>>2]=N;D[j+88|0]=1;wd(e,j+88|0);Ma(l,j);m=m+1|0;g=F[j+64>>2];break _}b=F[h+64>>2];a=F[h+68>>2];if((b|0)==a<<5){if((b+1|0)<0){break h}if(b>>>0<=1073741822){a=a<<6;b=(b&-32)+32|0;a=a>>>0>b>>>0?a:b}else{a=2147483647}$a(e,a);b=F[h+64>>2]}F[h+64>>2]=b+1;a=F[h+60>>2]+(b>>>3&536870908)|0;d=F[a>>2];M=a,N=oi(b)&d,F[M>>2]=N;b=F[h+76>>2];if((b|0)!=F[h+80>>2]){F[b>>2]=i;F[h+76>>2]=b+4;break _}f=F[l>>2];a=b-f|0;o=a>>2;d=o+1|0;if(d>>>0>=1073741824){break g}n=a>>>1|0;n=a>>>0>=2147483644?1073741823:d>>>0<n>>>0?n:d;if(n){if(n>>>0>=1073741824){break j}a=ka(n<<2)}else{a=0}d=a+(o<<2)|0;F[d>>2]=i;o=d+4|0;if((b|0)!=(f|0)){while(1){d=d-4|0;b=b-4|0;F[d>>2]=F[b>>2];if((b|0)!=(f|0)){continue}break}}F[h+80>>2]=a+(n<<2);F[h+76>>2]=o;F[h+72>>2]=d;if(!f){break _}ja(f)}if((c|0)!=(g|0)){continue}break}a=F[h+8>>2]}f=-1;if(((F[a+4>>2]-F[a>>2]>>2>>>0)/3|0)!=(m|0)){break l}f=F[a+28>>2]-F[a+24>>2]>>2;c=F[j+24>>2];n=F[j+28>>2];if((c|0)==(n|0)){break k}while(1){g=F[c>>2];e=F[a+24>>2];b=f-1|0;d=e+(b<<2)|0;if(F[d>>2]==-1){while(1){b=f-2|0;f=f-1|0;d=e+(b<<2)|0;if(F[d>>2]==-1){continue}break}}if(b>>>0>=g>>>0){F[j>>2]=a;d=F[d>>2];D[j+12|0]=1;F[j+8>>2]=d;F[j+4>>2]=d;if((d|0)!=-1){while(1){a=F[F[h+8>>2]>>2]+(d<<2)|0;if(F[a>>2]!=(b|0)){f=-1;break l}F[a>>2]=g;nc(j);d=F[j+8>>2];if((d|0)!=-1){continue}break}a=F[h+8>>2]}m=F[a+24>>2];e=m+(b<<2)|0;if((g|0)!=-1){F[m+(g<<2)>>2]=F[e>>2]}F[e>>2]=-1;e=1<<g;m=F[h+120>>2];g=m+(g>>>3&536870908)|0;d=1<<b;m=m+(b>>>3&536870908)|0;if(d&F[m>>2]){b=e|F[g>>2]}else{b=F[g>>2]&(e^-1)}F[g>>2]=b;F[m>>2]=F[m>>2]&(d^-1);f=f-1|0}c=c+4|0;if((n|0)!=(c|0)){continue}break}}c=F[j+24>>2]}if(c){ja(c)}a=F[j+48>>2];if(a){while(1){c=F[a>>2];ja(a);a=c;if(a){continue}break}}a=F[j+40>>2];F[j+40>>2]=0;if(a){ja(a)}a=F[j+64>>2];if(a){F[j+68>>2]=a;ja(a)}Z=j+96|0;a=f;break f}oa();v()}na();v()}na();v()}na();v()}b=a;if((a|0)==-1){break e}a=C;c=F[a+16>>2];g=c+F[a>>2]|0;c=F[a+8>>2]-c|0;a=F[F[h+4>>2]+32>>2];E[a+38>>1]=H[a+38>>1];F[a>>2]=g;F[a+16>>2]=0;F[a+20>>2]=0;F[a+8>>2]=c;F[a+12>>2]=0;ca:{if(F[h+216>>2]==F[h+220>>2]){break ca}a=F[h+8>>2];if(F[a+4>>2]==F[a>>2]){break ca}c=0;while(1){if(Ad(h,c)){c=c+3|0;a=F[h+8>>2];if(c>>>0<F[a+4>>2]-F[a>>2]>>2>>>0){continue}break ca}break}break e}if(G[h+308|0]){D[h+308|0]=0;g=F[h+292>>2];a=0;e=F[h+304>>2]+7|0;a=e>>>0<7?1:a;c=a>>>3|0;m=a<<29|e>>>3;a=m+F[h+288>>2]|0;e=c+g|0;F[h+288>>2]=a;F[h+292>>2]=a>>>0<m>>>0?e+1|0:e}c=F[h+216>>2];if((c|0)!=F[h+220>>2]){a=0;while(1){e=L(a,144);Zc((e+c|0)+4|0,F[h+8>>2]);g=F[y>>2];m=g+e|0;c=F[m+132>>2];m=F[m+136>>2];if((c|0)!=(m|0)){while(1){Xc((e+F[y>>2]|0)+4|0,F[c>>2]);c=c+4|0;if((m|0)!=(c|0)){continue}break}g=F[y>>2]}if(!Yc((g+e|0)+4|0)){break e}a=a+1|0;c=F[h+216>>2];if(a>>>0<(F[h+220>>2]-c|0)/144>>>0){continue}break}}a=F[h+8>>2];Hb(h+184|0,F[a+28>>2]-F[a+24>>2]>>2);g=F[h+216>>2];if((g|0)!=F[h+220>>2]){c=0;while(1){a=L(c,144)+g|0;g=F[a+60>>2]-F[a+56>>2]>>2;e=a+104|0;a=F[h+8>>2];a=F[a+28>>2]-F[a+24>>2]>>2;Hb(e,(a|0)<(g|0)?g:a);c=c+1|0;g=F[h+216>>2];if(c>>>0<(F[h+220>>2]-g|0)/144>>>0){continue}break}}K=zd(h,b)}}Z=t- -64|0;return K|0}function Cf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,E=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0;B=c;c=0;m=Z-96|0;Z=m;l=m+16|0;ma(l,0,76);F[m+92>>2]=-1;F[m+8>>2]=0;F[m>>2]=0;F[m+4>>2]=0;r=Z-16|0;Z=r;F[l+68>>2]=0;F[l+72>>2]=0;F[l>>2]=b;s=Z-16|0;Z=s;u=b;a=F[b+20>>2];a:{if((F[b+24>>2]-a|0)<=0){break a}a=F[a>>2];if((a|0)==-1){break a}c=F[F[u+8>>2]+(a<<2)>>2]}b:{c:{d:{if(!c){a=0;break d}a=F[u+100>>2];e=F[u+96>>2];F[s+8>>2]=0;F[s>>2]=0;F[s+4>>2]=0;f=a-e|0;b=(f|0)/12|0;e:{if((a|0)==(e|0)){break e}if(b>>>0>=357913942){break c}d=ka(f);F[s>>2]=d;F[s+8>>2]=d+L(b,12);a=0;n=d;f=f-12|0;d=(f-((f>>>0)%12|0)|0)+12|0;f=ma(n,0,d);F[s+4>>2]=d+f;if(G[c+84|0]){c=b>>>0<=1?1:b;h=c&1;if(b>>>0>=2){g=c&-2;c=0;while(1){d=L(a,12);b=d+e|0;i=F[b+4>>2];j=F[b>>2];d=d+f|0;F[d+8>>2]=F[b+8>>2];F[d>>2]=j;F[d+4>>2]=i;d=L(a|1,12);b=d+e|0;i=F[b+4>>2];j=F[b>>2];d=d+f|0;F[d+8>>2]=F[b+8>>2];F[d>>2]=j;F[d+4>>2]=i;a=a+2|0;c=c+2|0;if((g|0)!=(c|0)){continue}break}}if(!h){break e}b=L(a,12);a=b+e|0;c=F[a+4>>2];e=F[a>>2];b=b+f|0;F[b+8>>2]=F[a+8>>2];F[b>>2]=e;F[b+4>>2]=c;break e}h=b>>>0<=1?1:b;a=F[c+68>>2];c=0;while(1){d=L(c,12);b=d+e|0;g=F[a+(F[b>>2]<<2)>>2];i=F[a+(F[b+4>>2]<<2)>>2];d=d+f|0;F[d+8>>2]=F[a+(F[b+8>>2]<<2)>>2];F[d+4>>2]=i;F[d>>2]=g;c=c+1|0;if((h|0)!=(c|0)){continue}break}}d=0;H=Z-16|0;Z=H;h=ka(88);Zb(h);C=Z-16|0;Z=C;F[h+80>>2]=0;F[h+84>>2]=0;a=F[h+76>>2];F[h+76>>2]=0;if(a){ja(a)}F[h+68>>2]=0;F[h+72>>2]=0;b=h- -64|0;a=F[b>>2];F[b>>2]=0;if(a){ja(a)}g=F[s+4>>2];b=F[s>>2];c=(g-b|0)/12|0;a=L(c,3);f=F[h>>2];e=F[h+4>>2]-f>>2;f:{if(a>>>0>e>>>0){nd(h,a-e|0);g=F[s+4>>2];b=F[s>>2];c=(g-b|0)/12|0;break f}if(a>>>0>=e>>>0){break f}F[h+4>>2]=f+(a<<2)}g:{if((b|0)==(g|0)){break g}e=c>>>0<=1?1:c;g=e&1;a=F[h>>2];if(c>>>0>=2){i=e&-2;c=0;while(1){e=L(d,12);j=e+a|0;f=b+e|0;F[j>>2]=F[f>>2];F[a+(e|4)>>2]=F[f+4>>2];F[j+8>>2]=F[f+8>>2];f=L(d|1,12);e=f+a|0;f=b+f|0;F[e>>2]=F[f>>2];F[e+4>>2]=F[f+4>>2];F[e+8>>2]=F[f+8>>2];d=d+2|0;c=c+2|0;if((i|0)!=(c|0)){continue}break}}if(!g){break g}c=L(d,12);a=c+a|0;b=b+c|0;F[a>>2]=F[b>>2];F[a+4>>2]=F[b+4>>2];F[a+8>>2]=F[b+8>>2]}F[C+12>>2]=-1;a=0;e=0;g=0;f=Z-32|0;Z=f;h:{i:{w=C+12|0;j:{if(!w){break j}c=F[h+4>>2];j=F[h>>2];d=c-j|0;i=d>>2;n=F[h+12>>2];b=F[h+16>>2]-n>>2;k:{if(i>>>0>b>>>0){ab(h+12|0,i-b|0,10228);c=F[h+4>>2];j=F[h>>2];d=c-j|0;i=d>>2;break k}if(b>>>0<=i>>>0){break k}F[h+16>>2]=n+(i<<2)}F[f+24>>2]=0;F[f+16>>2]=0;F[f+20>>2]=0;b=(c|0)==(j|0);if(!b){if((d|0)<0){break i}e=ka(d);F[f+20>>2]=e;F[f+16>>2]=e;F[f+24>>2]=(i<<2)+e}l:{m:{n:{o:{p:{if(d){while(1){i=F[(a<<2)+j>>2];b=F[f+20>>2]-e>>2;q:{if(i>>>0<b>>>0){break q}F[f>>2]=0;d=i+1|0;if(d>>>0>b>>>0){Fa(f+16|0,d-b|0,f);j=F[h>>2];c=F[h+4>>2];e=F[f+16>>2];break q}if(b>>>0<=d>>>0){break q}F[f+20>>2]=(d<<2)+e}b=(i<<2)+e|0;F[b>>2]=F[b>>2]+1;a=a+1|0;d=c-j|0;i=d>>2;if(a>>>0<i>>>0){continue}break}break p}d=0;if(!b){break o}break n}if((c|0)==(j|0)){d=0;break n}if(d>>>0>=2147483645){break m}}d=ka(d<<1);ma(d,255,i<<3)}F[f+8>>2]=0;F[f>>2]=0;F[f+4>>2]=0;b=F[f+20>>2];a=b-e|0;t=a>>2;r:{s:{if((b|0)==(e|0)){break s}if((a|0)<0){break r}q=ka(a);F[f>>2]=q;F[f+8>>2]=(t<<2)+q;b=ma(q,0,a);F[f+4>>2]=b+a;c=t>>>0<=1?1:t;n=c&3;a=0;if(c-1>>>0>=3){o=c&-4;while(1){c=g<<2;F[c+b>>2]=a;x=c|4;a=F[c+e>>2]+a|0;F[x+b>>2]=a;y=c|8;a=a+F[e+x>>2]|0;F[y+b>>2]=a;c=c|12;a=a+F[e+y>>2]|0;F[c+b>>2]=a;a=a+F[c+e>>2]|0;g=g+4|0;p=p+4|0;if((o|0)!=(p|0)){continue}break}}if(!n){break s}while(1){c=g<<2;F[c+b>>2]=a;g=g+1|0;a=F[c+e>>2]+a|0;k=k+1|0;if((n|0)!=(k|0)){continue}break}}if(!i){break l}x=F[h+40>>2];y=F[h+12>>2];n=0;while(1){I=n<<2;a=I+j|0;k=-1;c=n+1|0;b=(c>>>0)%3|0?c:n-2|0;if((b|0)!=-1){k=F[(b<<2)+j>>2]}b=F[a>>2];t:{u:{if(!((n>>>0)%3|0)){p=-1;a=n+2|0;if((a|0)!=-1){p=F[(a<<2)+j>>2]}if(!((b|0)==(k|0)|(b|0)==(p|0))&(k|0)!=(p|0)){break u}x=x+1|0;F[h+40>>2]=x;c=n+3|0;break t}p=F[a-4>>2]}a=p<<2;A=F[a+e>>2];v:{w:{if((A|0)<=0){break w}a=F[a+q>>2];g=0;while(1){o=(a<<3)+d|0;z=F[o>>2];if((z|0)==-1){break w}x:{if((k|0)!=(z|0)){break x}o=F[o+4>>2];if((o|0)!=-1){z=F[(o<<2)+j>>2]}else{z=-1}if((z|0)==(b|0)){break x}while(1){y:{b=a;g=g+1|0;if((A|0)<=(g|0)){break y}a=b+1|0;J=(a<<3)+d|0;z=F[J>>2];K=(b<<3)+d|0;F[K+4>>2]=F[J+4>>2];F[K>>2]=z;if((z|0)!=-1){continue}}break}F[(b<<3)+d>>2]=-1;if((o|0)==-1){break w}F[y+I>>2]=o;F[y+(o<<2)>>2]=n;break v}a=a+1|0;g=g+1|0;if((A|0)!=(g|0)){continue}break}}a=k<<2;k=F[a+e>>2];if((k|0)<=0){break v}a=F[a+q>>2];g=0;while(1){b=(a<<3)+d|0;if(F[b>>2]==-1){F[b>>2]=p;F[b+4>>2]=n;break v}a=a+1|0;g=g+1|0;if((k|0)!=(g|0)){continue}break}}}n=c;if(n>>>0<i>>>0){continue}break}break l}break i}na();v()}F[w>>2]=t;if(q){ja(q)}if(d){ja(d)}a=F[f+16>>2];if(!a){break j}F[f+20>>2]=a;ja(a)}Z=f+32|0;x=(w|0)!=0;if(x){k=Z-32|0;Z=k;a=F[h>>2];g=F[h+4>>2];F[k+24>>2]=0;F[k+16>>2]=0;F[k+20>>2]=0;if((a|0)==(g|0)){c=g}else{a=g-a|0;if((a|0)<0){break i}a=a>>2;b=(a-1>>>5|0)+1|0;c=ka(b<<2);F[k+24>>2]=b;F[k+20>>2]=0;F[k+16>>2]=c;Yb(k+16|0,a);g=F[h>>2];c=F[h+4>>2]}F[k+8>>2]=0;F[k>>2]=0;while(1){z:{o=0;i=0;if((c|0)==(g|0)){break z}while(1){b=F[k+16>>2];A:{if(F[b+(i>>>3&536870908)>>2]>>>i&1){break A}c=F[k>>2];F[k+4>>2]=c;e=F[h+12>>2];a=i;while(1){B:{f=a+1|0;d=a;a=(f>>>0)%3|0?f:a-2|0;if((a|0)==-1){break B}a=F[e+(a<<2)>>2];if((a|0)==-1){break B}f=a+1|0;a=(f>>>0)%3|0?f:a-2|0;if((i|0)==(a|0)|(a|0)==-1){break B}if(!(F[b+(a>>>3&536870908)>>2]>>>a&1)){continue}}break}j=d;C:{D:{E:{while(1){a=F[k+16>>2]+(j>>>3&536870908)|0;F[a>>2]=F[a>>2]|1<<j;a=j+1|0;f=(a>>>0)%3|0?a:j-2|0;g=F[h>>2];y=(j>>>0)%3|0;b=(y?-1:2)+j|0;n=F[k>>2];A=(n|0)==(c|0);F:{if(A){break F}w=F[(f<<2)+g>>2];q=F[h+12>>2];a=n;if((b|0)!=-1){e=q+(b<<2)|0;while(1){G:{if((w|0)!=F[a>>2]){break G}p=F[a+4>>2];t=F[e>>2];if((p|0)==(t|0)){break G}e=b;c=-1;a=-1;if((p|0)==-1){break C}break D}a=a+8|0;if((c|0)!=(a|0)){continue}break}break F}while(1){if((w|0)==F[a>>2]){t=-1;e=-1;p=F[a+4>>2];if((p|0)!=-1){break D}}a=a+8|0;if((c|0)!=(a|0)){continue}break}}b=F[(b<<2)+g>>2];H:{if(F[k+8>>2]!=(c|0)){F[c>>2]=b;F[c+4>>2]=f;c=c+8|0;F[k+4>>2]=c;break H}a=c-n|0;p=a>>3;e=p+1|0;if(e>>>0>=536870912){break i}g=a>>>2|0;g=a>>>0>=2147483640?536870911:e>>>0<g>>>0?g:e;if(g){if(g>>>0>=536870912){break E}e=ka(g<<3)}else{e=0}a=e+(p<<3)|0;F[a>>2]=b;F[a+4>>2]=f;b=a+8|0;if(!A){while(1){c=c-8|0;f=F[c+4>>2];a=a-8|0;F[a>>2]=F[c>>2];F[a+4>>2]=f;if((c|0)!=(n|0)){continue}break}c=F[k>>2]}F[k+8>>2]=e+(g<<3);F[k+4>>2]=b;F[k>>2]=a;if(c){ja(c)}c=b}I:{J:{if(y){a=j-1|0;break J}a=j+2|0;if((a|0)==-1){break I}}a=F[F[h+12>>2]+(a<<2)>>2];if((a|0)==-1){break I}j=a+((a>>>0)%3|0?-1:2)|0;if((d|0)==(j|0)){break I}if((j|0)!=-1){continue}}break}g=F[h>>2];break A}oa();v()}c=F[q+(p<<2)>>2];b=e;a=p}if((t|0)!=-1){F[q+(t<<2)>>2]=-1}if((c|0)!=-1){F[q+(c<<2)>>2]=-1}F[q+(b<<2)>>2]=-1;F[q+(a<<2)>>2]=-1;o=1}i=i+1|0;c=F[h+4>>2];if(i>>>0<c-g>>2>>>0){continue}break}if(o){continue}}break}a=F[k>>2];if(a){ja(a)}a=F[k+16>>2];if(a){ja(a)}Z=k+32|0;n=0;g=Z-32|0;Z=g;e=F[C+12>>2];F[h+36>>2]=e;p=h+24|0;b=F[h+24>>2];a=F[h+28>>2]-b>>2;K:{L:{if(a>>>0<e>>>0){ab(p,e-a|0,10228);F[g+24>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;break L}if(a>>>0>e>>>0){F[h+28>>2]=b+(e<<2)}F[g+24>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;if(!e){break K}}if((e|0)<0){break i}a=(e-1>>>5|0)+1|0;b=ka(a<<2);F[g+24>>2]=a;F[g+20>>2]=0;F[g+16>>2]=b;Yb(g+16|0,e)}a=F[h>>2];b=F[h+4>>2];F[g+8>>2]=0;F[g>>2]=0;F[g+4>>2]=0;M:{if((a|0)==(b|0)){a=b}else{a=b-a|0;if((a|0)<0){break i}a=a>>2;b=(a-1>>>5|0)+1|0;c=ka(b<<2);F[g+8>>2]=b;F[g+4>>2]=0;F[g>>2]=c;Yb(g,a);b=F[h>>2];a=F[h+4>>2]}if(a-b>>>0<12){break M}N:{while(1){q=L(n,3);d=(q<<2)+b|0;f=F[d>>2];c=-1;i=q+1|0;if((i|0)!=-1){c=F[(i<<2)+b>>2]}O:{if((c|0)==(f|0)){break O}i=f;f=F[d+8>>2];if((i|0)==(f|0)|(c|0)==(f|0)){break O}k=0;i=F[g>>2];while(1){f=k+q|0;if(!(F[(f>>>3&536870908)+i>>2]>>>f&1)){a=F[(f<<2)+b>>2];c=1<<a;d=F[g+16>>2];b=a>>>5|0;i=F[d+(b<<2)>>2];t=c&i;if(t){c=F[h+28>>2];P:{if((c|0)!=F[h+32>>2]){F[c>>2]=-1;F[h+28>>2]=c+4;break P}i=F[p>>2];b=c-i|0;o=b>>2;d=o+1|0;if(d>>>0>=1073741824){break i}j=b>>>1|0;j=b>>>0>=2147483644?1073741823:d>>>0<j>>>0?j:d;if(j){if(j>>>0>=1073741824){break N}b=ka(j<<2)}else{b=0}d=b+(o<<2)|0;F[d>>2]=-1;o=d+4|0;if((c|0)!=(i|0)){while(1){d=d-4|0;c=c-4|0;F[d>>2]=F[c>>2];if((c|0)!=(i|0)){continue}break}}F[h+32>>2]=b+(j<<2);F[h+28>>2]=o;F[h+24>>2]=d;if(!i){break P}ja(i)}c=F[h+52>>2];Q:{if((c|0)!=F[h+56>>2]){F[c>>2]=a;F[h+52>>2]=c+4;break Q}i=F[h+48>>2];b=c-i|0;o=b>>2;d=o+1|0;if(d>>>0>=1073741824){break i}j=b>>>1|0;j=b>>>0>=2147483644?1073741823:d>>>0<j>>>0?j:d;if(j){if(j>>>0>=1073741824){break N}b=ka(j<<2)}else{b=0}d=b+(o<<2)|0;F[d>>2]=a;a=d+4|0;if((c|0)!=(i|0)){while(1){d=d-4|0;c=c-4|0;F[d>>2]=F[c>>2];if((c|0)!=(i|0)){continue}break}}F[h+56>>2]=b+(j<<2);F[h+52>>2]=a;F[h+48>>2]=d;if(!i){break Q}ja(i)}c=F[g+20>>2];a=F[g+24>>2];if((c|0)==a<<5){if((c+1|0)<0){break i}b=g+16|0;if(c>>>0<=1073741822){a=a<<6;c=(c&-32)+32|0;a=a>>>0>c>>>0?a:c}else{a=2147483647}$a(b,a);c=F[g+20>>2]}F[g+20>>2]=c+1;d=F[g+16>>2];a=d+(c>>>3&536870908)|0;b=F[a>>2];N=a,O=oi(c)&b,F[N>>2]=O;c=1<<e;b=e>>>5|0;i=F[(b<<2)+d>>2];a=e;e=a+1|0}F[(b<<2)+d>>2]=c|i;o=F[h+24>>2]+(a<<2)|0;j=F[h+12>>2];b=F[h>>2];i=F[g>>2];c=f;R:{S:{T:{while(1){if((c|0)==-1){break T}d=(c>>>3&536870908)+i|0;F[d>>2]=F[d>>2]|1<<c;F[o>>2]=c;if(t){F[(c<<2)+b>>2]=a}w=c+1|0;c=(w>>>0)%3|0?w:c-2|0;d=-1;U:{if((c|0)==-1){break U}c=F[j+(c<<2)>>2];d=-1;if((c|0)==-1){break U}d=c+1|0;d=(d>>>0)%3|0?d:c-2|0}c=d;if((f|0)!=(c|0)){continue}break}if((f|0)!=-1){break R}c=1;break S}if((f>>>0)%3|0){c=f-1|0;break S}c=f+2|0;if((c|0)==-1){break R}}c=F[j+(c<<2)>>2];if((c|0)==-1){break R}V:{if((c>>>0)%3|0){c=c-1|0;break V}c=c+2|0;if((c|0)==-1){break R}}f=F[h+12>>2];b=F[h>>2];while(1){d=(c>>>3&536870908)+i|0;F[d>>2]=F[d>>2]|1<<c;if(t){F[(c<<2)+b>>2]=a}W:{if((c>>>0)%3|0){c=c-1|0;break W}c=c+2|0;if((c|0)==-1){break R}}c=F[f+(c<<2)>>2];if((c|0)==-1){break R}c=c+((c>>>0)%3|0?-1:2)|0;if((c|0)!=-1){continue}break}}}k=k+1|0;if((k|0)!=3){continue}break}b=F[h>>2];a=F[h+4>>2]}n=n+1|0;if(n>>>0<(a-b>>2>>>0)/3>>>0){continue}break}break M}oa();v()}c=0;F[h+44>>2]=0;a=F[g+16>>2];b=F[g+20>>2];if(b){e=b&31;b=(b>>>3&536870908)+a|0;d=a;i=0;while(1){if(!(F[d>>2]>>>c&1)){i=i+1|0;F[h+44>>2]=i}f=(c|0)==31;c=f?0:c+1|0;d=(f<<2)+d|0;if((b|0)!=(d|0)|(c|0)!=(e|0)){continue}break}}b=F[g>>2];if(b){ja(b);a=F[g+16>>2]}if(a){ja(a)}Z=g+32|0}Z=C+16|0;if(!x){F[H+8>>2]=0;Za(h);h=0}Z=H+16|0;a=h;break h}na();v()}b=F[s>>2];if(!b){break d}F[s+4>>2]=b;ja(b)}Z=s+16|0;break b}na();v()}c=F[l+4>>2];b=a;F[l+4>>2]=a;if(c){Za(c);b=F[l+4>>2]}X:{if(!b){break X}a=F[u+100>>2];c=F[u+96>>2];D[r+12|0]=0;Ea(l+56|0,(a-c|0)/12|0,r+12|0);a=F[u+100>>2];c=F[u+96>>2];if((a|0)==(c|0)){break X}while(1){if(!(F[F[l+56>>2]+(E>>>3&536870908)>>2]>>>E&1)){a=L(E,3);Vb(l,0,a);c=F[l+8>>2];e=F[l+12>>2];Vb(l,1,a+1|0);f=F[l+20>>2];d=F[l+24>>2];Vb(l,2,a+2|0);n=(c|0)==(e|0)?-1:0;a=d-f>>2;c=e-c>>2;e=a>>>0>c>>>0;c=F[l+36>>2]-F[l+32>>2]>>2>>>0>(e?a:c)>>>0?2:e?1:n;Y:{if(F[l+68>>2]<=0){break Y}F[r+12>>2]=F[l+76>>2];F[r+8>>2]=m;Qa(r+8|0,r+12|0);a=F[((c<<2)+l|0)+44>>2];if((a|0)<0){a=-1}else{e=(a>>>0)/3|0;a=F[(F[F[l>>2]+96>>2]+L(e,12)|0)+(a-L(e,3)<<2)>>2]}F[r+12>>2]=a;F[r+8>>2]=m;Qa(r+8|0,r+12|0);e=F[l+72>>2];F[l+72>>2]=e+2;if(!(e&1)){break Y}F[r+12>>2]=a;F[r+8>>2]=m;Qa(r+8|0,r+12|0);F[l+72>>2]=F[l+72>>2]+1}d=0;e=Z-16|0;Z=e;F[l+68>>2]=F[l+68>>2]+1;a=L(c,12)+l|0;a=F[a+12>>2]-F[a+8>>2]|0;if((a|0)>0){a=a>>>2|0;h=a>>>0<=1?1:a;c=F[((c<<2)+l|0)+44>>2];while(1){a=c;f=(a>>>0)/3|0;c=(a|0)==-1;g=c?-1:f;i=F[l+56>>2]+(g>>>3&536870908)|0;F[i>>2]=F[i>>2]|1<<g;F[l+72>>2]=F[l+72>>2]+1;Z:{_:{$:{aa:{ba:{if(!d){ca:{if((a|0)>=0){F[e+12>>2]=F[(F[F[l>>2]+96>>2]+L(f,12)|0)+((a>>>0)%3<<2)>>2];F[e+8>>2]=m;Qa(e+8|0,e+12|0);break ca}F[e+12>>2]=-1;F[e+8>>2]=m;Qa(e+8|0,e+12|0);if(c){break ba}}c=-1;f=a+1|0;f=(f>>>0)%3|0?f:a-2|0;if((f|0)>=0){g=(f>>>0)/3|0;f=F[(F[F[l>>2]+96>>2]+L(g,12)|0)+(f-L(g,3)<<2)>>2]}else{f=-1}F[e+12>>2]=f;F[e+8>>2]=m;Qa(e+8|0,e+12|0);f=((a>>>0)%3|0?-1:2)+a|0;if((f|0)<0){break aa}c=(f>>>0)/3|0;c=F[(F[F[l>>2]+96>>2]+L(c,12)|0)+(f-L(c,3)<<2)>>2];break aa}c=(a|0)<0?-1:F[(F[F[l>>2]+96>>2]+L(f,12)|0)+((a>>>0)%3<<2)>>2];F[l+76>>2]=c;F[e+12>>2]=c;F[e+8>>2]=m;Qa(e+8|0,e+12|0);if(d&1){c=-1;if((a|0)==-1){break Z}if((L(f,3)|0)!=(a|0)){a=a-1|0;break _}a=a+2|0;break $}c=-1;if((a|0)==-1){break Z}c=a+1|0;a=(c>>>0)%3|0?c:a-2|0;break $}c=-1;F[e+12>>2]=-1;F[e+8>>2]=m;Qa(e+8|0,e+12|0)}F[l+76>>2]=c;F[e+12>>2]=c;F[e+8>>2]=m;Qa(e+8|0,e+12|0)}c=-1;if((a|0)==-1){break Z}}c=F[F[F[l+4>>2]+12>>2]+(a<<2)>>2]}d=d+1|0;if((h|0)!=(d|0)){continue}break}}Z=e+16|0;c=F[u+96>>2];a=F[u+100>>2]}E=E+1|0;if(E>>>0<(a-c|0)/12>>>0){continue}break}}Z=r+16|0;da:{if(b){a=F[B>>2];if(a){F[B+4>>2]=a;ja(a)}F[B>>2]=F[m>>2];F[B+4>>2]=F[m+4>>2];F[B+8>>2]=F[m+8>>2];M=F[m+84>>2];break da}a=F[m>>2];if(!a){break da}F[m+4>>2]=a;ja(a)}a=F[m+72>>2];if(a){ja(a)}a=F[m+48>>2];if(a){F[m+52>>2]=a;ja(a)}a=F[m+36>>2];if(a){F[m+40>>2]=a;ja(a)}a=F[m+24>>2];if(a){F[m+28>>2]=a;ja(a)}a=F[m+20>>2];F[m+20>>2]=0;if(a){Za(a)}Z=m+96|0;return M|0}function sf(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0;i=b;a=0;b=0;a:{b:{switch(d-1|0){case 0:j=F[i+80>>2];h=G[c+24|0];c:{if((L(j,h)|0)!=(e|0)){break c}d=F[c+28>>2]!=1;b=G[c+84|0];if(!(d|!b)){la(f,F[F[c>>2]>>2]+F[c+48>>2]|0,e);b=1;break c}if(h){a=ka(h);ma(a,0,h)}d:{if(!j){b=1;break d}if(!d){if(h){d=0;e=0;while(1){i=d+f|0;k=F[F[c>>2]>>2];m=F[c+48>>2];g=F[c+40>>2];b=ki(g,F[c+44>>2],G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],0);n=b;b=b+m|0;la(i,la(a,b+k|0,g),h);d=d+h|0;b=1;e=e+1|0;if((j|0)!=(e|0)){continue}break}break d}if(b){b=1;h=F[c>>2];e=F[c+48>>2];f=F[c+40>>2];i=F[c+44>>2];if((j|0)!=1){g=j&-2;c=0;d=0;while(1){k=F[h>>2];m=ki(f,i,c,0)+e|0;k=la(a,k+m|0,f);m=F[h>>2];n=ki(f,i,c|1,0)+e|0;la(k,m+n|0,f);c=c+2|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}g=c}if(!(j&1)){break d}c=F[h>>2];d=ki(g,0,f,i)+e|0;la(a,c+d|0,f);break d}b=1;h=F[c>>2];e=F[c+48>>2];g=F[c+68>>2];f=F[c+40>>2];i=F[c+44>>2];c=0;if((j|0)!=1){k=j&-2;d=0;while(1){m=F[h>>2];n=c<<2;l=ki(f,i,F[n+g>>2],0)+e|0;m=la(a,m+l|0,f);l=F[h>>2];n=ki(f,i,F[g+(n|4)>>2],0)+e|0;la(m,l+n|0,f);c=c+2|0;d=d+2|0;if((k|0)!=(d|0)){continue}break}}if(!(j&1)){break d}d=F[h>>2];c=ki(f,i,F[g+(c<<2)>>2],0)+e|0;la(a,c+d|0,f);break d}b=0;if(!h){d=0;while(1){if(!Cb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],a)){break d}d=d+1|0;b=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}break d}d=0;e=0;while(1){if(!Cb(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],a)){break d}la(d+f|0,a,h);d=d+h|0;e=e+1|0;b=j>>>0<=e>>>0;if((e|0)!=(j|0)){continue}break}}if(!a){break c}ja(a)}break a;case 2:n=G[c+24|0];l=n<<1;j=F[i+80>>2];e:{if((L(l,j)|0)!=(e|0)){break e}i=F[c+28>>2]!=3;d=G[c+84|0];if(!(i|!d)){la(f,F[F[c>>2]>>2]+F[c+48>>2]|0,e);a=1;break e}f:{if(!n){e=0;break f}e=ka(l);ma(e,0,l)}g:{if(!j){a=1;break g}if(!i){o=F[c+68>>2];k=F[c>>2];b=F[c+48>>2];i=F[c+40>>2];m=F[c+44>>2];if(n){if(!d){c=0;d=0;while(1){a=1;g=F[k>>2];p=ki(i,m,F[o+(d<<2)>>2],0)+b|0;la((c<<1)+f|0,la(e,g+p|0,i),l);c=c+n|0;d=d+1|0;if((j|0)!=(d|0)){continue}break}break g}c=0;while(1){a=1;o=F[k>>2];p=ki(g,h,i,m)+b|0;la((c<<1)+f|0,la(e,o+p|0,i),l);c=c+n|0;d=h;g=g+1|0;d=g?d:d+1|0;h=d;if((j|0)!=(g|0)|d){continue}break}break g}if(!d){a=1;c=0;if((j|0)!=1){f=j&-2;d=0;while(1){h=F[k>>2];g=c<<2;n=ki(i,m,F[g+o>>2],0)+b|0;h=la(e,h+n|0,i);n=F[k>>2];g=ki(i,m,F[o+(g|4)>>2],0)+b|0;la(h,g+n|0,i);c=c+2|0;d=d+2|0;if((f|0)!=(d|0)){continue}break}}if(!(j&1)){break g}d=F[k>>2];b=ki(i,m,F[o+(c<<2)>>2],0)+b|0;la(e,b+d|0,i);break g}n=j&1;a=1;if((j|0)!=1){j=j&-2;f=0;c=0;while(1){d=F[k>>2];l=ki(g,h,i,m)+b|0;d=la(e,d+l|0,i);l=F[k>>2];o=ki(i,m,g|1,h)+b|0;la(d,l+o|0,i);g=g+2|0;h=g>>>0<2?h+1|0:h;f=f+2|0;d=f>>>0<2?c+1|0:c;c=d;if((f|0)!=(j|0)|c){continue}break}}if(!n){break g}c=F[k>>2];b=ki(g,h,i,m)+b|0;la(e,b+c|0,i);break g}if(!n){d=0;while(1){if(!Ab(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break g}d=d+1|0;a=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}break g}d=0;while(1){if(!Ab(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break g}la((b<<1)+f|0,e,l);b=b+n|0;d=d+1|0;a=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}}if(!e){break e}ja(e)}b=a;break a;case 4:l=G[c+24|0];o=l<<2;j=F[i+80>>2];h:{if((L(o,j)|0)!=(e|0)){break h}i=F[c+28>>2]!=5;d=G[c+84|0];if(!(i|!d)){la(f,F[F[c>>2]>>2]+F[c+48>>2]|0,e);b=1;break h}i:{if(!l){e=0;break i}e=ka(o);ma(e,0,o)}b=1;j:{if(!j){break j}if(!i){a=F[c+68>>2];m=F[c>>2];i=F[c+48>>2];k=F[c+40>>2];n=F[c+44>>2];if(l){if(!d){c=0;d=0;while(1){g=F[m>>2];p=ki(k,n,F[a+(d<<2)>>2],0)+i|0;la((c<<2)+f|0,la(e,g+p|0,k),o);c=c+l|0;d=d+1|0;if((j|0)!=(d|0)){continue}break}break j}c=0;while(1){d=F[m>>2];p=ki(g,h,k,n)+i|0;la((c<<2)+f|0,la(e,d+p|0,k),o);c=c+l|0;g=g+1|0;a=g?h:h+1|0;h=a;if((j|0)!=(g|0)|h){continue}break}break j}if(!d){c=0;if((j|0)!=1){f=j&-2;d=0;while(1){h=F[m>>2];g=c<<2;l=ki(k,n,F[g+a>>2],0)+i|0;h=la(e,h+l|0,k);l=F[m>>2];g=ki(k,n,F[a+(g|4)>>2],0)+i|0;la(h,g+l|0,k);c=c+2|0;d=d+2|0;if((f|0)!=(d|0)){continue}break}}if(!(j&1)){break j}d=F[m>>2];a=ki(k,n,F[a+(c<<2)>>2],0)+i|0;la(e,a+d|0,k);break j}l=j&1;if((j|0)!=1){j=j&-2;f=0;c=0;while(1){a=F[m>>2];d=ki(g,h,k,n)+i|0;a=la(e,a+d|0,k);d=F[m>>2];o=ki(k,n,g|1,h)+i|0;la(a,d+o|0,k);d=h;g=g+2|0;h=g>>>0<2?d+1|0:d;f=f+2|0;a=f>>>0<2?c+1|0:c;c=a;if((f|0)!=(j|0)|c){continue}break}}if(!l){break j}a=F[m>>2];c=ki(g,h,k,n)+i|0;la(e,a+c|0,k);break j}b=0;if(!l){d=0;while(1){if(!yb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break j}d=d+1|0;b=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}break j}d=0;while(1){if(!yb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break j}la((a<<2)+f|0,e,o);a=a+l|0;d=d+1|0;b=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}}if(!e){break h}ja(e)}break a;case 1:j=F[i+80>>2];h=G[c+24|0];k:{if((L(j,h)|0)!=(e|0)){break k}d=F[c+28>>2]!=2;b=G[c+84|0];if(!(d|!b)){la(f,F[F[c>>2]>>2]+F[c+48>>2]|0,e);b=1;break k}if(h){a=ka(h);ma(a,0,h)}l:{if(!j){b=1;break l}if(!d){if(h){d=0;e=0;while(1){i=d+f|0;k=F[F[c>>2]>>2];m=F[c+48>>2];g=F[c+40>>2];b=ki(g,F[c+44>>2],G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],0);n=b;b=b+m|0;la(i,la(a,b+k|0,g),h);d=d+h|0;b=1;e=e+1|0;if((j|0)!=(e|0)){continue}break}break l}if(b){b=1;h=F[c>>2];e=F[c+48>>2];f=F[c+40>>2];i=F[c+44>>2];if((j|0)!=1){g=j&-2;c=0;d=0;while(1){k=F[h>>2];m=ki(f,i,c,0)+e|0;k=la(a,k+m|0,f);m=F[h>>2];n=ki(f,i,c|1,0)+e|0;la(k,m+n|0,f);c=c+2|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}g=c}if(!(j&1)){break l}c=F[h>>2];d=ki(g,0,f,i)+e|0;la(a,c+d|0,f);break l}b=1;h=F[c>>2];e=F[c+48>>2];g=F[c+68>>2];f=F[c+40>>2];i=F[c+44>>2];c=0;if((j|0)!=1){k=j&-2;d=0;while(1){m=F[h>>2];n=c<<2;l=ki(f,i,F[n+g>>2],0)+e|0;m=la(a,m+l|0,f);l=F[h>>2];n=ki(f,i,F[g+(n|4)>>2],0)+e|0;la(m,l+n|0,f);c=c+2|0;d=d+2|0;if((k|0)!=(d|0)){continue}break}}if(!(j&1)){break l}d=F[h>>2];c=ki(f,i,F[g+(c<<2)>>2],0)+e|0;la(a,c+d|0,f);break l}b=0;if(!h){d=0;while(1){if(!Bb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],a)){break l}d=d+1|0;b=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}break l}d=0;e=0;while(1){if(!Bb(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],a)){break l}la(d+f|0,a,h);d=d+h|0;e=e+1|0;b=j>>>0<=e>>>0;if((e|0)!=(j|0)){continue}break}}if(!a){break k}ja(a)}break a;case 3:n=G[c+24|0];l=n<<1;j=F[i+80>>2];m:{if((L(l,j)|0)!=(e|0)){break m}i=F[c+28>>2]!=4;d=G[c+84|0];if(!(i|!d)){la(f,F[F[c>>2]>>2]+F[c+48>>2]|0,e);a=1;break m}n:{if(!n){e=0;break n}e=ka(l);ma(e,0,l)}o:{if(!j){a=1;break o}if(!i){o=F[c+68>>2];k=F[c>>2];b=F[c+48>>2];i=F[c+40>>2];m=F[c+44>>2];if(n){if(!d){c=0;d=0;while(1){a=1;g=F[k>>2];p=ki(i,m,F[o+(d<<2)>>2],0)+b|0;la((c<<1)+f|0,la(e,g+p|0,i),l);c=c+n|0;d=d+1|0;if((j|0)!=(d|0)){continue}break}break o}c=0;while(1){a=1;o=F[k>>2];p=ki(g,h,i,m)+b|0;la((c<<1)+f|0,la(e,o+p|0,i),l);c=c+n|0;d=h;g=g+1|0;d=g?d:d+1|0;h=d;if((j|0)!=(g|0)|d){continue}break}break o}if(!d){a=1;c=0;if((j|0)!=1){f=j&-2;d=0;while(1){h=F[k>>2];g=c<<2;n=ki(i,m,F[g+o>>2],0)+b|0;h=la(e,h+n|0,i);n=F[k>>2];g=ki(i,m,F[o+(g|4)>>2],0)+b|0;la(h,g+n|0,i);c=c+2|0;d=d+2|0;if((f|0)!=(d|0)){continue}break}}if(!(j&1)){break o}d=F[k>>2];b=ki(i,m,F[o+(c<<2)>>2],0)+b|0;la(e,b+d|0,i);break o}n=j&1;a=1;if((j|0)!=1){j=j&-2;f=0;c=0;while(1){d=F[k>>2];l=ki(g,h,i,m)+b|0;d=la(e,d+l|0,i);l=F[k>>2];o=ki(i,m,g|1,h)+b|0;la(d,l+o|0,i);g=g+2|0;h=g>>>0<2?h+1|0:h;f=f+2|0;d=f>>>0<2?c+1|0:c;c=d;if((f|0)!=(j|0)|c){continue}break}}if(!n){break o}c=F[k>>2];b=ki(g,h,i,m)+b|0;la(e,b+c|0,i);break o}if(!n){d=0;while(1){if(!zb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break o}d=d+1|0;a=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}break o}d=0;while(1){if(!zb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break o}la((b<<1)+f|0,e,l);b=b+n|0;d=d+1|0;a=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}}if(!e){break m}ja(e)}b=a;break a;case 5:l=G[c+24|0];o=l<<2;j=F[i+80>>2];p:{if((L(o,j)|0)!=(e|0)){break p}i=F[c+28>>2]!=6;d=G[c+84|0];if(!(i|!d)){la(f,F[F[c>>2]>>2]+F[c+48>>2]|0,e);b=1;break p}q:{if(!l){e=0;break q}e=ka(o);ma(e,0,o)}b=1;r:{if(!j){break r}if(!i){a=F[c+68>>2];m=F[c>>2];i=F[c+48>>2];k=F[c+40>>2];n=F[c+44>>2];if(l){if(!d){c=0;d=0;while(1){g=F[m>>2];p=ki(k,n,F[a+(d<<2)>>2],0)+i|0;la((c<<2)+f|0,la(e,g+p|0,k),o);c=c+l|0;d=d+1|0;if((j|0)!=(d|0)){continue}break}break r}c=0;while(1){d=F[m>>2];p=ki(g,h,k,n)+i|0;la((c<<2)+f|0,la(e,d+p|0,k),o);c=c+l|0;g=g+1|0;a=g?h:h+1|0;h=a;if((j|0)!=(g|0)|h){continue}break}break r}if(!d){c=0;if((j|0)!=1){f=j&-2;d=0;while(1){h=F[m>>2];g=c<<2;l=ki(k,n,F[g+a>>2],0)+i|0;h=la(e,h+l|0,k);l=F[m>>2];g=ki(k,n,F[a+(g|4)>>2],0)+i|0;la(h,g+l|0,k);c=c+2|0;d=d+2|0;if((f|0)!=(d|0)){continue}break}}if(!(j&1)){break r}d=F[m>>2];a=ki(k,n,F[a+(c<<2)>>2],0)+i|0;la(e,a+d|0,k);break r}l=j&1;if((j|0)!=1){j=j&-2;f=0;c=0;while(1){a=F[m>>2];d=ki(g,h,k,n)+i|0;a=la(e,a+d|0,k);d=F[m>>2];o=ki(k,n,g|1,h)+i|0;la(a,d+o|0,k);d=h;g=g+2|0;h=g>>>0<2?d+1|0:d;f=f+2|0;a=f>>>0<2?c+1|0:c;c=a;if((f|0)!=(j|0)|c){continue}break}}if(!l){break r}a=F[m>>2];c=ki(g,h,k,n)+i|0;la(e,a+c|0,k);break r}b=0;if(!l){d=0;while(1){if(!xb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break r}d=d+1|0;b=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}break r}d=0;while(1){if(!xb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],e)){break r}la((a<<2)+f|0,e,o);a=a+l|0;d=d+1|0;b=j>>>0<=d>>>0;if((d|0)!=(j|0)){continue}break}}if(!e){break p}ja(e)}break a;case 8:p=G[c+24|0];q=p<<2;k=F[i+80>>2];s:{if((L(q,k)|0)!=(e|0)){break s}i=F[c+28>>2];t:{if(!p){break t}a=ka(q);d=a;m=q-4|0;l=(m>>>2|0)+1&7;if(l){e=0;while(1){F[d>>2]=-1073741824;d=d+4|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}if(m>>>0<28){break t}e=(p<<2)+a|0;while(1){F[d+24>>2]=-1073741824;F[d+28>>2]=-1073741824;F[d+16>>2]=-1073741824;F[d+20>>2]=-1073741824;F[d+8>>2]=-1073741824;F[d+12>>2]=-1073741824;F[d>>2]=-1073741824;F[d+4>>2]=-1073741824;d=d+32|0;if((e|0)!=(d|0)){continue}break}}u:{if(!k){b=1;break u}if((i|0)==9){r=F[c+68>>2];l=F[c>>2];i=F[c+48>>2];s=G[c+84|0];m=F[c+44>>2];c=F[c+40>>2];o=c;if(p){e=0;d=0;while(1){h=(e<<2)+f|0;g=F[l>>2];b=ki(c,m,s?d:F[r+(d<<2)>>2],0)+i|0;la(h,la(a,b+g|0,o),q);e=e+p|0;b=1;d=d+1|0;if((k|0)!=(d|0)){continue}break}break u}if(!s){b=1;d=0;if((k|0)!=1){f=k&-2;e=0;while(1){h=F[l>>2];g=d<<2;j=ki(c,m,F[g+r>>2],0)+i|0;h=la(a,h+j|0,o);j=F[l>>2];g=ki(c,m,F[r+(g|4)>>2],0)+i|0;la(h,j+g|0,o);d=d+2|0;e=e+2|0;if((f|0)!=(e|0)){continue}break}}if(!(k&1)){break u}e=F[l>>2];c=ki(c,m,F[r+(d<<2)>>2],0)+i|0;la(a,c+e|0,o);break u}f=k&1;b=1;if((k|0)!=1){k=k&-2;while(1){d=F[l>>2];e=ki(g,h,c,m)+i|0;d=la(a,d+e|0,o);e=F[l>>2];p=ki(c,m,g|1,h)+i|0;la(d,e+p|0,o);g=g+2|0;h=g>>>0<2?h+1|0:h;d=j;e=n+2|0;d=e>>>0<2?d+1|0:d;n=e;j=d;if((e|0)!=(k|0)|d){continue}break}}if(!f){break u}d=F[l>>2];c=ki(g,h,c,m)+i|0;la(a,c+d|0,o);break u}if(!p){d=0;while(1){if(!lb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],a)){break u}d=d+1|0;b=k>>>0<=d>>>0;if((d|0)!=(k|0)){continue}break}break u}e=0;d=0;while(1){if(!lb(c,G[c+84|0]?d:F[F[c+68>>2]+(d<<2)>>2],D[c+24|0],a)){break u}la((e<<2)+f|0,a,q);e=e+p|0;d=d+1|0;b=k>>>0<=d>>>0;if((d|0)!=(k|0)){continue}break}}if(!a){break s}ja(a)}a=b;break;default:break b}}b=a}return b|0}function Pd(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;h=Z-80|0;Z=h;e=F[c+36>>2];F[h+72>>2]=F[c+32>>2];F[h+76>>2]=e;f=F[c+28>>2];e=h- -64|0;F[e>>2]=F[c+24>>2];F[e+4>>2]=f;e=F[c+20>>2];F[h+56>>2]=F[c+16>>2];F[h+60>>2]=e;e=F[c+12>>2];F[h+48>>2]=F[c+8>>2];F[h+52>>2]=e;e=F[c+4>>2];F[h+40>>2]=F[c>>2];F[h+44>>2]=e;jc(a,h+40|0,h+24|0);a:{if(F[a>>2]){break a}if(D[a+15|0]<0){ja(F[a+4>>2])}if(G[h+31|0]!=1){b=ka(32);D[b+20|0]=0;c=G[1446]|G[1447]<<8|(G[1448]<<16|G[1449]<<24);D[b+16|0]=c;D[b+17|0]=c>>>8;D[b+18|0]=c>>>16;D[b+19|0]=c>>>24;c=G[1442]|G[1443]<<8|(G[1444]<<16|G[1445]<<24);d=G[1438]|G[1439]<<8|(G[1440]<<16|G[1441]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1434]|G[1435]<<8|(G[1436]<<16|G[1437]<<24);d=G[1430]|G[1431]<<8|(G[1432]<<16|G[1433]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;F[a>>2]=-1;ra(a+4|0,b,20);ja(b);break a}j=Z-16|0;Z=j;b:{c:{switch(G[h+32|0]){case 0:e=Kd(ka(48));F[e>>2]=9864;F[h+8>>2]=0;F[h+12>>2]=0;F[h>>2]=0;F[h+4>>2]=0;F[h+16>>2]=e;break b;case 1:e=Kd(ka(52));F[e+48>>2]=0;F[e>>2]=8176;F[h+8>>2]=0;F[h+12>>2]=0;F[h>>2]=0;F[h+4>>2]=0;F[h+16>>2]=e;break b;default:break c}}f=ka(32);D[f+28|0]=0;e=G[1520]|G[1521]<<8|(G[1522]<<16|G[1523]<<24);D[f+24|0]=e;D[f+25|0]=e>>>8;D[f+26|0]=e>>>16;D[f+27|0]=e>>>24;e=G[1516]|G[1517]<<8|(G[1518]<<16|G[1519]<<24);g=G[1512]|G[1513]<<8|(G[1514]<<16|G[1515]<<24);D[f+16|0]=g;D[f+17|0]=g>>>8;D[f+18|0]=g>>>16;D[f+19|0]=g>>>24;D[f+20|0]=e;D[f+21|0]=e>>>8;D[f+22|0]=e>>>16;D[f+23|0]=e>>>24;e=G[1508]|G[1509]<<8|(G[1510]<<16|G[1511]<<24);g=G[1504]|G[1505]<<8|(G[1506]<<16|G[1507]<<24);D[f+8|0]=g;D[f+9|0]=g>>>8;D[f+10|0]=g>>>16;D[f+11|0]=g>>>24;D[f+12|0]=e;D[f+13|0]=e>>>8;D[f+14|0]=e>>>16;D[f+15|0]=e>>>24;e=G[1500]|G[1501]<<8|(G[1502]<<16|G[1503]<<24);g=G[1496]|G[1497]<<8|(G[1498]<<16|G[1499]<<24);D[f|0]=g;D[f+1|0]=g>>>8;D[f+2|0]=g>>>16;D[f+3|0]=g>>>24;D[f+4|0]=e;D[f+5|0]=e>>>8;D[f+6|0]=e>>>16;D[f+7|0]=e>>>24;F[j>>2]=-1;e=j|4;ra(e,f,28);k=D[j+15|0];F[h>>2]=F[j>>2];g=h+4|0;d:{if((k|0)>=0){k=F[e+4>>2];F[g>>2]=F[e>>2];F[g+4>>2]=k;F[g+8>>2]=F[e+8>>2];F[h+16>>2]=0;break d}ra(g,F[j+4>>2],F[j+8>>2]);e=D[j+15|0];F[h+16>>2]=0;if((e|0)>=0){break d}ja(F[j+4>>2])}ja(f)}Z=j+16|0;e=F[h>>2];e:{if(e){F[a>>2]=e;a=a+4|0;if(D[h+15|0]>=0){b=h|4;c=F[b+4>>2];F[a>>2]=F[b>>2];F[a+4>>2]=c;F[a+8>>2]=F[b+8>>2];break e}ra(a,F[h+4>>2],F[h+8>>2]);break e}e=F[h+16>>2];F[h+16>>2]=0;F[e+44>>2]=d;f=Z-32|0;Z=f;F[e+32>>2]=c;F[e+40>>2]=b;F[e+4>>2]=d;jc(a,c,f+16|0);f:{if(F[a>>2]){break f}if(D[a+15|0]<0){ja(F[a+4>>2])}b=G[f+23|0];if(($[F[F[e>>2]+8>>2]](e)|0)!=(b|0)){b=ka(64);D[b+50|0]=0;c=G[1304]|G[1305]<<8;D[b+48|0]=c;D[b+49|0]=c>>>8;c=G[1300]|G[1301]<<8|(G[1302]<<16|G[1303]<<24);d=G[1296]|G[1297]<<8|(G[1298]<<16|G[1299]<<24);D[b+40|0]=d;D[b+41|0]=d>>>8;D[b+42|0]=d>>>16;D[b+43|0]=d>>>24;D[b+44|0]=c;D[b+45|0]=c>>>8;D[b+46|0]=c>>>16;D[b+47|0]=c>>>24;c=G[1292]|G[1293]<<8|(G[1294]<<16|G[1295]<<24);d=G[1288]|G[1289]<<8|(G[1290]<<16|G[1291]<<24);D[b+32|0]=d;D[b+33|0]=d>>>8;D[b+34|0]=d>>>16;D[b+35|0]=d>>>24;D[b+36|0]=c;D[b+37|0]=c>>>8;D[b+38|0]=c>>>16;D[b+39|0]=c>>>24;c=G[1284]|G[1285]<<8|(G[1286]<<16|G[1287]<<24);d=G[1280]|G[1281]<<8|(G[1282]<<16|G[1283]<<24);D[b+24|0]=d;D[b+25|0]=d>>>8;D[b+26|0]=d>>>16;D[b+27|0]=d>>>24;D[b+28|0]=c;D[b+29|0]=c>>>8;D[b+30|0]=c>>>16;D[b+31|0]=c>>>24;c=G[1276]|G[1277]<<8|(G[1278]<<16|G[1279]<<24);d=G[1272]|G[1273]<<8|(G[1274]<<16|G[1275]<<24);D[b+16|0]=d;D[b+17|0]=d>>>8;D[b+18|0]=d>>>16;D[b+19|0]=d>>>24;D[b+20|0]=c;D[b+21|0]=c>>>8;D[b+22|0]=c>>>16;D[b+23|0]=c>>>24;c=G[1268]|G[1269]<<8|(G[1270]<<16|G[1271]<<24);d=G[1264]|G[1265]<<8|(G[1266]<<16|G[1267]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1260]|G[1261]<<8|(G[1262]<<16|G[1263]<<24);d=G[1256]|G[1257]<<8|(G[1258]<<16|G[1259]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;F[a>>2]=-1;ra(a+4|0,b,50);ja(b);break f}c=G[f+21|0];D[e+36|0]=c;d=G[f+22|0];D[e+37|0]=d;if((c|0)!=2){b=ka(32);D[b+26|0]=0;c=G[1427]|G[1428]<<8;D[b+24|0]=c;D[b+25|0]=c>>>8;c=G[1423]|G[1424]<<8|(G[1425]<<16|G[1426]<<24);d=G[1419]|G[1420]<<8|(G[1421]<<16|G[1422]<<24);D[b+16|0]=d;D[b+17|0]=d>>>8;D[b+18|0]=d>>>16;D[b+19|0]=d>>>24;D[b+20|0]=c;D[b+21|0]=c>>>8;D[b+22|0]=c>>>16;D[b+23|0]=c>>>24;c=G[1415]|G[1416]<<8|(G[1417]<<16|G[1418]<<24);d=G[1411]|G[1412]<<8|(G[1413]<<16|G[1414]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1407]|G[1408]<<8|(G[1409]<<16|G[1410]<<24);d=G[1403]|G[1404]<<8|(G[1405]<<16|G[1406]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;F[a>>2]=-5;ra(a+4|0,b,26);ja(b);break f}b=b?2:3;if((b|0)!=(d|0)){b=ka(32);D[b+26|0]=0;c=G[1400]|G[1401]<<8;D[b+24|0]=c;D[b+25|0]=c>>>8;c=G[1396]|G[1397]<<8|(G[1398]<<16|G[1399]<<24);d=G[1392]|G[1393]<<8|(G[1394]<<16|G[1395]<<24);D[b+16|0]=d;D[b+17|0]=d>>>8;D[b+18|0]=d>>>16;D[b+19|0]=d>>>24;D[b+20|0]=c;D[b+21|0]=c>>>8;D[b+22|0]=c>>>16;D[b+23|0]=c>>>24;c=G[1388]|G[1389]<<8|(G[1390]<<16|G[1391]<<24);d=G[1384]|G[1385]<<8|(G[1386]<<16|G[1387]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1380]|G[1381]<<8|(G[1382]<<16|G[1383]<<24);d=G[1376]|G[1377]<<8|(G[1378]<<16|G[1379]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;F[a>>2]=-5;ra(a+4|0,b,26);ja(b);break f}E[F[e+32>>2]+38>>1]=b|512;g:{if(E[f+26>>1]>=0){break g}j=Z-16|0;Z=j;d=ka(36);b=d;F[b+4>>2]=0;F[b+8>>2]=0;F[b+24>>2]=0;F[b+28>>2]=0;b=b+16|0;F[b>>2]=0;F[b+4>>2]=0;F[d>>2]=d+4;F[d+32>>2]=0;F[d+12>>2]=b;F[j>>2]=0;c=F[e+32>>2];k=Z-16|0;Z=k;b=0;h:{if(!d){break h}F[j>>2]=c;F[k+12>>2]=0;b=0;if(!fb(1,k+12|0,c)){break h}n=F[k+12>>2];if(n){while(1){i:{if(fb(1,k+8|0,F[j>>2])){b=ka(28);F[b+4>>2]=0;F[b+8>>2]=0;c=b+16|0;F[c>>2]=0;F[c+4>>2]=0;F[b>>2]=b+4;F[b+12>>2]=c;F[b+24>>2]=F[k+8>>2];if(Vc(j,b)){break i}Ca(b+12|0,F[b+16>>2]);Ba(b,F[b+4>>2]);ja(b)}b=0;break h}g=Z-16|0;Z=g;F[g+8>>2]=b;j:{if(!b){break j}c=F[d+28>>2];k:{if(c>>>0<I[d+32>>2]){F[g+8>>2]=0;F[c>>2]=b;F[d+28>>2]=c+4;break k}c=0;l:{m:{n:{i=F[d+24>>2];m=F[d+28>>2]-i>>2;b=m+1|0;if(b>>>0<1073741824){i=F[d+32>>2]-i|0;l=i>>>1|0;i=i>>>0>=2147483644?1073741823:b>>>0<l>>>0?l:b;if(i){if(i>>>0>=1073741824){break n}c=ka(i<<2)}l=F[g+8>>2];F[g+8>>2]=0;b=(m<<2)+c|0;F[b>>2]=l;i=(i<<2)+c|0;m=b+4|0;c=F[d+28>>2];l=F[d+24>>2];if((c|0)==(l|0)){break m}while(1){c=c-4|0;p=F[c>>2];F[c>>2]=0;b=b-4|0;F[b>>2]=p;if((c|0)!=(l|0)){continue}break}F[d+32>>2]=i;i=F[d+28>>2];F[d+28>>2]=m;c=F[d+24>>2];F[d+24>>2]=b;if((c|0)==(i|0)){break l}while(1){i=i-4|0;b=F[i>>2];F[i>>2]=0;if(b){Ca(b+12|0,F[b+16>>2]);Ba(b,F[b+4>>2]);ja(b)}if((c|0)!=(i|0)){continue}break}break l}na();v()}oa();v()}F[d+32>>2]=i;F[d+28>>2]=m;F[d+24>>2]=b}if(c){ja(c)}}b=F[g+8>>2];F[g+8>>2]=0;if(!b){break j}Ca(b+12|0,F[b+16>>2]);Ba(b,F[b+4>>2]);ja(b)}Z=g+16|0;o=o+1|0;if((n|0)!=(o|0)){continue}break}}b=Vc(j,d)}Z=k+16|0;o:{if(b){c=F[e+4>>2];b=F[c+4>>2];F[c+4>>2]=d;if(b){ic(b)}F[a>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a+12>>2]=0;break o}b=ka(32);D[b+26|0]=0;c=G[1549]|G[1550]<<8;D[b+24|0]=c;D[b+25|0]=c>>>8;c=G[1545]|G[1546]<<8|(G[1547]<<16|G[1548]<<24);g=G[1541]|G[1542]<<8|(G[1543]<<16|G[1544]<<24);D[b+16|0]=g;D[b+17|0]=g>>>8;D[b+18|0]=g>>>16;D[b+19|0]=g>>>24;D[b+20|0]=c;D[b+21|0]=c>>>8;D[b+22|0]=c>>>16;D[b+23|0]=c>>>24;c=G[1537]|G[1538]<<8|(G[1539]<<16|G[1540]<<24);g=G[1533]|G[1534]<<8|(G[1535]<<16|G[1536]<<24);D[b+8|0]=g;D[b+9|0]=g>>>8;D[b+10|0]=g>>>16;D[b+11|0]=g>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1529]|G[1530]<<8|(G[1531]<<16|G[1532]<<24);g=G[1525]|G[1526]<<8|(G[1527]<<16|G[1528]<<24);D[b|0]=g;D[b+1|0]=g>>>8;D[b+2|0]=g>>>16;D[b+3|0]=g>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;F[a>>2]=-1;ra(a+4|0,b,26);ja(b);F[j+8>>2]=0;ic(d)}Z=j+16|0;if(F[a>>2]){break f}if(D[a+15|0]>=0){break g}ja(F[a+4>>2])}if(!($[F[F[e>>2]+12>>2]](e)|0)){b=ka(48);D[b+33|0]=0;D[b+32|0]=G[1374];c=G[1370]|G[1371]<<8|(G[1372]<<16|G[1373]<<24);d=G[1366]|G[1367]<<8|(G[1368]<<16|G[1369]<<24);D[b+24|0]=d;D[b+25|0]=d>>>8;D[b+26|0]=d>>>16;D[b+27|0]=d>>>24;D[b+28|0]=c;D[b+29|0]=c>>>8;D[b+30|0]=c>>>16;D[b+31|0]=c>>>24;c=G[1362]|G[1363]<<8|(G[1364]<<16|G[1365]<<24);d=G[1358]|G[1359]<<8|(G[1360]<<16|G[1361]<<24);D[b+16|0]=d;D[b+17|0]=d>>>8;D[b+18|0]=d>>>16;D[b+19|0]=d>>>24;D[b+20|0]=c;D[b+21|0]=c>>>8;D[b+22|0]=c>>>16;D[b+23|0]=c>>>24;c=G[1354]|G[1355]<<8|(G[1356]<<16|G[1357]<<24);d=G[1350]|G[1351]<<8|(G[1352]<<16|G[1353]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1346]|G[1347]<<8|(G[1348]<<16|G[1349]<<24);d=G[1342]|G[1343]<<8|(G[1344]<<16|G[1345]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;F[a>>2]=-1;ra(a+4|0,b,33);ja(b);break f}if(!($[F[F[e>>2]+20>>2]](e)|0)){b=Eb(f,1552);F[a>>2]=-1;c=a+4|0;if(D[b+11|0]>=0){d=F[b+4>>2];F[c>>2]=F[b>>2];F[c+4>>2]=d;F[c+8>>2]=F[b+8>>2];break f}ra(c,F[b>>2],F[b+4>>2]);if(D[b+11|0]>=0){break f}ja(F[b>>2]);break f}if(!($[F[F[e>>2]+24>>2]](e)|0)){b=Eb(f,1307);F[a>>2]=-1;c=a+4|0;if(D[b+11|0]>=0){d=F[b+4>>2];F[c>>2]=F[b>>2];F[c+4>>2]=d;F[c+8>>2]=F[b+8>>2];break f}ra(c,F[b>>2],F[b+4>>2]);if(D[b+11|0]>=0){break f}ja(F[b>>2]);break f}F[a>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a+12>>2]=0}Z=f+32|0;if(!F[a>>2]){if(D[a+15|0]<0){ja(F[a+4>>2])}F[a>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a+12>>2]=0}$[F[F[e>>2]+4>>2]](e)}a=F[h+16>>2];F[h+16>>2]=0;if(a){$[F[F[a>>2]+4>>2]](a)}if(D[h+15|0]>=0){break a}ja(F[h+4>>2])}Z=h+80|0}function Ub(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;l=Z-16|0;Z=l;a:{b:{c:{d:{e:{f:{g:{h:{i:{if(a>>>0<=244){g=F[2941];h=a>>>0<11?16:a+11&-8;c=h>>>3|0;b=g>>>c|0;if(b&3){c=c+((b^-1)&1)|0;a=c<<3;b=a+11804|0;d=F[a+11812>>2];a=F[d+8>>2];j:{if((b|0)==(a|0)){m=11764,n=oi(c)&g,F[m>>2]=n;break j}F[a+12>>2]=b;F[b+8>>2]=a}a=d+8|0;b=c<<3;F[d+4>>2]=b|3;b=b+d|0;F[b+4>>2]=F[b+4>>2]|1;break a}k=F[2943];if(k>>>0>=h>>>0){break i}if(b){a=2<<c;a=(0-a|a)&b<<c;d=ji(0-a&a);a=d<<3;b=a+11804|0;e=F[a+11812>>2];a=F[e+8>>2];k:{if((b|0)==(a|0)){g=oi(d)&g;F[2941]=g;break k}F[a+12>>2]=b;F[b+8>>2]=a}F[e+4>>2]=h|3;c=e+h|0;a=d<<3;d=a-h|0;F[c+4>>2]=d|1;F[a+e>>2]=d;if(k){b=(k&-8)+11804|0;f=F[2946];a=1<<(k>>>3);l:{if(!(a&g)){F[2941]=a|g;a=b;break l}a=F[b+8>>2]}F[b+8>>2]=f;F[a+12>>2]=f;F[f+12>>2]=b;F[f+8>>2]=a}a=e+8|0;F[2946]=c;F[2943]=d;break a}j=F[2942];if(!j){break i}c=F[(ji(0-j&j)<<2)+12068>>2];f=(F[c+4>>2]&-8)-h|0;b=c;while(1){m:{a=F[b+16>>2];if(!a){a=F[b+20>>2];if(!a){break m}}b=(F[a+4>>2]&-8)-h|0;d=b>>>0<f>>>0;f=d?b:f;c=d?a:c;b=a;continue}break}i=F[c+24>>2];d=F[c+12>>2];if((d|0)!=(c|0)){a=F[c+8>>2];F[a+12>>2]=d;F[d+8>>2]=a;break b}b=c+20|0;a=F[b>>2];if(!a){a=F[c+16>>2];if(!a){break h}b=c+16|0}while(1){e=b;d=a;b=a+20|0;a=F[b>>2];if(a){continue}b=d+16|0;a=F[d+16>>2];if(a){continue}break}F[e>>2]=0;break b}h=-1;if(a>>>0>4294967231){break i}a=a+11|0;h=a&-8;j=F[2942];if(!j){break i}f=0-h|0;g=0;n:{if(h>>>0<256){break n}g=31;if(h>>>0>16777215){break n}a=O(a>>>8|0);g=((h>>>38-a&1)-(a<<1)|0)+62|0}b=F[(g<<2)+12068>>2];o:{p:{q:{if(!b){a=0;break q}a=0;c=h<<((g|0)!=31?25-(g>>>1|0)|0:0);while(1){r:{e=(F[b+4>>2]&-8)-h|0;if(e>>>0>=f>>>0){break r}d=b;f=e;if(e){break r}f=0;a=b;break p}e=F[b+20>>2];b=F[((c>>>29&4)+b|0)+16>>2];a=e?(e|0)==(b|0)?a:e:a;c=c<<1;if(b){continue}break}}if(!(a|d)){d=0;a=2<<g;a=(0-a|a)&j;if(!a){break i}a=F[(ji(a&0-a)<<2)+12068>>2]}if(!a){break o}}while(1){b=(F[a+4>>2]&-8)-h|0;c=b>>>0<f>>>0;f=c?b:f;d=c?a:d;b=F[a+16>>2];if(b){a=b}else{a=F[a+20>>2]}if(a){continue}break}}if(!d|F[2943]-h>>>0<=f>>>0){break i}g=F[d+24>>2];c=F[d+12>>2];if((d|0)!=(c|0)){a=F[d+8>>2];F[a+12>>2]=c;F[c+8>>2]=a;break c}b=d+20|0;a=F[b>>2];if(!a){a=F[d+16>>2];if(!a){break g}b=d+16|0}while(1){e=b;c=a;b=a+20|0;a=F[b>>2];if(a){continue}b=c+16|0;a=F[c+16>>2];if(a){continue}break}F[e>>2]=0;break c}a=F[2943];if(a>>>0>=h>>>0){d=F[2946];b=a-h|0;s:{if(b>>>0>=16){c=d+h|0;F[c+4>>2]=b|1;F[a+d>>2]=b;F[d+4>>2]=h|3;break s}F[d+4>>2]=a|3;a=a+d|0;F[a+4>>2]=F[a+4>>2]|1;c=0;b=0}F[2943]=b;F[2946]=c;a=d+8|0;break a}i=F[2944];if(i>>>0>h>>>0){b=i-h|0;F[2944]=b;c=F[2947];a=c+h|0;F[2947]=a;F[a+4>>2]=b|1;F[c+4>>2]=h|3;a=c+8|0;break a}a=0;j=h+47|0;if(F[3059]){c=F[3061]}else{F[3062]=-1;F[3063]=-1;F[3060]=4096;F[3061]=4096;F[3059]=l+12&-16^1431655768;F[3064]=0;F[3052]=0;c=4096}e=j+c|0;f=0-c|0;b=e&f;if(b>>>0<=h>>>0){break a}d=F[3051];if(d){c=F[3049];g=c+b|0;if(d>>>0<g>>>0|c>>>0>=g>>>0){break a}}t:{if(!(G[12208]&4)){u:{v:{w:{x:{d=F[2947];if(d){a=12212;while(1){c=F[a>>2];if(c>>>0<=d>>>0&d>>>0<c+F[a+4>>2]>>>0){break x}a=F[a+8>>2];if(a){continue}break}}c=eb(0);if((c|0)==-1){break u}g=b;d=F[3060];a=d-1|0;if(a&c){g=(b-c|0)+(a+c&0-d)|0}if(g>>>0<=h>>>0){break u}d=F[3051];if(d){a=F[3049];f=a+g|0;if(d>>>0<f>>>0|a>>>0>=f>>>0){break u}}a=eb(g);if((c|0)!=(a|0)){break w}break t}g=f&e-i;c=eb(g);if((c|0)==(F[a>>2]+F[a+4>>2]|0)){break v}a=c}if((a|0)==-1){break u}if(h+48>>>0<=g>>>0){c=a;break t}c=F[3061];c=c+(j-g|0)&0-c;if((eb(c)|0)==-1){break u}g=c+g|0;c=a;break t}if((c|0)!=-1){break t}}F[3052]=F[3052]|4}c=eb(b);a=eb(0);if((c|0)==-1|(a|0)==-1|a>>>0<=c>>>0){break d}g=a-c|0;if(g>>>0<=h+40>>>0){break d}}a=F[3049]+g|0;F[3049]=a;if(a>>>0>I[3050]){F[3050]=a}y:{e=F[2947];if(e){a=12212;while(1){d=F[a>>2];b=F[a+4>>2];if((d+b|0)==(c|0)){break y}a=F[a+8>>2];if(a){continue}break}break f}a=F[2945];if(!(a>>>0<=c>>>0?a:0)){F[2945]=c}a=0;F[3054]=g;F[3053]=c;F[2949]=-1;F[2950]=F[3059];F[3056]=0;while(1){d=a<<3;b=d+11804|0;F[d+11812>>2]=b;F[d+11816>>2]=b;a=a+1|0;if((a|0)!=32){continue}break}d=g-40|0;a=c+8&7?-8-c&7:0;b=d-a|0;F[2944]=b;a=a+c|0;F[2947]=a;F[a+4>>2]=b|1;F[(c+d|0)+4>>2]=40;F[2948]=F[3063];break e}if(G[a+12|0]&8|d>>>0>e>>>0|c>>>0<=e>>>0){break f}F[a+4>>2]=b+g;a=e+8&7?-8-e&7:0;c=a+e|0;F[2947]=c;b=F[2944]+g|0;a=b-a|0;F[2944]=a;F[c+4>>2]=a|1;F[(b+e|0)+4>>2]=40;F[2948]=F[3063];break e}d=0;break b}c=0;break c}if(I[2945]>c>>>0){F[2945]=c}b=c+g|0;a=12212;z:{A:{B:{C:{D:{E:{while(1){if((b|0)!=F[a>>2]){a=F[a+8>>2];if(a){continue}break E}break}if(!(G[a+12|0]&8)){break D}}a=12212;while(1){b=F[a>>2];if(b>>>0<=e>>>0){f=b+F[a+4>>2]|0;if(f>>>0>e>>>0){break C}}a=F[a+8>>2];continue}}F[a>>2]=c;F[a+4>>2]=F[a+4>>2]+g;j=(c+8&7?-8-c&7:0)+c|0;F[j+4>>2]=h|3;g=b+(b+8&7?-8-b&7:0)|0;i=h+j|0;a=g-i|0;if((e|0)==(g|0)){F[2947]=i;a=F[2944]+a|0;F[2944]=a;F[i+4>>2]=a|1;break A}if(F[2946]==(g|0)){F[2946]=i;a=F[2943]+a|0;F[2943]=a;F[i+4>>2]=a|1;F[a+i>>2]=a;break A}f=F[g+4>>2];if((f&3)==1){e=f&-8;F:{if(f>>>0<=255){d=F[g+8>>2];b=f>>>3|0;c=F[g+12>>2];if((c|0)==(d|0)){m=11764,n=F[2941]&oi(b),F[m>>2]=n;break F}F[d+12>>2]=c;F[c+8>>2]=d;break F}h=F[g+24>>2];c=F[g+12>>2];G:{if((g|0)!=(c|0)){b=F[g+8>>2];F[b+12>>2]=c;F[c+8>>2]=b;break G}H:{f=g+20|0;b=F[f>>2];if(b){break H}f=g+16|0;b=F[f>>2];if(b){break H}c=0;break G}while(1){d=f;c=b;f=c+20|0;b=F[f>>2];if(b){continue}f=c+16|0;b=F[c+16>>2];if(b){continue}break}F[d>>2]=0}if(!h){break F}d=F[g+28>>2];b=(d<<2)+12068|0;I:{if(F[b>>2]==(g|0)){F[b>>2]=c;if(c){break I}m=11768,n=F[2942]&oi(d),F[m>>2]=n;break F}F[h+(F[h+16>>2]==(g|0)?16:20)>>2]=c;if(!c){break F}}F[c+24>>2]=h;b=F[g+16>>2];if(b){F[c+16>>2]=b;F[b+24>>2]=c}b=F[g+20>>2];if(!b){break F}F[c+20>>2]=b;F[b+24>>2]=c}g=e+g|0;f=F[g+4>>2];a=a+e|0}F[g+4>>2]=f&-2;F[i+4>>2]=a|1;F[a+i>>2]=a;if(a>>>0<=255){b=(a&-8)+11804|0;c=F[2941];a=1<<(a>>>3);J:{if(!(c&a)){F[2941]=a|c;a=b;break J}a=F[b+8>>2]}F[b+8>>2]=i;F[a+12>>2]=i;F[i+12>>2]=b;F[i+8>>2]=a;break A}f=31;if(a>>>0<=16777215){b=O(a>>>8|0);f=((a>>>38-b&1)-(b<<1)|0)+62|0}F[i+28>>2]=f;F[i+16>>2]=0;F[i+20>>2]=0;b=(f<<2)+12068|0;d=F[2942];c=1<<f;K:{if(!(d&c)){F[2942]=c|d;F[b>>2]=i;break K}f=a<<((f|0)!=31?25-(f>>>1|0)|0:0);c=F[b>>2];while(1){b=c;if((F[c+4>>2]&-8)==(a|0)){break B}c=f>>>29|0;f=f<<1;d=(c&4)+b|0;c=F[d+16>>2];if(c){continue}break}F[d+16>>2]=i}F[i+24>>2]=b;F[i+12>>2]=i;F[i+8>>2]=i;break A}d=g-40|0;a=c+8&7?-8-c&7:0;b=d-a|0;F[2944]=b;a=a+c|0;F[2947]=a;F[a+4>>2]=b|1;F[(c+d|0)+4>>2]=40;F[2948]=F[3063];a=(f+(f-39&7?39-f&7:0)|0)-47|0;d=a>>>0<e+16>>>0?e:a;F[d+4>>2]=27;a=F[3056];F[d+16>>2]=F[3055];F[d+20>>2]=a;a=F[3054];F[d+8>>2]=F[3053];F[d+12>>2]=a;F[3055]=d+8;F[3054]=g;F[3053]=c;F[3056]=0;a=d+24|0;while(1){F[a+4>>2]=7;b=a+8|0;a=a+4|0;if(b>>>0<f>>>0){continue}break}if((d|0)==(e|0)){break e}F[d+4>>2]=F[d+4>>2]&-2;f=d-e|0;F[e+4>>2]=f|1;F[d>>2]=f;if(f>>>0<=255){b=(f&-8)+11804|0;c=F[2941];a=1<<(f>>>3);L:{if(!(c&a)){F[2941]=a|c;a=b;break L}a=F[b+8>>2]}F[b+8>>2]=e;F[a+12>>2]=e;F[e+12>>2]=b;F[e+8>>2]=a;break e}a=31;if(f>>>0<=16777215){a=O(f>>>8|0);a=((f>>>38-a&1)-(a<<1)|0)+62|0}F[e+28>>2]=a;F[e+16>>2]=0;F[e+20>>2]=0;b=(a<<2)+12068|0;d=F[2942];c=1<<a;M:{if(!(d&c)){F[2942]=c|d;F[b>>2]=e;break M}a=f<<((a|0)!=31?25-(a>>>1|0)|0:0);d=F[b>>2];while(1){b=d;if((f|0)==(F[b+4>>2]&-8)){break z}c=a>>>29|0;a=a<<1;c=(c&4)+b|0;d=F[c+16>>2];if(d){continue}break}F[c+16>>2]=e}F[e+24>>2]=b;F[e+12>>2]=e;F[e+8>>2]=e;break e}a=F[b+8>>2];F[a+12>>2]=i;F[b+8>>2]=i;F[i+24>>2]=0;F[i+12>>2]=b;F[i+8>>2]=a}a=j+8|0;break a}a=F[b+8>>2];F[a+12>>2]=e;F[b+8>>2]=e;F[e+24>>2]=0;F[e+12>>2]=b;F[e+8>>2]=a}a=F[2944];if(a>>>0<=h>>>0){break d}b=a-h|0;F[2944]=b;c=F[2947];a=c+h|0;F[2947]=a;F[a+4>>2]=b|1;F[c+4>>2]=h|3;a=c+8|0;break a}F[2940]=48;a=0;break a}N:{if(!g){break N}b=F[d+28>>2];a=(b<<2)+12068|0;O:{if(F[a>>2]==(d|0)){F[a>>2]=c;if(c){break O}j=oi(b)&j;F[2942]=j;break N}F[g+(F[g+16>>2]==(d|0)?16:20)>>2]=c;if(!c){break N}}F[c+24>>2]=g;a=F[d+16>>2];if(a){F[c+16>>2]=a;F[a+24>>2]=c}a=F[d+20>>2];if(!a){break N}F[c+20>>2]=a;F[a+24>>2]=c}P:{if(f>>>0<=15){a=f+h|0;F[d+4>>2]=a|3;a=a+d|0;F[a+4>>2]=F[a+4>>2]|1;break P}F[d+4>>2]=h|3;e=d+h|0;F[e+4>>2]=f|1;F[e+f>>2]=f;if(f>>>0<=255){b=(f&-8)+11804|0;c=F[2941];a=1<<(f>>>3);Q:{if(!(c&a)){F[2941]=a|c;a=b;break Q}a=F[b+8>>2]}F[b+8>>2]=e;F[a+12>>2]=e;F[e+12>>2]=b;F[e+8>>2]=a;break P}a=31;if(f>>>0<=16777215){a=O(f>>>8|0);a=((f>>>38-a&1)-(a<<1)|0)+62|0}F[e+28>>2]=a;F[e+16>>2]=0;F[e+20>>2]=0;b=(a<<2)+12068|0;R:{c=1<<a;S:{if(!(c&j)){F[2942]=c|j;F[b>>2]=e;break S}a=f<<((a|0)!=31?25-(a>>>1|0)|0:0);h=F[b>>2];while(1){b=h;if((F[b+4>>2]&-8)==(f|0)){break R}c=a>>>29|0;a=a<<1;c=(c&4)+b|0;h=F[c+16>>2];if(h){continue}break}F[c+16>>2]=e}F[e+24>>2]=b;F[e+12>>2]=e;F[e+8>>2]=e;break P}a=F[b+8>>2];F[a+12>>2]=e;F[b+8>>2]=e;F[e+24>>2]=0;F[e+12>>2]=b;F[e+8>>2]=a}a=d+8|0;break a}T:{if(!i){break T}b=F[c+28>>2];a=(b<<2)+12068|0;U:{if(F[a>>2]==(c|0)){F[a>>2]=d;if(d){break U}m=11768,n=oi(b)&j,F[m>>2]=n;break T}F[i+(F[i+16>>2]==(c|0)?16:20)>>2]=d;if(!d){break T}}F[d+24>>2]=i;a=F[c+16>>2];if(a){F[d+16>>2]=a;F[a+24>>2]=d}a=F[c+20>>2];if(!a){break T}F[d+20>>2]=a;F[a+24>>2]=d}V:{if(f>>>0<=15){a=f+h|0;F[c+4>>2]=a|3;a=a+c|0;F[a+4>>2]=F[a+4>>2]|1;break V}F[c+4>>2]=h|3;d=c+h|0;F[d+4>>2]=f|1;F[d+f>>2]=f;if(k){b=(k&-8)+11804|0;e=F[2946];a=1<<(k>>>3);W:{if(!(a&g)){F[2941]=a|g;a=b;break W}a=F[b+8>>2]}F[b+8>>2]=e;F[a+12>>2]=e;F[e+12>>2]=b;F[e+8>>2]=a}F[2946]=d;F[2943]=f}a=c+8|0}Z=l+16|0;return a|0}function Vc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0;m=Z-32|0;Z=m;o=ka(12);F[o+8>>2]=0;F[o+4>>2]=b;F[o>>2]=0;s=o+12|0;b=s;a:{b:{c:{while(1){b=b-12|0;w=F[b+8>>2];j=F[b+4>>2];t=F[b>>2];if(t){if((w|0)>1e3){break a}F[m+24>>2]=0;F[m+16>>2]=0;F[m+20>>2]=0;d=1;c=F[a>>2];e=F[c+8>>2];h=F[c+12>>2];g=F[c+20>>2];f=F[c+16>>2];d:{if((h|0)<=(g|0)&f>>>0>=e>>>0|(g|0)>(h|0)){break d}e=G[f+F[c>>2]|0];h=c;c=g;f=f+1|0;c=f?c:c+1|0;F[h+16>>2]=f;F[h+20>>2]=c;Sb(m+16|0,e);if(e){c=F[a>>2];n=Tb(m+16|0);p=F[c+8>>2];g=F[c+12>>2];h=F[c+20>>2];f=F[c+16>>2];k=f+e|0;h=k>>>0<e>>>0?h+1|0:h;if((g|0)<=(h|0)&k>>>0>p>>>0|(g|0)<(h|0)){break d}la(n,f+F[c>>2]|0,e);d=F[c+20>>2];f=e;e=e+F[c+16>>2]|0;d=f>>>0>e>>>0?d+1|0:d;F[c+16>>2]=e;F[c+20>>2]=d}j=ka(24);c=j;F[c+4>>2]=0;F[c+8>>2]=0;c=c+16|0;F[c>>2]=0;F[c+4>>2]=0;F[j>>2]=j+4;F[j+12>>2]=c;e=Z-32|0;Z=e;h=t+12|0;c=m+16|0;u=Ya(h,c);i=t+16|0;e:{if((u|0)==(i|0)){F[e+16>>2]=c;f:{g:{d=F[h+4>>2];h:{if(!d){f=h+4|0;c=f;break h}f=G[c+11|0];g=f<<24>>24<0;n=g?F[c>>2]:c;g=g?F[c+4>>2]:f;while(1){c=d;d=G[c+27|0];f=d<<24>>24<0;d=f?F[c+20>>2]:d;p=d>>>0<g>>>0;i:{j:{k:{l:{k=p?d:g;m:{if(k){f=f?F[c+16>>2]:c+16|0;q=sa(n,f,k);if(!q){if(d>>>0>g>>>0){break m}break l}if((q|0)>=0){break l}break m}if(d>>>0<=g>>>0){break k}}f=c;d=F[c>>2];if(d){continue}break h}d=sa(f,n,k);if(d){break j}}if(p){break i}break g}if((d|0)>=0){break g}}d=F[c+4>>2];if(d){continue}break}f=c+4|0}d=ka(32);n=d+16|0;g=F[e+16>>2];n:{if(D[g+11|0]>=0){p=F[g+4>>2];F[n>>2]=F[g>>2];F[n+4>>2]=p;F[n+8>>2]=F[g+8>>2];break n}ra(n,F[g>>2],F[g+4>>2])}F[d+8>>2]=c;F[d>>2]=0;F[d+4>>2]=0;F[d+28>>2]=0;F[f>>2]=d;c=d;g=F[F[h>>2]>>2];if(g){F[h>>2]=g;c=F[f>>2]}nb(F[h+4>>2],c);F[h+8>>2]=F[h+8>>2]+1;c=1;break f}d=c;c=0}D[e+28|0]=c;F[e+24>>2]=d;d=F[e+24>>2];c=F[d+28>>2];F[d+28>>2]=j;if(!c){break e}Ca(c+12|0,F[c+16>>2]);Ba(c,F[c+4>>2]);ja(c);break e}if(!j){break e}Ca(j+12|0,F[j+16>>2]);Ba(j,F[j+4>>2]);ja(j)}Z=e+32|0;d=(i|0)!=(u|0)}if(D[m+27|0]<0){ja(F[m+16>>2])}if(d){break a}}if(!j){break a}F[m+16>>2]=0;if(!fb(1,m+16|0,F[a>>2])){break a}q=0;x=F[m+16>>2];if(x){while(1){d=0;i=Z-32|0;Z=i;F[i+24>>2]=0;F[i+16>>2]=0;F[i+20>>2]=0;c=F[a>>2];f=F[c+8>>2];o:{p:{h=F[c+12>>2];g=F[c+20>>2];e=F[c+16>>2];q:{if((h|0)<=(g|0)&e>>>0>=f>>>0|(g|0)>(h|0)){break q}f=G[e+F[c>>2]|0];h=c;c=g;e=e+1|0;c=e?c:c+1|0;F[h+16>>2]=e;F[h+20>>2]=c;Sb(i+16|0,f);if(f){e=F[a>>2];n=Tb(i+16|0);p=F[e+8>>2];g=F[e+12>>2];c=F[e+20>>2];h=F[e+16>>2];k=h+f|0;c=k>>>0<f>>>0?c+1|0:c;if(k>>>0>p>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){break q}la(n,h+F[e>>2]|0,f);c=F[e+20>>2];g=f;f=f+F[e+16>>2]|0;c=g>>>0>f>>>0?c+1|0:c;F[e+16>>2]=f;F[e+20>>2]=c}F[i+12>>2]=0;if(!fb(1,i+12|0,F[a>>2])){break q}f=F[i+12>>2];if(!f){break q}e=F[a>>2];c=F[e+8>>2];h=F[e+16>>2];g=c-h|0;c=F[e+12>>2]-(F[e+20>>2]+(c>>>0<h>>>0)|0)|0;if((c|0)<=0&f>>>0>g>>>0|(c|0)<0){break q}F[i+8>>2]=0;F[i>>2]=0;F[i+4>>2]=0;if((f|0)<0){break p}d=ka(f);F[i>>2]=d;c=d+f|0;F[i+8>>2]=c;l=ma(d,0,f);F[i+4>>2]=c;h=F[e+12>>2];y=h;p=F[e+8>>2];c=F[e+20>>2];k=F[e+16>>2];g=f+k|0;c=g>>>0<f>>>0?c+1|0:c;u=g;n=c;r:{if((c|0)<=(h|0)&g>>>0<=p>>>0|(c|0)<(h|0)){la(l,F[e>>2]+k|0,f);d=F[e+20>>2];c=f+F[e+16>>2]|0;d=c>>>0<f>>>0?d+1|0:d;F[e+16>>2]=c;F[e+20>>2]=d;h=Z-48|0;Z=h;e=Ya(j,i+16|0);if((e|0)!=(j+4|0)){c=F[e+4>>2];s:{if(!c){c=e;while(1){d=F[c+8>>2];f=F[d>>2]!=(c|0);c=d;if(f){continue}break}break s}while(1){d=c;c=F[c>>2];if(c){continue}break}}if((e|0)==F[j>>2]){F[j>>2]=d}F[j+8>>2]=F[j+8>>2]-1;f=F[j+4>>2];t:{u:{g=e;d=e;e=F[d>>2];if(e){c=F[g+4>>2];if(!c){break u}while(1){d=c;c=F[c>>2];if(c){continue}break}}e=F[d+4>>2];if(e){break u}e=0;k=1;break t}F[e+8>>2]=F[d+8>>2];k=0}l=F[d+8>>2];c=F[l>>2];v:{if((d|0)==(c|0)){F[l>>2]=e;if((d|0)==(f|0)){c=0;f=e;break v}c=F[l+4>>2];break v}F[l+4>>2]=e}r=!G[d+12|0];if((d|0)!=(g|0)){l=F[g+8>>2];F[d+8>>2]=l;F[l+(((g|0)!=F[F[g+8>>2]>>2])<<2)>>2]=d;l=F[g>>2];F[d>>2]=l;F[l+8>>2]=d;l=F[g+4>>2];F[d+4>>2]=l;if(l){F[l+8>>2]=d}D[d+12|0]=G[g+12|0];f=(f|0)==(g|0)?d:f}w:{if(r|!f){break w}if(k){while(1){e=G[c+12|0];x:{d=F[c+8>>2];if(F[d>>2]!=(c|0)){if(!e){D[c+12|0]=1;D[d+12|0]=0;e=F[d+4>>2];k=F[e>>2];F[d+4>>2]=k;if(k){F[k+8>>2]=d}F[e+8>>2]=F[d+8>>2];k=F[d+8>>2];F[(((d|0)!=F[k>>2])<<2)+k>>2]=e;F[e>>2]=d;F[d+8>>2]=e;d=c;c=F[c>>2];f=(c|0)==(f|0)?d:f;c=F[c+4>>2]}y:{z:{d=F[c>>2];A:{if(!(G[d+12|0]?0:d)){e=F[c+4>>2];if(G[e+12|0]?0:e){break A}D[c+12|0]=0;c=F[c+8>>2];B:{if((f|0)==(c|0)){c=f;break B}if(G[c+12|0]){break x}}D[c+12|0]=1;break w}e=F[c+4>>2];if(!e){break z}}if(G[e+12|0]){break z}d=c;break y}D[d+12|0]=1;D[c+12|0]=0;e=F[d+4>>2];F[c>>2]=e;if(e){F[e+8>>2]=c}F[d+8>>2]=F[c+8>>2];e=F[c+8>>2];F[((F[e>>2]!=(c|0))<<2)+e>>2]=d;F[d+4>>2]=c;F[c+8>>2]=d;e=c}c=F[d+8>>2];D[d+12|0]=G[c+12|0];D[c+12|0]=1;D[e+12|0]=1;d=F[c+4>>2];e=F[d>>2];F[c+4>>2]=e;if(e){F[e+8>>2]=c}F[d+8>>2]=F[c+8>>2];e=F[c+8>>2];F[(((c|0)!=F[e>>2])<<2)+e>>2]=d;F[d>>2]=c;F[c+8>>2]=d;break w}if(!e){D[c+12|0]=1;D[d+12|0]=0;e=F[c+4>>2];F[d>>2]=e;if(e){F[e+8>>2]=d}F[c+8>>2]=F[d+8>>2];e=F[d+8>>2];F[(((d|0)!=F[e>>2])<<2)+e>>2]=c;F[c+4>>2]=d;F[d+8>>2]=c;f=(d|0)==(f|0)?c:f;c=F[d>>2]}e=F[c>>2];C:{if(!(!e|G[e+12|0])){d=c;break C}d=F[c+4>>2];if(!(G[d+12|0]?0:d)){D[c+12|0]=0;c=F[c+8>>2];if((c|0)!=(f|0)?G[c+12|0]:0){break x}D[c+12|0]=1;break w}if(e){if(!G[e+12|0]){d=c;break C}d=F[c+4>>2]}D[d+12|0]=1;D[c+12|0]=0;e=F[d>>2];F[c+4>>2]=e;if(e){F[e+8>>2]=c}F[d+8>>2]=F[c+8>>2];e=F[c+8>>2];F[((F[e>>2]!=(c|0))<<2)+e>>2]=d;F[d>>2]=c;F[c+8>>2]=d;e=c}c=F[d+8>>2];D[d+12|0]=G[c+12|0];D[c+12|0]=1;D[e+12|0]=1;d=F[c>>2];e=F[d+4>>2];F[c>>2]=e;if(e){F[e+8>>2]=c}F[d+8>>2]=F[c+8>>2];e=F[c+8>>2];F[(((c|0)!=F[e>>2])<<2)+e>>2]=d;F[d+4>>2]=c;F[c+8>>2]=d;break w}d=c;c=F[c+8>>2];c=F[(((d|0)==F[c>>2])<<2)+c>>2];continue}}D[e+12|0]=1}c=F[g+28>>2];if(c){F[g+32>>2]=c;ja(c)}if(D[g+27|0]<0){ja(F[g+16>>2])}ja(g)}F[h+8>>2]=0;F[h>>2]=0;F[h+4>>2]=0;c=F[i+4>>2];d=F[i>>2];f=c-d|0;e=0;D:{E:{if((c|0)!=(d|0)){if((f|0)<0){break E}e=ka(f);c=ma(e,0,f);g=c+f|0;F[h+8>>2]=g;F[h+4>>2]=g;F[h>>2]=c;c=d}la(e,c,f);F:{if(D[i+27|0]>=0){F[h+24>>2]=F[i+24>>2];c=F[i+20>>2];F[h+16>>2]=F[i+16>>2];F[h+20>>2]=c;break F}ra(h+16|0,F[i+16>>2],F[i+20>>2])}Tc(h+28|0,h);f=h+16|0;c=f;G:{H:{d=F[j+4>>2];I:{if(!d){e=j+4|0;c=e;break I}e=G[c+11|0];g=e<<24>>24<0;k=g?F[c>>2]:c;g=g?F[c+4>>2]:e;while(1){c=d;d=G[c+27|0];e=d<<24>>24<0;d=e?F[c+20>>2]:d;l=d>>>0<g>>>0;J:{K:{L:{M:{r=l?d:g;N:{if(r){e=e?F[c+16>>2]:c+16|0;z=sa(k,e,r);if(!z){if(d>>>0>g>>>0){break N}break M}if((z|0)>=0){break M}break N}if(d>>>0<=g>>>0){break L}}e=c;d=F[c>>2];if(d){continue}break I}d=sa(e,k,r);if(d){break K}}if(l){break J}break H}if((d|0)>=0){break H}}d=F[c+4>>2];if(d){continue}break}e=c+4|0}d=ka(40);F[d+24>>2]=F[f+8>>2];g=F[f+4>>2];F[d+16>>2]=F[f>>2];F[d+20>>2]=g;F[f>>2]=0;F[f+4>>2]=0;F[f+8>>2]=0;Tc(d+28|0,f+12|0);F[d+8>>2]=c;F[d>>2]=0;F[d+4>>2]=0;F[e>>2]=d;c=d;f=F[F[j>>2]>>2];if(f){F[j>>2]=f;c=F[e>>2]}nb(F[j+4>>2],c);F[j+8>>2]=F[j+8>>2]+1;c=1;break G}d=c;c=0}D[h+44|0]=c;F[h+40>>2]=d;c=F[h+28>>2];if(c){F[h+32>>2]=c;ja(c)}if(D[h+27|0]<0){ja(F[h+16>>2])}c=F[h>>2];if(c){F[h+4>>2]=c;ja(c)}Z=h+48|0;break D}na();v()}d=F[i>>2];if(!d){break r}}F[i+4>>2]=d;ja(d)}d=(n|0)<=(y|0)&p>>>0>=u>>>0|(n|0)<(y|0)}if(D[i+27|0]<0){ja(F[i+16>>2])}Z=i+32|0;break o}na();v()}if(!d){break a}q=q+1|0;if((x|0)!=(q|0)){continue}break}}F[m+12>>2]=0;if(!fb(1,m+12|0,F[a>>2])){break a}c=F[a>>2];e=F[c+8>>2];f=F[c+16>>2];h=e-f|0;d=F[m+12>>2];c=F[c+12>>2]-(F[c+20>>2]+(e>>>0<f>>>0)|0)|0;if(h>>>0<d>>>0&(c|0)<=0|(c|0)<0){break a}if(d){q=0;h=((t|0)!=0)+w|0;while(1){O:{if(b>>>0<s>>>0){F[b+8>>2]=h;F[b+4>>2]=0;F[b>>2]=j;b=b+12|0;d=F[m+12>>2];break O}c=b-o|0;g=(c|0)/12|0;b=g+1|0;if(b>>>0>=357913942){break c}e=(s-o|0)/12|0;f=e<<1;e=e>>>0>=178956970?357913941:b>>>0<f>>>0?f:b;if(e){if(e>>>0>=357913942){break b}f=ka(L(e,12))}else{f=0}b=f+L(g,12)|0;F[b+8>>2]=h;F[b+4>>2]=0;F[b>>2]=j;c=pa(b+L((c|0)/-12|0,12)|0,o,c);s=f+L(e,12)|0;b=b+12|0;if(o){ja(o)}o=c}q=q+1|0;if(q>>>0<d>>>0){continue}break}}if((b|0)!=(o|0)){continue}break}A=1;break a}na();v()}oa();v()}if(o){ja(o)}Z=m+32|0;return A}function me(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;h=Z-48|0;Z=h;a:{if((c|0)!=1){break a}i=F[a+4>>2];c=F[a+12>>2];F[h+40>>2]=0;F[h+32>>2]=0;F[h+36>>2]=0;F[h+24>>2]=0;F[h+28>>2]=0;F[h+16>>2]=0;F[h+20>>2]=0;F[h+8>>2]=0;F[h+12>>2]=0;d=h+8|0;b:{if((b|0)==-2){break b}k=F[F[F[i+4>>2]+8>>2]+(c<<2)>>2];if(($[F[F[i>>2]+8>>2]](i)|0)==1){j=Z-32|0;Z=j;l=F[F[F[i+4>>2]+8>>2]+(c<<2)>>2];c:{d:{e:{if(($[F[F[i>>2]+8>>2]](i)|0)!=1|b-1>>>0>5){break e}g=$[F[F[i>>2]+36>>2]](i)|0;f=$[F[F[i>>2]+44>>2]](i,c)|0;if(!g|!f){break e}c=$[F[F[i>>2]+40>>2]](i,c)|0;if(c){a=F[i+44>>2];F[j+12>>2]=c;F[j+8>>2]=a;F[j+20>>2]=f;F[j+16>>2]=f+12;f=j+8|0;a=0;f:{g:{switch(b-1|0){case 0:b=ka(60);F[b+4>>2]=l;F[b>>2]=2960;a=F[d+4>>2];F[b+8>>2]=F[d>>2];F[b+12>>2]=a;a=F[d+12>>2];F[b+16>>2]=F[d+8>>2];F[b+20>>2]=a;a=F[d+20>>2];F[b+24>>2]=F[d+16>>2];F[b+28>>2]=a;F[b+40>>2]=0;F[b+32>>2]=0;F[b+36>>2]=0;a=F[d+24>>2];g=F[d+28>>2];if((a|0)!=(g|0)){c=g-a|0;if((c|0)<0){break d}e=ka(c);F[b+32>>2]=e;F[b+40>>2]=(c&-4)+e;while(1){F[e>>2]=F[a>>2];e=e+4|0;a=a+4|0;if((g|0)!=(a|0)){continue}break}F[b+36>>2]=e}a=F[f+4>>2];F[b+44>>2]=F[f>>2];F[b+48>>2]=a;a=F[f+12>>2];F[b+52>>2]=F[f+8>>2];F[b+56>>2]=a;F[b>>2]=2252;a=b;break f;case 3:b=ka(112);F[b+4>>2]=l;F[b>>2]=2960;a=F[d+4>>2];F[b+8>>2]=F[d>>2];F[b+12>>2]=a;a=F[d+12>>2];F[b+16>>2]=F[d+8>>2];F[b+20>>2]=a;a=F[d+20>>2];F[b+24>>2]=F[d+16>>2];F[b+28>>2]=a;F[b+40>>2]=0;F[b+32>>2]=0;F[b+36>>2]=0;a=F[d+24>>2];g=F[d+28>>2];if((a|0)!=(g|0)){c=g-a|0;if((c|0)<0){break d}e=ka(c);F[b+32>>2]=e;F[b+40>>2]=(c&-4)+e;while(1){F[e>>2]=F[a>>2];e=e+4|0;a=a+4|0;if((g|0)!=(a|0)){continue}break}F[b+36>>2]=e}a=F[f+4>>2];F[b+44>>2]=F[f>>2];F[b+48>>2]=a;a=F[f+12>>2];F[b+52>>2]=F[f+8>>2];F[b+56>>2]=a;F[b+60>>2]=0;F[b+64>>2]=0;F[b>>2]=3016;F[b+68>>2]=0;F[b+72>>2]=0;F[b+76>>2]=0;F[b+80>>2]=0;F[b+84>>2]=0;F[b+88>>2]=0;F[b+92>>2]=0;F[b+96>>2]=0;F[b+100>>2]=0;F[b+104>>2]=0;F[b+108>>2]=0;a=b;break f;case 4:b=ka(104);F[b+4>>2]=l;F[b>>2]=2960;a=F[d+4>>2];F[b+8>>2]=F[d>>2];F[b+12>>2]=a;a=F[d+12>>2];F[b+16>>2]=F[d+8>>2];F[b+20>>2]=a;a=F[d+20>>2];F[b+24>>2]=F[d+16>>2];F[b+28>>2]=a;F[b+40>>2]=0;F[b+32>>2]=0;F[b+36>>2]=0;a=F[d+24>>2];g=F[d+28>>2];if((a|0)!=(g|0)){c=g-a|0;if((c|0)<0){break d}e=ka(c);F[b+32>>2]=e;F[b+40>>2]=(c&-4)+e;while(1){F[e>>2]=F[a>>2];e=e+4|0;a=a+4|0;if((g|0)!=(a|0)){continue}break}F[b+36>>2]=e}a=F[f+4>>2];F[b+44>>2]=F[f>>2];F[b+48>>2]=a;a=F[f+12>>2];F[b+52>>2]=F[f+8>>2];F[b+56>>2]=a;F[b+84>>2]=0;F[b+76>>2]=0;F[b+80>>2]=0;F[b+60>>2]=0;F[b+64>>2]=0;F[b>>2]=3264;a=F[f+4>>2];F[b+88>>2]=F[f>>2];F[b+92>>2]=a;a=F[f+12>>2];F[b+96>>2]=F[f+8>>2];F[b+100>>2]=a;a=b;break f;case 5:break g;default:break f}}a=ka(128);F[a+4>>2]=l;F[a>>2]=2960;b=F[d+4>>2];F[a+8>>2]=F[d>>2];F[a+12>>2]=b;b=F[d+12>>2];F[a+16>>2]=F[d+8>>2];F[a+20>>2]=b;b=F[d+20>>2];F[a+24>>2]=F[d+16>>2];F[a+28>>2]=b;F[a+40>>2]=0;F[a+32>>2]=0;F[a+36>>2]=0;h:{i:{c=F[d+28>>2];b=F[d+24>>2];if((c|0)!=(b|0)){c=c-b|0;if((c|0)<0){break i}b=ka(c);F[a+36>>2]=b;F[a+32>>2]=b;F[a+40>>2]=(c&-4)+b;e=F[d+24>>2];c=F[d+28>>2];if((e|0)!=(c|0)){while(1){F[b>>2]=F[e>>2];b=b+4|0;e=e+4|0;if((c|0)!=(e|0)){continue}break}}F[a+36>>2]=b}F[a>>2]=2904;b=F[f+4>>2];F[a+44>>2]=F[f>>2];F[a+48>>2]=b;b=F[f+12>>2];F[a+52>>2]=F[f+8>>2];F[a+56>>2]=b;b=a- -64|0;F[b>>2]=0;F[b+4>>2]=0;F[a+60>>2]=4128;F[a>>2]=3500;b=F[f+4>>2];F[a+72>>2]=F[f>>2];F[a+76>>2]=b;b=F[f+12>>2];F[a+80>>2]=F[f+8>>2];F[a+84>>2]=b;F[a+104>>2]=1065353216;F[a+108>>2]=-1;F[a+96>>2]=-1;F[a+100>>2]=-1;F[a+88>>2]=1;F[a+92>>2]=-1;F[a+60>>2]=3736;F[a+112>>2]=0;F[a+116>>2]=0;D[a+117|0]=0;D[a+118|0]=0;D[a+119|0]=0;D[a+120|0]=0;D[a+121|0]=0;D[a+122|0]=0;D[a+123|0]=0;D[a+124|0]=0;break h}na();v()}break f}e=a;break e}a=F[i+44>>2];F[j+12>>2]=g;F[j+8>>2]=a;F[j+20>>2]=f;F[j+16>>2]=f+12;f=j+8|0;a=0;j:{k:{switch(b-1|0){case 0:b=ka(60);F[b+4>>2]=l;F[b>>2]=2960;a=F[d+4>>2];F[b+8>>2]=F[d>>2];F[b+12>>2]=a;a=F[d+12>>2];F[b+16>>2]=F[d+8>>2];F[b+20>>2]=a;a=F[d+20>>2];F[b+24>>2]=F[d+16>>2];F[b+28>>2]=a;F[b+40>>2]=0;F[b+32>>2]=0;F[b+36>>2]=0;a=F[d+24>>2];g=F[d+28>>2];if((a|0)!=(g|0)){c=g-a|0;if((c|0)<0){break d}e=ka(c);F[b+32>>2]=e;F[b+40>>2]=(c&-4)+e;while(1){F[e>>2]=F[a>>2];e=e+4|0;a=a+4|0;if((g|0)!=(a|0)){continue}break}F[b+36>>2]=e}a=F[f+4>>2];F[b+44>>2]=F[f>>2];F[b+48>>2]=a;a=F[f+12>>2];F[b+52>>2]=F[f+8>>2];F[b+56>>2]=a;F[b>>2]=4156;a=b;break j;case 3:b=ka(112);F[b+4>>2]=l;F[b>>2]=2960;a=F[d+4>>2];F[b+8>>2]=F[d>>2];F[b+12>>2]=a;a=F[d+12>>2];F[b+16>>2]=F[d+8>>2];F[b+20>>2]=a;a=F[d+20>>2];F[b+24>>2]=F[d+16>>2];F[b+28>>2]=a;F[b+40>>2]=0;F[b+32>>2]=0;F[b+36>>2]=0;a=F[d+24>>2];g=F[d+28>>2];if((a|0)!=(g|0)){c=g-a|0;if((c|0)<0){break d}e=ka(c);F[b+32>>2]=e;F[b+40>>2]=(c&-4)+e;while(1){F[e>>2]=F[a>>2];e=e+4|0;a=a+4|0;if((g|0)!=(a|0)){continue}break}F[b+36>>2]=e}a=F[f+4>>2];F[b+44>>2]=F[f>>2];F[b+48>>2]=a;a=F[f+12>>2];F[b+52>>2]=F[f+8>>2];F[b+56>>2]=a;F[b+60>>2]=0;F[b+64>>2]=0;F[b>>2]=4580;F[b+68>>2]=0;F[b+72>>2]=0;F[b+76>>2]=0;F[b+80>>2]=0;F[b+84>>2]=0;F[b+88>>2]=0;F[b+92>>2]=0;F[b+96>>2]=0;F[b+100>>2]=0;F[b+104>>2]=0;F[b+108>>2]=0;a=b;break j;case 4:b=ka(104);F[b+4>>2]=l;F[b>>2]=2960;a=F[d+4>>2];F[b+8>>2]=F[d>>2];F[b+12>>2]=a;a=F[d+12>>2];F[b+16>>2]=F[d+8>>2];F[b+20>>2]=a;a=F[d+20>>2];F[b+24>>2]=F[d+16>>2];F[b+28>>2]=a;F[b+40>>2]=0;F[b+32>>2]=0;F[b+36>>2]=0;a=F[d+24>>2];g=F[d+28>>2];if((a|0)!=(g|0)){c=g-a|0;if((c|0)<0){break d}e=ka(c);F[b+32>>2]=e;F[b+40>>2]=(c&-4)+e;while(1){F[e>>2]=F[a>>2];e=e+4|0;a=a+4|0;if((g|0)!=(a|0)){continue}break}F[b+36>>2]=e}a=F[f+4>>2];F[b+44>>2]=F[f>>2];F[b+48>>2]=a;a=F[f+12>>2];F[b+52>>2]=F[f+8>>2];F[b+56>>2]=a;F[b+84>>2]=0;F[b+76>>2]=0;F[b+80>>2]=0;F[b+60>>2]=0;F[b+64>>2]=0;F[b>>2]=4816;a=F[f+4>>2];F[b+88>>2]=F[f>>2];F[b+92>>2]=a;a=F[f+12>>2];F[b+96>>2]=F[f+8>>2];F[b+100>>2]=a;a=b;break j;case 5:break k;default:break j}}a=ka(128);F[a+4>>2]=l;F[a>>2]=2960;b=F[d+4>>2];F[a+8>>2]=F[d>>2];F[a+12>>2]=b;b=F[d+12>>2];F[a+16>>2]=F[d+8>>2];F[a+20>>2]=b;b=F[d+20>>2];F[a+24>>2]=F[d+16>>2];F[a+28>>2]=b;F[a+40>>2]=0;F[a+32>>2]=0;F[a+36>>2]=0;l:{m:{c=F[d+28>>2];b=F[d+24>>2];if((c|0)!=(b|0)){c=c-b|0;if((c|0)<0){break m}b=ka(c);F[a+36>>2]=b;F[a+32>>2]=b;F[a+40>>2]=(c&-4)+b;e=F[d+24>>2];c=F[d+28>>2];if((e|0)!=(c|0)){while(1){F[b>>2]=F[e>>2];b=b+4|0;e=e+4|0;if((c|0)!=(e|0)){continue}break}}F[a+36>>2]=b}F[a>>2]=4524;b=F[f+4>>2];F[a+44>>2]=F[f>>2];F[a+48>>2]=b;b=F[f+12>>2];F[a+52>>2]=F[f+8>>2];F[a+56>>2]=b;b=a- -64|0;F[b>>2]=0;F[b+4>>2]=0;F[a+60>>2]=5624;F[a>>2]=5040;b=F[f+4>>2];F[a+72>>2]=F[f>>2];F[a+76>>2]=b;b=F[f+12>>2];F[a+80>>2]=F[f+8>>2];F[a+84>>2]=b;F[a+104>>2]=1065353216;F[a+108>>2]=-1;F[a+96>>2]=-1;F[a+100>>2]=-1;F[a+88>>2]=1;F[a+92>>2]=-1;F[a+60>>2]=5260;F[a+112>>2]=0;F[a+116>>2]=0;D[a+117|0]=0;D[a+118|0]=0;D[a+119|0]=0;D[a+120|0]=0;D[a+121|0]=0;D[a+122|0]=0;D[a+123|0]=0;D[a+124|0]=0;break l}na();v()}break j}e=a}Z=j+32|0;break c}na();v()}if(e){break b}}e=ka(44);F[e+4>>2]=k;F[e>>2]=2960;a=F[d+4>>2];F[e+8>>2]=F[d>>2];F[e+12>>2]=a;a=F[d+12>>2];F[e+16>>2]=F[d+8>>2];F[e+20>>2]=a;a=F[d+20>>2];F[e+24>>2]=F[d+16>>2];F[e+28>>2]=a;F[e+40>>2]=0;F[e+32>>2]=0;F[e+36>>2]=0;n:{c=F[d+24>>2];b=F[d+28>>2];if((c|0)!=(b|0)){a=b-c|0;if((a|0)<0){break n}k=ka(a);F[e+32>>2]=k;F[e+40>>2]=(a&-4)+k;while(1){F[k>>2]=F[c>>2];k=k+4|0;c=c+4|0;if((b|0)!=(c|0)){continue}break}F[e+36>>2]=k}F[e>>2]=5652;break b}na();v()}k=e;a=F[h+32>>2];if(!a){break a}F[h+36>>2]=a;ja(a)}Z=h+48|0;return k|0}function rf(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0;m=Z-16|0;Z=m;F[m+12>>2]=b;b=ka(32);F[m>>2]=b;F[m+4>>2]=24;F[m+8>>2]=-2147483616;c=G[1196]|G[1197]<<8|(G[1198]<<16|G[1199]<<24);d=G[1192]|G[1193]<<8|(G[1194]<<16|G[1195]<<24);D[b+16|0]=d;D[b+17|0]=d>>>8;D[b+18|0]=d>>>16;D[b+19|0]=d>>>24;D[b+20|0]=c;D[b+21|0]=c>>>8;D[b+22|0]=c>>>16;D[b+23|0]=c>>>24;c=G[1188]|G[1189]<<8|(G[1190]<<16|G[1191]<<24);d=G[1184]|G[1185]<<8|(G[1186]<<16|G[1187]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1180]|G[1181]<<8|(G[1182]<<16|G[1183]<<24);d=G[1176]|G[1177]<<8|(G[1178]<<16|G[1179]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;D[b+24|0]=0;l=Z-48|0;Z=l;f=F[m+12>>2];d=a;a=a+16|0;b=F[a>>2];a:{b:{if(!b){break b}c=a;while(1){e=(f|0)>F[b+16>>2];c=e?c:b;b=F[(e?b+4|0:b)>>2];if(b){continue}break}if((a|0)==(c|0)){break b}if((f|0)>=F[c+16>>2]){break a}}F[l+28>>2]=0;F[l+32>>2]=0;y=l+24|0;F[l+24>>2]=y|4;a=l+16|0;F[a>>2]=0;F[a+4>>2]=0;F[l+8>>2]=f;F[l+12>>2]=a;t=l+8|0;a=t;x=Z-16|0;Z=x;u=d+12|0;c=F[u+4>>2];c:{d:{if(!c){o=u+4|0;d=o;break d}a=F[a>>2];while(1){d=c;b=F[c+16>>2];if((b|0)>(a|0)){o=d;c=F[d>>2];if(c){continue}break d}if((a|0)<=(b|0)){g=d;a=0;break c}c=F[d+4>>2];if(c){continue}break}o=d+4|0}g=ka(32);b=F[t>>2];q=g+24|0;a=q;F[a>>2]=0;F[a+4>>2]=0;F[g+16>>2]=b;r=g+20|0;F[r>>2]=a;c=F[t+4>>2];z=t+8|0;if((c|0)!=(z|0)){while(1){p=Z-16|0;Z=p;a=p+8|0;k=c+16|0;e:{f:{g:{h:{i:{j:{k:{f=q;e=r+4|0;l:{if((f|0)==(e|0)){break l}b=G[f+27|0];h=b<<24>>24<0;i=G[k+11|0];n=i<<24>>24;j=(n|0)<0;i=j?F[k+4>>2]:i;b=h?F[f+20>>2]:b;s=i>>>0>b>>>0;w=s?b:i;if(w){j=j?F[k>>2]:k;h=h?F[f+16>>2]:f+16|0;A=sa(j,h,w);if(!A){if(b>>>0>i>>>0){break l}break k}if((A|0)>=0){break k}break l}if(b>>>0<=i>>>0){break j}}h=F[f>>2];m:{a=f;n:{if((a|0)==F[r>>2]){break n}o:{if(!h){b=f;while(1){a=F[b+8>>2];i=F[a>>2]==(b|0);b=a;if(i){continue}break}break o}b=h;while(1){a=b;b=F[b+4>>2];if(b){continue}break}}i=G[k+11|0];s=i<<24>>24;b=(s|0)<0;j=G[a+27|0];n=j<<24>>24<0;p:{i=b?F[k+4>>2]:i;j=n?F[a+20>>2]:j;w=i>>>0<j>>>0?i:j;if(w){b=sa(n?F[a+16>>2]:a+16|0,b?F[k>>2]:k,w);if(b){break p}}if(i>>>0>j>>>0){break n}break m}if((b|0)>=0){break m}}if(!h){F[p+12>>2]=f;a=f;break e}F[p+12>>2]=a;a=a+4|0;break e}b=F[e>>2];if(!b){F[p+12>>2]=e;a=e;break e}h=(s|0)<0?F[k>>2]:k;f=e;while(1){a=b;b=G[b+27|0];e=b<<24>>24<0;b=e?F[a+20>>2]:b;k=b>>>0<i>>>0;q:{r:{s:{t:{n=k?b:i;u:{if(n){e=e?F[a+16>>2]:a+16|0;j=sa(h,e,n);if(!j){if(b>>>0>i>>>0){break u}break t}if((j|0)>=0){break t}break u}if(b>>>0<=i>>>0){break s}}f=a;b=F[a>>2];if(b){continue}break g}b=sa(e,h,n);if(b){break r}}if(k){break q}break g}if((b|0)>=0){break g}}f=a+4|0;b=F[a+4>>2];if(b){continue}break}break g}b=sa(h,j,w);if(b){break i}}if(s){break h}break f}if((b|0)>=0){break f}}h=F[f+4>>2];v:{if(!h){b=f;while(1){a=F[b+8>>2];j=F[a>>2]!=(b|0);b=a;if(j){continue}break}break v}b=h;while(1){a=b;b=F[b>>2];if(b){continue}break}}w:{x:{if((a|0)==(e|0)){break x}j=G[a+27|0];b=j<<24>>24<0;y:{j=b?F[a+20>>2]:j;s=i>>>0>j>>>0?j:i;if(s){b=sa((n|0)<0?F[k>>2]:k,b?F[a+16>>2]:a+16|0,s);if(b){break y}}if(i>>>0<j>>>0){break x}break w}if((b|0)>=0){break w}}if(!h){F[p+12>>2]=f;a=f+4|0;break e}F[p+12>>2]=a;break e}b=F[e>>2];if(!b){F[p+12>>2]=e;a=e;break e}h=(n|0)<0?F[k>>2]:k;f=e;while(1){a=b;b=G[b+27|0];e=b<<24>>24<0;b=e?F[a+20>>2]:b;k=b>>>0<i>>>0;z:{A:{B:{C:{n=k?b:i;D:{if(n){e=e?F[a+16>>2]:a+16|0;j=sa(h,e,n);if(!j){if(b>>>0>i>>>0){break D}break C}if((j|0)>=0){break C}break D}if(b>>>0<=i>>>0){break B}}f=a;b=F[a>>2];if(b){continue}break g}b=sa(e,h,n);if(b){break A}}if(k){break z}break g}if((b|0)>=0){break g}}f=a+4|0;b=F[a+4>>2];if(b){continue}break}}F[p+12>>2]=a;a=f;break e}F[p+12>>2]=f;F[a>>2]=f}f=a;a=F[a>>2];if(a){b=0}else{a=ka(40);b=a+16|0;E:{if(D[c+27|0]>=0){e=F[c+20>>2];F[b>>2]=F[c+16>>2];F[b+4>>2]=e;F[b+8>>2]=F[c+24>>2];break E}ra(b,F[c+16>>2],F[c+20>>2])}b=a+28|0;F:{if(D[c+39|0]>=0){e=F[c+32>>2];F[b>>2]=F[c+28>>2];F[b+4>>2]=e;F[b+8>>2]=F[c+36>>2];break F}ra(b,F[c+28>>2],F[c+32>>2])}F[a+8>>2]=F[p+12>>2];F[a>>2]=0;F[a+4>>2]=0;F[f>>2]=a;b=a;e=F[F[r>>2]>>2];if(e){F[r>>2]=e;b=F[f>>2]}nb(F[r+4>>2],b);F[r+8>>2]=F[r+8>>2]+1;b=1}D[x+12|0]=b;F[x+8>>2]=a;Z=p+16|0;b=F[c+4>>2];G:{if(b){while(1){c=b;b=F[b>>2];if(b){continue}break G}}while(1){a=c;c=F[c+8>>2];if((a|0)!=F[c>>2]){continue}break}}if((c|0)!=(z|0)){continue}break}}F[g+8>>2]=d;F[g>>2]=0;F[g+4>>2]=0;F[o>>2]=g;c=g;a=F[F[u>>2]>>2];if(a){F[u>>2]=a;c=F[o>>2]}nb(F[u+4>>2],c);F[u+8>>2]=F[u+8>>2]+1;a=1}D[l+44|0]=a;F[l+40>>2]=g;Z=x+16|0;c=F[l+40>>2];ib(t|4,F[l+16>>2]);ib(y,F[l+28>>2])}f=Z-48|0;Z=f;d=f+8|0;g=Z-32|0;Z=g;o=g+32|0;b=o;a=g+21|0;H:{if((b|0)==(a|0)){break H}}e=b-a|0;I:{if((e|0)<=9){h=61;if((e|0)<(I[2684]<=1|0)){break I}}D[a|0]=49;b=a+1|0;h=0}F[g+12>>2]=h;F[g+8>>2]=b;h=Z-16|0;Z=h;e=Z-16|0;Z=e;J:{q=F[g+8>>2];g=q-a|0;if(g>>>0<=2147483631){K:{if(g>>>0<11){D[d+11|0]=g|G[d+11|0]&128;D[d+11|0]=G[d+11|0]&127;b=d;break K}t=e+8|0;if(g>>>0>=11){k=g+16&-16;b=k-1|0;b=(b|0)==11?k:b}else{b=10}sb(t,b+1|0);b=F[e+8>>2];F[d>>2]=b;F[d+8>>2]=F[d+8>>2]&-2147483648|F[e+12>>2]&2147483647;F[d+8>>2]=F[d+8>>2]|-2147483648;F[d+4>>2]=g}while(1){if((a|0)!=(q|0)){D[b|0]=G[a|0];b=b+1|0;a=a+1|0;continue}break}D[e+7|0]=0;D[b|0]=G[e+7|0];Z=e+16|0;break J}za();v()}Z=h+16|0;Z=o;F[f+32>>2]=m;L:{M:{a=c+20|0;d=F[a+4>>2];N:{if(!d){g=a+4|0;c=g;break N}b=G[m+11|0];c=b<<24>>24<0;e=c?F[m>>2]:m;b=c?F[m+4>>2]:b;while(1){c=d;d=G[c+27|0];g=d<<24>>24<0;d=g?F[c+20>>2]:d;o=d>>>0<b>>>0;O:{P:{Q:{R:{h=o?d:b;S:{if(h){g=g?F[c+16>>2]:c+16|0;q=sa(e,g,h);if(!q){if(b>>>0<d>>>0){break S}break R}if((q|0)>=0){break R}break S}if(b>>>0>=d>>>0){break Q}}g=c;d=F[c>>2];if(d){continue}break N}d=sa(g,e,h);if(d){break P}}if(o){break O}break M}if((d|0)>=0){break M}}d=F[c+4>>2];if(d){continue}break}g=c+4|0}d=ka(40);e=d+16|0;b=F[f+32>>2];T:{if(D[b+11|0]>=0){o=F[b+4>>2];F[e>>2]=F[b>>2];F[e+4>>2]=o;F[e+8>>2]=F[b+8>>2];break T}ra(e,F[b>>2],F[b+4>>2])}F[d+8>>2]=c;F[d>>2]=0;F[d+4>>2]=0;F[d+36>>2]=0;F[d+28>>2]=0;F[d+32>>2]=0;F[g>>2]=d;c=d;b=F[F[a>>2]>>2];if(b){F[a>>2]=b;c=F[g>>2]}nb(F[a+4>>2],c);F[a+8>>2]=F[a+8>>2]+1;a=1;break L}d=c;a=0}D[f+44|0]=a;F[f+40>>2]=d;a=F[f+40>>2];if(D[a+39|0]<0){ja(F[a+28>>2])}b=F[f+12>>2];F[a+28>>2]=F[f+8>>2];F[a+32>>2]=b;F[a+36>>2]=F[f+16>>2];Z=f+48|0;Z=l+48|0;if(D[m+11|0]<0){ja(F[m>>2])}Z=m+16|0}function zd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0;h=Z-32|0;Z=h;g=F[F[a+4>>2]+44>>2];c=F[a+8>>2];d=F[c>>2];c=F[c+4>>2];F[h+24>>2]=0;F[h+16>>2]=0;F[h+20>>2]=0;d=(c-d>>2>>>0)/3|0;c=F[g+96>>2];f=(F[g+100>>2]-c|0)/12|0;a:{if(d>>>0>f>>>0){e=d-f|0;i=F[g+104>>2];c=F[g+100>>2];if(e>>>0<=(i-c|0)/12>>>0){b:{if(!e){break b}d=c;f=L(e,12)-12|0;i=((f>>>0)/12|0)+1&3;if(i){while(1){l=F[h+20>>2];F[d>>2]=F[h+16>>2];F[d+4>>2]=l;F[d+8>>2]=F[h+24>>2];d=d+12|0;j=j+1|0;if((i|0)!=(j|0)){continue}break}}c=L(e,12)+c|0;if(f>>>0<36){break b}while(1){f=F[h+20>>2];F[d>>2]=F[h+16>>2];F[d+4>>2]=f;F[d+8>>2]=F[h+24>>2];F[d+20>>2]=F[h+24>>2];f=F[h+20>>2];F[d+12>>2]=F[h+16>>2];F[d+16>>2]=f;F[d+32>>2]=F[h+24>>2];f=F[h+20>>2];F[d+24>>2]=F[h+16>>2];F[d+28>>2]=f;f=F[h+20>>2];F[d+36>>2]=F[h+16>>2];F[d+40>>2]=f;F[d+44>>2]=F[h+24>>2];d=d+48|0;if((d|0)!=(c|0)){continue}break}}F[g+100>>2]=c;break a}c:{f=F[g+96>>2];n=(c-f|0)/12|0;d=n+e|0;if(d>>>0<357913942){f=(i-f|0)/12|0;i=f<<1;i=f>>>0>=178956970?357913941:d>>>0<i>>>0?i:d;if(i){if(i>>>0>=357913942){break c}l=ka(L(i,12))}f=L(n,12)+l|0;d=f;e=L(e,12);n=e-12|0;q=((n>>>0)/12|0)+1&3;if(q){while(1){r=F[h+20>>2];F[d>>2]=F[h+16>>2];F[d+4>>2]=r;F[d+8>>2]=F[h+24>>2];d=d+12|0;j=j+1|0;if((q|0)!=(j|0)){continue}break}}e=e+f|0;if(n>>>0>=36){while(1){j=F[h+20>>2];F[d>>2]=F[h+16>>2];F[d+4>>2]=j;F[d+8>>2]=F[h+24>>2];F[d+20>>2]=F[h+24>>2];j=F[h+20>>2];F[d+12>>2]=F[h+16>>2];F[d+16>>2]=j;F[d+32>>2]=F[h+24>>2];j=F[h+20>>2];F[d+24>>2]=F[h+16>>2];F[d+28>>2]=j;j=F[h+20>>2];F[d+36>>2]=F[h+16>>2];F[d+40>>2]=j;F[d+44>>2]=F[h+24>>2];d=d+48|0;if((e|0)!=(d|0)){continue}break}}j=F[g+96>>2];if((j|0)!=(c|0)){while(1){c=c-12|0;n=F[c+4>>2];f=f-12|0;d=f;F[d>>2]=F[c>>2];F[d+4>>2]=n;F[d+8>>2]=F[c+8>>2];if((c|0)!=(j|0)){continue}break}c=F[g+96>>2]}F[g+104>>2]=L(i,12)+l;F[g+100>>2]=e;F[g+96>>2]=f;if(c){ja(c)}break a}na();v()}oa();v()}if(d>>>0>=f>>>0){break a}F[g+100>>2]=c+L(d,12)}d:{if(F[a+216>>2]==F[a+220>>2]){j=F[a+4>>2];c=F[j+44>>2];d=F[c+100>>2];f=F[c+96>>2];if((d|0)!=(f|0)){c=(d-f|0)/12|0;o=c>>>0<=1?1:c;c=0;while(1){d=F[a+8>>2];i=f+L(c,12)|0;g=L(c,3);e:{f:{if((g|0)==-1){e=F[(F[d>>2]+(g<<2)|0)+4>>2];k=-1;g=1;break f}e=-1;k=F[F[d>>2]+(g<<2)>>2];l=g+1|0;if((l|0)==-1){g=0;break f}e=F[F[d>>2]+(l<<2)>>2];g=g+2|0;m=-1;if((g|0)==-1){break e}}m=F[F[d>>2]+(g<<2)>>2]}F[i+8>>2]=m;F[i+4>>2]=e;F[i>>2]=k;c=c+1|0;if((o|0)!=(c|0)){continue}break}}F[F[j+4>>2]+80>>2]=b;c=1;break d}d=0;F[h+24>>2]=0;F[h+16>>2]=0;F[h+20>>2]=0;l=F[a+8>>2];c=F[l>>2];g=F[l+4>>2];F[h+8>>2]=0;F[h>>2]=0;F[h+4>>2]=0;b=0;g:{h:{i:{j:{k:{l:{if((c|0)!=(g|0)){c=g-c|0;if((c|0)<0){break l}b=ka(c);F[h>>2]=b;F[h+8>>2]=(c&-4)+b;u=h,w=ma(b,0,c)+c|0,F[u+4>>2]=w}c=F[l+24>>2];if((F[l+28>>2]-c|0)<4){break h}f=0;while(1){g=F[(p<<2)+c>>2];m:{if((g|0)==-1){break m}n:{if(F[F[a+120>>2]+(p>>>3&536870908)>>2]>>>p&1){break n}n=F[a+216>>2];c=F[a+220>>2];if((n|0)==(c|0)){break n}e=g+2|0;i=(g>>>0)%3|0;q=i?g-1|0:e;c=(c-n|0)/144|0;r=c>>>0<=1?1:c;j=0;t=(i|0)!=0|(e|0)!=-1;while(1){s=g<<2;i=L(j,144)+n|0;c=F[s+F[F[i+68>>2]>>2]>>2];o:{if(!(F[F[i+16>>2]+(c>>>3&536870908)>>2]>>>c&1)){break o}c=-1;p:{if(!t){break p}e=F[F[l+12>>2]+(q<<2)>>2];c=-1;if((e|0)==-1){break p}c=e-1|0;if((e>>>0)%3|0){break p}c=e+2|0}if((g|0)==(c|0)){break o}e=s;s=F[i+32>>2];i=F[e+s>>2];while(1){e=0;if((c|0)==-1){break g}if((i|0)!=F[s+(c<<2)>>2]){g=c;break n}q:{r:{if((c>>>0)%3|0){e=c-1|0;break r}e=c+2|0;m=-1;if((e|0)==-1){break q}}c=F[F[l+12>>2]+(e<<2)>>2];m=-1;if((c|0)==-1){break q}m=c-1|0;if((c>>>0)%3|0){break q}m=c+2|0}c=m;if((g|0)!=(c|0)){continue}break}}j=j+1|0;if((r|0)!=(j|0)){continue}break}}i=k-f|0;e=i>>2;F[(g<<2)+b>>2]=e;s:{if(k>>>0<o>>>0){F[k>>2]=g;k=k+4|0;F[h+20>>2]=k;break s}c=e+1|0;if(c>>>0>=1073741824){break k}d=o-f|0;k=d>>>1|0;c=d>>>0>=2147483644?1073741823:c>>>0<k>>>0?k:c;if(c){if(c>>>0>=1073741824){break j}d=ka(c<<2)}else{d=0}e=d+(e<<2)|0;F[e>>2]=g;m=c<<2;c=pa(d,f,i);o=m+c|0;F[h+24>>2]=o;k=e+4|0;F[h+20>>2]=k;F[h+16>>2]=c;if(f){ja(f);l=F[a+8>>2]}f=c}if((g|0)==-1){break m}t:{if((g>>>0)%3|0){c=g-1|0;break t}c=g+2|0;if((c|0)==-1){break m}}c=F[F[l+12>>2]+(c<<2)>>2];if((c|0)==-1){break m}c=c+((c>>>0)%3|0?-1:2)|0;if((c|0)==-1){break m}e=g;if((c|0)==(g|0)){break m}while(1){i=c;u:{v:{c=F[a+220>>2];j=F[a+216>>2];if((c|0)==(j|0)){break v}c=(c-j|0)/144|0;n=c>>>0<=1?1:c;c=0;while(1){q=F[(j+L(c,144)|0)+32>>2];r=i<<2;if(F[q+r>>2]==F[q+(e<<2)>>2]){c=c+1|0;if((n|0)!=(c|0)){continue}break v}break}j=k-d|0;e=j>>2;F[b+r>>2]=e;if(k>>>0<o>>>0){F[k>>2]=i;k=k+4|0;F[h+20>>2]=k;f=d;break u}c=e+1|0;if(c>>>0>=1073741824){break i}f=o-d|0;k=f>>>1|0;c=f>>>0>=2147483644?1073741823:c>>>0<k>>>0?k:c;if(c){if(c>>>0>=1073741824){break j}f=ka(c<<2)}else{f=0}e=f+(e<<2)|0;F[e>>2]=i;m=c<<2;c=pa(f,d,j);o=m+c|0;F[h+24>>2]=o;k=e+4|0;F[h+20>>2]=k;F[h+16>>2]=c;if(!d){d=c;break u}ja(d);l=F[a+8>>2];d=c;break u}F[(i<<2)+b>>2]=F[(e<<2)+b>>2]}if((i|0)==-1){break m}w:{if((i>>>0)%3|0){c=i-1|0;break w}c=i+2|0;if((c|0)==-1){break m}}c=F[F[l+12>>2]+(c<<2)>>2];if((c|0)==-1){break m}c=c+((c>>>0)%3|0?-1:2)|0;if((c|0)==-1){break m}e=i;if((c|0)!=(g|0)){continue}break}}p=p+1|0;c=F[l+24>>2];if((p|0)<F[l+28>>2]-c>>2){continue}break}break h}na();v()}na();v()}oa();v()}na();v()}i=F[a+4>>2];a=F[i+44>>2];c=F[a+100>>2];a=F[a+96>>2];x:{if((c|0)==(a|0)){break x}g=(c-a|0)/12|0;f=g>>>0<=1?1:g;l=f&1;c=0;if(g>>>0>=2){j=f&-2;g=0;while(1){e=L(c,12);f=e+b|0;o=F[f>>2];p=F[f+4>>2];e=a+e|0;F[e+8>>2]=F[f+8>>2];F[e>>2]=o;F[e+4>>2]=p;e=L(c|1,12);f=e+b|0;o=F[f>>2];p=F[f+4>>2];e=a+e|0;F[e+8>>2]=F[f+8>>2];F[e>>2]=o;F[e+4>>2]=p;c=c+2|0;g=g+2|0;if((j|0)!=(g|0)){continue}break}}if(!l){break x}g=L(c,12);c=g+b|0;f=F[c>>2];e=F[c+4>>2];a=a+g|0;F[a+8>>2]=F[c+8>>2];F[a>>2]=f;F[a+4>>2]=e}F[F[i+4>>2]+80>>2]=k-d>>2;e=1}c=e;if(b){ja(b)}if(!d){break d}F[h+20>>2]=d;ja(d)}Z=h+32|0;return c}function de(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0;g=Z+-64|0;Z=g;F[a+8>>2]=e;y=a+32|0;f=F[y>>2];d=F[a+36>>2]-f>>2;a:{b:{if(d>>>0<e>>>0){qa(y,e-d|0);F[g+56>>2]=0;F[g+60>>2]=0;F[g+48>>2]=0;F[g+52>>2]=0;F[g+40>>2]=0;F[g+44>>2]=0;F[g+32>>2]=0;F[g+36>>2]=0;F[g+24>>2]=0;F[g+28>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;F[g>>2]=0;break b}if(d>>>0>e>>>0){F[a+36>>2]=f+(e<<2)}F[g+56>>2]=0;F[g+60>>2]=0;F[g+48>>2]=0;F[g+52>>2]=0;F[g+40>>2]=0;F[g+44>>2]=0;F[g+32>>2]=0;F[g+36>>2]=0;F[g+24>>2]=0;F[g+28>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;F[g>>2]=0;d=0;if(!e){break a}}Fa(g+16|0,e,g);h=F[g+28>>2];d=F[g+32>>2]}F[g>>2]=0;d=d-h>>2;c:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break c}F[g+32>>2]=(e<<2)+h;break c}Fa(g+16|12,e-d|0,g)}F[g>>2]=0;f=F[g+40>>2];d=F[g+44>>2]-f>>2;d:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break d}F[g+44>>2]=f+(e<<2);break d}Fa(g+40|0,e-d|0,g)}F[g>>2]=0;f=F[g+52>>2];d=F[g+56>>2]-f>>2;e:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break e}F[g+56>>2]=f+(e<<2);break e}Fa(g+52|0,e-d|0,g)}f:{if(F[a+8>>2]<=0){break f}i=F[g+16>>2];j=F[a+32>>2];h=0;while(1){d=h<<2;f=F[d+i>>2];m=F[a+16>>2];g:{if((f|0)>(m|0)){F[d+j>>2]=m;break g}d=d+j|0;m=F[a+12>>2];if((m|0)>(f|0)){F[d>>2]=m;break g}F[d>>2]=f}h=h+1|0;d=F[a+8>>2];if((h|0)<(d|0)){continue}break}if((d|0)<=0){break f}d=0;while(1){i=d<<2;f=i+c|0;i=F[b+i>>2]+F[j+i>>2]|0;F[f>>2]=i;h:{if((i|0)>F[a+16>>2]){i=i-F[a+20>>2]|0}else{if((i|0)>=F[a+12>>2]){break h}i=i+F[a+20>>2]|0}F[f>>2]=i}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}}H=F[a+52>>2];t=F[a+48>>2];z=ka(16);d=z;F[d>>2]=0;F[d+4>>2]=0;F[d+8>>2]=0;F[d+12>>2]=0;F[g+8>>2]=0;F[g>>2]=0;F[g+4>>2]=0;i:{if(e){if(e>>>0>=1073741824){break i}d=e<<2;r=ka(d);F[g>>2]=r;F[g+8>>2]=d+r;ma(r,0,d)}A=1;d=F[a+56>>2];B=F[d>>2];d=F[d+4>>2]-B|0;j:{if((d|0)<8){break j}w=d>>2;I=(w|0)<=2?2:w;J=w>>>0<=1?1:w;C=e&-2;D=e&1;K=e&-4;E=e&3;G=e-1|0;M=e<<2;N=e>>>0<4;A=0;m=1;while(1){k:{l:{m:{n:{if((m|0)!=(J|0)){o:{p:{f=F[(m<<2)+B>>2];if((f|0)==-1){break p}k=1;d=f+2|0;j=(f>>>0)%3|0;x=j?f-1|0:d;s=1<<x;n=F[t>>2];O=n+(x>>>3&536870908)|0;i=0;P=(j|0)!=0|(d|0)!=-1;d=f;q:{while(1){r:{if(F[n+(d>>>3&536870908)>>2]>>>d&1){break r}j=F[F[F[t+64>>2]+12>>2]+(d<<2)>>2];if((j|0)==-1){break r}l=F[H>>2];h=F[t+28>>2];p=F[l+(F[h+(j<<2)>>2]<<2)>>2];if((p|0)>=(m|0)){break r}q=j+1|0;q=F[l+(F[h+(((q>>>0)%3|0?q:j-2|0)<<2)>>2]<<2)>>2];if((q|0)>=(m|0)){break r}h=F[l+(F[h+(j+((j>>>0)%3|0?-1:2)<<2)>>2]<<2)>>2];if((h|0)>=(m|0)){break r}s:{if(!e){break s}j=F[(g+16|0)+L(i,12)>>2];l=L(e,h);q=L(e,q);p=L(e,p);h=0;o=0;if(G){while(1){F[j+(h<<2)>>2]=(F[(h+l<<2)+c>>2]+F[(h+q<<2)+c>>2]|0)-F[(h+p<<2)+c>>2];u=h|1;F[j+(u<<2)>>2]=(F[(l+u<<2)+c>>2]+F[(q+u<<2)+c>>2]|0)-F[(p+u<<2)+c>>2];h=h+2|0;o=o+2|0;if((C|0)!=(o|0)){continue}break}}if(!D){break s}F[j+(h<<2)>>2]=(F[(h+l<<2)+c>>2]+F[(h+q<<2)+c>>2]|0)-F[(h+p<<2)+c>>2]}j=4;i=i+1|0;if((i|0)==4){break q}}t:{if(k&1){h=d-2|0;j=d+1|0;d=-1;j=(j>>>0)%3|0?j:h;if((j|0)==-1|F[n+(j>>>3&536870908)>>2]>>>j&1){break t}j=F[F[F[t+64>>2]+12>>2]+(j<<2)>>2];if((j|0)==-1){break t}d=j+1|0;d=(d>>>0)%3|0?d:j-2|0;break t}u:{if((d>>>0)%3|0){h=d-1|0;break u}h=d+2|0;d=-1;if((h|0)==-1){break t}}d=-1;if(F[n+(h>>>3&536870908)>>2]>>>h&1){break t}j=F[F[F[t+64>>2]+12>>2]+(h<<2)>>2];if((j|0)==-1){break t}if((j>>>0)%3|0){d=j-1|0;break t}d=j+2|0}v:{if((d|0)==(f|0)){break v}if((d|0)==-1&k){if(!P|s&F[O>>2]){break v}d=F[F[F[t+64>>2]+12>>2]+(x<<2)>>2];if((d|0)==-1){break v}k=0;d=(d>>>0)%3|0?d-1|0:d+2|0}if((d|0)!=-1){continue}}break}j=i;if((j|0)<=0){break p}}if(e){ma(r,0,M)}d=j-1|0;q=(d<<2)+z|0;d=L(d,12)+a|0;u=d;x=F[d- -64>>2];k=0;d=F[g>>2];f=0;while(1){i=F[q>>2];F[q>>2]=i+1;if(i>>>0>=x>>>0){break j}w:{if(F[F[u+60>>2]+(i>>>3&536870908)>>2]>>>i&1){break w}f=f+1|0;if(!e){break w}n=F[(g+16|0)+L(k,12)>>2];i=0;h=0;p=0;if(!N){while(1){l=h<<2;o=l+d|0;F[o>>2]=F[l+n>>2]+F[o>>2];o=l|4;s=o+d|0;F[s>>2]=F[n+o>>2]+F[s>>2];o=l|8;s=o+d|0;F[s>>2]=F[n+o>>2]+F[s>>2];l=l|12;o=l+d|0;F[o>>2]=F[l+n>>2]+F[o>>2];h=h+4|0;p=p+4|0;if((K|0)!=(p|0)){continue}break}}if(!E){break w}while(1){l=h<<2;p=l+d|0;F[p>>2]=F[l+n>>2]+F[p>>2];h=h+1|0;i=i+1|0;if((E|0)!=(i|0)){continue}break}}k=k+1|0;if((k|0)!=(j|0)){continue}break}i=L(e,m);if(!f){break o}if(!e){break l}h=0;d=0;if(G){break n}break m}i=L(e,m)}if(F[a+8>>2]<=0){break k}k=(L(m-1|0,e)<<2)+c|0;j=F[y>>2];h=0;while(1){d=h<<2;f=F[d+k>>2];n=F[a+16>>2];x:{if((f|0)>(n|0)){F[d+j>>2]=n;break x}d=d+j|0;n=F[a+12>>2];if((n|0)>(f|0)){F[d>>2]=n;break x}F[d>>2]=f}h=h+1|0;f=F[a+8>>2];if((h|0)<(f|0)){continue}break}d=0;if((f|0)<=0){break k}f=i<<2;h=f+c|0;k=b+f|0;while(1){i=d<<2;f=i+h|0;i=F[i+k>>2]+F[j+i>>2]|0;F[f>>2]=i;y:{if((i|0)>F[a+16>>2]){i=i-F[a+20>>2]|0}else{if((i|0)>=F[a+12>>2]){break y}i=i+F[a+20>>2]|0}F[f>>2]=i}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}break k}ta();v()}while(1){j=h<<2;k=j+r|0;F[k>>2]=F[k>>2]/(f|0);j=(j|4)+r|0;F[j>>2]=F[j>>2]/(f|0);h=h+2|0;d=d+2|0;if((C|0)!=(d|0)){continue}break}}if(!D){break l}d=(h<<2)+r|0;F[d>>2]=F[d>>2]/(f|0)}if(F[a+8>>2]<=0){break k}j=F[y>>2];h=0;while(1){d=h<<2;f=F[d+r>>2];k=F[a+16>>2];z:{if((f|0)>(k|0)){F[d+j>>2]=k;break z}d=d+j|0;k=F[a+12>>2];if((k|0)>(f|0)){F[d>>2]=k;break z}F[d>>2]=f}h=h+1|0;f=F[a+8>>2];if((h|0)<(f|0)){continue}break}d=0;if((f|0)<=0){break k}f=i<<2;h=f+c|0;k=b+f|0;while(1){i=d<<2;f=i+h|0;i=F[i+k>>2]+F[j+i>>2]|0;F[f>>2]=i;A:{if((i|0)>F[a+16>>2]){i=i-F[a+20>>2]|0}else{if((i|0)>=F[a+12>>2]){break A}i=i+F[a+20>>2]|0}F[f>>2]=i}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}}m=m+1|0;A=(w|0)<=(m|0);if((m|0)!=(I|0)){continue}break}}a=F[g>>2];if(a){ja(a)}ja(z);a=F[g+52>>2];if(a){F[g+56>>2]=a;ja(a)}a=F[g+40>>2];if(a){F[g+44>>2]=a;ja(a)}a=F[g+28>>2];if(a){F[g+32>>2]=a;ja(a)}a=F[g+16>>2];if(a){F[g+20>>2]=a;ja(a)}Z=g- -64|0;return A|0}na();v()}function $h(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=0,E=0,G=0,H=0,I=0,J=0,K=0,M=0,N=0;h=Z+-64|0;Z=h;F[a+8>>2]=e;x=a+32|0;f=F[x>>2];d=F[a+36>>2]-f>>2;a:{b:{if(d>>>0<e>>>0){qa(x,e-d|0);F[h+56>>2]=0;F[h+60>>2]=0;F[h+48>>2]=0;F[h+52>>2]=0;F[h+40>>2]=0;F[h+44>>2]=0;F[h+32>>2]=0;F[h+36>>2]=0;F[h+24>>2]=0;F[h+28>>2]=0;F[h+16>>2]=0;F[h+20>>2]=0;F[h>>2]=0;break b}if(d>>>0>e>>>0){F[a+36>>2]=f+(e<<2)}F[h+56>>2]=0;F[h+60>>2]=0;F[h+48>>2]=0;F[h+52>>2]=0;F[h+40>>2]=0;F[h+44>>2]=0;F[h+32>>2]=0;F[h+36>>2]=0;F[h+24>>2]=0;F[h+28>>2]=0;F[h+16>>2]=0;F[h+20>>2]=0;F[h>>2]=0;d=0;if(!e){break a}}Fa(h+16|0,e,h);i=F[h+28>>2];d=F[h+32>>2]}F[h>>2]=0;d=d-i>>2;c:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break c}F[h+32>>2]=(e<<2)+i;break c}Fa(h+16|12,e-d|0,h)}F[h>>2]=0;f=F[h+40>>2];d=F[h+44>>2]-f>>2;d:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break d}F[h+44>>2]=f+(e<<2);break d}Fa(h+40|0,e-d|0,h)}F[h>>2]=0;f=F[h+52>>2];d=F[h+56>>2]-f>>2;e:{if(d>>>0>=e>>>0){if(d>>>0<=e>>>0){break e}F[h+56>>2]=f+(e<<2);break e}Fa(h+52|0,e-d|0,h)}f:{if(F[a+8>>2]<=0){break f}g=F[h+16>>2];j=F[a+32>>2];i=0;while(1){d=i<<2;f=F[d+g>>2];m=F[a+16>>2];g:{if((f|0)>(m|0)){F[d+j>>2]=m;break g}d=d+j|0;m=F[a+12>>2];if((m|0)>(f|0)){F[d>>2]=m;break g}F[d>>2]=f}i=i+1|0;d=F[a+8>>2];if((i|0)<(d|0)){continue}break}if((d|0)<=0){break f}d=0;while(1){g=d<<2;f=g+c|0;g=F[b+g>>2]+F[g+j>>2]|0;F[f>>2]=g;h:{if((g|0)>F[a+16>>2]){g=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break h}g=g+F[a+20>>2]|0}F[f>>2]=g}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}}H=F[a+52>>2];A=F[a+48>>2];y=ka(16);d=y;F[d>>2]=0;F[d+4>>2]=0;F[d+8>>2]=0;F[d+12>>2]=0;F[h+8>>2]=0;F[h>>2]=0;F[h+4>>2]=0;i:{if(e){if(e>>>0>=1073741824){break i}d=e<<2;t=ka(d);F[h>>2]=t;F[h+8>>2]=d+t;ma(t,0,d)}z=1;d=F[a+56>>2];B=F[d>>2];d=F[d+4>>2]-B|0;j:{if((d|0)<8){break j}w=d>>2;I=(w|0)<=2?2:w;J=w>>>0<=1?1:w;C=e&-2;D=e&1;K=e&-4;E=e&3;G=e-1|0;M=e<<2;N=e>>>0<4;z=0;m=1;while(1){k:{l:{m:{n:{if((m|0)!=(J|0)){o:{p:{f=F[(m<<2)+B>>2];if((f|0)==-1){break p}n=F[A+12>>2];d=f+2|0;g=(f>>>0)%3|0;q=n+((g?f-1|0:d)<<2)|0;j=0;u=(g|0)!=0|(d|0)!=-1;k=1;d=f;q:{while(1){g=F[n+(d<<2)>>2];r:{if((g|0)==-1){break r}l=-1;p=F[H>>2];r=F[A>>2];i=p+(F[r+(g<<2)>>2]<<2)|0;o=g+1|0;o=(o>>>0)%3|0?o:g-2|0;if((o|0)!=-1){l=F[r+(o<<2)>>2]}o=F[i>>2];s:{t:{if((g>>>0)%3|0){i=g-1|0;break t}i=g+2|0;s=-1;if((i|0)==-1){break s}}s=F[r+(i<<2)>>2]}if((m|0)<=(o|0)){break r}i=F[p+(l<<2)>>2];if((i|0)>=(m|0)){break r}l=F[p+(s<<2)>>2];if((l|0)>=(m|0)){break r}g=F[(h+16|0)+L(j,12)>>2];u:{if(!e){break u}l=L(e,l);r=L(e,i);p=L(e,o);i=0;s=0;if(G){while(1){F[g+(i<<2)>>2]=(F[(i+l<<2)+c>>2]+F[(i+r<<2)+c>>2]|0)-F[(i+p<<2)+c>>2];o=i|1;F[g+(o<<2)>>2]=(F[(l+o<<2)+c>>2]+F[(o+r<<2)+c>>2]|0)-F[(o+p<<2)+c>>2];i=i+2|0;s=s+2|0;if((C|0)!=(s|0)){continue}break}}if(!D){break u}F[g+(i<<2)>>2]=(F[(i+l<<2)+c>>2]+F[(i+r<<2)+c>>2]|0)-F[(i+p<<2)+c>>2]}g=4;j=j+1|0;if((j|0)==4){break q}}v:{if(k&1){i=d+1|0;d=(i>>>0)%3|0?i:d-2|0;g=-1;if((d|0)==-1){break v}d=F[n+(d<<2)>>2];g=-1;if((d|0)==-1){break v}g=d+1|0;g=(g>>>0)%3|0?g:d-2|0;break v}w:{if((d>>>0)%3|0){i=d-1|0;break w}i=d+2|0;g=-1;if((i|0)==-1){break v}}d=F[n+(i<<2)>>2];g=-1;if((d|0)==-1){break v}g=d-1|0;if((d>>>0)%3|0){break v}g=d+2|0}d=g;x:{if((f|0)==(d|0)){break x}if((d|0)==-1&k){if(!u){break x}d=F[q>>2];if((d|0)==-1){break x}k=0;d=(d>>>0)%3|0?d-1|0:d+2|0}if((d|0)!=-1){continue}}break}g=j;if((g|0)<=0){break p}}if(e){ma(t,0,M)}d=g-1|0;r=(d<<2)+y|0;d=L(d,12)+a|0;o=d;s=F[d- -64>>2];k=0;d=F[h>>2];f=0;while(1){j=F[r>>2];F[r>>2]=j+1;if(j>>>0>=s>>>0){break j}y:{if(F[F[o+60>>2]+(j>>>3&536870908)>>2]>>>j&1){break y}f=f+1|0;if(!e){break y}j=F[(h+16|0)+L(k,12)>>2];l=0;i=0;p=0;if(!N){while(1){n=i<<2;q=n+d|0;F[q>>2]=F[j+n>>2]+F[q>>2];q=n|4;u=q+d|0;F[u>>2]=F[j+q>>2]+F[u>>2];q=n|8;u=q+d|0;F[u>>2]=F[j+q>>2]+F[u>>2];n=n|12;q=n+d|0;F[q>>2]=F[j+n>>2]+F[q>>2];i=i+4|0;p=p+4|0;if((K|0)!=(p|0)){continue}break}}if(!E){break y}while(1){n=i<<2;p=n+d|0;F[p>>2]=F[j+n>>2]+F[p>>2];i=i+1|0;l=l+1|0;if((E|0)!=(l|0)){continue}break}}k=k+1|0;if((k|0)!=(g|0)){continue}break}g=L(e,m);if(!f){break o}if(!e){break l}i=0;d=0;if(G){break n}break m}g=L(e,m)}if(F[a+8>>2]<=0){break k}k=(L(m-1|0,e)<<2)+c|0;j=F[x>>2];i=0;while(1){d=i<<2;f=F[d+k>>2];l=F[a+16>>2];z:{if((f|0)>(l|0)){F[d+j>>2]=l;break z}d=d+j|0;l=F[a+12>>2];if((l|0)>(f|0)){F[d>>2]=l;break z}F[d>>2]=f}i=i+1|0;f=F[a+8>>2];if((i|0)<(f|0)){continue}break}d=0;if((f|0)<=0){break k}f=g<<2;i=f+c|0;k=b+f|0;while(1){g=d<<2;f=g+i|0;g=F[g+k>>2]+F[g+j>>2]|0;F[f>>2]=g;A:{if((g|0)>F[a+16>>2]){g=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break A}g=g+F[a+20>>2]|0}F[f>>2]=g}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}break k}ta();v()}while(1){j=i<<2;k=j+t|0;F[k>>2]=F[k>>2]/(f|0);j=(j|4)+t|0;F[j>>2]=F[j>>2]/(f|0);i=i+2|0;d=d+2|0;if((C|0)!=(d|0)){continue}break}}if(!D){break l}d=(i<<2)+t|0;F[d>>2]=F[d>>2]/(f|0)}if(F[a+8>>2]<=0){break k}j=F[x>>2];i=0;while(1){d=i<<2;f=F[d+t>>2];k=F[a+16>>2];B:{if((f|0)>(k|0)){F[d+j>>2]=k;break B}d=d+j|0;k=F[a+12>>2];if((k|0)>(f|0)){F[d>>2]=k;break B}F[d>>2]=f}i=i+1|0;f=F[a+8>>2];if((i|0)<(f|0)){continue}break}d=0;if((f|0)<=0){break k}f=g<<2;i=f+c|0;k=b+f|0;while(1){g=d<<2;f=g+i|0;g=F[g+k>>2]+F[g+j>>2]|0;F[f>>2]=g;C:{if((g|0)>F[a+16>>2]){g=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break C}g=g+F[a+20>>2]|0}F[f>>2]=g}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}}m=m+1|0;z=(w|0)<=(m|0);if((m|0)!=(I|0)){continue}break}}a=F[h>>2];if(a){ja(a)}ja(y);a=F[h+52>>2];if(a){F[h+56>>2]=a;ja(a)}a=F[h+40>>2];if(a){F[h+44>>2]=a;ja(a)}a=F[h+28>>2];if(a){F[h+32>>2]=a;ja(a)}a=F[h+16>>2];if(a){F[h+20>>2]=a;ja(a)}Z=h- -64|0;return z|0}na();v()}function Yh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,E=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,$=0,aa=0;a:{b:{if((e|0)!=2){break b}F[a+8>>2]=2;F[a- -64>>2]=f;M=a+32|0;e=F[M>>2];d=F[a+36>>2]-e|0;c:{if(d>>>0<=7){qa(M,2-(d>>>2|0)|0);break c}if((d|0)==8){break c}F[a+36>>2]=e+8}i=1;d=F[a+56>>2];d=F[d+4>>2]-F[d>>2]|0;if((d|0)<=0){break b}o=a+60|0;d=d>>>2|0;X=d>>>0<=1?1:d;Y=a+68|0;d=0;while(1){f=F[a+56>>2];e=F[f>>2];if(F[f+4>>2]-e>>2>>>0<=d>>>0){break a}k=Z-80|0;Z=k;f=-1;d:{e:{e=F[e+(d<<2)>>2];if((e|0)==-1){break e}i=F[o+32>>2];g=e+1|0;g=(g>>>0)%3|0?g:e-2|0;if((g|0)!=-1){f=F[F[i>>2]+(g<<2)>>2]}p=-1;e=e+((e>>>0)%3|0?-1:2)|0;if((e|0)!=-1){p=F[F[i>>2]+(e<<2)>>2]}i=F[o+36>>2];e=F[i>>2];i=F[i+4>>2]-e>>2;if(i>>>0<=f>>>0|i>>>0<=p>>>0){break e}f:{g:{h:{i:{j:{k:{j=F[e+(p<<2)>>2];f=F[e+(f<<2)>>2];if((j|0)>=(d|0)|(f|0)>=(d|0)){break k}i=(j<<3)+c|0;w=F[i+4>>2];g=(f<<3)+c|0;e=F[g+4>>2];l=F[i>>2];i=F[g>>2];if(!((l|0)!=(i|0)|(e|0)!=(w|0))){F[o+8>>2]=i;F[o+12>>2]=e;break j}p=F[F[o+4>>2]+(d<<2)>>2];F[k+72>>2]=0;F[k+76>>2]=0;g=k- -64|0;F[g>>2]=0;F[g+4>>2]=0;F[k+56>>2]=0;F[k+60>>2]=0;g=F[o>>2];if(!G[g+84|0]){p=F[F[g+68>>2]+(p<<2)>>2]}Ga(g,p,D[g+24|0],k+56|0);p=F[F[o+4>>2]+(f<<2)>>2];F[k+48>>2]=0;F[k+52>>2]=0;F[k+40>>2]=0;F[k+44>>2]=0;F[k+32>>2]=0;F[k+36>>2]=0;g=F[o>>2];if(!G[g+84|0]){p=F[F[g+68>>2]+(p<<2)>>2]}Ga(g,p,D[g+24|0],k+32|0);p=F[F[o+4>>2]+(j<<2)>>2];F[k+24>>2]=0;F[k+28>>2]=0;F[k+16>>2]=0;F[k+20>>2]=0;F[k+8>>2]=0;F[k+12>>2]=0;g=F[o>>2];if(!G[g+84|0]){p=F[F[g+68>>2]+(p<<2)>>2]}Ga(g,p,D[g+24|0],k+8|0);g=F[k+16>>2];n=F[k+40>>2];x=g-n|0;N=F[k+44>>2];g=F[k+20>>2]-(N+(g>>>0<n>>>0)|0)|0;H=g;j=ki(x,g,x,g);q=_;g=F[k+8>>2];z=F[k+32>>2];A=g-z|0;O=F[k+36>>2];g=F[k+12>>2]-(O+(g>>>0<z>>>0)|0)|0;I=g;h=j;j=ki(A,g,A,g);g=h+j|0;h=_+q|0;h=g>>>0<j>>>0?h+1|0:h;j=F[k+24>>2];B=F[k+48>>2];C=j-B|0;P=F[k+52>>2];j=F[k+28>>2]-(P+(j>>>0<B>>>0)|0)|0;J=j;m=g;g=ki(C,j,C,j);r=m+g|0;h=_+h|0;s=g>>>0>r>>>0?h+1|0:h;if(!(s|r)){break k}p=0;E=mi(-1,2147483647,r,s);f=i>>31;R=f;h=f>>31;Q=i;g=h;q=i^g;i=q-g|0;f=(f^g)-((g>>>0>q>>>0)+g|0)|0;g=f;f=e>>31;S=f;K=e;e=f>>31;q=K^e;m=q-e|0;h=f>>31;e=(h^f)-((e>>>0>q>>>0)+h|0)|0;f=(g|0)==(e|0)&i>>>0>m>>>0|e>>>0<g>>>0;i=f?i:m;j=_;e=f?g:e;if((j|0)==(e|0)&i>>>0>E>>>0|e>>>0>j>>>0){break f}i=F[k+64>>2];T=F[k+68>>2];e=ki(i-n|0,T-((i>>>0<n>>>0)+N|0)|0,x,H);f=_;g=F[k+56>>2];U=F[k+60>>2];j=ki(g-z|0,U-((g>>>0<z>>>0)+O|0)|0,A,I);e=j+e|0;h=_+f|0;h=e>>>0<j>>>0?h+1|0:h;f=e;m=F[k+72>>2];V=F[k+76>>2];e=ki(m-B|0,V-((m>>>0<B>>>0)+P|0)|0,C,J);j=f+e|0;f=_+h|0;q=e>>>0>j>>>0?f+1|0:f;e=l;E=e-Q|0;e=(e>>31)-((e>>>0<Q>>>0)+R|0)|0;W=e;l=e>>31;y=l^E;f=y-l|0;h=e>>31;e=(h^e)-((l>>>0>y>>>0)+h|0)|0;h=e;y=w-K|0;e=(w>>31)-((w>>>0<K>>>0)+S|0)|0;w=e;l=f;t=e>>31;u=t^y;L=u-t|0;f=e>>31;e=(f^e)-((t>>>0>u>>>0)+f|0)|0;f=(h|0)==(e|0)&l>>>0>L>>>0|e>>>0<h>>>0;f=mi(-1,2147483647,f?l:L,f?h:e)>>>0<j>>>0;e=_;if(f&(e|0)<=(q|0)|(e|0)<(q|0)){break f}e=I>>31;f=e;l=e^A;e=l-e|0;f=(f^I)-((f>>>0>l>>>0)+f|0)|0;h=H>>31;t=h^x;u=t-h|0;l=(h^H)-((h>>>0>t>>>0)+h|0)|0;h=(f|0)==(l|0)&e>>>0>u>>>0|f>>>0>l>>>0;e=h?e:u;f=h?f:l;h=J>>31;L=e;t=h^C;u=t-h|0;l=(h^J)-((h>>>0>t>>>0)+h|0)|0;e=(f|0)==(l|0)&e>>>0>u>>>0|f>>>0>l>>>0;f=mi(-1,2147483647,e?L:u,e?f:l)>>>0<j>>>0;e=_;if(f&(e|0)<=(q|0)|(e|0)<(q|0)){break f}l=1;e=0;f=n;n=li(ki(j,q,x,H),_,r,s);f=f+n|0;h=_+N|0;h=f>>>0<n>>>0?h+1|0:h;n=i-f|0;f=T-((f>>>0>i>>>0)+h|0)|0;n=ki(n,f,n,f);x=_;f=g;h=li(ki(j,q,A,I),_,r,s);i=h+z|0;g=_+O|0;g=h>>>0>i>>>0?g+1|0:g;h=f-i|0;f=U-((f>>>0<i>>>0)+g|0)|0;g=ki(h,f,h,f);i=g+n|0;f=_+x|0;f=g>>>0>i>>>0?f+1|0:f;n=i;g=li(ki(j,q,C,J),_,r,s);i=g+B|0;h=_+P|0;h=g>>>0>i>>>0?h+1|0:h;g=m-i|0;i=V-((i>>>0>m>>>0)+h|0)|0;m=ki(g,i,g,i);i=m+n|0;g=_+f|0;f=ki(i,i>>>0<m>>>0?g+1|0:g,r,s);i=_;m=i;if(!i&f>>>0<=1){break i}h=f;while(1){g=e<<1|l>>>31;l=l<<1;e=g;n=!i&h>>>0>7|(i|0)!=0;h=(i&3)<<30|h>>>2;i=i>>>2|0;if(n){continue}break}break h}if((d|0)>(f|0)){e=f<<1}else{if((d|0)<=0){F[o+8>>2]=0;F[o+12>>2]=0;break j}e=(d<<1)-2|0}e=(e<<2)+c|0;F[o+8>>2]=F[e>>2];F[o+12>>2]=F[e+4>>2]}p=1;break f}e=m;l=f;if(f-1|0){break g}}while(1){i=mi(f,m,l,e);h=e+_|0;e=i+l|0;h=e>>>0<l>>>0?h+1|0:h;l=(h&1)<<31|e>>>1;e=h>>>1|0;i=ki(l,e,l,e);g=_;if((m|0)==(g|0)&f>>>0<i>>>0|g>>>0>m>>>0){continue}break}}f=F[o+20>>2];if(!f){break f}g=f-1|0;h=F[F[o+16>>2]+(g>>>3&536870908)>>2];F[o+20>>2]=g;p=1;f=ki(j,q,y,w);i=_;n=ki(r,s,K,S);m=n+f|0;f=_+i|0;f=m>>>0<n>>>0?f+1|0:f;i=ki(l,e,E,W);g=h>>>g&1;h=g?0-i|0:i;m=h+m|0;n=f;f=_;i=n+(g?0-(f+((i|0)!=0)|0)|0:f)|0;$=o,aa=li(m,h>>>0>m>>>0?i+1|0:i,r,s),F[$+12>>2]=aa;f=ki(j,q,E,W);i=_;j=ki(r,s,Q,R);f=j+f|0;h=_+i|0;e=ki(l,e,y,w);i=0-e|0;l=_;h=(f>>>0<j>>>0?h+1|0:h)+(g?l:0-(((e|0)!=0)+l|0)|0)|0;i=g?e:i;f=i+f|0;$=o,aa=li(f,f>>>0<i>>>0?h+1|0:h,r,s),F[$+8>>2]=aa}Z=k+80|0;e=p;break d}ta();v()}i=e;if(!e){return 0}l:{if(F[a+8>>2]<=0){break l}l=F[M>>2];e=0;while(1){f=e<<2;g=F[f+Y>>2];j=F[a+16>>2];m:{if((g|0)>(j|0)){F[f+l>>2]=j;break m}f=f+l|0;j=F[a+12>>2];if((j|0)>(g|0)){F[f>>2]=j;break m}F[f>>2]=g}e=e+1|0;g=F[a+8>>2];if((e|0)<(g|0)){continue}break}f=0;if((g|0)<=0){break l}e=d<<3;j=e+c|0;q=b+e|0;while(1){g=f<<2;e=g+j|0;g=F[g+q>>2]+F[g+l>>2]|0;F[e>>2]=g;n:{if((g|0)>F[a+16>>2]){g=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break n}g=g+F[a+20>>2]|0}F[e>>2]=g}f=f+1|0;if((f|0)<F[a+8>>2]){continue}break}}d=d+1|0;if((X|0)!=(d|0)){continue}break}}return i|0}ta();v()}function hi(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,A=0,B=0,C=0,E=0,H=0,I=0,J=0,K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=0,S=0,T=0,U=0,V=0,W=0,X=0,Y=0,$=0,aa=0;a:{b:{if((e|0)!=2){break b}F[a+8>>2]=2;F[a- -64>>2]=f;M=a+32|0;e=F[M>>2];d=F[a+36>>2]-e|0;c:{if(d>>>0<=7){qa(M,2-(d>>>2|0)|0);break c}if((d|0)==8){break c}F[a+36>>2]=e+8}p=1;d=F[a+56>>2];d=F[d+4>>2]-F[d>>2]|0;if((d|0)<=0){break b}o=a+60|0;d=d>>>2|0;X=d>>>0<=1?1:d;Y=a+68|0;d=0;while(1){e=F[a+56>>2];h=F[e>>2];if(F[e+4>>2]-h>>2>>>0<=d>>>0){break a}k=Z-80|0;Z=k;f=-1;h=F[h+(d<<2)>>2];e=-1;d:{if((h|0)==-1){break d}e=h+1|0;f=(e>>>0)%3|0?e:h-2|0;e=h-1|0;if((h>>>0)%3|0){break d}e=h+2|0}g=F[o+36>>2];h=F[g>>2];e:{f:{g:{h:{i:{g=F[g+4>>2]-h>>2;i=f<<2;f=F[F[o+32>>2]+28>>2];j=F[i+f>>2];if(g>>>0<=j>>>0){break i}e=F[f+(e<<2)>>2];if(e>>>0>=g>>>0){break i}j:{k:{l=F[h+(e<<2)>>2];f=F[h+(j<<2)>>2];if((l|0)>=(d|0)|(f|0)>=(d|0)){break k}h=(l<<3)+c|0;w=F[h+4>>2];g=(f<<3)+c|0;e=F[g+4>>2];j=F[h>>2];h=F[g>>2];if(!((j|0)!=(h|0)|(e|0)!=(w|0))){F[o+8>>2]=h;F[o+12>>2]=e;break j}p=F[F[o+4>>2]+(d<<2)>>2];F[k+72>>2]=0;F[k+76>>2]=0;g=k- -64|0;F[g>>2]=0;F[g+4>>2]=0;F[k+56>>2]=0;F[k+60>>2]=0;g=F[o>>2];if(!G[g+84|0]){p=F[F[g+68>>2]+(p<<2)>>2]}Ga(g,p,D[g+24|0],k+56|0);p=F[F[o+4>>2]+(f<<2)>>2];F[k+48>>2]=0;F[k+52>>2]=0;F[k+40>>2]=0;F[k+44>>2]=0;F[k+32>>2]=0;F[k+36>>2]=0;g=F[o>>2];if(!G[g+84|0]){p=F[F[g+68>>2]+(p<<2)>>2]}Ga(g,p,D[g+24|0],k+32|0);p=F[F[o+4>>2]+(l<<2)>>2];F[k+24>>2]=0;F[k+28>>2]=0;F[k+16>>2]=0;F[k+20>>2]=0;F[k+8>>2]=0;F[k+12>>2]=0;g=F[o>>2];if(!G[g+84|0]){p=F[F[g+68>>2]+(p<<2)>>2]}Ga(g,p,D[g+24|0],k+8|0);g=F[k+16>>2];n=F[k+40>>2];x=g-n|0;N=F[k+44>>2];g=F[k+20>>2]-(N+(g>>>0<n>>>0)|0)|0;H=g;l=ki(x,g,x,g);q=_;g=F[k+8>>2];z=F[k+32>>2];A=g-z|0;O=F[k+36>>2];g=F[k+12>>2]-(O+(g>>>0<z>>>0)|0)|0;I=g;i=l;l=ki(A,g,A,g);g=i+l|0;i=_+q|0;i=g>>>0<l>>>0?i+1|0:i;l=F[k+24>>2];B=F[k+48>>2];C=l-B|0;P=F[k+52>>2];l=F[k+28>>2]-(P+(l>>>0<B>>>0)|0)|0;J=l;m=g;g=ki(C,l,C,l);r=m+g|0;i=_+i|0;s=g>>>0>r>>>0?i+1|0:i;if(!(s|r)){break k}p=0;E=mi(-1,2147483647,r,s);f=h>>31;R=f;i=f>>31;Q=h;g=i;q=h^g;h=q-g|0;f=(f^g)-((g>>>0>q>>>0)+g|0)|0;g=f;f=e>>31;S=f;K=e;e=f>>31;q=K^e;m=q-e|0;i=f>>31;e=(i^f)-((e>>>0>q>>>0)+i|0)|0;f=(g|0)==(e|0)&h>>>0>m>>>0|e>>>0<g>>>0;h=f?h:m;l=_;e=f?g:e;if((l|0)==(e|0)&h>>>0>E>>>0|e>>>0>l>>>0){break e}h=F[k+64>>2];T=F[k+68>>2];e=ki(h-n|0,T-((h>>>0<n>>>0)+N|0)|0,x,H);f=_;g=F[k+56>>2];U=F[k+60>>2];l=ki(g-z|0,U-((g>>>0<z>>>0)+O|0)|0,A,I);e=l+e|0;i=_+f|0;i=e>>>0<l>>>0?i+1|0:i;f=e;m=F[k+72>>2];V=F[k+76>>2];e=ki(m-B|0,V-((m>>>0<B>>>0)+P|0)|0,C,J);l=f+e|0;f=_+i|0;q=e>>>0>l>>>0?f+1|0:f;e=j;E=e-Q|0;e=(e>>31)-((e>>>0<Q>>>0)+R|0)|0;W=e;j=e>>31;y=j^E;f=y-j|0;i=e>>31;e=(i^e)-((j>>>0>y>>>0)+i|0)|0;i=e;y=w-K|0;e=(w>>31)-((w>>>0<K>>>0)+S|0)|0;w=e;j=f;t=e>>31;u=t^y;L=u-t|0;f=e>>31;e=(f^e)-((t>>>0>u>>>0)+f|0)|0;f=(i|0)==(e|0)&j>>>0>L>>>0|e>>>0<i>>>0;f=mi(-1,2147483647,f?j:L,f?i:e)>>>0<l>>>0;e=_;if(f&(e|0)<=(q|0)|(e|0)<(q|0)){break e}e=I>>31;f=e;j=e^A;e=j-e|0;f=(f^I)-((f>>>0>j>>>0)+f|0)|0;i=H>>31;t=i^x;u=t-i|0;j=(i^H)-((i>>>0>t>>>0)+i|0)|0;i=(f|0)==(j|0)&e>>>0>u>>>0|f>>>0>j>>>0;e=i?e:u;f=i?f:j;i=J>>31;L=e;t=i^C;u=t-i|0;j=(i^J)-((i>>>0>t>>>0)+i|0)|0;e=(f|0)==(j|0)&e>>>0>u>>>0|f>>>0>j>>>0;f=mi(-1,2147483647,e?L:u,e?f:j)>>>0<l>>>0;e=_;if(f&(e|0)<=(q|0)|(e|0)<(q|0)){break e}j=1;e=0;f=n;n=li(ki(l,q,x,H),_,r,s);f=f+n|0;i=_+N|0;i=f>>>0<n>>>0?i+1|0:i;n=h-f|0;f=T-((f>>>0>h>>>0)+i|0)|0;n=ki(n,f,n,f);x=_;f=g;i=li(ki(l,q,A,I),_,r,s);h=i+z|0;g=_+O|0;g=h>>>0<i>>>0?g+1|0:g;i=f-h|0;f=U-((f>>>0<h>>>0)+g|0)|0;g=ki(i,f,i,f);h=g+n|0;f=_+x|0;f=h>>>0<g>>>0?f+1|0:f;n=h;g=li(ki(l,q,C,J),_,r,s);h=g+B|0;i=_+P|0;i=h>>>0<g>>>0?i+1|0:i;g=m-h|0;h=V-((h>>>0>m>>>0)+i|0)|0;m=ki(g,h,g,h);h=m+n|0;g=_+f|0;f=ki(h,h>>>0<m>>>0?g+1|0:g,r,s);h=_;m=h;if(!h&f>>>0<=1){break h}i=f;while(1){g=e<<1|j>>>31;j=j<<1;e=g;n=!h&i>>>0>7|(h|0)!=0;i=(h&3)<<30|i>>>2;h=h>>>2|0;if(n){continue}break}break g}if((d|0)>(f|0)){e=f<<1}else{if((d|0)<=0){F[o+8>>2]=0;F[o+12>>2]=0;break j}e=(d<<1)-2|0}e=(e<<2)+c|0;F[o+8>>2]=F[e>>2];F[o+12>>2]=F[e+4>>2]}p=1;break e}ta();v()}e=m;j=f;if(f-1|0){break f}}while(1){h=mi(f,m,j,e);i=e+_|0;e=h+j|0;i=e>>>0<j>>>0?i+1|0:i;j=(i&1)<<31|e>>>1;e=i>>>1|0;h=ki(j,e,j,e);g=_;if((m|0)==(g|0)&f>>>0<h>>>0|g>>>0>m>>>0){continue}break}}f=F[o+20>>2];if(!f){break e}g=f-1|0;i=F[F[o+16>>2]+(g>>>3&536870908)>>2];F[o+20>>2]=g;p=1;f=ki(l,q,y,w);h=_;n=ki(r,s,K,S);m=n+f|0;f=_+h|0;f=m>>>0<n>>>0?f+1|0:f;h=ki(j,e,E,W);g=i>>>g&1;i=g?0-h|0:h;m=i+m|0;n=f;f=_;h=n+(g?0-(f+((h|0)!=0)|0)|0:f)|0;$=o,aa=li(m,i>>>0>m>>>0?h+1|0:h,r,s),F[$+12>>2]=aa;f=ki(l,q,E,W);h=_;l=ki(r,s,Q,R);f=l+f|0;i=_+h|0;e=ki(j,e,y,w);h=0-e|0;j=_;i=(f>>>0<l>>>0?i+1|0:i)+(g?j:0-(((e|0)!=0)+j|0)|0)|0;h=g?e:h;f=h+f|0;$=o,aa=li(f,f>>>0<h>>>0?i+1|0:i,r,s),F[$+8>>2]=aa}Z=k+80|0;if(!p){return 0}l:{if(F[a+8>>2]<=0){break l}g=F[M>>2];e=0;while(1){f=e<<2;h=F[f+Y>>2];j=F[a+16>>2];m:{if((h|0)>(j|0)){F[f+g>>2]=j;break m}f=f+g|0;j=F[a+12>>2];if((j|0)>(h|0)){F[f>>2]=j;break m}F[f>>2]=h}e=e+1|0;h=F[a+8>>2];if((e|0)<(h|0)){continue}break}f=0;if((h|0)<=0){break l}e=d<<3;j=e+c|0;l=b+e|0;while(1){h=f<<2;e=h+j|0;h=F[h+l>>2]+F[h+g>>2]|0;F[e>>2]=h;n:{if((h|0)>F[a+16>>2]){i=h-F[a+20>>2]|0}else{if((h|0)>=F[a+12>>2]){break n}i=h+F[a+20>>2]|0}F[e>>2]=i}f=f+1|0;if((f|0)<F[a+8>>2]){continue}break}}d=d+1|0;if((X|0)!=(d|0)){continue}break}}return p|0}ta();v()}function Gd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;n=Z-96|0;Z=n;m=F[a+4>>2];d=F[m+32>>2];j=F[d+8>>2];i=F[d+12>>2];e=i;c=F[d+20>>2];f=F[d+16>>2];a:{if((e|0)<=(c|0)&f>>>0>=j>>>0|(c|0)>(e|0)){break a}o=F[d>>2];h=G[o+f|0];g=f+1|0;e=g?c:c+1|0;F[d+16>>2]=g;F[d+20>>2]=e;if((e|0)>=(i|0)&g>>>0>=j>>>0|(e|0)>(i|0)){break a}p=G[g+o|0];g=f+2|0;e=g>>>0<2?c+1|0:c;F[d+16>>2]=g;F[d+20>>2]=e;l=h<<24>>24;b:{if((l|0)>=0){k=F[a+216>>2];if(h>>>0>=(F[a+220>>2]-k|0)/144>>>0){break a}k=k+L(h,144)|0;if(F[k>>2]<0){break b}break a}if(F[a+212>>2]>=0){break a}k=a+212|0}F[k>>2]=b;c:{d:{e:{f:{g:{h:{k=H[m+36>>1];i:{if(((k<<8|k>>>8)&65535)>>>0>=258){if((e|0)>=(i|0)&g>>>0>=j>>>0|(e|0)>(i|0)){break a}e=G[g+o|0];f=f+3|0;c=f>>>0<3?c+1|0:c;F[d+16>>2]=f;F[d+20>>2]=c;if(e>>>0>1){break a}d=e>>>0<2?e:0;if(!p){break i}if(!d){break h}break a}if(p){break g}d=0}if((l|0)<0){e=a+184|0}else{c=F[a+216>>2]+L(h,144)|0;D[c+100|0]=0;e=c+104|0}if((d|0)!=1){break e}c=Z-112|0;Z=c;g=F[F[a+4>>2]+44>>2];d=ka(120);F[d>>2]=8924;F[d+4>>2]=0;F[d+116>>2]=0;F[d+112>>2]=e;F[d+108>>2]=g;F[d+12>>2]=0;F[d+16>>2]=0;F[d+20>>2]=0;F[d+24>>2]=0;F[d+28>>2]=0;F[d+32>>2]=0;F[d+36>>2]=0;F[d+40>>2]=0;F[d+44>>2]=0;F[d+48>>2]=0;F[d+52>>2]=0;F[d+56>>2]=0;F[d+60>>2]=0;F[d+8>>2]=9136;f=d- -64|0;F[f>>2]=0;F[f+4>>2]=0;F[d+72>>2]=0;F[d+76>>2]=0;F[d+80>>2]=0;F[d+84>>2]=0;F[d+88>>2]=0;F[d+104>>2]=0;F[d+96>>2]=0;F[d+100>>2]=0;f=F[a+8>>2];F[c+48>>2]=0;F[c+52>>2]=0;F[c+40>>2]=0;F[c+44>>2]=0;j=c+32|0;F[j>>2]=0;F[j+4>>2]=0;F[c+24>>2]=0;F[c+28>>2]=0;h=c- -64|0;F[h>>2]=0;F[h+4>>2]=0;F[c+72>>2]=0;F[c+76>>2]=0;F[c+80>>2]=0;F[c+84>>2]=0;F[c+88>>2]=0;F[c+104>>2]=0;F[c+16>>2]=0;F[c+20>>2]=0;F[c+56>>2]=0;F[c+60>>2]=0;F[c+8>>2]=9136;F[c+96>>2]=0;F[c+100>>2]=0;F[c+12>>2]=f;h=F[f>>2];i=F[f+4>>2];D[c+111|0]=0;k=j;j=c+111|0;Ea(k,(i-h>>2>>>0)/3|0,j);h=F[c+12>>2];i=F[h+28>>2];h=F[h+24>>2];D[c+111|0]=0;Ea(c+44|0,i-h>>2,j);F[c+28>>2]=d;F[c+24>>2]=g;F[c+20>>2]=e;F[c+16>>2]=f;f=d+8|0;e=c+8|0;lc(f,e);j:{if((e|0)==(f|0)){F[d+92>>2]=F[e+84>>2];break j}gb(d+56|0,F[e+48>>2],F[e+52>>2]);gb(d+68|0,F[e+60>>2],F[e- -64>>2]);gb(d+80|0,F[e+72>>2],F[e+76>>2]);F[d+92>>2]=F[e+84>>2];k:{h=F[e+92>>2];j=F[e+88>>2];i=h-j|0;e=i>>2;f=F[d+104>>2];g=F[d+96>>2];if(e>>>0<=f-g>>2>>>0){i=F[d+100>>2]-g|0;f=i+j|0;m=i>>2;i=e>>>0>m>>>0?f:h;l=i-j|0;if((i|0)!=(j|0)){pa(g,j,l)}if(e>>>0>m>>>0){e=F[d+100>>2];if((h|0)!=(i|0)){while(1){F[e>>2]=F[f>>2];e=e+4|0;f=f+4|0;if((h|0)!=(f|0)){continue}break}}F[d+100>>2]=e;break k}F[d+100>>2]=g+l;break k}if(g){F[d+100>>2]=g;ja(g);F[d+104>>2]=0;F[d+96>>2]=0;F[d+100>>2]=0;f=0}l:{if((i|0)<0){break l}g=f>>>1|0;e=f>>>0>=2147483644?1073741823:e>>>0<g>>>0?g:e;if(e>>>0>=1073741824){break l}f=e<<2;e=ka(f);F[d+96>>2]=e;F[d+104>>2]=e+f;if((h|0)!=(j|0)){f=e;e=(i-4&-4)+4|0;e=la(f,j,e)+e|0}F[d+100>>2]=e;break k}na();v()}}F[c+8>>2]=9136;e=F[c+96>>2];if(e){F[c+100>>2]=e;ja(e)}e=F[c+80>>2];if(e){F[c+84>>2]=e;ja(e)}e=F[c+68>>2];if(e){F[c+72>>2]=e;ja(e)}e=F[c+56>>2];if(e){F[c+60>>2]=e;ja(e)}F[c+8>>2]=9372;e=F[c+44>>2];if(e){ja(e)}e=F[c+32>>2];if(e){ja(e)}Z=c+112|0;break d}if((l|0)>=0){break f}break a}if((l|0)<0){break a}}e=F[a+216>>2];c=F[m+44>>2];d=ka(80);F[d>>2]=9684;F[d+4>>2]=0;F[d+76>>2]=0;F[d+68>>2]=c;F[d+8>>2]=8624;F[d+12>>2]=0;F[d+16>>2]=0;F[d+20>>2]=0;F[d+24>>2]=0;F[d+28>>2]=0;F[d+32>>2]=0;F[d+36>>2]=0;F[d+40>>2]=0;F[d+44>>2]=0;F[d+48>>2]=0;F[d+52>>2]=0;e=e+L(h,144)|0;f=e+104|0;F[d+72>>2]=f;F[d- -64>>2]=0;F[d+56>>2]=0;F[d+60>>2]=0;F[n+24>>2]=c;c=n;F[c+68>>2]=0;F[c+72>>2]=0;F[c+60>>2]=0;F[c+64>>2]=0;F[c+52>>2]=0;F[c+56>>2]=0;F[c+44>>2]=0;F[c+48>>2]=0;F[c+84>>2]=0;F[c+88>>2]=0;F[c+76>>2]=0;F[c+80>>2]=0;F[c+28>>2]=d;g=F[c+28>>2];F[c+8>>2]=F[c+24>>2];F[c+12>>2]=g;F[c+20>>2]=f;f=e+4|0;F[c+16>>2]=f;F[c+36>>2]=0;F[c+40>>2]=0;F[c+32>>2]=8624;e=F[c+20>>2];F[c>>2]=F[c+16>>2];F[c+4>>2]=e;e=c+32|0;Fd(e,f,c);c=d+8|0;lc(c,e);if((c|0)!=(e|0)){gb(d+56|0,F[e+48>>2],F[e+52>>2])}Ed(e);break c}c=Z+-64|0;Z=c;g=F[F[a+4>>2]+44>>2];d=ka(80);F[d>>2]=9392;F[d+4>>2]=0;F[d+76>>2]=0;F[d+72>>2]=e;F[d+68>>2]=g;F[d+8>>2]=9556;F[d+12>>2]=0;F[d+16>>2]=0;F[d+20>>2]=0;F[d+24>>2]=0;F[d+28>>2]=0;F[d+32>>2]=0;F[d+36>>2]=0;F[d+40>>2]=0;F[d+44>>2]=0;F[d+48>>2]=0;F[d+52>>2]=0;F[d- -64>>2]=0;j=d+56|0;f=j;F[f>>2]=0;F[f+4>>2]=0;f=F[a+8>>2];F[c+40>>2]=0;F[c+44>>2]=0;F[c+32>>2]=0;F[c+36>>2]=0;h=c+24|0;F[h>>2]=0;F[h+4>>2]=0;F[c+16>>2]=0;F[c+20>>2]=0;F[c+56>>2]=0;F[c+8>>2]=0;F[c+12>>2]=0;F[c+48>>2]=0;F[c+52>>2]=0;F[c>>2]=9556;F[c+4>>2]=f;i=F[f>>2];l=F[f+4>>2];D[c+63|0]=0;k=h;h=c+63|0;Ea(k,(l-i>>2>>>0)/3|0,h);i=F[c+4>>2];l=F[i+28>>2];i=F[i+24>>2];D[c+63|0]=0;Ea(c+36|0,l-i>>2,h);F[c+20>>2]=d;F[c+16>>2]=g;F[c+12>>2]=e;F[c+8>>2]=f;lc(d+8|0,c);gb(j,F[c+48>>2],F[c+52>>2]);F[c>>2]=9556;e=F[c+48>>2];if(e){F[c+52>>2]=e;ja(e)}F[c>>2]=9372;e=F[c+36>>2];if(e){ja(e)}e=F[c+24>>2];if(e){ja(e)}Z=c- -64|0}if(!d){break a}}d=yc(ka(64),d);c=F[a+4>>2];a=d;d=b;m:{n:{if((d|0)>=0){g=c+8|0;b=F[c+12>>2];j=F[c+8>>2];e=b-j>>2;o:{if((e|0)>(d|0)){break o}f=d+1|0;if(d>>>0>=e>>>0){Pb(g,f-e|0);break o}if(e>>>0<=f>>>0){break o}f=j+(f<<2)|0;if((f|0)!=(b|0)){while(1){b=b-4|0;e=F[b>>2];F[b>>2]=0;if(e){$[F[F[e>>2]+4>>2]](e)}if((b|0)!=(f|0)){continue}break}}F[c+12>>2]=f}c=F[g>>2]+(d<<2)|0;b=F[c>>2];F[c>>2]=a;if(b){break n}break m}b=a;if(!a){break m}}$[F[F[b>>2]+4>>2]](b)}q=(d^-1)>>>31|0}Z=n+96|0;return q|0}function Ab(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=M(0),n=M(0),o=0;a:{b:{if(!d){break b}c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}E[(g<<1)+d>>1]=D[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}E[(g<<1)+d>>1]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}E[(g<<1)+d>>1]=H[b>>1];b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){return 0}e=E[b>>1];if((e|0)<0){break b}E[(g<<1)+d>>1]=e;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}e=F[b>>2];if(e+32768>>>0>65535){break b}E[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}e=F[b>>2];if(e>>>0>32767){break b}E[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;k=F[e+4>>2];while(1){if(b>>>0>=k>>>0){break b}h=F[b+4>>2];e=F[b>>2];i=e+32768|0;h=i>>>0<32768?h+1|0:h;if(!h&i>>>0>65535|h){break b}E[(g<<1)+d>>1]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}k=F[b+4>>2];e=F[b>>2];if(!k&e>>>0>32767|k){break b}E[(g<<1)+d>>1]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 8:d:{e:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break e}e=F[a>>2];j=F[e>>2];g=j;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-j|0;if(!G[a+32|0]){j=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(32767)|m<M(-32768)|m!=m){break d}n=M(N(m));if(n==M(Infinity)){break d}e=(b<<1)+d|0;if(n<M(2147483648)){i=~~m}else{i=-2147483648}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}j=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(32767)|m<M(-32768)|(M(N(m))==M(Infinity)|m!=m)){break d}if(m<M(0)|m>M(1)){break d}e=(b<<1)+d|0;l=R(+m*32767+.5);f:{if(N(l)<2147483648){i=~~l;break f}i=-2147483648}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}j=1;if(c>>>0<=e>>>0){break d}ma((e<<1)+d|0,0,c-e<<1)}return j;case 9:g:{h:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break h}e=F[a>>2];j=F[e>>2];g=j;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-j|0;if(!G[a+32|0]){j=0;if((b|0)>=(e|0)){break g}b=0;while(1){l=K[g>>3];if(l>=32767|l<-32768|l!=l){break g}o=N(l);if(o==Infinity){break g}e=(b<<1)+d|0;if(o<2147483648){i=~~l}else{i=-2147483648}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}j=0;if((b|0)>=(e|0)){break g}b=0;while(1){l=K[g>>3];if(l>=32767|l<-32768|(N(l)==Infinity|l!=l)){break g}if(l<0|l>1){break g}e=(b<<1)+d|0;l=R(l*32767+.5);i:{if(N(l)<2147483648){i=~~l;break i}i=-2147483648}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}j=1;if(c>>>0<=e>>>0){break g}ma((e<<1)+d|0,0,c-e<<1)}return j;case 10:break c;default:break b}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}E[(g<<1)+d>>1]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}ma((e<<1)+d|0,0,(c&255)-e<<1)}return j}ma((e<<1)+d|0,0,(c&255)-e<<1);return 1}function yb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=M(0),n=M(0),o=0;a:{b:{if(!d){break b}c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=D[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=E[b>>1];b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=H[b>>1];b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=F[b>>2];b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){return 0}e=F[b>>2];if((e|0)<0){break b}F[(g<<2)+d>>2]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;k=F[e+4>>2];while(1){if(b>>>0>=k>>>0){break b}h=F[b+4>>2];e=F[b>>2];if(e- -2147483648>>>0<2147483648?h+1|0:h){break b}F[(g<<2)+d>>2]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}k=F[b+4>>2];e=F[b>>2];if(!k&e>>>0>2147483647|k){break b}F[(g<<2)+d>>2]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}break a;case 8:d:{e:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break e}e=F[a>>2];j=F[e>>2];g=j;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-j|0;if(!G[a+32|0]){j=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(2147483648)|m<M(-2147483648)|m!=m){break d}n=M(N(m));if(n==M(Infinity)){break d}e=(b<<2)+d|0;if(n<M(2147483648)){i=~~m}else{i=-2147483648}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}j=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(2147483648)|m<M(-2147483648)|(M(N(m))==M(Infinity)|m!=m)){break d}if(m<M(0)|m>M(1)){break d}e=(b<<2)+d|0;l=R(+m*2147483647+.5);f:{if(N(l)<2147483648){i=~~l;break f}i=-2147483648}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}j=1;if(c>>>0<=e>>>0){break d}ma((e<<2)+d|0,0,c-e<<2)}return j;case 9:g:{h:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break h}e=F[a>>2];j=F[e>>2];g=j;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-j|0;if(!G[a+32|0]){j=0;if((b|0)>=(e|0)){break g}b=0;while(1){l=K[g>>3];if(l>=2147483647|l<-2147483648|l!=l){break g}o=N(l);if(o==Infinity){break g}e=(b<<2)+d|0;if(o<2147483648){i=~~l}else{i=-2147483648}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}j=0;if((b|0)>=(e|0)){break g}b=0;while(1){l=K[g>>3];if(l>=2147483647|l<-2147483648|(N(l)==Infinity|l!=l)){break g}if(l<0|l>1){break g}e=(b<<2)+d|0;l=R(l*2147483647+.5);i:{if(N(l)<2147483648){i=~~l;break i}i=-2147483648}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}j=1;if(c>>>0<=e>>>0){break g}ma((e<<2)+d|0,0,c-e<<2)}return j;case 10:break c;default:break b}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}j=1;if(e>>>0>=f>>>0){break b}ma((e<<2)+d|0,0,(c&255)-e<<2)}return j}ma((e<<2)+d|0,0,(c&255)-e<<2);return 1}function zb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=M(0);a:{b:{if(!d){break b}c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){return 0}e=D[b|0];if((e|0)<0){break b}E[(g<<1)+d>>1]=e&255;b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}E[(g<<1)+d>>1]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){return 0}e=E[b>>1];if((e|0)<0){break b}E[(g<<1)+d>>1]=e;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}E[(g<<1)+d>>1]=H[b>>1];b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}e=F[b>>2];if(e>>>0>65535){break b}E[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}e=F[b>>2];if(e>>>0>65535){break b}E[(g<<1)+d>>1]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}k=F[b+4>>2];e=F[b>>2];if(!k&e>>>0>65535|k){break b}E[(g<<1)+d>>1]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}k=F[b+4>>2];e=F[b>>2];if(!k&e>>>0>65535|k){break b}E[(g<<1)+d>>1]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}break a;case 8:d:{e:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break e}e=F[a>>2];l=F[e>>2];g=l;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-l|0;if(!G[a+32|0]){l=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(65535)|m<M(0)|(M(N(m))==M(Infinity)|m!=m)){break d}e=(b<<1)+d|0;if(m<M(4294967296)&m>=M(0)){i=~~m>>>0}else{i=0}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}l=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(65535)|m<M(0)|(M(N(m))==M(Infinity)|m!=m)){break d}if(m>M(1)){break d}e=(b<<1)+d|0;j=R(+m*65535+.5);f:{if(j<4294967296&j>=0){i=~~j>>>0;break f}i=0}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}l=1;if(c>>>0<=e>>>0){break d}ma((e<<1)+d|0,0,c-e<<1)}return l;case 9:g:{h:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break h}e=F[a>>2];l=F[e>>2];g=l;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-l|0;if(!G[a+32|0]){l=0;if((b|0)>=(e|0)){break g}b=0;while(1){j=K[g>>3];if(j>=65535|j<0|(N(j)==Infinity|j!=j)){break g}e=(b<<1)+d|0;if(j<4294967296&j>=0){i=~~j>>>0}else{i=0}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}l=0;if((b|0)>=(e|0)){break g}b=0;while(1){j=K[g>>3];if(j>=65535|j<0|(N(j)==Infinity|j!=j)){break g}if(j>1){break g}e=(b<<1)+d|0;j=R(j*65535+.5);i:{if(j<4294967296&j>=0){i=~~j>>>0;break i}i=0}E[e>>1]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}l=1;if(c>>>0<=e>>>0){break g}ma((e<<1)+d|0,0,c-e<<1)}return l;case 10:break c;default:break b}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];k=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+k|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}E[(g<<1)+d>>1]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break b}ma((e<<1)+d|0,0,(c&255)-e<<1)}return l}ma((e<<1)+d|0,0,(c&255)-e<<1);return 1}function Ga(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=M(0),l=0,m=0,n=M(0),o=0;a:{if(!d){break a}b:{c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(g<<3)+d|0;i=D[b|0];F[e>>2]=i;F[e+4>>2]=i>>31;b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(g<<3)+d|0;F[e>>2]=G[b|0];F[e+4>>2]=0;b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(g<<3)+d|0;i=E[b>>1];F[e>>2]=i;F[e+4>>2]=i>>31;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(g<<3)+d|0;F[e>>2]=H[b>>1];F[e+4>>2]=0;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(g<<3)+d|0;i=F[b>>2];F[e>>2]=i;F[e+4>>2]=i>>31;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(g<<3)+d|0;F[e>>2]=F[b>>2];F[e+4>>2]=0;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}i=F[b+4>>2];e=(g<<3)+d|0;F[e>>2]=F[b>>2];F[e+4>>2]=i;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=F[b>>2];i=F[b+4>>2];if((i|0)<0){break a}j=(g<<3)+d|0;F[j>>2]=e;F[j+4>>2]=i;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 8:d:{e=G[a+24|0];f=c&255;if(!(e>>>0<f>>>0?e:f)){break d}if(G[a+32|0]){break a}e=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+e|0;e=F[a>>2];i=F[e+4>>2];e=F[e>>2];if((b|0)>=(i-e|0)){break a}g=b+e|0;h=c&255;b=0;while(1){k=J[g>>2];if(k>=M(0x8000000000000000)|k<M(-0x8000000000000000)|k!=k){break a}n=M(N(k));if(n==M(Infinity)){break a}e=(b<<3)+d|0;e:{if(n<M(0x8000000000000000)){j=M(N(k))>=M(1)?~~(k>M(0)?M(P(M(R(M(k*M(2.3283064365386963e-10)))),M(4294967296))):M(S(M(M(k-M(~~k>>>0>>>0))*M(2.3283064365386963e-10)))))>>>0:0;m=~~k>>>0;break e}j=-2147483648;m=0}F[e>>2]=m;F[e+4>>2]=j;b=b+1|0;e=G[a+24|0];if(b>>>0>=(e>>>0<h>>>0?e:h)>>>0){break d}g=g+4|0;if(i>>>0>g>>>0){continue}break}break a}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 9:f:{e=G[a+24|0];f=c&255;if(!(e>>>0<f>>>0?e:f)){break f}if(G[a+32|0]){break a}e=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+e|0;e=F[a>>2];i=F[e+4>>2];e=F[e>>2];if((b|0)>=(i-e|0)){break a}g=b+e|0;h=c&255;b=0;while(1){l=K[g>>3];if(l>=0x8000000000000000|l<-0x8000000000000000|l!=l){break a}o=N(l);if(o==Infinity){break a}e=(b<<3)+d|0;g:{if(o<0x8000000000000000){j=N(l)>=1?~~(l>0?P(R(l*2.3283064365386963e-10),4294967295):S((l-+(~~l>>>0>>>0))*2.3283064365386963e-10))>>>0:0;m=~~l>>>0;break g}j=-2147483648;m=0}F[e>>2]=m;F[e+4>>2]=j;b=b+1|0;e=G[a+24|0];if(b>>>0>=(e>>>0<h>>>0?e:h)>>>0){break f}g=g+8|0;if(i>>>0>g>>>0){continue}break}break a}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0;break b;case 10:break c;default:break a}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);j=b;b=b+i|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break a}e=(g<<3)+d|0;F[e>>2]=G[b|0];F[e+4>>2]=0;b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}if(e>>>0>=f>>>0){break a}d=(e<<3)+d|0;a=(c&255)-e|0}ma(d,0,a<<3)}}function le(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;j=a;a:{b:{c:{d:{e:{f:{g:{h:{a=F[a+8>>2];switch(F[a+28>>2]-1|0){case 4:break c;case 5:break d;case 2:break e;case 3:break f;case 0:break g;case 1:break h;default:break a}}f=G[a+24|0];c=ka(f);a=F[j+16>>2];if(F[a+80>>2]){g=F[F[a>>2]>>2]+F[a+48>>2]|0}else{g=0}if(!b){break b}if(f){o=f&252;l=f&3;h=f>>>0<4;while(1){a=0;e=0;if(!h){while(1){k=g+(d<<2)|0;D[a+c|0]=F[k>>2];D[(a|1)+c|0]=F[k+4>>2];D[(a|2)+c|0]=F[k+8>>2];D[(a|3)+c|0]=F[k+12>>2];a=a+4|0;d=d+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}e=0;if(l){while(1){D[a+c|0]=F[g+(d<<2)>>2];a=a+1|0;d=d+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}la(F[F[F[j+8>>2]+64>>2]>>2]+m|0,c,f);m=f+m|0;n=n+1|0;if((n|0)!=(b|0)){continue}break}break b}a=0;if((b|0)!=1){g=b&-2;while(1){la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,f);a=a+f|0;la(a+F[F[F[j+8>>2]+64>>2]>>2]|0,c,f);a=a+f|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}}if(!(b&1)){break b}la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,f);break b}f=G[a+24|0];c=ka(f);a=F[j+16>>2];if(F[a+80>>2]){g=F[F[a>>2]>>2]+F[a+48>>2]|0}else{g=0}if(!b){break b}if(f){o=f&252;l=f&3;h=f>>>0<4;while(1){a=0;e=0;if(!h){while(1){k=g+(d<<2)|0;D[a+c|0]=F[k>>2];D[(a|1)+c|0]=F[k+4>>2];D[(a|2)+c|0]=F[k+8>>2];D[(a|3)+c|0]=F[k+12>>2];a=a+4|0;d=d+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}e=0;if(l){while(1){D[a+c|0]=F[g+(d<<2)>>2];a=a+1|0;d=d+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}la(F[F[F[j+8>>2]+64>>2]>>2]+m|0,c,f);m=f+m|0;n=n+1|0;if((n|0)!=(b|0)){continue}break}break b}a=0;if((b|0)!=1){g=b&-2;while(1){la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,f);a=a+f|0;la(a+F[F[F[j+8>>2]+64>>2]>>2]|0,c,f);a=a+f|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}}if(!(b&1)){break b}la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,f);break b}h=G[a+24|0];i=h<<1;c=ka(i);a=F[j+16>>2];if(F[a+80>>2]){g=F[F[a>>2]>>2]+F[a+48>>2]|0}else{g=0}if(!b){break b}if(h){o=h&252;l=h&3;h=h>>>0<4;while(1){a=0;e=0;if(!h){while(1){f=a<<1;k=g+(d<<2)|0;E[f+c>>1]=F[k>>2];E[(f|2)+c>>1]=F[k+4>>2];E[(f|4)+c>>1]=F[k+8>>2];E[(f|6)+c>>1]=F[k+12>>2];a=a+4|0;d=d+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}e=0;if(l){while(1){E[(a<<1)+c>>1]=F[g+(d<<2)>>2];a=a+1|0;d=d+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}la(F[F[F[j+8>>2]+64>>2]>>2]+n|0,c,i);n=i+n|0;m=m+1|0;if((m|0)!=(b|0)){continue}break}break b}a=0;if((b|0)!=1){g=b&-2;while(1){la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i);a=a+i|0;la(a+F[F[F[j+8>>2]+64>>2]>>2]|0,c,i);a=a+i|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}}if(!(b&1)){break b}la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i);break b}h=G[a+24|0];i=h<<1;c=ka(i);a=F[j+16>>2];if(F[a+80>>2]){g=F[F[a>>2]>>2]+F[a+48>>2]|0}else{g=0}if(!b){break b}if(h){o=h&252;l=h&3;h=h>>>0<4;while(1){a=0;e=0;if(!h){while(1){f=a<<1;k=g+(d<<2)|0;E[f+c>>1]=F[k>>2];E[(f|2)+c>>1]=F[k+4>>2];E[(f|4)+c>>1]=F[k+8>>2];E[(f|6)+c>>1]=F[k+12>>2];a=a+4|0;d=d+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}e=0;if(l){while(1){E[(a<<1)+c>>1]=F[g+(d<<2)>>2];a=a+1|0;d=d+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}la(F[F[F[j+8>>2]+64>>2]>>2]+n|0,c,i);n=i+n|0;m=m+1|0;if((m|0)!=(b|0)){continue}break}break b}a=0;if((b|0)!=1){g=b&-2;while(1){la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i);a=a+i|0;la(a+F[F[F[j+8>>2]+64>>2]>>2]|0,c,i);a=a+i|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}}if(!(b&1)){break b}la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i);break b}h=G[a+24|0];i=h<<2;c=ka(i);a=F[j+16>>2];if(F[a+80>>2]){g=F[F[a>>2]>>2]+F[a+48>>2]|0}else{g=0}if(!b){break b}if(h){o=h&252;l=h&3;h=h>>>0<4;while(1){a=0;e=0;if(!h){while(1){f=a<<2;k=g+(d<<2)|0;F[f+c>>2]=F[k>>2];F[(f|4)+c>>2]=F[k+4>>2];F[(f|8)+c>>2]=F[k+8>>2];F[(f|12)+c>>2]=F[k+12>>2];a=a+4|0;d=d+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}e=0;if(l){while(1){F[(a<<2)+c>>2]=F[g+(d<<2)>>2];a=a+1|0;d=d+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}la(F[F[F[j+8>>2]+64>>2]>>2]+n|0,c,i);n=i+n|0;m=m+1|0;if((m|0)!=(b|0)){continue}break}break b}a=0;if((b|0)!=1){g=b&-2;while(1){la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i);a=a+i|0;la(a+F[F[F[j+8>>2]+64>>2]>>2]|0,c,i);a=a+i|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}}if(!(b&1)){break b}la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i);break b}h=G[a+24|0];i=h<<2;c=ka(i);a=F[j+16>>2];if(F[a+80>>2]){g=F[F[a>>2]>>2]+F[a+48>>2]|0}else{g=0}if(!b){break b}if(h){o=h&252;l=h&3;h=h>>>0<4;while(1){a=0;e=0;if(!h){while(1){f=a<<2;k=g+(d<<2)|0;F[f+c>>2]=F[k>>2];F[(f|4)+c>>2]=F[k+4>>2];F[(f|8)+c>>2]=F[k+8>>2];F[(f|12)+c>>2]=F[k+12>>2];a=a+4|0;d=d+4|0;e=e+4|0;if((o|0)!=(e|0)){continue}break}}e=0;if(l){while(1){F[(a<<2)+c>>2]=F[g+(d<<2)>>2];a=a+1|0;d=d+1|0;e=e+1|0;if((l|0)!=(e|0)){continue}break}}la(F[F[F[j+8>>2]+64>>2]>>2]+n|0,c,i);n=i+n|0;m=m+1|0;if((m|0)!=(b|0)){continue}break}break b}a=0;if((b|0)!=1){g=b&-2;while(1){la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i);a=a+i|0;la(a+F[F[F[j+8>>2]+64>>2]>>2]|0,c,i);a=a+i|0;d=d+2|0;if((g|0)!=(d|0)){continue}break}}if(!(b&1)){break b}la(F[F[F[j+8>>2]+64>>2]>>2]+a|0,c,i)}ja(c);c=1}return c|0}function xb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=M(0);a:{b:{if(!d){break b}c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=D[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=E[b>>1];b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=H[b>>1];b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=F[b>>2];b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=F[b>>2];b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}e=F[b>>2];if(F[b+4>>2]){break b}F[(g<<2)+d>>2]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}e=F[b>>2];if(F[b+4>>2]){break b}F[(g<<2)+d>>2]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 8:d:{e:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break e}e=F[a>>2];k=F[e>>2];g=k;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-k|0;if(!G[a+32|0]){k=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(4294967296)|m<M(0)|(M(N(m))==M(Infinity)|m!=m)){break d}e=(b<<2)+d|0;if(m<M(4294967296)&m>=M(0)){i=~~m>>>0}else{i=0}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}k=0;if((b|0)>=(e|0)){break d}b=0;while(1){m=J[g>>2];if(m>=M(4294967296)|m<M(0)|(M(N(m))==M(Infinity)|m!=m)){break d}if(m>M(1)){break d}e=(b<<2)+d|0;j=R(+m*4294967295+.5);f:{if(j<4294967296&j>=0){i=~~j>>>0;break f}i=0}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break e}g=g+4|0;if(f>>>0>g>>>0){continue}break}break d}k=1;if(c>>>0<=e>>>0){break d}ma((e<<2)+d|0,0,c-e<<2)}return k;case 9:g:{h:{e=G[a+24|0];c=c&255;if(!(c>>>0>e>>>0?e:c)){break h}e=F[a>>2];k=F[e>>2];g=k;f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+f|0;g=b+g|0;f=F[e+4>>2];e=f-k|0;if(!G[a+32|0]){k=0;if((b|0)>=(e|0)){break g}b=0;while(1){j=K[g>>3];if(j>=4294967295|j<0|(N(j)==Infinity|j!=j)){break g}e=(b<<2)+d|0;if(j<4294967296&j>=0){i=~~j>>>0}else{i=0}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}k=0;if((b|0)>=(e|0)){break g}b=0;while(1){j=K[g>>3];if(j>=4294967295|j<0|(N(j)==Infinity|j!=j)){break g}if(j>1){break g}e=(b<<2)+d|0;j=R(j*4294967295+.5);i:{if(j<4294967296&j>=0){i=~~j>>>0;break i}i=0}F[e>>2]=i;b=b+1|0;e=G[a+24|0];if(b>>>0>=(c>>>0>e>>>0?e:c)>>>0){break h}g=g+8|0;if(f>>>0>g>>>0){continue}break}break g}k=1;if(c>>>0<=e>>>0){break g}ma((e<<2)+d|0,0,c-e<<2)}return k;case 10:break c;default:break b}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];h=F[e>>2];l=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);i=b;b=b+l|0;b=b+h|0;h=F[e+4>>2];while(1){if(b>>>0>=h>>>0){break b}F[(g<<2)+d>>2]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}ma((e<<2)+d|0,0,(c&255)-e<<2)}return k}ma((e<<2)+d|0,0,(c&255)-e<<2);return 1}function rd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{b:{c:{d:{e:{if(F[a+92>>2]==F[a+88>>2]){break e}c=F[a+52>>2];f:{if((c|0)!=F[a+56>>2]){F[c>>2]=b;F[a+52>>2]=c+4;break f}h=F[a+48>>2];g=c-h|0;d=g>>2;f=d+1|0;if(f>>>0>=1073741824){break a}e=g>>>1|0;g=g>>>0>=2147483644?1073741823:f>>>0<e>>>0?e:f;if(g){if(g>>>0>=1073741824){break d}e=ka(g<<2)}else{e=0}f=e+(d<<2)|0;F[f>>2]=b;d=f+4|0;if((c|0)!=(h|0)){while(1){f=f-4|0;c=c-4|0;F[f>>2]=F[c>>2];if((c|0)!=(h|0)){continue}break}}F[a+56>>2]=e+(g<<2);F[a+52>>2]=d;F[a+48>>2]=f;if(!h){break f}ja(h)}F[a+84>>2]=0;c=-1;e=-1;g:{if((b|0)==-1){break g}d=F[a+4>>2];e=b+1|0;e=(e>>>0)%3|0?e:b-2|0;if((e|0)!=-1){c=F[F[d>>2]+(e<<2)>>2]}h:{if((b>>>0)%3|0){l=b-1|0;break h}l=b+2|0;e=-1;if((l|0)==-1){break g}}e=F[F[d>>2]+(l<<2)>>2]}i=e>>>3&536870908;d=F[a+36>>2];h=d+(c>>>3&536870908)|0;g=F[h>>2];f=1<<c;if(!(g&f)){F[h>>2]=f|g;f=a+8|0;if((b|0)!=-1){d=b+1|0;d=(d>>>0)%3|0?d:b-2|0}else{d=-1}Ka(f,c,d);d=F[a+36>>2]}f=d+i|0;d=F[f>>2];c=1<<e;if(!(d&c)){F[f>>2]=c|d;d=a+8|0;c=-1;i:{if((b|0)==-1){break i}c=b-1|0;if((b>>>0)%3|0){break i}c=b+2|0}Ka(d,e,c)}c=-1;c=(b|0)!=-1?F[F[F[a+4>>2]>>2]+(b<<2)>>2]:c;f=F[a+36>>2]+(c>>>3&536870908)|0;d=F[f>>2];e=1<<c;if(!(d&e)){F[f>>2]=d|e;Ka(a+8|0,c,b)}d=F[a+84>>2];if((d|0)>2){break e}while(1){e=L(d,12)+a|0;b=F[e+52>>2];if((b|0)==F[e+48>>2]){d=d+1|0;if((d|0)!=3){continue}break e}b=b-4|0;c=F[b>>2];F[e+52>>2]=b;F[a+84>>2]=d;if((c|0)==-1){break e}f=F[a+24>>2];b=(c>>>0)/3|0;j:{if(F[f+(b>>>3&268435452)>>2]>>>b&1){break j}k:{while(1){k=(c>>>0)/3|0;b=(k>>>3&268435452)+f|0;F[b>>2]=F[b>>2]|1<<k;d=-1;l:{m:{n:{o:{p:{q:{r:{s:{d=(c|0)!=-1?F[F[F[a+4>>2]>>2]+(c<<2)>>2]:d;f=F[a+36>>2]+(d>>>3&536870908)|0;e=F[f>>2];b=1<<d;if(!(e&b)){F[f>>2]=b|e;i=F[(F[F[a+16>>2]+96>>2]+L(k,12)|0)+((c>>>0)%3<<2)>>2];l=F[F[a+20>>2]+4>>2];f=F[l+4>>2];t:{if((f|0)!=F[l+8>>2]){F[f>>2]=i;F[l+4>>2]=f+4;break t}j=F[l>>2];h=f-j|0;g=h>>2;e=g+1|0;if(e>>>0>=1073741824){break s}b=h>>>1|0;h=h>>>0>=2147483644?1073741823:b>>>0>e>>>0?b:e;if(h){if(h>>>0>=1073741824){break d}e=ka(h<<2)}else{e=0}b=e+(g<<2)|0;F[b>>2]=i;g=b+4|0;if((f|0)!=(j|0)){while(1){b=b-4|0;f=f-4|0;F[b>>2]=F[f>>2];if((f|0)!=(j|0)){continue}break}}F[l+8>>2]=e+(h<<2);F[l+4>>2]=g;F[l>>2]=b;if(!j){break t}ja(j)}j=F[a+12>>2];f=F[j+4>>2];u:{if((f|0)!=F[j+8>>2]){F[f>>2]=c;F[j+4>>2]=f+4;break u}i=F[j>>2];h=f-i|0;g=h>>2;e=g+1|0;if(e>>>0>=1073741824){break r}b=h>>>1|0;h=h>>>0>=2147483644?1073741823:b>>>0>e>>>0?b:e;if(h){if(h>>>0>=1073741824){break d}e=ka(h<<2)}else{e=0}b=e+(g<<2)|0;F[b>>2]=c;g=b+4|0;if((f|0)!=(i|0)){while(1){b=b-4|0;f=f-4|0;F[b>>2]=F[f>>2];if((f|0)!=(i|0)){continue}break}}F[j+8>>2]=e+(h<<2);F[j+4>>2]=g;F[j>>2]=b;if(!i){break u}ja(i)}b=F[a+12>>2];F[F[b+12>>2]+(d<<2)>>2]=F[b+24>>2];F[b+24>>2]=F[b+24>>2]+1}if((c|0)==-1){break k}g=F[a+4>>2];f=-1;b=c+1|0;b=(b>>>0)%3|0?b:c-2|0;if((b|0)!=-1){f=F[F[g+12>>2]+(b<<2)>>2]}v:{w:{if((L(k,3)|0)!=(c|0)){d=c-1|0;break w}d=c+2|0;c=-1;if((d|0)==-1){break v}}c=F[F[g+12>>2]+(d<<2)>>2]}d=(c|0)==-1;e=(c>>>0)/3|0;if((f|0)!=-1){b=(f>>>0)/3|0;b=F[F[a+24>>2]+(b>>>3&268435452)>>2]&1<<b;if(d){break q}l=(b|0)!=0;break p}l=1;if(!d){break p}break k}na();v()}na();v()}if(!b){break o}break k}b=d?-1:e;x:{if(F[F[a+24>>2]+(b>>>3&536870908)>>2]>>>b&1){break x}k=0;b=F[F[g>>2]+(c<<2)>>2];if(!(F[F[a+36>>2]+(b>>>3&536870908)>>2]>>>b&1)){b=F[a+88>>2]+(b<<2)|0;e=F[b>>2];F[b>>2]=e+1;k=(e|0)<=0?2:1}if(F[a+84>>2]>=(k|0)&l){break m}j=L(k,12)+a|0;b=F[j+52>>2];y:{if((b|0)!=F[j+56>>2]){F[b>>2]=c;F[j+52>>2]=b+4;break y}i=F[j+48>>2];h=b-i|0;d=h>>2;g=d+1|0;if(g>>>0>=1073741824){break c}e=h>>>1|0;g=h>>>0>=2147483644?1073741823:e>>>0>g>>>0?e:g;if(g){if(g>>>0>=1073741824){break d}e=ka(g<<2)}else{e=0}d=e+(d<<2)|0;F[d>>2]=c;c=d+4|0;if((b|0)!=(i|0)){while(1){d=d-4|0;b=b-4|0;F[d>>2]=F[b>>2];if((b|0)!=(i|0)){continue}break}}F[j+48>>2]=d;F[j+52>>2]=c;F[j+56>>2]=e+(g<<2);if(!i){break y}ja(i)}if(F[a+84>>2]<=(k|0)){break x}F[a+84>>2]=k}if(l){break k}c=-1;if((f|0)==-1){break n}}c=F[F[F[a+4>>2]>>2]+(f<<2)>>2]}b=0;if(!(F[F[a+36>>2]+(c>>>3&536870908)>>2]>>>c&1)){b=F[a+88>>2]+(c<<2)|0;c=F[b>>2];F[b>>2]=c+1;b=(c|0)<=0?2:1}if(F[a+84>>2]<(b|0)){break l}c=f}f=F[a+24>>2];continue}break}k=L(b,12)+a|0;c=F[k+52>>2];z:{if((c|0)!=F[k+56>>2]){F[c>>2]=f;F[k+52>>2]=c+4;break z}i=F[k+48>>2];h=c-i|0;d=h>>2;g=d+1|0;if(g>>>0>=1073741824){break b}e=h>>>1|0;g=h>>>0>=2147483644?1073741823:e>>>0>g>>>0?e:g;if(g){if(g>>>0>=1073741824){break d}e=ka(g<<2)}else{e=0}d=e+(d<<2)|0;F[d>>2]=f;f=d+4|0;if((c|0)!=(i|0)){while(1){d=d-4|0;c=c-4|0;F[d>>2]=F[c>>2];if((c|0)!=(i|0)){continue}break}}F[k+48>>2]=d;F[k+52>>2]=f;F[k+56>>2]=e+(g<<2);if(!i){break z}ja(i)}d=F[a+84>>2];if((d|0)<=(b|0)){break j}F[a+84>>2]=b;d=b;break j}d=F[a+84>>2]}if((d|0)<3){continue}break}}return 1}oa();v()}na();v()}na();v()}na();v()}function Mc(a){var b=0,c=0,d=0,e=0,f=0,g=0;e=Z-16|0;Z=e;F[e+12>>2]=a;a:{if(a>>>0<=211){d=F[Lc(10352,10544,e+12|0)>>2];break a}if(a>>>0>=4294967292){V();v()}f=(a>>>0)/210|0;d=L(f,210);F[e+8>>2]=a-d;g=Lc(10544,10736,e+8|0)-10544>>2;while(1){d=F[(g<<2)+10544>>2]+d|0;a=5;while(1){b:{if((a|0)==47){a=211;while(1){b=(d>>>0)/(a>>>0)|0;if(b>>>0<a>>>0){break a}if((L(a,b)|0)==(d|0)){break b}b=a+10|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+12|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+16|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+18|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+22|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+28|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+30|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+36|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+40|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+42|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+46|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+52|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+58|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+60|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+66|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+70|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+72|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+78|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+82|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+88|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+96|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+100|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+102|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+106|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+108|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+112|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+120|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+126|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+130|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+136|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+138|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+142|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+148|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+150|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+156|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+162|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+166|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+168|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+172|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+178|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+180|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+186|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+190|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+192|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+196|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+198|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}if((L(b,c)|0)==(d|0)){break b}b=a+208|0;c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}a=a+210|0;if((L(b,c)|0)!=(d|0)){continue}break}break b}b=F[(a<<2)+10352>>2];c=(d>>>0)/(b>>>0)|0;if(b>>>0>c>>>0){break a}a=a+1|0;if((L(b,c)|0)!=(d|0)){continue}}break}d=g+1|0;a=(d|0)==48;g=a?0:d;f=a+f|0;d=L(f,210);continue}}Z=e+16|0;return d}function lb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=M(0),k=0,l=0;a:{if(!d){break a}b:{c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(D[b|0]);J[(h<<2)+d>>2]=i?M(j/M(127)):j;b=b+1|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(G[b|0]);J[(h<<2)+d>>2]=i?M(j/M(255)):j;b=b+1|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(E[b>>1]);J[(h<<2)+d>>2]=i?M(j/M(32767)):j;b=b+2|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(H[b>>1]);J[(h<<2)+d>>2]=i?M(j/M(65535)):j;b=b+2|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(F[b>>2]);J[(h<<2)+d>>2]=i?M(j*M(4.656612873077393e-10)):j;b=b+4|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(I[b>>2]);J[(h<<2)+d>>2]=i?M(j*M(2.3283064365386963e-10)):j;b=b+4|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(+I[b>>2]+ +F[b+4>>2]*4294967296);J[(h<<2)+d>>2]=i?M(j*M(10842021724855044e-35)):j;b=b+8|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];i=G[a+32|0];while(1){if(b>>>0>=g>>>0){break a}j=M(+I[b>>2]+ +I[b+4>>2]*4294967296);J[(h<<2)+d>>2]=i?M(j*M(5.421010862427522e-20)):j;b=b+8|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 8:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];while(1){if(b>>>0>=g>>>0){break a}J[(h<<2)+d>>2]=J[b>>2];b=b+4|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 9:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];while(1){if(b>>>0>=g>>>0){break a}J[(h<<2)+d>>2]=K[b>>3];b=b+8|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0;break b;case 10:break c;default:break a}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[a>>2];g=F[e>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);k=b;b=b+i|0;b=b+g|0;g=F[e+4>>2];while(1){if(b>>>0>=g>>>0){break a}J[(h<<2)+d>>2]=G[b|0]?M(1):M(0);b=b+1|0;h=h+1|0;e=G[a+24|0];if(h>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}l=1;if(e>>>0>=f>>>0){break a}d=(e<<2)+d|0;a=(c&255)-e|0}ma(d,0,a<<2)}return l}function Cb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=M(0),m=M(0);a:{b:{if(!d){break b}c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}D[d+g|0]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){return 0}e=D[b|0];if((e|0)<0){break b}D[d+g|0]=e;b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=H[b>>1];if((e+128&65535)>>>0>255){break b}D[d+g|0]=e;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=H[b>>1];if(e>>>0>127){break b}D[d+g|0]=e;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=F[b>>2];if(e+128>>>0>255){break b}D[d+g|0]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=F[b>>2];if(e>>>0>127){break b}D[d+g|0]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}i=F[b+4>>2];e=F[b>>2];h=e+128|0;i=h>>>0<128?i+1|0:i;if(!i&h>>>0>255|i){break b}D[d+g|0]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}i=F[b+4>>2];e=F[b>>2];if(!i&e>>>0>127|i){break b}D[d+g|0]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 8:e=G[a+24|0];c=c&255;d:{if(c>>>0>e>>>0?e:c){e=F[F[a>>2]>>2];f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+f|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break d}l=J[b>>2];if(l>=M(127)|l<M(-128)|l!=l){break d}m=M(N(l));if(m==M(Infinity)){break d}e=d+g|0;e:{f:{if(G[a+32|0]){if(l<M(0)|l>M(1)){break d}j=R(+l*127+.5);if(!(N(j)<2147483648)){break f}h=~~j;break e}if(!(m<M(2147483648))){break f}h=~~l;break e}h=-2147483648}D[e|0]=h;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(c>>>0>e>>>0?e:c)>>>0){continue}break}}k=1;if(c>>>0<=e>>>0){break d}ma(d+e|0,0,c-e|0)}return k;case 9:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}j=K[b>>3];if(j>=127|j<-128|(N(j)==Infinity|j!=j)){break b}e=d+g|0;if(G[a+32|0]){if(j<0|j>1){break b}j=R(j*127+.5)}g:{if(N(j)<2147483648){h=~~j;break g}h=-2147483648}D[e|0]=h;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 10:break c;default:break b}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}D[d+g|0]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}ma(d+e|0,0,(c&255)-e|0)}return k}ma(d+e|0,0,(c&255)-e|0);return 1}function Bb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=M(0);a:{b:{if(!d){break b}c:{switch(F[a+28>>2]-1|0){case 0:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){return 0}e=D[b|0];if((e|0)<0){break b}D[d+g|0]=e;b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 1:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}D[d+g|0]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 2:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=H[b>>1];if(e>>>0>255){break b}D[d+g|0]=e;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 3:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=H[b>>1];if(e>>>0>255){break b}D[d+g|0]=e;b=b+2|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 4:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=F[b>>2];if(e>>>0>255){break b}D[d+g|0]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 5:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}e=F[b>>2];if(e>>>0>255){break b}D[d+g|0]=e;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 6:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}i=F[b+4>>2];e=F[b>>2];if(!i&e>>>0>255|i){break b}D[d+g|0]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 7:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}i=F[b+4>>2];e=F[b>>2];if(!i&e>>>0>255|i){break b}D[d+g|0]=e;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 8:e=G[a+24|0];c=c&255;d:{if(c>>>0>e>>>0?e:c){e=F[F[a>>2]>>2];f=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+f|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break d}l=J[b>>2];if(l>=M(255)|l<M(0)|(M(N(l))==M(Infinity)|l!=l)){break d}e=d+g|0;e:{f:{if(G[a+32|0]){if(l>M(1)){break d}j=R(+l*255+.5);if(!(j<4294967296&j>=0)){break f}h=~~j>>>0;break e}if(!(l<M(4294967296)&l>=M(0))){break f}h=~~l>>>0;break e}h=0}D[e|0]=h;b=b+4|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(c>>>0>e>>>0?e:c)>>>0){continue}break}}k=1;if(c>>>0<=e>>>0){break d}ma(d+e|0,0,c-e|0)}return k;case 9:e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}j=K[b>>3];if(j>=255|j<0|(N(j)==Infinity|j!=j)){break b}e=d+g|0;if(G[a+32|0]){if(j>1){break b}j=R(j*255+.5)}g:{if(j<4294967296&j>=0){h=~~j>>>0;break g}h=0}D[e|0]=h;b=b+8|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}break a;case 10:break c;default:break b}}e=G[a+24|0];f=c&255;if(e>>>0<f>>>0?e:f){e=F[F[a>>2]>>2];i=F[a+48>>2];b=ki(F[a+40>>2],F[a+44>>2],b,0);h=b;b=b+i|0;b=b+e|0;while(1){if(I[F[a>>2]+4>>2]<=b>>>0){break b}D[d+g|0]=G[b|0];b=b+1|0;g=g+1|0;e=G[a+24|0];if(g>>>0<(e>>>0<f>>>0?e:f)>>>0){continue}break}}k=1;if(e>>>0>=f>>>0){break b}ma(d+e|0,0,(c&255)-e|0)}return k}ma(d+e|0,0,(c&255)-e|0);return 1}function jc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0;e=Z-48|0;Z=e;f=H[5053]|H[5054]<<16;d=H[5051]|H[5052]<<16;E[e+38>>1]=d;E[e+40>>1]=d>>>16;E[e+42>>1]=f;E[e+44>>1]=f>>>16;d=F[2525];F[e+32>>2]=F[2524];F[e+36>>2]=d;d=F[2523];F[e+24>>2]=F[2522];F[e+28>>2]=d;d=F[2521];F[e+16>>2]=F[2520];F[e+20>>2]=d;g=F[b+8>>2];i=F[b+12>>2];h=F[b+20>>2];d=F[b+16>>2];f=d+5|0;h=f>>>0<5?h+1|0:h;a:{b:{if(g>>>0<f>>>0&(h|0)>=(i|0)|(h|0)>(i|0)){d=ya(e+16|0);if(d>>>0>=2147483632){break a}c:{d:{if(d>>>0>=11){b=(d|15)+1|0;c=ka(b);F[e+8>>2]=b|-2147483648;F[e>>2]=c;F[e+4>>2]=d;b=c+d|0;break d}D[e+11|0]=d;b=d+e|0;c=e;if(!d){break c}}la(c,e+16|0,d)}D[b|0]=0;F[a>>2]=-2;b=a+4|0;if(D[e+11|0]>=0){a=F[e+4>>2];F[b>>2]=F[e>>2];F[b+4>>2]=a;F[b+8>>2]=F[e+8>>2];break b}ra(b,F[e>>2],F[e+4>>2]);if(D[e+11|0]>=0){break b}ja(F[e>>2]);break b}f=d+F[b>>2]|0;d=G[f|0]|G[f+1|0]<<8|(G[f+2|0]<<16|G[f+3|0]<<24);D[c|0]=d;D[c+1|0]=d>>>8;D[c+2|0]=d>>>16;D[c+3|0]=d>>>24;D[c+4|0]=G[f+4|0];d=F[b+20>>2];f=F[b+16>>2]+5|0;d=f>>>0<5?d+1|0:d;F[b+16>>2]=f;F[b+20>>2]=d;if(sa(c,1250,5)){d=ka(32);D[d+17|0]=0;D[d+16|0]=G[1494];c=G[1490]|G[1491]<<8|(G[1492]<<16|G[1493]<<24);b=G[1486]|G[1487]<<8|(G[1488]<<16|G[1489]<<24);D[d+8|0]=b;D[d+9|0]=b>>>8;D[d+10|0]=b>>>16;D[d+11|0]=b>>>24;D[d+12|0]=c;D[d+13|0]=c>>>8;D[d+14|0]=c>>>16;D[d+15|0]=c>>>24;c=G[1482]|G[1483]<<8|(G[1484]<<16|G[1485]<<24);b=G[1478]|G[1479]<<8|(G[1480]<<16|G[1481]<<24);D[d|0]=b;D[d+1|0]=b>>>8;D[d+2|0]=b>>>16;D[d+3|0]=b>>>24;D[d+4|0]=c;D[d+5|0]=c>>>8;D[d+6|0]=c>>>16;D[d+7|0]=c>>>24;F[a>>2]=-1;ra(a+4|0,d,17);ja(d);break b}g=F[b+12>>2];if((g|0)<=(d|0)&I[b+8>>2]<=f>>>0|(d|0)>(g|0)){d=ya(e+16|0);if(d>>>0>=2147483632){break a}e:{f:{if(d>>>0>=11){b=(d|15)+1|0;c=ka(b);F[e+8>>2]=b|-2147483648;F[e>>2]=c;F[e+4>>2]=d;b=c+d|0;break f}D[e+11|0]=d;b=d+e|0;c=e;if(!d){break e}}la(c,e+16|0,d)}D[b|0]=0;F[a>>2]=-2;b=a+4|0;if(D[e+11|0]>=0){a=F[e+4>>2];F[b>>2]=F[e>>2];F[b+4>>2]=a;F[b+8>>2]=F[e+8>>2];break b}ra(b,F[e>>2],F[e+4>>2]);if(D[e+11|0]>=0){break b}ja(F[e>>2]);break b}D[c+5|0]=G[f+F[b>>2]|0];g=F[b+20>>2];d=F[b+16>>2]+1|0;g=d?g:g+1|0;F[b+16>>2]=d;F[b+20>>2]=g;f=F[b+12>>2];if((f|0)<=(g|0)&I[b+8>>2]<=d>>>0|(g|0)>(f|0)){d=ya(e+16|0);if(d>>>0>=2147483632){break a}g:{h:{if(d>>>0>=11){b=(d|15)+1|0;c=ka(b);F[e+8>>2]=b|-2147483648;F[e>>2]=c;F[e+4>>2]=d;b=c+d|0;break h}D[e+11|0]=d;b=d+e|0;c=e;if(!d){break g}}la(c,e+16|0,d)}D[b|0]=0;F[a>>2]=-2;b=a+4|0;if(D[e+11|0]>=0){a=F[e+4>>2];F[b>>2]=F[e>>2];F[b+4>>2]=a;F[b+8>>2]=F[e+8>>2];break b}ra(b,F[e>>2],F[e+4>>2]);if(D[e+11|0]>=0){break b}ja(F[e>>2]);break b}D[c+6|0]=G[d+F[b>>2]|0];h=F[b+20>>2];d=F[b+16>>2]+1|0;h=d?h:h+1|0;F[b+16>>2]=d;F[b+20>>2]=h;f=F[b+12>>2];if((f|0)<=(h|0)&I[b+8>>2]<=d>>>0|(f|0)<(h|0)){d=ya(e+16|0);if(d>>>0>=2147483632){break a}i:{j:{if(d>>>0>=11){b=(d|15)+1|0;c=ka(b);F[e+8>>2]=b|-2147483648;F[e>>2]=c;F[e+4>>2]=d;b=c+d|0;break j}D[e+11|0]=d;b=d+e|0;c=e;if(!d){break i}}la(c,e+16|0,d)}D[b|0]=0;F[a>>2]=-2;b=a+4|0;if(D[e+11|0]>=0){a=F[e+4>>2];F[b>>2]=F[e>>2];F[b+4>>2]=a;F[b+8>>2]=F[e+8>>2];break b}ra(b,F[e>>2],F[e+4>>2]);if(D[e+11|0]>=0){break b}ja(F[e>>2]);break b}D[c+7|0]=G[d+F[b>>2]|0];g=F[b+20>>2];d=F[b+16>>2]+1|0;g=d?g:g+1|0;F[b+16>>2]=d;F[b+20>>2]=g;f=F[b+12>>2];if((f|0)<=(g|0)&I[b+8>>2]<=d>>>0|(g|0)>(f|0)){c=Eb(e,e+16|0);F[a>>2]=-2;b=a+4|0;if(D[c+11|0]>=0){a=F[c+4>>2];F[b>>2]=F[c>>2];F[b+4>>2]=a;F[b+8>>2]=F[c+8>>2];break b}ra(b,F[c>>2],F[c+4>>2]);if(D[c+11|0]>=0){break b}ja(F[c>>2]);break b}D[c+8|0]=G[d+F[b>>2]|0];d=F[b+20>>2];g=F[b+16>>2];f=g+1|0;i=f?d:d+1|0;F[b+16>>2]=f;F[b+20>>2]=i;i=F[b+8>>2];h=F[b+12>>2];g=g+3|0;d=g>>>0<3?d+1|0:d;if(g>>>0>i>>>0&(d|0)>=(h|0)|(d|0)>(h|0)){c=Eb(e,e+16|0);F[a>>2]=-2;b=a+4|0;if(D[c+11|0]>=0){a=F[c+4>>2];F[b>>2]=F[c>>2];F[b+4>>2]=a;F[b+8>>2]=F[c+8>>2];break b}ra(b,F[c>>2],F[c+4>>2]);if(D[c+11|0]>=0){break b}ja(F[c>>2]);break b}d=c;c=F[b>>2]+f|0;E[d+10>>1]=G[c|0]|G[c+1|0]<<8;g=F[b+20>>2];c=F[b+16>>2]+2|0;g=c>>>0<2?g+1|0:g;F[b+16>>2]=c;F[b+20>>2]=g;F[a+8>>2]=0;F[a+12>>2]=0;F[a>>2]=0;F[a+4>>2]=0}Z=e+48|0;return}za();v()}function Mb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0;e=Z-96|0;Z=e;f=F[a+16>>2];D[e+92|0]=1;F[e+88>>2]=b;F[e+84>>2]=b;F[e+80>>2]=f;j=F[a+20>>2];d=F[j>>2];a:{b:{f=F[F[f+28>>2]+(b<<2)>>2];if(f>>>0<F[j+4>>2]-d>>2>>>0){d=F[F[a+8>>2]+(F[d+(f<<2)>>2]<<2)>>2];f=F[a+4>>2];if(!G[f+84|0]){d=F[F[f+68>>2]+(d<<2)>>2]}F[e+72>>2]=0;F[e+76>>2]=0;j=e- -64|0;F[j>>2]=0;F[j+4>>2]=0;F[e+56>>2]=0;F[e+60>>2]=0;Ga(f,d,D[f+24|0],e+56|0);if((b|0)!=-1){f=b+1|0;j=(f>>>0)%3|0?f:b-2|0;m=((b>>>0)%3|0?-1:2)+b|0;while(1){d=j;f=m;c:{if(!F[a+28>>2]){break c}f=b+1|0;d=(f>>>0)%3|0?f:b-2|0;f=b-1|0;if((b>>>0)%3|0){break c}f=b+2|0}n=F[a+20>>2];b=F[n>>2];d=F[F[F[a+16>>2]+28>>2]+(d<<2)>>2];if(d>>>0>=F[n+4>>2]-b>>2>>>0){break b}d=F[F[a+8>>2]+(F[b+(d<<2)>>2]<<2)>>2];b=F[a+4>>2];if(!G[b+84|0]){d=F[F[b+68>>2]+(d<<2)>>2]}F[e+48>>2]=0;F[e+52>>2]=0;F[e+40>>2]=0;F[e+44>>2]=0;F[e+32>>2]=0;F[e+36>>2]=0;Ga(b,d,D[b+24|0],e+32|0);d=F[a+20>>2];b=F[d>>2];f=F[F[F[a+16>>2]+28>>2]+(f<<2)>>2];if(f>>>0>=F[d+4>>2]-b>>2>>>0){break a}d=F[F[a+8>>2]+(F[b+(f<<2)>>2]<<2)>>2];b=F[a+4>>2];if(!G[b+84|0]){d=F[F[b+68>>2]+(d<<2)>>2]}F[e+24>>2]=0;F[e+28>>2]=0;F[e+16>>2]=0;F[e+20>>2]=0;F[e+8>>2]=0;F[e+12>>2]=0;Ga(b,d,D[b+24|0],e+8|0);g=F[e+8>>2];b=F[e+56>>2];d=g-b|0;p=F[e+60>>2];t=F[e+12>>2]-(p+(b>>>0>g>>>0)|0)|0;h=F[e+40>>2];f=F[e+64>>2];n=h-f|0;u=F[e+68>>2];y=F[e+44>>2]-(u+(f>>>0>h>>>0)|0)|0;g=ki(d,t,n,y);w=o-g|0;x=i-(_+(g>>>0>o>>>0)|0)|0;i=w;h=F[e+16>>2];g=h-f|0;u=F[e+20>>2]-((f>>>0>h>>>0)+u|0)|0;k=F[e+32>>2];h=k-b|0;w=F[e+36>>2]-((b>>>0>k>>>0)+p|0)|0;b=ki(g,u,h,w);o=i+b|0;i=_+x|0;i=b>>>0>o>>>0?i+1|0:i;b=l;l=d;p=t;k=F[e+48>>2];f=F[e+72>>2];d=k-f|0;t=F[e+76>>2];x=F[e+52>>2]-(t+(f>>>0>k>>>0)|0)|0;l=ki(l,p,d,x);k=b+l|0;b=_+q|0;b=k>>>0<l>>>0?b+1|0:b;l=F[e+24>>2];p=l-f|0;f=F[e+28>>2]-((f>>>0>l>>>0)+t|0)|0;q=ki(p,f,h,w);l=k-q|0;q=b-(_+(k>>>0<q>>>0)|0)|0;b=ki(g,u,d,x);d=r-b|0;b=s-(_+(b>>>0>r>>>0)|0)|0;s=ki(p,f,n,y);r=s+d|0;b=_+b|0;s=r>>>0<s>>>0?b+1|0:b;b=F[e+88>>2];f=F[e+80>>2];d:{if(G[e+92|0]){e:{f:{g:{h:{if((b|0)==-1){break h}d=b+1|0;b=(d>>>0)%3|0?d:b-2|0;if((b|0)==-1|F[F[f>>2]+(b>>>3&536870908)>>2]>>>b&1){break h}b=F[F[F[f+64>>2]+12>>2]+(b<<2)>>2];if((b|0)!=-1){break g}}F[e+88>>2]=-1;break f}d=b+1|0;b=(d>>>0)%3|0?d:b-2|0;F[e+88>>2]=b;if((b|0)!=-1){break e}}b=F[e+84>>2];d=-1;i:{if((b|0)==-1){break i}j:{if((b>>>0)%3|0){b=b-1|0;break j}b=b+2|0;d=-1;if((b|0)==-1){break i}}d=-1;if(F[F[f>>2]+(b>>>3&536870908)>>2]>>>b&1){break i}b=F[F[F[f+64>>2]+12>>2]+(b<<2)>>2];d=-1;if((b|0)==-1){break i}d=b-1|0;if((b>>>0)%3|0){break i}d=b+2|0}D[e+92|0]=0;F[e+88>>2]=d;break d}if((b|0)!=F[e+84>>2]){break d}F[e+88>>2]=-1;break d}d=-1;k:{if((b|0)==-1){break k}l:{if((b>>>0)%3|0){b=b-1|0;break l}b=b+2|0;d=-1;if((b|0)==-1){break k}}d=-1;if(F[F[f>>2]+(b>>>3&536870908)>>2]>>>b&1){break k}b=F[F[F[f+64>>2]+12>>2]+(b<<2)>>2];d=-1;if((b|0)==-1){break k}d=b-1|0;if((b>>>0)%3|0){break k}d=b+2|0}F[e+88>>2]=d}b=F[e+88>>2];if((b|0)!=-1){continue}break}}b=s>>31;f=b^r;d=f-b|0;b=(b^s)-((b>>>0>f>>>0)+b|0)|0;m=-1;f=2147483647;g=q>>31;h=g^l;j=h-g|0;n=(g^q)-((h>>>0<g>>>0)+g|0)|0;h=n;k=j^-1;g=h^2147483647;n=i;m:{n:{if(!F[a+28>>2]){if((b|0)==(g|0)&d>>>0>k>>>0|b>>>0>g>>>0){break m}b=b+h|0;a=d+j|0;b=a>>>0<j>>>0?b+1|0:b;f=a;g=i;a=g>>31;d=a;m=d^o;a=m-d|0;i=a;d=(d^g)-((d>>>0>m>>>0)+d|0)|0;a=a+f|0;d=d^2147483647;i=(d|0)==(b|0)&(i^-1)>>>0<f>>>0|b>>>0>d>>>0;a=i?-1:a;if(!(i&0)&(a|0)<=536870912|(a|0)<536870912){break m}b=0;a=a>>>29|0;break n}o:{if((b|0)==(g|0)&d>>>0>k>>>0|b>>>0>g>>>0){break o}b=b+h|0;a=d+j|0;b=a>>>0<j>>>0?b+1|0:b;k=i;d=i>>31;h=d^o;i=h-d|0;j=(d^k)-((d>>>0>h>>>0)+d|0)|0;g=j^2147483647;d=a;a=i;if((g|0)==(b|0)&d>>>0>(a^-1)>>>0|b>>>0>g>>>0){break o}b=b+j|0;m=a+d|0;b=m>>>0<a>>>0?b+1|0:b;f=b;if(!b&m>>>0<536870913){break m}}b=f>>>29|0;a=(f&536870911)<<3|m>>>29}o=li(o,n,a,b);l=li(l,q,a,b);r=li(r,s,a,b)}F[c+8>>2]=o;F[c+4>>2]=l;F[c>>2]=r;Z=e+96|0;return}ta();v()}ta();v()}ta();v()}function te(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;g=Z-16|0;Z=g;f=1;m=$[F[F[a>>2]+24>>2]](a)|0;a:{if((m|0)<=0){break a}r=a+48|0;f=0;while(1){b:{c:{if(!F[($[F[F[a>>2]+28>>2]](a)|0)+40>>2]){break c}o=l<<2;d=F[o+F[a+36>>2]>>2];c=F[d+8>>2];e=bb(d);if(!e){break c}h=F[($[F[F[a>>2]+28>>2]](a)|0)+40>>2];F[g+12>>2]=F[c+56>>2];d=ka(32);F[g>>2]=d;F[g+4>>2]=24;F[g+8>>2]=-2147483616;c=G[1196]|G[1197]<<8|(G[1198]<<16|G[1199]<<24);b=G[1192]|G[1193]<<8|(G[1194]<<16|G[1195]<<24);D[d+16|0]=b;D[d+17|0]=b>>>8;D[d+18|0]=b>>>16;D[d+19|0]=b>>>24;D[d+20|0]=c;D[d+21|0]=c>>>8;D[d+22|0]=c>>>16;D[d+23|0]=c>>>24;c=G[1188]|G[1189]<<8|(G[1190]<<16|G[1191]<<24);b=G[1184]|G[1185]<<8|(G[1186]<<16|G[1187]<<24);D[d+8|0]=b;D[d+9|0]=b>>>8;D[d+10|0]=b>>>16;D[d+11|0]=b>>>24;D[d+12|0]=c;D[d+13|0]=c>>>8;D[d+14|0]=c>>>16;D[d+15|0]=c>>>24;c=G[1180]|G[1181]<<8|(G[1182]<<16|G[1183]<<24);b=G[1176]|G[1177]<<8|(G[1178]<<16|G[1179]<<24);D[d|0]=b;D[d+1|0]=b>>>8;D[d+2|0]=b>>>16;D[d+3|0]=b>>>24;D[d+4|0]=c;D[d+5|0]=c>>>8;D[d+6|0]=c>>>16;D[d+7|0]=c>>>24;D[d+24|0]=0;c=h+16|0;b=F[c>>2];d:{e:{if(!b){break e}i=F[g+12>>2];d=c;while(1){k=(i|0)>F[b+16>>2];d=k?d:b;b=F[(k?b+4|0:b)>>2];if(b){continue}break}if((c|0)==(d|0)|(i|0)<F[d+16>>2]){break e}b=F[d+24>>2];if(!b){break e}i=d+20|0;d=G[g+11|0];c=d<<24>>24<0;k=c?F[g>>2]:g;d=c?F[g+4>>2]:d;while(1){c=G[b+27|0];j=c<<24>>24<0;c=j?F[b+20>>2]:c;p=c>>>0<d>>>0;f:{g:{h:{i:{j:{k:{n=p?c:d;if(n){j=j?F[b+16>>2]:b+16|0;q=sa(k,j,n);if(q){break k}if(c>>>0<=d>>>0){break j}break f}if(c>>>0<=d>>>0){break i}break f}if((q|0)<0){break f}}c=sa(j,k,n);if(c){break h}}if(p){break g}d=gc(i,g);break d}if((c|0)<0){break g}d=gc(i,g);break d}b=b+4|0}b=F[b>>2];if(b){continue}break}}d=gc(h,g)}if(D[g+11|0]<0){ja(F[g>>2])}if(!d){break c}d=0;c=F[F[o+F[a+36>>2]>>2]+8>>2];if(!F[c+64>>2]){b=ka(32);F[b+16>>2]=0;F[b+20>>2]=0;F[b+8>>2]=0;F[b>>2]=0;F[b+4>>2]=0;F[b+24>>2]=0;F[b+28>>2]=0;f=F[c+64>>2];F[c+64>>2]=b;if(f){b=F[f>>2];if(b){F[f+4>>2]=b;ja(b)}ja(f);b=F[c+64>>2]}F[c>>2]=b;f=F[b+20>>2];F[c+8>>2]=F[b+16>>2];F[c+12>>2]=f;f=F[b+24>>2];b=F[b+28>>2];F[c+48>>2]=0;F[c+52>>2]=0;F[c+40>>2]=0;F[c+44>>2]=0;F[c+16>>2]=f;F[c+20>>2]=b}l:{D[c+24|0]=G[e+24|0];F[c+28>>2]=F[e+28>>2];D[c+32|0]=G[e+32|0];b=F[e+44>>2];F[c+40>>2]=F[e+40>>2];F[c+44>>2]=b;b=F[e+52>>2];F[c+48>>2]=F[e+48>>2];F[c+52>>2]=b;F[c+56>>2]=F[e+56>>2];b=F[e+12>>2];F[c+8>>2]=F[e+8>>2];F[c+12>>2]=b;b=F[e+20>>2];F[c+16>>2]=F[e+16>>2];F[c+20>>2]=b;F[c+60>>2]=F[e+60>>2];f=F[e>>2];m:{if(!f){F[c>>2]=0;b=1;break m}h=F[c>>2];b=0;if(!h){break m}b=F[f>>2];f=F[f+4>>2]-b|0;md(h,b,f,0);b=1}if(!b){break l}D[c+84|0]=G[e+84|0];F[c+80>>2]=F[e+80>>2];if((c|0)!=(e|0)){gb(c+68|0,F[e+68>>2],F[e+72>>2])}n:{h=F[e+88>>2];o:{if(h){f=ka(40);e=F[h>>2];F[f+16>>2]=0;F[f+8>>2]=0;F[f+12>>2]=0;F[f>>2]=e;e=F[h+12>>2];b=F[h+8>>2];if((e|0)!=(b|0)){b=e-b|0;if((b|0)<0){break n}e=ka(b);F[f+12>>2]=e;F[f+8>>2]=e;F[f+16>>2]=b+e;b=F[h+8>>2];i=F[h+12>>2];p:{if((b|0)==(i|0)){break p}k=i+(b^-1)|0;j=i-b&7;if(j){while(1){D[e|0]=G[b|0];e=e+1|0;b=b+1|0;d=d+1|0;if((j|0)!=(d|0)){continue}break}}if(k>>>0<7){break p}while(1){D[e|0]=G[b|0];D[e+1|0]=G[b+1|0];D[e+2|0]=G[b+2|0];D[e+3|0]=G[b+3|0];D[e+4|0]=G[b+4|0];D[e+5|0]=G[b+5|0];D[e+6|0]=G[b+6|0];D[e+7|0]=G[b+7|0];e=e+8|0;b=b+8|0;if((i|0)!=(b|0)){continue}break}}F[f+12>>2]=e}d=F[h+36>>2];F[f+32>>2]=F[h+32>>2];F[f+36>>2]=d;d=F[h+28>>2];F[f+24>>2]=F[h+24>>2];F[f+28>>2]=d;e=F[c+88>>2];F[c+88>>2]=f;if(e){break o}break l}e=F[c+88>>2];F[c+88>>2]=0;if(!e){break l}}d=F[e+8>>2];if(d){F[e+12>>2]=d;ja(d)}ja(e);break l}na();v()}break b}d=F[F[a+36>>2]+(l<<2)>>2];if(!($[F[F[d>>2]+24>>2]](d,r)|0)){break a}}l=l+1|0;f=(m|0)<=(l|0);if((l|0)!=(m|0)){continue}break}}Z=g+16|0;return f|0}function Jg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;f=Z-32|0;Z=f;a:{if(!hb(1,f+28|0,F[a+32>>2])){break a}if(!hb(1,f+24|0,F[a+32>>2])){break a}l=F[f+28>>2];if(l>>>0>1431655765){break a}d=F[a+32>>2];c=d;i=F[c+8>>2];b=F[c+16>>2];h=F[c+12>>2];c=F[c+20>>2];g=li(i-b|0,h-(c+(b>>>0>i>>>0)|0)|0,3,0);if(!_&g>>>0<l>>>0){break a}n=F[f+24>>2];g=ki(l,0,3,0);if(!_&g>>>0<n>>>0|((c|0)>=(h|0)&b>>>0>=i>>>0|(c|0)>(h|0))){break a}i=G[b+F[d>>2]|0];b=b+1|0;c=b?c:c+1|0;F[d+16>>2]=b;F[d+20>>2]=c;b:{c:{if(!i){d=0;c=Z-32|0;Z=c;F[c+24>>2]=0;F[c+16>>2]=0;F[c+20>>2]=0;d:{e:{b=L(l,3);if(b){if(b>>>0>=1073741824){break e}i=L(l,12);d=ka(i);ma(d,0,i)}b=mc(b,1,F[a+32>>2],d);f:{g:{if(!(!l|!b)){i=0;while(1){h:{g=e;b=(i<<2)+d|0;h=F[b>>2];e=h>>>1|0;h=g+(h&1?0-e|0:e)|0;if((h|0)<0){break h}F[c>>2]=h;e=F[b+4>>2];g=e>>>1|0;h=h+(e&1?0-g|0:g)|0;if((h|0)<0){break h}F[c+4>>2]=h;b=F[b+8>>2];e=b>>>1|0;e=h+(b&1?0-e|0:e)|0;if((e|0)<0){break h}F[c+8>>2]=e;mb(F[a+44>>2]+96|0,c);i=i+3|0;b=1;j=j+1|0;if((j|0)!=(l|0)){continue}break g}break}b=0;break g}if(!d){break f}}ja(d)}Z=c+32|0;break d}na();v()}if(b){break c}break a}if(n>>>0<=255){if(!l){break c}while(1){i:{F[f+16>>2]=0;F[f+8>>2]=0;F[f+12>>2]=0;d=F[a+32>>2];b=d;i=F[b+16>>2];e=F[b+8>>2];c=F[b+20>>2];g=F[b+12>>2];b=g;if(e>>>0<=i>>>0&(c|0)>=(b|0)|(b|0)<(c|0)){break i}j=F[d>>2];m=G[j+i|0];b=c;h=i+1|0;b=h?b:b+1|0;F[d+16>>2]=h;F[d+20>>2]=b;F[f+8>>2]=m;m=e>>>0<i>>>0&(c|0)>=(g|0)|(c|0)>(g|0);e=m?i:e;g=m?c:g;if((e|0)==(h|0)&(g|0)==(b|0)){break i}m=G[h+j|0];b=c;h=i+2|0;b=h>>>0<2?b+1|0:b;F[d+16>>2]=h;F[d+20>>2]=b;F[f+12>>2]=m;if((e|0)==(h|0)&(b|0)==(g|0)){break i}h=G[h+j|0];b=c;c=i+3|0;b=c>>>0<3?b+1|0:b;F[d+16>>2]=c;F[d+20>>2]=b;F[f+16>>2]=h;mb(F[a+44>>2]+96|0,f+8|0);k=k+1|0;if((l|0)!=(k|0)){continue}break c}break}k=0;break a}if(n>>>0<=65535){if(!l){break c}while(1){j:{F[f+16>>2]=0;F[f+8>>2]=0;F[f+12>>2]=0;j=F[a+32>>2];b=j;c=F[b+8>>2];d=F[b+12>>2];h=F[b+16>>2];b=F[b+20>>2];i=b;e=h+2|0;b=e>>>0<2?b+1|0:b;if(c>>>0<e>>>0&(b|0)>=(d|0)|(b|0)>(d|0)){break j}m=F[j>>2];g=m+h|0;g=G[g|0]|G[g+1|0]<<8;F[j+16>>2]=e;F[j+20>>2]=b;F[f+8>>2]=g;b=i;g=h+4|0;b=g>>>0<4?b+1|0:b;if(c>>>0<g>>>0&(b|0)>=(d|0)|(b|0)>(d|0)){break j}e=e+m|0;e=G[e|0]|G[e+1|0]<<8;F[j+16>>2]=g;F[j+20>>2]=b;F[f+12>>2]=e;e=c;b=i;c=h+6|0;b=c>>>0<6?b+1|0:b;if(c>>>0>e>>>0&(b|0)>=(d|0)|(b|0)>(d|0)){break j}d=g+m|0;d=G[d|0]|G[d+1|0]<<8;F[j+16>>2]=c;F[j+20>>2]=b;F[f+16>>2]=d;mb(F[a+44>>2]+96|0,f+8|0);k=k+1|0;if((l|0)!=(k|0)){continue}break c}break}k=0;break a}k:{if(n>>>0>2097151){break k}b=H[a+36>>1];if(((b<<8|b>>>8)&65535)>>>0<514){break k}if(!l){break c}while(1){l:{F[f+16>>2]=0;F[f+8>>2]=0;F[f+12>>2]=0;if(!hb(1,f+4|0,F[a+32>>2])){break l}F[f+8>>2]=F[f+4>>2];if(!hb(1,f+4|0,F[a+32>>2])){break l}F[f+12>>2]=F[f+4>>2];if(!hb(1,f+4|0,F[a+32>>2])){break l}F[f+16>>2]=F[f+4>>2];mb(F[a+44>>2]+96|0,f+8|0);k=k+1|0;if((l|0)!=(k|0)){continue}break c}break}k=0;break a}if(!l){break c}while(1){F[f+16>>2]=0;F[f+8>>2]=0;F[f+12>>2]=0;j=F[a+32>>2];b=j;c=F[b+8>>2];d=F[b+12>>2];h=F[b+16>>2];b=F[b+20>>2];i=b;e=h+4|0;b=e>>>0<4?b+1|0:b;if(c>>>0<e>>>0&(b|0)>=(d|0)|(b|0)>(d|0)){break b}m=F[j>>2];g=m+h|0;g=G[g|0]|G[g+1|0]<<8|(G[g+2|0]<<16|G[g+3|0]<<24);F[j+16>>2]=e;F[j+20>>2]=b;F[f+8>>2]=g;b=i;g=h+8|0;b=g>>>0<8?b+1|0:b;if(c>>>0<g>>>0&(b|0)>=(d|0)|(b|0)>(d|0)){break b}e=e+m|0;e=G[e|0]|G[e+1|0]<<8|(G[e+2|0]<<16|G[e+3|0]<<24);F[j+16>>2]=g;F[j+20>>2]=b;F[f+12>>2]=e;e=c;b=i;c=h+12|0;b=c>>>0<12?b+1|0:b;if(c>>>0>e>>>0&(b|0)>=(d|0)|(b|0)>(d|0)){break b}d=g+m|0;d=G[d|0]|G[d+1|0]<<8|(G[d+2|0]<<16|G[d+3|0]<<24);F[j+16>>2]=c;F[j+20>>2]=b;F[f+16>>2]=d;mb(F[a+44>>2]+96|0,f+8|0);k=k+1|0;if((l|0)!=(k|0)){continue}break}}F[F[a+4>>2]+80>>2]=n;k=1;break a}k=0}Z=f+32|0;return k|0}function Ld(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0;g=Z+-64|0;Z=g;F[g+56>>2]=0;F[g+48>>2]=0;F[g+52>>2]=0;F[g+40>>2]=0;F[g+44>>2]=0;F[g+32>>2]=0;F[g+36>>2]=0;F[g+24>>2]=0;F[g+28>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;F[g+8>>2]=0;F[g+12>>2]=0;h=g+8|0;a:{b:{if(!H[b+38>>1]){break b}if(!Ta(1,h+12|0,b)){break b}e=F[b+8>>2];f=F[b+16>>2];j=e-f|0;k=F[h+12>>2];e=F[b+12>>2]-(F[b+20>>2]+(e>>>0<f>>>0)|0)|0;if(j>>>0<k>>>6>>>0&(e|0)<=0|(e|0)<0){break b}e=F[h>>2];d=F[h+4>>2]-e>>2;c:{if(d>>>0<k>>>0){qa(h,k-d|0);k=F[h+12>>2];break c}if(d>>>0<=k>>>0){break c}F[h+4>>2]=e+(k<<2)}i=1;if(!k){break a}e=F[b+16>>2];d=F[b+20>>2];r=F[h>>2];l=F[b+8>>2];o=F[b+12>>2];j=0;while(1){i=0;if((d|0)>=(o|0)&e>>>0>=l>>>0|(d|0)>(o|0)){break a}i=F[b>>2];p=G[i+e|0];e=e+1|0;d=e?d:d+1|0;F[b+16>>2]=e;F[b+20>>2]=d;f=p>>>2|0;m=0;d:{e:{f:{g:{s=p&3;switch(s|0){case 3:break g;case 0:break e;default:break f}}f=f+j|0;i=0;if(f>>>0>=k>>>0){break a}ma(r+(j<<2)|0,0,(p&252)+4|0);j=f;break d}while(1){if((e|0)==(l|0)&(d|0)==(o|0)){break b}k=G[e+i|0];e=e+1|0;d=e?d:d+1|0;F[b+16>>2]=e;F[b+20>>2]=d;f=k<<(m<<3|6)|f;m=m+1|0;if((s|0)!=(m|0)){continue}break}}F[r+(j<<2)>>2]=f}j=j+1|0;k=F[h+12>>2];if(j>>>0<k>>>0){continue}break}d=h+16|0;o=F[h>>2];f=F[h+16>>2];e=F[h+20>>2]-f|0;h:{if(e>>>0<=4194303){qa(d,1048576-(e>>>2|0)|0);break h}if((e|0)==4194304){break h}F[h+20>>2]=f+4194304}e=h+28|0;j=F[e>>2];f=F[h+32>>2]-j>>3;i:{if(f>>>0<k>>>0){_a(e,k-f|0);j=F[e>>2];break i}if(f>>>0>k>>>0){F[h+32>>2]=(k<<3)+j}if(!k){break b}}l=F[d>>2];d=0;i=0;while(1){e=o+(d<<2)|0;h=F[e>>2];m=(d<<3)+j|0;f=i;F[m+4>>2]=f;F[m>>2]=h;e=F[e>>2];i=e+f|0;if(i>>>0>1048576){break b}j:{if(f>>>0>=i>>>0){break j}m=0;h=e&7;if(h){while(1){F[l+(f<<2)>>2]=d;f=f+1|0;m=m+1|0;if((h|0)!=(m|0)){continue}break}}if(e-1>>>0<=6){break j}while(1){e=l+(f<<2)|0;F[e>>2]=d;F[e+28>>2]=d;F[e+24>>2]=d;F[e+20>>2]=d;F[e+16>>2]=d;F[e+12>>2]=d;F[e+8>>2]=d;F[e+4>>2]=d;f=f+8|0;if((i|0)!=(f|0)){continue}break}}d=d+1|0;if((k|0)!=(d|0)){continue}break}n=(i|0)==1048576}i=n}k:{if(!i|(F[g+20>>2]?0:a)){break k}i=0;j=Z-16|0;Z=j;l:{if(!Sa(1,j+8|0,b)){break l}d=F[b+8>>2];f=F[b+16>>2];l=d-f|0;n=F[j+12>>2];h=F[b+20>>2];d=F[b+12>>2]-(h+(d>>>0<f>>>0)|0)|0;e=F[j+8>>2];if((n|0)==(d|0)&e>>>0>l>>>0|d>>>0<n>>>0){break l}d=h+n|0;l=e+f|0;d=l>>>0<f>>>0?d+1|0:d;F[b+16>>2]=l;F[b+20>>2]=d;if((e|0)<=0){break l}b=f+F[b>>2]|0;F[g+48>>2]=b;d=e-1|0;f=d+b|0;l=G[f|0];m:{if(l>>>0<=63){F[g+52>>2]=d;b=G[f|0]&63;break m}n:{switch((l>>>6|0)-1|0){case 0:if(e>>>0<2){break l}d=e-2|0;F[g+52>>2]=d;b=b+d|0;b=G[b+1|0]<<8&16128|G[b|0];break m;case 1:if(e>>>0<3){break l}d=e-3|0;F[g+52>>2]=d;b=b+d|0;b=G[b+1|0]<<8|G[b+2|0]<<16&4128768|G[b|0];break m;default:break n}}d=e-4|0;F[g+52>>2]=d;b=b+d|0;b=(G[b|0]|G[b+1|0]<<8|(G[b+2|0]<<16|G[b+3|0]<<24))&1073741823}F[g+56>>2]=b+4194304;i=b>>>0<1069547520}Z=j+16|0;if(!i){break k}if(!a){t=1;break k}b=F[g+52>>2];f=F[g+56>>2];d=F[g+36>>2];e=F[g+48>>2];j=F[g+24>>2];while(1){o:{if(f>>>0>4194303){break o}while(1){if((b|0)<=0){break o}b=b-1|0;F[g+52>>2]=b;f=G[b+e|0]|f<<8;F[g+56>>2]=f;if(f>>>0<4194304){continue}break}}i=f&1048575;l=F[j+(i<<2)>>2];n=d+(l<<3)|0;f=(L(F[n>>2],f>>>20|0)+i|0)-F[n+4>>2]|0;F[g+56>>2]=f;F[(q<<2)+c>>2]=l;t=1;q=q+1|0;if((q|0)!=(a|0)){continue}break}}a=F[g+36>>2];if(a){F[g+40>>2]=a;ja(a)}a=F[g+24>>2];if(a){F[g+28>>2]=a;ja(a)}a=F[g+8>>2];if(a){F[g+12>>2]=a;ja(a)}Z=g- -64|0;return t}function kh(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=M(0),f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=M(0),p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0;if(F[c>>2]==F[c+4>>2]){m=F[d+80>>2];u=Z-16|0;Z=u;g=F[a+4>>2];k=G[b+24|0];h=F[d+48>>2];n=F[F[d>>2]>>2];c=u+8|0;F[c>>2]=1065353216;d=c;J[c>>2]=M(-1<<g^-1)/J[a+20>>2];c=ka(k<<2);a:{if(!m|!k){break a}p=h+n|0;o=J[d>>2];n=F[a+8>>2];v=F[b>>2];d=F[b+48>>2];g=F[b+40>>2];w=F[b+44>>2];if(!G[b+84|0]){f=F[b+68>>2];s=k&254;t=k&1;a=0;while(1){b=F[v>>2];l=ki(g,w,F[f+(i<<2)>>2],0)+d|0;h=la(c,b+l|0,g);b=0;q=0;if((k|0)!=1){while(1){l=p+(a<<2)|0;j=b<<2;e=M(R(M(M(o*M(J[j+h>>2]-J[n+j>>2]))+M(.5))));b:{if(M(N(e))<M(2147483648)){r=~~e;break b}r=-2147483648}F[l>>2]=r;j=j|4;e=M(R(M(M(o*M(J[j+h>>2]-J[n+j>>2]))+M(.5))));c:{if(M(N(e))<M(2147483648)){j=~~e;break c}j=-2147483648}F[l+4>>2]=j;b=b+2|0;a=a+2|0;q=q+2|0;if((s|0)!=(q|0)){continue}break}}if(t){l=p+(a<<2)|0;b=b<<2;e=M(R(M(M(o*M(J[b+h>>2]-J[b+n>>2]))+M(.5))));d:{if(M(N(e))<M(2147483648)){b=~~e;break d}b=-2147483648}F[l>>2]=b;a=a+1|0}i=i+1|0;if((m|0)!=(i|0)){continue}break}break a}s=k&254;t=k&1;a=0;while(1){b=F[v>>2];h=ki(g,w,i,l)+d|0;j=la(c,b+h|0,g);b=0;q=0;if((k|0)!=1){while(1){h=p+(a<<2)|0;f=b<<2;e=M(R(M(M(o*M(J[f+j>>2]-J[f+n>>2]))+M(.5))));e:{if(M(N(e))<M(2147483648)){r=~~e;break e}r=-2147483648}F[h>>2]=r;f=f|4;e=M(R(M(M(o*M(J[f+j>>2]-J[f+n>>2]))+M(.5))));f:{if(M(N(e))<M(2147483648)){f=~~e;break f}f=-2147483648}F[h+4>>2]=f;b=b+2|0;a=a+2|0;q=q+2|0;if((s|0)!=(q|0)){continue}break}}if(t){h=p+(a<<2)|0;b=b<<2;e=M(R(M(M(o*M(J[b+j>>2]-J[b+n>>2]))+M(.5))));g:{if(M(N(e))<M(2147483648)){b=~~e;break g}b=-2147483648}F[h>>2]=b;a=a+1|0}b=l;i=i+1|0;b=i?b:b+1|0;l=b;if((i|0)!=(m|0)|b){continue}break}}ja(c);Z=u+16|0;return 1}j=Z-16|0;Z=j;m=F[a+4>>2];i=G[b+24|0];g=F[d+48>>2];h=F[F[d>>2]>>2];d=j+8|0;F[d>>2]=1065353216;l=d;J[d>>2]=M(-1<<m^-1)/J[a+20>>2];d=ka(i<<2);m=F[c+4>>2];q=F[c>>2];h:{if(!i|(m|0)==(q|0)){break h}n=h+g|0;c=m-q>>2;u=c>>>0<=1?1:c;o=J[l>>2];h=F[a+8>>2];v=F[b>>2];l=F[b+48>>2];m=F[b+40>>2];w=F[b+44>>2];if(G[b+84|0]){s=i&254;t=i&1;a=0;c=0;while(1){b=F[v>>2];g=ki(m,w,F[q+(c<<2)>>2],0)+l|0;p=la(d,b+g|0,m);b=0;k=0;if((i|0)!=1){while(1){g=n+(a<<2)|0;f=b<<2;e=M(R(M(M(o*M(J[f+p>>2]-J[h+f>>2]))+M(.5))));i:{if(M(N(e))<M(2147483648)){r=~~e;break i}r=-2147483648}F[g>>2]=r;f=f|4;e=M(R(M(M(o*M(J[f+p>>2]-J[h+f>>2]))+M(.5))));j:{if(M(N(e))<M(2147483648)){f=~~e;break j}f=-2147483648}F[g+4>>2]=f;b=b+2|0;a=a+2|0;k=k+2|0;if((s|0)!=(k|0)){continue}break}}if(t){g=n+(a<<2)|0;b=b<<2;e=M(R(M(M(o*M(J[b+p>>2]-J[b+h>>2]))+M(.5))));k:{if(M(N(e))<M(2147483648)){b=~~e;break k}b=-2147483648}F[g>>2]=b;a=a+1|0}c=c+1|0;if((u|0)!=(c|0)){continue}break}break h}s=F[b+68>>2];t=i&254;x=i&1;a=0;c=0;while(1){b=F[v>>2];g=ki(m,w,F[s+(F[q+(c<<2)>>2]<<2)>>2],0)+l|0;p=la(d,b+g|0,m);b=0;k=0;if((i|0)!=1){while(1){g=n+(a<<2)|0;f=b<<2;e=M(R(M(M(o*M(J[f+p>>2]-J[h+f>>2]))+M(.5))));l:{if(M(N(e))<M(2147483648)){r=~~e;break l}r=-2147483648}F[g>>2]=r;f=f|4;e=M(R(M(M(o*M(J[f+p>>2]-J[h+f>>2]))+M(.5))));m:{if(M(N(e))<M(2147483648)){f=~~e;break m}f=-2147483648}F[g+4>>2]=f;b=b+2|0;a=a+2|0;k=k+2|0;if((t|0)!=(k|0)){continue}break}}if(x){g=n+(a<<2)|0;b=b<<2;e=M(R(M(M(o*M(J[b+p>>2]-J[b+h>>2]))+M(.5))));n:{if(M(N(e))<M(2147483648)){b=~~e;break n}b=-2147483648}F[g>>2]=b;a=a+1|0}c=c+1|0;if((u|0)!=(c|0)){continue}break}}ja(d);Z=j+16|0;return 1}
function Cd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;c=F[a+4>>2];e=F[a>>2];f=(c-e|0)/144|0;if(f>>>0<b>>>0){e=a;b=b-f|0;h=F[a+8>>2];c=F[a+4>>2];a:{if(b>>>0<=(h-c|0)/144>>>0){b:{if(!b){break b}a=c;f=b&7;if(f){while(1){va(a);a=a+144|0;d=d+1|0;if((f|0)!=(d|0)){continue}break}}c=L(b,144)+c|0;if((b-1&268435455)>>>0<7){break b}while(1){va(a);va(a+144|0);va(a+288|0);va(a+432|0);va(a+576|0);va(a+720|0);va(a+864|0);va(a+1008|0);a=a+1152|0;if((c|0)!=(a|0)){continue}break}}F[e+4>>2]=c;break a}c:{d:{e:{a=c;c=F[e>>2];i=(a-c|0)/144|0;a=i+b|0;if(a>>>0<29826162){c=(h-c|0)/144|0;f=c<<1;f=c>>>0>=14913080?29826161:a>>>0<f>>>0?f:a;if(f){if(f>>>0>=29826162){break e}g=ka(L(f,144))}c=L(i,144)+g|0;a=c;h=b&7;if(h){while(1){va(a);a=a+144|0;d=d+1|0;if((h|0)!=(d|0)){continue}break}}h=L(b,144)+c|0;if((b-1&268435455)>>>0>=7){while(1){va(a);va(a+144|0);va(a+288|0);va(a+432|0);va(a+576|0);va(a+720|0);va(a+864|0);va(a+1008|0);a=a+1152|0;if((h|0)!=(a|0)){continue}break}}b=L(f,144)+g|0;d=F[e+4>>2];f=F[e>>2];if((d|0)==(f|0)){break d}while(1){c=c-144|0;d=d-144|0;a=d;F[c>>2]=F[a>>2];F[c+4>>2]=F[a+4>>2];F[c+8>>2]=F[a+8>>2];F[c+12>>2]=F[a+12>>2];F[a+12>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[c+16>>2]=F[a+16>>2];F[c+20>>2]=F[a+20>>2];F[c+24>>2]=F[a+24>>2];F[a+24>>2]=0;F[a+16>>2]=0;F[a+20>>2]=0;g=G[a+28|0];F[c+40>>2]=0;F[c+32>>2]=0;F[c+36>>2]=0;D[c+28|0]=g;F[c+32>>2]=F[a+32>>2];F[c+36>>2]=F[a+36>>2];F[c+40>>2]=F[a+40>>2];F[a+40>>2]=0;F[a+32>>2]=0;F[a+36>>2]=0;F[c+52>>2]=0;F[c+44>>2]=0;F[c+48>>2]=0;F[c+44>>2]=F[a+44>>2];F[c+48>>2]=F[a+48>>2];F[c+52>>2]=F[a+52>>2];F[a+52>>2]=0;F[a+44>>2]=0;F[a+48>>2]=0;g=c- -64|0;F[g>>2]=0;F[c+56>>2]=0;F[c+60>>2]=0;F[c+56>>2]=F[a+56>>2];F[c+60>>2]=F[a+60>>2];i=g;g=a- -64|0;F[i>>2]=F[g>>2];F[g>>2]=0;F[a+56>>2]=0;F[a+60>>2]=0;F[c+68>>2]=F[a+68>>2];g=F[a+72>>2];F[c+84>>2]=0;F[c+76>>2]=0;F[c+80>>2]=0;F[c+72>>2]=g;F[c+76>>2]=F[a+76>>2];F[c+80>>2]=F[a+80>>2];F[c+84>>2]=F[a+84>>2];F[a+84>>2]=0;F[a+76>>2]=0;F[a+80>>2]=0;F[c+96>>2]=0;F[c+88>>2]=0;F[c+92>>2]=0;F[c+88>>2]=F[a+88>>2];F[c+92>>2]=F[a+92>>2];F[c+96>>2]=F[a+96>>2];F[a+96>>2]=0;F[a+88>>2]=0;F[a+92>>2]=0;g=G[a+100|0];F[c+112>>2]=0;F[c+104>>2]=0;F[c+108>>2]=0;D[c+100|0]=g;F[c+104>>2]=F[a+104>>2];F[c+108>>2]=F[a+108>>2];F[c+112>>2]=F[a+112>>2];F[a+112>>2]=0;F[a+104>>2]=0;F[a+108>>2]=0;F[c+124>>2]=0;F[c+116>>2]=0;F[c+120>>2]=0;F[c+116>>2]=F[a+116>>2];F[c+120>>2]=F[a+120>>2];F[c+124>>2]=F[a+124>>2];F[a+124>>2]=0;F[a+116>>2]=0;F[a+120>>2]=0;g=F[a+128>>2];F[c+140>>2]=0;F[c+132>>2]=0;F[c+136>>2]=0;F[c+128>>2]=g;F[c+132>>2]=F[a+132>>2];F[c+136>>2]=F[a+136>>2];F[c+140>>2]=F[a+140>>2];F[a+140>>2]=0;F[a+132>>2]=0;F[a+136>>2]=0;if((a|0)!=(f|0)){continue}break}F[e+8>>2]=b;a=F[e+4>>2];F[e+4>>2]=h;d=F[e>>2];F[e>>2]=c;if((a|0)==(d|0)){break c}while(1){b=a-144|0;c=F[b+132>>2];if(c){F[a-8>>2]=c;ja(c)}c=F[a-28>>2];if(c){F[a-24>>2]=c;ja(c)}c=F[a-40>>2];if(c){F[a-36>>2]=c;ja(c)}Gb(a-140|0);a=b;if((d|0)!=(a|0)){continue}break}break c}na();v()}oa();v()}F[e+8>>2]=b;F[e+4>>2]=h;F[e>>2]=c}if(d){ja(d)}}return}if(b>>>0<f>>>0){e=e+L(b,144)|0;if((e|0)!=(c|0)){while(1){b=c-144|0;d=F[b+132>>2];if(d){F[c-8>>2]=d;ja(d)}d=F[c-28>>2];if(d){F[c-24>>2]=d;ja(d)}d=F[c-40>>2];if(d){F[c-36>>2]=d;ja(d)}Gb(c-140|0);c=b;if((e|0)!=(c|0)){continue}break}}F[a+4>>2]=e}}function Yc(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;F[a+56>>2]=F[a+52>>2];F[a+44>>2]=F[a+40>>2];b=F[a+64>>2];c=F[b+24>>2];if((c|0)==F[b+28>>2]){return 1}a:{b:{c:{while(1){g=i;i=F[(k<<2)+c>>2];d:{if((i|0)==-1){i=g;break d}b=F[a+56>>2];e:{if((b|0)!=F[a+60>>2]){F[b>>2]=g;F[a+56>>2]=b+4;break e}d=F[a+52>>2];e=b-d|0;h=e>>2;c=h+1|0;if(c>>>0>=1073741824){break c}f=e>>>1|0;f=e>>>0>=2147483644?1073741823:c>>>0<f>>>0?f:c;if(f){if(f>>>0>=1073741824){break b}e=ka(f<<2)}else{e=0}c=e+(h<<2)|0;F[c>>2]=g;h=c+4|0;if((b|0)!=(d|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(d|0)){continue}break}}F[a+60>>2]=e+(f<<2);F[a+56>>2]=h;F[a+52>>2]=c;if(!d){break e}ja(d)}f:{g:{if(!(F[F[a+12>>2]+(k>>>3&536870908)>>2]>>>k&1)){break g}e=i+1|0;e=(e>>>0)%3|0?e:i-2|0;if((e|0)==-1|F[F[a>>2]+(e>>>3&536870908)>>2]>>>e&1){break g}e=F[F[F[a+64>>2]+12>>2]+(e<<2)>>2];if((e|0)==-1){break g}b=e+1|0;b=(b>>>0)%3|0?b:e-2|0;if((b|0)==-1){break g}c=F[a+64>>2];f=F[a>>2];while(1){e=b;b=-1;d=e+1|0;d=(d>>>0)%3|0?d:e-2|0;h:{if((d|0)==-1|F[f+(d>>>3&536870908)>>2]>>>d&1){break h}d=F[F[c+12>>2]+(d<<2)>>2];if((d|0)==-1){break h}b=d+1|0;b=(b>>>0)%3|0?b:d-2|0}if((b|0)!=(i|0)){if((b|0)==-1){break f}continue}break}return 0}e=i}F[F[a+28>>2]+(e<<2)>>2]=g;b=F[a+44>>2];i:{if((b|0)!=F[a+48>>2]){F[b>>2]=e;F[a+44>>2]=b+4;break i}d=F[a+40>>2];i=b-d|0;h=i>>2;c=h+1|0;if(c>>>0>=1073741824){break a}f=i>>>1|0;f=i>>>0>=2147483644?1073741823:c>>>0<f>>>0?f:c;if(f){if(f>>>0>=1073741824){break b}i=ka(f<<2)}else{i=0}c=i+(h<<2)|0;F[c>>2]=e;h=c+4|0;if((b|0)!=(d|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(d|0)){continue}break}}F[a+48>>2]=i+(f<<2);F[a+44>>2]=h;F[a+40>>2]=c;if(!d){break i}ja(d)}i=g+1|0;b=F[a+64>>2];if((e|0)==-1){break d}j:{if((e>>>0)%3|0){c=e-1|0;break j}c=e+2|0;if((c|0)==-1){break d}}d=F[F[b+12>>2]+(c<<2)>>2];if((d|0)==-1){break d}f=d+((d>>>0)%3|0?-1:2)|0;if((f|0)==-1|(e|0)==(f|0)){break d}while(1){b=f+1|0;b=(b>>>0)%3|0?b:f-2|0;if(F[F[a>>2]+(b>>>3&536870908)>>2]>>>b&1){b=F[a+56>>2];k:{if((b|0)!=F[a+60>>2]){F[b>>2]=i;F[a+56>>2]=b+4;break k}d=F[a+52>>2];g=b-d|0;j=g>>2;c=j+1|0;if(c>>>0>=1073741824){break c}h=g>>>1|0;h=g>>>0>=2147483644?1073741823:c>>>0<h>>>0?h:c;if(h){if(h>>>0>=1073741824){break b}g=ka(h<<2)}else{g=0}c=g+(j<<2)|0;F[c>>2]=i;j=c+4|0;if((b|0)!=(d|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(d|0)){continue}break}}F[a+60>>2]=g+(h<<2);F[a+56>>2]=j;F[a+52>>2]=c;if(!d){break k}ja(d)}d=i+1|0;b=F[a+44>>2];l:{if((b|0)!=F[a+48>>2]){F[b>>2]=f;F[a+44>>2]=b+4;break l}h=F[a+40>>2];g=b-h|0;l=g>>2;c=l+1|0;if(c>>>0>=1073741824){break a}j=g>>>1|0;j=g>>>0>=2147483644?1073741823:c>>>0<j>>>0?j:c;if(j){if(j>>>0>=1073741824){break b}g=ka(j<<2)}else{g=0}c=g+(l<<2)|0;F[c>>2]=f;l=c+4|0;if((b|0)!=(h|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(h|0)){continue}break}}F[a+48>>2]=g+(j<<2);F[a+44>>2]=l;F[a+40>>2]=c;if(!h){break l}ja(h)}g=i;i=d}F[F[a+28>>2]+(f<<2)>>2]=g;b=F[a+64>>2];m:{if((f>>>0)%3|0){c=f-1|0;break m}c=f+2|0;if((c|0)==-1){break d}}d=F[F[b+12>>2]+(c<<2)>>2];if((d|0)==-1){break d}f=d+((d>>>0)%3|0?-1:2)|0;if((f|0)==-1){break d}if((e|0)!=(f|0)){continue}break}}k=k+1|0;c=F[b+24>>2];if(k>>>0<F[b+28>>2]-c>>2>>>0){continue}break}return 1}na();v()}oa();v()}na();v()}function Kb(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0;f=Z-96|0;Z=f;e=F[a+16>>2];D[f+92|0]=1;F[f+88>>2]=b;F[f+84>>2]=b;F[f+80>>2]=e;a:{if((b|0)==-1){break a}j=F[a+20>>2];d=F[j>>2];e=F[F[e>>2]+(b<<2)>>2];if(e>>>0>=F[j+4>>2]-d>>2>>>0){break a}e=F[F[a+8>>2]+(F[d+(e<<2)>>2]<<2)>>2];d=F[a+4>>2];if(!G[d+84|0]){e=F[F[d+68>>2]+(e<<2)>>2]}F[f+72>>2]=0;F[f+76>>2]=0;j=f- -64|0;F[j>>2]=0;F[j+4>>2]=0;F[f+56>>2]=0;F[f+60>>2]=0;Ga(d,e,D[d+24|0],f+56|0);e=b+1|0;j=(e>>>0)%3|0?e:b-2|0;n=((b>>>0)%3|0?-1:2)+b|0;b:{c:{while(1){d=j;e=n;d:{if(!F[a+28>>2]){break d}e=b+1|0;d=(e>>>0)%3|0?e:b-2|0;e=b-1|0;if((b>>>0)%3|0){break d}e=b+2|0}if((d|0)==-1){break b}m=F[a+20>>2];b=F[m>>2];d=F[F[F[a+16>>2]>>2]+(d<<2)>>2];if(d>>>0>=F[m+4>>2]-b>>2>>>0){break b}d=F[F[a+8>>2]+(F[(d<<2)+b>>2]<<2)>>2];b=F[a+4>>2];if(!G[b+84|0]){d=F[F[b+68>>2]+(d<<2)>>2]}F[f+48>>2]=0;F[f+52>>2]=0;F[f+40>>2]=0;F[f+44>>2]=0;F[f+32>>2]=0;F[f+36>>2]=0;Ga(b,d,D[b+24|0],f+32|0);if((e|0)==-1){break c}d=F[a+20>>2];b=F[d>>2];e=F[F[F[a+16>>2]>>2]+(e<<2)>>2];if(e>>>0>=F[d+4>>2]-b>>2>>>0){break c}d=F[F[a+8>>2]+(F[b+(e<<2)>>2]<<2)>>2];b=F[a+4>>2];if(!G[b+84|0]){d=F[F[b+68>>2]+(d<<2)>>2]}F[f+24>>2]=0;F[f+28>>2]=0;F[f+16>>2]=0;F[f+20>>2]=0;F[f+8>>2]=0;F[f+12>>2]=0;Ga(b,d,D[b+24|0],f+8|0);g=F[f+8>>2];b=F[f+56>>2];d=g-b|0;p=F[f+60>>2];t=F[f+12>>2]-(p+(b>>>0>g>>>0)|0)|0;i=F[f+40>>2];e=F[f+64>>2];m=i-e|0;u=F[f+68>>2];y=F[f+44>>2]-(u+(e>>>0>i>>>0)|0)|0;g=ki(d,t,m,y);w=o-g|0;x=h-(_+(g>>>0>o>>>0)|0)|0;h=w;i=F[f+16>>2];g=i-e|0;u=F[f+20>>2]-((e>>>0>i>>>0)+u|0)|0;k=F[f+32>>2];i=k-b|0;w=F[f+36>>2]-((b>>>0>k>>>0)+p|0)|0;b=ki(g,u,i,w);o=h+b|0;h=_+x|0;h=b>>>0>o>>>0?h+1|0:h;b=l;l=d;p=t;k=F[f+48>>2];e=F[f+72>>2];d=k-e|0;t=F[f+76>>2];x=F[f+52>>2]-(t+(e>>>0>k>>>0)|0)|0;l=ki(l,p,d,x);k=b+l|0;b=_+q|0;b=k>>>0<l>>>0?b+1|0:b;l=F[f+24>>2];p=l-e|0;e=F[f+28>>2]-((e>>>0>l>>>0)+t|0)|0;q=ki(p,e,i,w);l=k-q|0;q=b-(_+(k>>>0<q>>>0)|0)|0;b=ki(g,u,d,x);d=r-b|0;b=s-(_+(b>>>0>r>>>0)|0)|0;s=ki(p,e,m,y);r=s+d|0;b=_+b|0;s=r>>>0<s>>>0?b+1|0:b;nc(f+80|0);b=F[f+88>>2];if((b|0)!=-1){continue}break}b=s>>31;e=b^r;d=e-b|0;b=(b^s)-((b>>>0>e>>>0)+b|0)|0;n=-1;e=2147483647;m=q>>31;g=m;i=g^l;j=i-g|0;m=(g^q)-((i>>>0<g>>>0)+g|0)|0;i=m;k=j^-1;g=i^2147483647;m=h;e:{f:{if(!F[a+28>>2]){if((b|0)==(g|0)&d>>>0>k>>>0|b>>>0>g>>>0){break e}b=b+i|0;a=d+j|0;b=a>>>0<j>>>0?b+1|0:b;e=a;g=h;a=g>>31;d=a;n=d^o;a=n-d|0;h=a;d=(d^g)-((d>>>0>n>>>0)+d|0)|0;a=a+e|0;d=d^2147483647;h=(d|0)==(b|0)&(h^-1)>>>0<e>>>0|b>>>0>d>>>0;a=h?-1:a;if(!(h&0)&(a|0)<=536870912|(a|0)<536870912){break e}b=0;a=a>>>29|0;break f}g:{if((b|0)==(g|0)&d>>>0>k>>>0|b>>>0>g>>>0){break g}b=b+i|0;a=d+j|0;b=a>>>0<j>>>0?b+1|0:b;k=h;h=h>>31;g=h;i=g^o;h=i-g|0;j=(g^k)-((g>>>0>i>>>0)+g|0)|0;g=j^2147483647;d=a;a=h;if((g|0)==(b|0)&d>>>0>(a^-1)>>>0|b>>>0>g>>>0){break g}b=b+j|0;n=a+d|0;b=n>>>0<a>>>0?b+1|0:b;e=b;if(!b&n>>>0<536870913){break e}}b=e>>>29|0;a=(e&536870911)<<3|n>>>29}o=li(o,m,a,b);l=li(l,q,a,b);r=li(r,s,a,b)}F[c+8>>2]=o;F[c+4>>2]=l;F[c>>2]=r;Z=f+96|0;return}ta();v()}ta();v()}ta();v()}function Nc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;a:{if((b|0)<0){break a}c=F[a+12>>2];d=F[a+8>>2];if(c-d>>2>>>0<=b>>>0){break a}d=d+(b<<2)|0;e=F[d>>2];i=F[e+60>>2];f=F[e+56>>2];e=d+4|0;if((e|0)!=(c|0)){while(1){h=F[e>>2];F[e>>2]=0;g=F[d>>2];F[d>>2]=h;if(g){xa(g)}d=d+4|0;e=e+4|0;if((e|0)!=(c|0)){continue}break}c=F[a+12>>2]}if((c|0)!=(d|0)){while(1){c=c-4|0;e=F[c>>2];F[c>>2]=0;if(e){xa(e)}if((c|0)!=(d|0)){continue}break}}F[a+12>>2]=d;g=F[a+4>>2];b:{if(!g|(i|0)<0){break b}c=F[g+24>>2];d=F[g+28>>2];if((c|0)==(d|0)){break b}while(1){if((i|0)==F[F[c>>2]+24>>2]){d=c+4|0;i=F[g+28>>2];if((d|0)!=(i|0)){while(1){h=F[d>>2];F[d>>2]=0;e=F[c>>2];F[c>>2]=h;if(e){Ca(e+12|0,F[e+16>>2]);Ba(e,F[e+4>>2]);ja(e)}c=c+4|0;d=d+4|0;if((i|0)!=(d|0)){continue}break}d=F[g+28>>2]}if((c|0)!=(d|0)){while(1){d=d-4|0;e=F[d>>2];F[d>>2]=0;if(e){Ca(e+12|0,F[e+16>>2]);Ba(e,F[e+4>>2]);ja(e)}if((c|0)!=(d|0)){continue}break}}F[g+28>>2]=c;break b}c=c+4|0;if((d|0)!=(c|0)){continue}break}}c:{if((f|0)>4){break c}d:{e=L(f,12)+a|0;c=F[e+20>>2];d=F[e+24>>2];if((c|0)==(d|0)){break d}while(1){if(F[c>>2]==(b|0)){break d}c=c+4|0;if((d|0)!=(c|0)){continue}break}break c}if((c|0)==(d|0)){break c}f=c;c=c+4|0;pa(f,c,d-c|0);F[e+24>>2]=d-4}c=F[a+24>>2];d=F[a+20>>2];e:{if((c|0)==(d|0)){break e}e=c-d|0;c=e>>2;g=c>>>0<=1?1:c;i=g&1;c=0;if(e>>>0>=8){g=g&-2;e=0;while(1){f=c<<2;h=f+d|0;j=F[h>>2];if((j|0)>(b|0)){F[h>>2]=j-1}f=d+(f|4)|0;h=F[f>>2];if((h|0)>(b|0)){F[f>>2]=h-1}c=c+2|0;e=e+2|0;if((g|0)!=(e|0)){continue}break}}if(!i){break e}c=d+(c<<2)|0;d=F[c>>2];if((d|0)<=(b|0)){break e}F[c>>2]=d-1}c=F[a+36>>2];d=F[a+32>>2];f:{if((c|0)==(d|0)){break f}e=c-d|0;c=e>>2;g=c>>>0<=1?1:c;i=g&1;c=0;if(e>>>0>=8){g=g&-2;e=0;while(1){f=c<<2;h=f+d|0;j=F[h>>2];if((j|0)>(b|0)){F[h>>2]=j-1}f=d+(f|4)|0;h=F[f>>2];if((h|0)>(b|0)){F[f>>2]=h-1}c=c+2|0;e=e+2|0;if((g|0)!=(e|0)){continue}break}}if(!i){break f}c=d+(c<<2)|0;d=F[c>>2];if((d|0)<=(b|0)){break f}F[c>>2]=d-1}c=F[a+48>>2];d=F[a+44>>2];g:{if((c|0)==(d|0)){break g}e=c-d|0;c=e>>2;g=c>>>0<=1?1:c;i=g&1;c=0;if(e>>>0>=8){g=g&-2;e=0;while(1){f=c<<2;h=f+d|0;j=F[h>>2];if((j|0)>(b|0)){F[h>>2]=j-1}f=d+(f|4)|0;h=F[f>>2];if((h|0)>(b|0)){F[f>>2]=h-1}c=c+2|0;e=e+2|0;if((g|0)!=(e|0)){continue}break}}if(!i){break g}c=d+(c<<2)|0;d=F[c>>2];if((d|0)<=(b|0)){break g}F[c>>2]=d-1}c=F[a+60>>2];d=F[a+56>>2];h:{if((c|0)==(d|0)){break h}e=c-d|0;c=e>>2;g=c>>>0<=1?1:c;i=g&1;c=0;if(e>>>0>=8){g=g&-2;e=0;while(1){f=c<<2;h=f+d|0;j=F[h>>2];if((j|0)>(b|0)){F[h>>2]=j-1}f=d+(f|4)|0;h=F[f>>2];if((h|0)>(b|0)){F[f>>2]=h-1}c=c+2|0;e=e+2|0;if((g|0)!=(e|0)){continue}break}}if(!i){break h}c=d+(c<<2)|0;d=F[c>>2];if((d|0)<=(b|0)){break h}F[c>>2]=d-1}c=F[a+72>>2];a=F[a+68>>2];if((c|0)==(a|0)){break a}d=c-a|0;c=d>>2;e=c>>>0<=1?1:c;g=e&1;c=0;if(d>>>0>=8){d=e&-2;e=0;while(1){i=c<<2;f=i+a|0;h=F[f>>2];if((h|0)>(b|0)){F[f>>2]=h-1}i=a+(i|4)|0;f=F[i>>2];if((f|0)>(b|0)){F[i>>2]=f-1}c=c+2|0;e=e+2|0;if((d|0)!=(e|0)){continue}break}}if(!g){break a}f=b;a=a+(c<<2)|0;b=F[a>>2];if((f|0)>=(b|0)){break a}F[a>>2]=b-1}}function ja(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;a:{if(!a){break a}d=a-8|0;b=F[a-4>>2];a=b&-8;f=d+a|0;b:{if(b&1){break b}if(!(b&3)){break a}b=F[d>>2];d=d-b|0;if(d>>>0<I[2945]){break a}a=a+b|0;if(F[2946]!=(d|0)){if(b>>>0<=255){e=F[d+8>>2];b=b>>>3|0;c=F[d+12>>2];if((c|0)==(e|0)){i=11764,j=F[2941]&oi(b),F[i>>2]=j;break b}F[e+12>>2]=c;F[c+8>>2]=e;break b}h=F[d+24>>2];b=F[d+12>>2];c:{if((d|0)!=(b|0)){c=F[d+8>>2];F[c+12>>2]=b;F[b+8>>2]=c;break c}d:{e=d+20|0;c=F[e>>2];if(c){break d}e=d+16|0;c=F[e>>2];if(c){break d}b=0;break c}while(1){g=e;b=c;e=b+20|0;c=F[e>>2];if(c){continue}e=b+16|0;c=F[b+16>>2];if(c){continue}break}F[g>>2]=0}if(!h){break b}e=F[d+28>>2];c=(e<<2)+12068|0;e:{if(F[c>>2]==(d|0)){F[c>>2]=b;if(b){break e}i=11768,j=F[2942]&oi(e),F[i>>2]=j;break b}F[h+(F[h+16>>2]==(d|0)?16:20)>>2]=b;if(!b){break b}}F[b+24>>2]=h;c=F[d+16>>2];if(c){F[b+16>>2]=c;F[c+24>>2]=b}c=F[d+20>>2];if(!c){break b}F[b+20>>2]=c;F[c+24>>2]=b;break b}b=F[f+4>>2];if((b&3)!=3){break b}F[2943]=a;F[f+4>>2]=b&-2;F[d+4>>2]=a|1;F[a+d>>2]=a;return}if(d>>>0>=f>>>0){break a}b=F[f+4>>2];if(!(b&1)){break a}f:{if(!(b&2)){if(F[2947]==(f|0)){F[2947]=d;a=F[2944]+a|0;F[2944]=a;F[d+4>>2]=a|1;if(F[2946]!=(d|0)){break a}F[2943]=0;F[2946]=0;return}if(F[2946]==(f|0)){F[2946]=d;a=F[2943]+a|0;F[2943]=a;F[d+4>>2]=a|1;F[a+d>>2]=a;return}a=(b&-8)+a|0;g:{if(b>>>0<=255){e=F[f+8>>2];b=b>>>3|0;c=F[f+12>>2];if((c|0)==(e|0)){i=11764,j=F[2941]&oi(b),F[i>>2]=j;break g}F[e+12>>2]=c;F[c+8>>2]=e;break g}h=F[f+24>>2];b=F[f+12>>2];h:{if((f|0)!=(b|0)){c=F[f+8>>2];F[c+12>>2]=b;F[b+8>>2]=c;break h}i:{e=f+20|0;c=F[e>>2];if(c){break i}e=f+16|0;c=F[e>>2];if(c){break i}b=0;break h}while(1){g=e;b=c;e=b+20|0;c=F[e>>2];if(c){continue}e=b+16|0;c=F[b+16>>2];if(c){continue}break}F[g>>2]=0}if(!h){break g}e=F[f+28>>2];c=(e<<2)+12068|0;j:{if(F[c>>2]==(f|0)){F[c>>2]=b;if(b){break j}i=11768,j=F[2942]&oi(e),F[i>>2]=j;break g}F[h+(F[h+16>>2]==(f|0)?16:20)>>2]=b;if(!b){break g}}F[b+24>>2]=h;c=F[f+16>>2];if(c){F[b+16>>2]=c;F[c+24>>2]=b}c=F[f+20>>2];if(!c){break g}F[b+20>>2]=c;F[c+24>>2]=b}F[d+4>>2]=a|1;F[a+d>>2]=a;if(F[2946]!=(d|0)){break f}F[2943]=a;return}F[f+4>>2]=b&-2;F[d+4>>2]=a|1;F[a+d>>2]=a}if(a>>>0<=255){b=(a&-8)+11804|0;c=F[2941];a=1<<(a>>>3);k:{if(!(c&a)){F[2941]=a|c;a=b;break k}a=F[b+8>>2]}F[b+8>>2]=d;F[a+12>>2]=d;F[d+12>>2]=b;F[d+8>>2]=a;return}e=31;if(a>>>0<=16777215){b=O(a>>>8|0);e=((a>>>38-b&1)-(b<<1)|0)+62|0}F[d+28>>2]=e;F[d+16>>2]=0;F[d+20>>2]=0;g=(e<<2)+12068|0;l:{m:{c=F[2942];b=1<<e;n:{if(!(c&b)){F[2942]=b|c;F[g>>2]=d;F[d+24>>2]=g;break n}e=a<<((e|0)!=31?25-(e>>>1|0)|0:0);b=F[g>>2];while(1){c=b;if((F[b+4>>2]&-8)==(a|0)){break m}b=e>>>29|0;e=e<<1;g=c+(b&4)|0;b=F[g+16>>2];if(b){continue}break}F[g+16>>2]=d;F[d+24>>2]=c}F[d+12>>2]=d;F[d+8>>2]=d;break l}a=F[c+8>>2];F[a+12>>2]=d;F[c+8>>2]=d;F[d+24>>2]=0;F[d+12>>2]=c;F[d+8>>2]=a}a=F[2949]-1|0;F[2949]=a?a:-1}}function di(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0;F[a+8>>2]=e;n=a+32|0;h=F[n>>2];f=F[a+36>>2]-h>>2;a:{if(f>>>0<e>>>0){qa(n,e-f|0);d=F[a+8>>2];break a}d=e;if(d>>>0>=f>>>0){break a}F[a+36>>2]=h+(e<<2);d=e}s=F[a+52>>2];p=F[a+48>>2];f=0;h=e>>>0>1073741823?-1:e<<2;m=ma(ka(h),0,h);b:{if((d|0)<=0){break b}g=F[a+32>>2];while(1){d=f<<2;h=F[d+m>>2];j=F[a+16>>2];c:{if((h|0)>(j|0)){F[d+g>>2]=j;break c}d=d+g|0;j=F[a+12>>2];if((j|0)>(h|0)){F[d>>2]=j;break c}F[d>>2]=h}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}if((d|0)<=0){break b}f=0;while(1){h=f<<2;d=h+c|0;h=F[b+h>>2]+F[g+h>>2]|0;F[d>>2]=h;d:{if((h|0)>F[a+16>>2]){i=h-F[a+20>>2]|0}else{if((h|0)>=F[a+12>>2]){break d}i=h+F[a+20>>2]|0}F[d>>2]=i}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}f=F[a+56>>2];q=F[f>>2];f=F[f+4>>2]-q|0;if((f|0)>=5){o=f>>>2|0;t=o>>>0<=2?2:o;u=e&-2;w=e&1;h=1;while(1){e:{f:{if((h|0)!=(o|0)){r=L(e,h);f=F[(h<<2)+q>>2];if((f|0)==-1){break f}f=F[F[p+12>>2]+(f<<2)>>2];if((f|0)==-1){break f}j=F[s>>2];g=F[p>>2];k=F[j+(F[g+(f<<2)>>2]<<2)>>2];i=f+1|0;i=(i>>>0)%3|0?i:f-2|0;if((i|0)!=-1){i=F[g+(i<<2)>>2]}else{i=-1}g:{h:{if((f>>>0)%3|0){f=f-1|0;break h}f=f+2|0;l=-1;if((f|0)==-1){break g}}l=F[g+(f<<2)>>2]}if((h|0)<=(k|0)){break f}f=F[(i<<2)+j>>2];if((f|0)>=(h|0)){break f}g=F[j+(l<<2)>>2];if((g|0)>=(h|0)){break f}i:{if((e|0)<=0){break i}g=L(e,g);j=L(e,f);k=L(e,k);f=0;l=0;if((e|0)!=1){while(1){F[(f<<2)+m>>2]=(F[(f+g<<2)+c>>2]+F[(f+j<<2)+c>>2]|0)-F[(f+k<<2)+c>>2];i=f|1;F[(i<<2)+m>>2]=(F[(g+i<<2)+c>>2]+F[(j+i<<2)+c>>2]|0)-F[(i+k<<2)+c>>2];f=f+2|0;l=l+2|0;if((u|0)!=(l|0)){continue}break}}if(!w){break i}F[(f<<2)+m>>2]=(F[(f+g<<2)+c>>2]+F[(f+j<<2)+c>>2]|0)-F[(f+k<<2)+c>>2]}if((d|0)<=0){break e}j=F[n>>2];f=0;while(1){d=f<<2;g=F[d+m>>2];k=F[a+16>>2];j:{if((g|0)>(k|0)){F[d+j>>2]=k;break j}d=d+j|0;k=F[a+12>>2];if((k|0)>(g|0)){F[d>>2]=k;break j}F[d>>2]=g}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=r<<2;k=d+c|0;i=b+d|0;while(1){g=f<<2;d=g+k|0;g=F[g+i>>2]+F[g+j>>2]|0;F[d>>2]=g;k:{if((g|0)>F[a+16>>2]){l=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break k}l=g+F[a+20>>2]|0}F[d>>2]=l}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}break e}ta();v()}if((d|0)<=0){break e}k=(L(h-1|0,e)<<2)+c|0;j=F[n>>2];f=0;while(1){d=f<<2;g=F[d+k>>2];i=F[a+16>>2];l:{if((g|0)>(i|0)){F[d+j>>2]=i;break l}d=d+j|0;i=F[a+12>>2];if((i|0)>(g|0)){F[d>>2]=i;break l}F[d>>2]=g}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=r<<2;k=d+c|0;i=b+d|0;while(1){g=f<<2;d=g+k|0;g=F[g+i>>2]+F[g+j>>2]|0;F[d>>2]=g;m:{if((g|0)>F[a+16>>2]){l=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break m}l=g+F[a+20>>2]|0}F[d>>2]=l}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}h=h+1|0;if((t|0)!=(h|0)){continue}break}}ja(m);return 1}function od(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;if((b|0)==-1){return 1}g=(b>>>0)/3|0;if(!(F[F[a+24>>2]+(g>>>3&268435452)>>2]>>>g&1)){e=F[a+48>>2];F[a+52>>2]=e;a:{if((e|0)!=F[a+56>>2]){F[e>>2]=b;F[a+52>>2]=e+4;break a}d=ka(4);F[d>>2]=b;c=d+4|0;F[a+56>>2]=c;F[a+52>>2]=c;F[a+48>>2]=d;if(!e){break a}ja(e)}c=b+1|0;i=(c>>>0)%3|0?c:b-2|0;c=F[F[a+4>>2]+28>>2];k=F[(i<<2)+c>>2];if((k|0)==-1){return 0}e=(b-L(g,3)|0?-1:2)+b|0;j=F[c+(e<<2)>>2];if((j|0)==-1){return 0}b=F[a+36>>2];g=b+(k>>>3&536870908)|0;d=F[g>>2];c=1<<k;if(!(d&c)){F[g>>2]=c|d;Ka(a+8|0,k,i);b=F[a+36>>2]}d=(j>>>3&536870908)+b|0;c=F[d>>2];b=1<<j;if(!(c&b)){F[d>>2]=b|c;Ka(a+8|0,j,e)}f=F[a+52>>2];if((f|0)==F[a+48>>2]){return 1}k=a+8|0;while(1){b:{c:{f=f-4|0;b=F[f>>2];if((b|0)==-1){break c}c=(b>>>0)/3|0;g=F[a+24>>2]+(c>>>3&268435452)|0;d=F[g>>2];c=1<<c;if(d&c){break c}F[g>>2]=c|d;h=F[a+4>>2];c=F[F[h+28>>2]+(b<<2)>>2];if((c|0)==-1){return 0}while(1){d=b;d:{e:{j=F[a+36>>2]+(c>>>3&536870908)|0;i=F[j>>2];e=1<<c;if(i&e){break e}f:{g=F[F[h+40>>2]+(c<<2)>>2];g:{if((g|0)==-1){break g}b=g+1|0;b=(b>>>0)%3|0?b:g-2|0;if((b|0)==-1|F[F[h>>2]+(b>>>3&536870908)>>2]>>>b&1){break g}g=F[F[F[h+64>>2]+12>>2]+(b<<2)>>2];if((g|0)!=-1){break f}}F[j>>2]=e|i;Ka(k,c,d);h=F[a+4>>2];break e}F[j>>2]=e|i;Ka(k,c,d);h=F[a+4>>2];b=g+1|0;if((((b>>>0)%3|0?b:g-2|0)|0)==-1){break e}b=-1;h:{if((d|0)==-1){break h}c=d+1|0;c=(c>>>0)%3|0?c:d-2|0;if((c|0)==-1|F[F[h>>2]+(c>>>3&536870908)>>2]>>>c&1){break h}b=F[F[F[h+64>>2]+12>>2]+(c<<2)>>2]}c=(b>>>0)/3|0;d=1<<c;f=F[a+24>>2];e=c>>>5|0;j=F[f+(e<<2)>>2];break d}i:{j:{if((d|0)==-1){break j}c=-1;b=d+1|0;b=(b>>>0)%3|0?b:d-2|0;if(!((b|0)==-1|F[F[h>>2]+(b>>>3&536870908)>>2]>>>b&1)){c=F[F[F[h+64>>2]+12>>2]+(b<<2)>>2]}k:{l:{if((d>>>0)%3|0){f=d-1|0;break l}f=d+2|0;b=-1;if((f|0)==-1){break k}}b=-1;if(F[F[h>>2]+(f>>>3&536870908)>>2]>>>f&1){break k}b=F[F[F[h+64>>2]+12>>2]+(f<<2)>>2]}g=(b|0)==-1;i=g?-1:(b>>>0)/3|0;if((c|0)!=-1){f=F[a+24>>2];d=(c>>>0)/3|0;e=d>>>5|0;j=F[f+(e<<2)>>2];d=1<<d;if(!(j&d)){break i}}if(g){break j}d=1<<i;f=F[a+24>>2];e=i>>>5|0;j=F[f+(e<<2)>>2];if(!(d&j)){break d}}f=F[a+52>>2]-4|0;F[a+52>>2]=f;break b}if(g){b=c;break d}if(F[(i>>>3&536870908)+f>>2]>>>i&1){b=c;break d}h=F[a+52>>2];F[h-4>>2]=b;if(F[a+56>>2]!=(h|0)){F[h>>2]=c;f=h+4|0;break c}m:{i=F[a+48>>2];e=h-i|0;g=e>>2;d=g+1|0;if(d>>>0<1073741824){b=e>>>1|0;e=e>>>0>=2147483644?1073741823:b>>>0>d>>>0?b:d;if(e){if(e>>>0>=1073741824){break m}d=ka(e<<2)}else{d=0}b=d+(g<<2)|0;F[b>>2]=c;f=b+4|0;if((h|0)!=(i|0)){while(1){b=b-4|0;h=h-4|0;F[b>>2]=F[h>>2];if((h|0)!=(i|0)){continue}break}}F[a+56>>2]=d+(e<<2);F[a+52>>2]=f;F[a+48>>2]=b;if(!i){break b}ja(i);f=F[a+52>>2];break b}na();v()}oa();v()}F[(e<<2)+f>>2]=d|j;c=F[F[h+28>>2]+(b<<2)>>2];if((c|0)!=-1){continue}break}return 0}F[a+52>>2]=f}if(F[a+48>>2]!=(f|0)){continue}break}}return 1}function he(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0;F[a+8>>2]=e;m=a+32|0;h=F[m>>2];f=F[a+36>>2]-h>>2;a:{if(f>>>0<e>>>0){qa(m,e-f|0);d=F[a+8>>2];break a}d=e;if(d>>>0>=f>>>0){break a}F[a+36>>2]=h+(e<<2);d=e}s=F[a+52>>2];n=F[a+48>>2];f=0;h=e>>>0>1073741823?-1:e<<2;l=ma(ka(h),0,h);b:{if((d|0)<=0){break b}g=F[a+32>>2];while(1){d=f<<2;h=F[d+l>>2];i=F[a+16>>2];c:{if((h|0)>(i|0)){F[d+g>>2]=i;break c}d=d+g|0;i=F[a+12>>2];if((i|0)>(h|0)){F[d>>2]=i;break c}F[d>>2]=h}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}if((d|0)<=0){break b}f=0;while(1){h=f<<2;d=h+c|0;h=F[b+h>>2]+F[g+h>>2]|0;F[d>>2]=h;d:{if((h|0)>F[a+16>>2]){h=h-F[a+20>>2]|0}else{if((h|0)>=F[a+12>>2]){break d}h=h+F[a+20>>2]|0}F[d>>2]=h}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}f=F[a+56>>2];q=F[f>>2];f=F[f+4>>2]-q|0;if((f|0)>=5){o=f>>>2|0;t=o>>>0<=2?2:o;u=e&-2;w=e&1;h=1;while(1){e:{f:{if((h|0)!=(o|0)){r=L(e,h);f=F[(h<<2)+q>>2];if((f|0)==-1|F[F[n>>2]+(f>>>3&536870908)>>2]>>>f&1){break f}f=F[F[F[n+64>>2]+12>>2]+(f<<2)>>2];if((f|0)==-1){break f}i=F[s>>2];g=F[n+28>>2];k=F[i+(F[g+(f<<2)>>2]<<2)>>2];if((k|0)>=(h|0)){break f}j=f+1|0;j=F[i+(F[g+(((j>>>0)%3|0?j:f-2|0)<<2)>>2]<<2)>>2];if((j|0)>=(h|0)){break f}f=F[i+(F[g+(f+((f>>>0)%3|0?-1:2)<<2)>>2]<<2)>>2];if((f|0)>=(h|0)){break f}g:{if((e|0)<=0){break g}g=L(e,f);i=L(e,j);k=L(e,k);f=0;p=0;if((e|0)!=1){while(1){F[(f<<2)+l>>2]=(F[(f+g<<2)+c>>2]+F[(f+i<<2)+c>>2]|0)-F[(f+k<<2)+c>>2];j=f|1;F[(j<<2)+l>>2]=(F[(g+j<<2)+c>>2]+F[(i+j<<2)+c>>2]|0)-F[(k+j<<2)+c>>2];f=f+2|0;p=p+2|0;if((u|0)!=(p|0)){continue}break}}if(!w){break g}F[(f<<2)+l>>2]=(F[(f+g<<2)+c>>2]+F[(f+i<<2)+c>>2]|0)-F[(f+k<<2)+c>>2]}if((d|0)<=0){break e}i=F[m>>2];f=0;while(1){d=f<<2;g=F[d+l>>2];k=F[a+16>>2];h:{if((g|0)>(k|0)){F[d+i>>2]=k;break h}d=d+i|0;k=F[a+12>>2];if((k|0)>(g|0)){F[d>>2]=k;break h}F[d>>2]=g}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=r<<2;k=d+c|0;j=b+d|0;while(1){g=f<<2;d=g+k|0;g=F[g+j>>2]+F[g+i>>2]|0;F[d>>2]=g;i:{if((g|0)>F[a+16>>2]){g=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break i}g=g+F[a+20>>2]|0}F[d>>2]=g}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}break e}ta();v()}if((d|0)<=0){break e}k=(L(h-1|0,e)<<2)+c|0;i=F[m>>2];f=0;while(1){d=f<<2;g=F[d+k>>2];j=F[a+16>>2];j:{if((g|0)>(j|0)){F[d+i>>2]=j;break j}d=d+i|0;j=F[a+12>>2];if((j|0)>(g|0)){F[d>>2]=j;break j}F[d>>2]=g}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}f=0;if((d|0)<=0){break e}d=r<<2;k=d+c|0;j=b+d|0;while(1){g=f<<2;d=g+k|0;g=F[g+j>>2]+F[g+i>>2]|0;F[d>>2]=g;k:{if((g|0)>F[a+16>>2]){g=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break k}g=g+F[a+20>>2]|0}F[d>>2]=g}d=F[a+8>>2];f=f+1|0;if((d|0)>(f|0)){continue}break}}h=h+1|0;if((t|0)!=(h|0)){continue}break}}ja(l);return 1}function Fb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=M(0),k=0,l=0,m=M(0);i=F[c>>2];a:{b:{f=F[b+4>>2];if(!f){break b}g=ni(f);c:{if(g>>>0>=2){e=i;if(f>>>0<=e>>>0){e=(i>>>0)%(f>>>0)|0}c=F[F[b>>2]+(e<<2)>>2];if(!c){break b}if(g>>>0<=1){break c}while(1){c=F[c>>2];if(!c){break b}g=F[c+4>>2];if((g|0)!=(i|0)){if(f>>>0<=g>>>0){g=(g>>>0)%(f>>>0)|0}if((e|0)!=(g|0)){break b}}if(F[c+8>>2]!=(i|0)){continue}break}b=0;break a}e=f-1&i;c=F[F[b>>2]+(e<<2)>>2];if(!c){break b}}h=f-1|0;while(1){c=F[c>>2];if(!c){break b}g=F[c+4>>2];if((g|0)!=(i|0)&(g&h)!=(e|0)){break b}if(F[c+8>>2]!=(i|0)){continue}break}b=0;break a}c=ka(16);d=F[F[d>>2]>>2];F[c+12>>2]=0;F[c+8>>2]=d;F[c+4>>2]=i;F[c>>2]=0;m=M(F[b+12>>2]+1>>>0);j=J[b+16>>2];d:{if(m>M(j*M(f>>>0))?0:f){break d}e=2;d=(f-1&f)!=0|f>>>0<3|f<<1;j=M(S(M(m/j)));e:{if(j<M(4294967296)&j>=M(0)){g=~~j>>>0;break e}g=0}d=d>>>0>g>>>0?d:g;f:{if((d|0)==1){break f}if(!(d&d-1)){e=d;break f}e=Mc(d);f=F[b+4>>2]}g:{if(e>>>0<=f>>>0){if(e>>>0>=f>>>0){break g}g=f>>>0<3;j=M(S(M(M(I[b+12>>2])/J[b+16>>2])));h:{if(j<M(4294967296)&j>=M(0)){d=~~j>>>0;break h}d=0}i:{j:{if(g){break j}if(ni(f)>>>0>1){break j}d=d>>>0<2?d:1<<32-O(d-1|0);break i}d=Mc(d)}e=d>>>0<e>>>0?e:d;if(f>>>0<=e>>>0){break g}}f=0;g=0;h=e;k:{l:{m:{n:{if(e){if(h>>>0>=1073741824){break n}d=ka(h<<2);e=F[b>>2];F[b>>2]=d;if(e){ja(e)}F[b+4>>2]=h;d=0;if(h>>>0>=4){e=h&-4;while(1){k=d<<2;F[k+F[b>>2]>>2]=0;F[F[b>>2]+(k|4)>>2]=0;F[F[b>>2]+(k|8)>>2]=0;F[F[b>>2]+(k|12)>>2]=0;d=d+4|0;g=g+4|0;if((e|0)!=(g|0)){continue}break}}e=h&3;if(e){while(1){F[F[b>>2]+(d<<2)>>2]=0;d=d+1|0;f=f+1|0;if((e|0)!=(f|0)){continue}break}}e=F[b+8>>2];if(!e){break k}d=b+8|0;f=F[e+4>>2];g=ni(h);if(g>>>0<2){break m}f=f>>>0>=h>>>0?(f>>>0)%(h>>>0)|0:f;F[F[b>>2]+(f<<2)>>2]=d;d=F[e>>2];if(!d){break k}if(g>>>0<=1){break l}while(1){g=F[d+4>>2];if(h>>>0<=g>>>0){g=(g>>>0)%(h>>>0)|0}o:{if((f|0)==(g|0)){e=d;break o}l=g<<2;k=l+F[b>>2]|0;if(!F[k>>2]){F[k>>2]=e;e=d;f=g;break o}F[e>>2]=F[d>>2];F[d>>2]=F[F[l+F[b>>2]>>2]>>2];F[F[l+F[b>>2]>>2]>>2]=d}d=F[e>>2];if(d){continue}break}break k}d=F[b>>2];F[b>>2]=0;if(d){ja(d)}F[b+4>>2]=0;break k}oa();v()}f=h-1&f;F[F[b>>2]+(f<<2)>>2]=d;d=F[e>>2];if(!d){break k}}k=h-1|0;while(1){g=k&F[d+4>>2];p:{if((g|0)==(f|0)){e=d;break p}l=g<<2;h=l+F[b>>2]|0;if(F[h>>2]){F[e>>2]=F[d>>2];F[d>>2]=F[F[l+F[b>>2]>>2]>>2];F[F[l+F[b>>2]>>2]>>2]=d;break p}F[h>>2]=e;e=d;f=g}d=F[e>>2];if(d){continue}break}}}f=F[b+4>>2];d=f-1|0;if(!(d&f)){e=d&i;break d}if(f>>>0>i>>>0){e=i;break d}e=(i>>>0)%(f>>>0)|0}e=F[b>>2]+(e<<2)|0;d=F[e>>2];q:{r:{if(!d){d=b+8|0;F[c>>2]=F[d>>2];F[b+8>>2]=c;F[e>>2]=d;d=F[c>>2];if(!d){break q}d=F[d+4>>2];e=f-1|0;s:{if(!(e&f)){d=d&e;break s}if(d>>>0<f>>>0){break s}d=(d>>>0)%(f>>>0)|0}d=F[b>>2]+(d<<2)|0;break r}F[c>>2]=F[d>>2]}F[d>>2]=c}F[b+12>>2]=F[b+12>>2]+1;b=1}D[a+4|0]=b;F[a>>2]=c}function Vb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;j=L(b,12)+a|0;F[j+12>>2]=F[j+8>>2];m=(c|0)==-1?-1:(c>>>0)/3|0;d=1;k=c;a:{b:{c:{while(1){d:{l=d;if(!d){if((k|0)==-1){break d}if((Wc(a,((k>>>0)%3|0?-1:2)+k|0)|0)==-1){break a}c=k+1|0;d=(c>>>0)%3|0?c:k-2|0;if((d|0)==-1){break a}c=d+1|0;c=(c>>>0)%3|0?c:d-2|0;if((c|0)==-1){break a}d=F[F[F[a+4>>2]+12>>2]+(c<<2)>>2];if((d|0)==-1){break a}c=d+1|0;c=(c>>>0)%3|0?c:d-2|0;if((c|0)==-1){break a}m=(c>>>0)/3|0}e:{d=F[a+56>>2]+(m>>>3&536870908)|0;h=F[d>>2];e=1<<m;if(h&e){break e}f=0;while(1){F[d>>2]=e|h;d=F[j+12>>2];f:{if((d|0)!=F[j+16>>2]){F[d>>2]=m;F[j+12>>2]=d+4;break f}n=F[j+8>>2];h=d-n|0;e=h>>2;i=e+1|0;if(i>>>0>=1073741824){break c}g=h>>>1|0;i=h>>>0>=2147483644?1073741823:i>>>0<g>>>0?g:i;if(i){if(i>>>0>=1073741824){break b}g=ka(i<<2)}else{g=0}h=g+(e<<2)|0;F[h>>2]=m;e=h+4|0;if((d|0)!=(n|0)){while(1){h=h-4|0;d=d-4|0;F[h>>2]=F[d>>2];if((d|0)!=(n|0)){continue}break}}F[j+8>>2]=h;F[j+12>>2]=e;F[j+16>>2]=g+(i<<2);if(!n){break f}ja(n)}g=f+1|0;g:{h:{i:{if(!f){break i}if(g&1){if((c|0)==-1){c=-1;break g}d=c+1|0;c=(d>>>0)%3|0?d:c-2|0;break i}k=l?k:c;if((c|0)==-1){c=-1;break g}if((c>>>0)%3|0){d=c-1|0;break h}c=c+2|0}d=c;c=-1;if((d|0)==-1){break g}}c=F[F[F[a+4>>2]+12>>2]+(d<<2)>>2];h=-1;f=-1;e=d+1|0;e=(e>>>0)%3|0?e:d-2|0;if((e|0)>=0){f=(e>>>0)/3|0;f=F[(F[F[a>>2]+96>>2]+L(f,12)|0)+(e-L(f,3)<<2)>>2]}j:{if((c|0)==-1){break j}i=((c>>>0)%3|0?-1:2)+c|0;if((i|0)<0){break j}e=(i>>>0)/3|0;h=F[(F[F[a>>2]+96>>2]+L(e,12)|0)+(i-L(e,3)<<2)>>2]}if((f|0)!=(h|0)){c=-1;break g}k:{l:{f=((d>>>0)%3|0?-1:2)+d|0;if((f|0)>=0){d=(f>>>0)/3|0;if((c|0)!=-1){break l}c=-1;break g}d=-1;if((c|0)!=-1){break k}c=-1;break g}d=F[(F[F[a>>2]+96>>2]+L(d,12)|0)+(f-L(d,3)<<2)>>2]}f=c+1|0;e=(f>>>0)%3|0?f:c-2|0;if((e|0)>=0){f=(e>>>0)/3|0;f=F[(F[F[a>>2]+96>>2]+L(f,12)|0)+(e-L(f,3)<<2)>>2]}else{f=-1}if((f|0)!=(d|0)){c=-1;break g}f=g;m=(c>>>0)/3|0;d=F[a+56>>2]+(m>>>3&268435452)|0;h=F[d>>2];e=1<<m;if(!(h&e)){continue}}break}if(l|!(g&1)){break e}l=F[j+12>>2]-4|0;g=F[l>>2];d=F[a+56>>2]+(g>>>3&536870908)|0;c=F[d>>2];o=d,p=oi(g)&c,F[o>>2]=p;F[j+12>>2]=l;break a}d=0;if(l){continue}break a}break}k=-1;Wc(a,-1);break a}na();v()}oa();v()}F[((b<<2)+a|0)+44>>2]=k;b=F[j+12>>2];i=F[j+8>>2];m:{if((b|0)==(i|0)){break m}c=b-i|0;b=c>>2;b=b>>>0<=1?1:b;k=b&1;e=F[a+56>>2];d=0;if(c>>>0>=8){f=b&-2;c=0;while(1){l=d<<2;g=F[l+i>>2];b=e+(g>>>3&536870908)|0;a=F[b>>2];o=b,p=oi(g)&a,F[o>>2]=p;g=F[i+(l|4)>>2];b=e+(g>>>3&536870908)|0;a=F[b>>2];o=b,p=oi(g)&a,F[o>>2]=p;d=d+2|0;c=c+2|0;if((f|0)!=(c|0)){continue}break}}if(!k){break m}c=F[i+(d<<2)>>2];b=e+(c>>>3&536870908)|0;a=F[b>>2];o=b,p=oi(c)&a,F[o>>2]=p}}function pd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;if((b|0)==-1){return 1}g=(b>>>0)/3|0;if(!(F[F[a+24>>2]+(g>>>3&268435452)>>2]>>>g&1)){f=F[a+48>>2];F[a+52>>2]=f;a:{if((f|0)!=F[a+56>>2]){F[f>>2]=b;F[a+52>>2]=f+4;break a}d=ka(4);F[d>>2]=b;c=d+4|0;F[a+56>>2]=c;F[a+52>>2]=c;F[a+48>>2]=d;if(!f){break a}ja(f)}e=-1;d=F[a+4>>2];c=b+1|0;i=(c>>>0)%3|0?c:b-2|0;if((i|0)!=-1){e=F[F[d>>2]+(i<<2)>>2]}b:{h=b-L(g,3)|0;if(h){c=b-1|0;break b}c=b+2|0;if((c|0)!=-1){break b}return 0}if((e|0)==-1){return 0}j=F[F[d>>2]+(c<<2)>>2];if((j|0)==-1){return 0}c=F[a+36>>2];f=c+(e>>>3&536870908)|0;g=F[f>>2];d=1<<e;if(!(g&d)){F[f>>2]=d|g;Ka(a+8|0,e,i);c=F[a+36>>2]}g=(j>>>3&536870908)+c|0;d=F[g>>2];c=1<<j;if(!(d&c)){F[g>>2]=c|d;Ka(a+8|0,j,(h?-1:2)+b|0)}c=F[a+52>>2];if((c|0)==F[a+48>>2]){return 1}j=a+8|0;while(1){c:{d:{c=c-4|0;b=F[c>>2];if((b|0)==-1){break d}d=(b>>>0)/3|0;f=F[a+24>>2]+(d>>>3&268435452)|0;g=F[f>>2];d=1<<d;if(g&d){break d}F[f>>2]=d|g;while(1){i=F[a+4>>2];e=F[F[i>>2]+(b<<2)>>2];if((e|0)==-1){return 0}e:{f:{h=F[a+36>>2]+(e>>>3&536870908)|0;f=F[h>>2];g=1<<e;if(f&g){break f}g:{d=F[F[i+24>>2]+(e<<2)>>2];h:{if((d|0)==-1){break h}c=d+1|0;c=(c>>>0)%3|0?c:d-2|0;if((c|0)==-1){break h}d=F[F[i+12>>2]+(c<<2)>>2];if((d|0)!=-1){break g}}F[h>>2]=f|g;Ka(j,e,b);break f}F[h>>2]=f|g;Ka(j,e,b);c=d+1|0;if((((c>>>0)%3|0?c:d-2|0)|0)==-1){break f}c=b-2|0;d=b+1|0;b=-1;c=(d>>>0)%3|0?d:c;if((c|0)!=-1){b=F[F[F[a+4>>2]+12>>2]+(c<<2)>>2]}c=(b>>>0)/3|0;d=1<<c;e=F[a+24>>2];f=c>>>5|0;i=F[e+(f<<2)>>2];break e}c=-1;g=F[a+4>>2];d=b+1|0;d=(d>>>0)%3|0?d:b-2|0;if((d|0)!=-1){c=F[F[g+12>>2]+(d<<2)>>2]}i:{j:{if((b>>>0)%3|0){e=b-1|0;break j}e=b+2|0;b=-1;if((e|0)==-1){break i}}b=F[F[g+12>>2]+(e<<2)>>2]}g=(b|0)==-1;h=g?-1:(b>>>0)/3|0;k:{if((c|0)!=-1){e=F[a+24>>2];d=(c>>>0)/3|0;f=d>>>5|0;i=F[e+(f<<2)>>2];d=1<<d;if(!(i&d)){break k}}if(!g){d=1<<h;e=F[a+24>>2];f=h>>>5|0;i=F[e+(f<<2)>>2];if(!(d&i)){break e}}c=F[a+52>>2]-4|0;F[a+52>>2]=c;break c}if(g){b=c;break e}if(F[(h>>>3&536870908)+e>>2]>>>h&1){b=c;break e}e=F[a+52>>2];F[e-4>>2]=b;if(F[a+56>>2]!=(e|0)){F[e>>2]=c;c=e+4|0;break d}l:{h=F[a+48>>2];f=e-h|0;g=f>>2;d=g+1|0;if(d>>>0<1073741824){b=f>>>1|0;f=f>>>0>=2147483644?1073741823:b>>>0>d>>>0?b:d;if(f){if(f>>>0>=1073741824){break l}d=ka(f<<2)}else{d=0}b=d+(g<<2)|0;F[b>>2]=c;c=b+4|0;if((e|0)!=(h|0)){while(1){b=b-4|0;e=e-4|0;F[b>>2]=F[e>>2];if((e|0)!=(h|0)){continue}break}}F[a+56>>2]=d+(f<<2);F[a+52>>2]=c;F[a+48>>2]=b;if(!h){break c}ja(h);c=F[a+52>>2];break c}na();v()}oa();v()}F[(f<<2)+e>>2]=d|i;if((b|0)!=-1){continue}break}return 0}F[a+52>>2]=c}if(F[a+48>>2]!=(c|0)){continue}break}}return 1}function ee(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=Z-32|0;Z=h;a:{b:{if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[F[a+48>>2]+64>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}c:{if(d){Na(a+60|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+60>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[F[a+48>>2]+64>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}if(d){e=0;Na(a+72|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+72>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[F[a+48>>2]+64>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}if(d){e=0;Na(a+84|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+84>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[F[a+48>>2]+64>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}if(d){e=0;Na(a+96|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+96>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}e=0;c=F[b+8>>2];f=F[b+12>>2];d=c;c=F[b+20>>2];i=c;g=F[b+16>>2];j=g+4|0;c=j>>>0<4?c+1|0:c;if(d>>>0<j>>>0&(c|0)>=(f|0)|(c|0)>(f|0)){break a}m=F[b>>2];k=m+g|0;l=G[k|0]|G[k+1|0]<<8|(G[k+2|0]<<16|G[k+3|0]<<24);F[b+16>>2]=j;F[b+20>>2]=c;k=d;d=f;c=i;f=g+8|0;c=f>>>0<8?c+1|0:c;if(f>>>0>k>>>0&(c|0)>=(d|0)|(c|0)>(d|0)){break a}d=j+m|0;d=G[d|0]|G[d+1|0]<<8|(G[d+2|0]<<16|G[d+3|0]<<24);F[b+16>>2]=f;F[b+20>>2]=c;if((d|0)<(l|0)){break a}F[a+16>>2]=d;F[a+12>>2]=l;c=(d>>31)-((l>>31)+(d>>>0<l>>>0)|0)|0;b=d-l|0;if(!c&b>>>0>2147483646|c){break a}e=1;b=b+1|0;F[a+20>>2]=b;c=b>>>1|0;F[a+24>>2]=c;F[a+28>>2]=0-c;if(b&1){break a}F[a+24>>2]=c-1;break a}}e=0}Z=h+32|0;return e|0}function ai(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=Z-32|0;Z=h;a:{b:{if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[a+48>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}c:{if(d){Na(a+60|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+60>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[a+48>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}if(d){e=0;Na(a+72|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+72>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[a+48>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}if(d){e=0;Na(a+84|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+84>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}if(!Oa(1,h+28|0,b)){break b}d=F[h+28>>2];c=F[a+48>>2];if(d>>>0>F[c+4>>2]-F[c>>2]>>2>>>0){break b}if(d){e=0;Na(a+96|0,d);c=h+8|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;if(!Aa(c,b)){break c}while(1){f=1<<e;j=wa(c);g=F[a+96>>2]+(e>>>3&536870908)|0;if(j){i=f|F[g>>2]}else{i=F[g>>2]&(f^-1)}F[g>>2]=i;e=e+1|0;if((d|0)!=(e|0)){continue}break}}e=0;c=F[b+8>>2];f=F[b+12>>2];d=c;c=F[b+20>>2];i=c;g=F[b+16>>2];j=g+4|0;c=j>>>0<4?c+1|0:c;if(d>>>0<j>>>0&(c|0)>=(f|0)|(c|0)>(f|0)){break a}m=F[b>>2];k=m+g|0;l=G[k|0]|G[k+1|0]<<8|(G[k+2|0]<<16|G[k+3|0]<<24);F[b+16>>2]=j;F[b+20>>2]=c;k=d;d=f;c=i;f=g+8|0;c=f>>>0<8?c+1|0:c;if(f>>>0>k>>>0&(c|0)>=(d|0)|(c|0)>(d|0)){break a}d=j+m|0;d=G[d|0]|G[d+1|0]<<8|(G[d+2|0]<<16|G[d+3|0]<<24);F[b+16>>2]=f;F[b+20>>2]=c;if((d|0)<(l|0)){break a}F[a+16>>2]=d;F[a+12>>2]=l;c=(d>>31)-((l>>31)+(d>>>0<l>>>0)|0)|0;b=d-l|0;if(!c&b>>>0>2147483646|c){break a}e=1;b=b+1|0;F[a+20>>2]=b;c=b>>>1|0;F[a+24>>2]=c;F[a+28>>2]=0-c;if(b&1){break a}F[a+24>>2]=c-1;break a}}e=0}Z=h+32|0;return e|0}function uh(a){a=a|0;var b=0,c=0,d=0,e=0;c=F[a+32>>2];d=F[c+16>>2];e=F[c+12>>2];b=F[c+20>>2];if(I[c+8>>2]>d>>>0&(e|0)>=(b|0)|(b|0)<(e|0)){e=G[F[c>>2]+d|0];d=d+1|0;b=d?b:b+1|0;F[c+16>>2]=d;F[c+20>>2]=b;b=F[a+48>>2];F[a+48>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}a:{b:{c:{d:{switch(e|0){case 0:b=ka(384);F[b>>2]=8284;ma(b+4|0,0,80);F[b+96>>2]=0;F[b+100>>2]=0;F[b+92>>2]=-1;F[b+84>>2]=-1;F[b+88>>2]=-1;F[b+104>>2]=0;F[b+108>>2]=0;F[b+112>>2]=0;F[b+116>>2]=0;F[b+120>>2]=0;F[b+124>>2]=0;F[b+128>>2]=0;F[b+132>>2]=0;F[b+136>>2]=0;F[b+140>>2]=0;F[b+144>>2]=0;F[b+148>>2]=0;F[b+156>>2]=0;F[b+160>>2]=0;F[b+152>>2]=1065353216;F[b+164>>2]=0;F[b+168>>2]=0;F[b+172>>2]=0;F[b+176>>2]=0;F[b+180>>2]=0;F[b+184>>2]=0;F[b+188>>2]=0;F[b+192>>2]=0;F[b+196>>2]=0;F[b+200>>2]=0;F[b+204>>2]=0;F[b+208>>2]=0;F[b+212>>2]=-1;F[b+216>>2]=0;F[b+220>>2]=0;F[b+224>>2]=0;Ja(b+232|0);Ja(b+272|0);c=b+312|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;Ja(b+328|0);F[b+376>>2]=0;F[b+368>>2]=0;F[b+372>>2]=0;break c;case 2:break d;default:break b}}b=ka(440);F[b>>2]=8336;ma(b+4|0,0,80);F[b+96>>2]=0;F[b+100>>2]=0;F[b+92>>2]=-1;F[b+84>>2]=-1;F[b+88>>2]=-1;F[b+104>>2]=0;F[b+108>>2]=0;F[b+112>>2]=0;F[b+116>>2]=0;F[b+120>>2]=0;F[b+124>>2]=0;F[b+128>>2]=0;F[b+132>>2]=0;F[b+136>>2]=0;F[b+140>>2]=0;F[b+144>>2]=0;F[b+148>>2]=0;F[b+156>>2]=0;F[b+160>>2]=0;F[b+152>>2]=1065353216;F[b+164>>2]=0;F[b+168>>2]=0;F[b+172>>2]=0;F[b+176>>2]=0;F[b+180>>2]=0;F[b+184>>2]=0;F[b+188>>2]=0;F[b+192>>2]=0;F[b+196>>2]=0;F[b+200>>2]=0;F[b+204>>2]=0;F[b+208>>2]=0;F[b+212>>2]=-1;F[b+216>>2]=0;F[b+220>>2]=0;F[b+224>>2]=0;Ja(b+232|0);Ja(b+272|0);c=b+312|0;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;Ja(b+328|0);F[b+392>>2]=0;F[b+396>>2]=0;F[b+384>>2]=0;F[b+388>>2]=0;F[b+376>>2]=0;F[b+380>>2]=0;F[b+368>>2]=0;F[b+372>>2]=0;F[b+416>>2]=0;F[b+420>>2]=0;F[b+408>>2]=2;F[b+412>>2]=7;F[b+400>>2]=-1;F[b+404>>2]=-1;F[b+424>>2]=0;F[b+428>>2]=0;F[b+432>>2]=0;F[b+436>>2]=0}c=F[a+48>>2];F[a+48>>2]=b;if(!c){break a}$[F[F[c>>2]+4>>2]](c)}b=F[a+48>>2];if(b){break a}return 0}a=$[F[F[b>>2]+8>>2]](b,a)|0}else{a=0}return a|0}function ei(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;g=Z-32|0;Z=g;F[a+68>>2]=f;d=F[a+56>>2];e=F[d>>2];d=F[d+4>>2];F[g+24>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;a:{d=d-e|0;if((d|0)>0){m=a+60|0;d=d>>>2|0;n=d>>>0<=1?1:d;o=a+112|0;while(1){e=F[a+56>>2];d=F[e>>2];if(F[e+4>>2]-d>>2>>>0<=j>>>0){break a}Mb(m,F[d+(j<<2)>>2],g+16|0);i=F[g+20>>2];d=i>>31;h=F[g+16>>2];e=h>>31;f=(d^i)-d+((e^h)-e)|0;k=F[g+24>>2];d=k>>31;e=(d^k)-d|0;d=0;l=e;e=e+f|0;d=l>>>0>e>>>0?1:d;b:{if(!(d|e)){F[g+16>>2]=F[a+108>>2];break b}f=F[a+108>>2];l=f>>31;h=li(ki(f,l,h,h>>31),_,e,d);F[g+16>>2]=h;d=li(ki(f,l,i,i>>31),_,e,d);F[g+20>>2]=d;e=d;d=d>>31;e=(e^d)-d|0;d=h>>31;d=e+((d^h)-d|0)|0;if((k|0)>=0){F[g+24>>2]=f-d;break b}F[g+24>>2]=d-f}d=wa(o);f=F[g+16>>2];c:{if(d){F[g+24>>2]=0-F[g+24>>2];e=0-F[g+20>>2]|0;F[g+20>>2]=e;f=0-f|0;F[g+16>>2]=f;break c}e=F[g+20>>2]}d:{if((f|0)>=0){f=F[a+108>>2];d=f+F[g+24>>2]|0;f=e+f|0;break d}e:{if((e|0)<0){d=F[g+24>>2];f=d>>31;f=(d^f)-f|0;break e}d=F[g+24>>2];f=d>>31;f=F[a+100>>2]+(f-(d^f)|0)|0}if((d|0)<0){d=e>>31;d=(d^e)-d|0;break d}d=e>>31;d=F[a+100>>2]+(d-(d^e)|0)|0}e=F[a+100>>2];f:{if(!(d|f)){d=e;f=d;break f}if(!((d|0)!=(e|0)|f)){f=d;break f}if(!((e|0)!=(f|0)|d)){d=f;break f}g:{if(f){break g}i=F[a+108>>2];if((i|0)>=(d|0)){break g}d=(i<<1)-d|0;f=0;break f}h:{if((e|0)!=(f|0)){break h}i=F[a+108>>2];if((i|0)<=(d|0)){break h}d=(i<<1)-d|0;break f}i:{if((d|0)!=(e|0)){break i}e=F[a+108>>2];if((e|0)<=(f|0)){break i}f=(e<<1)-f|0;break f}if(d){break f}d=0;e=F[a+108>>2];if((e|0)>=(f|0)){break f}f=(e<<1)-f|0}F[g+12>>2]=d;F[g+8>>2]=f;j:{if(F[a+8>>2]<=0){break j}i=F[a+32>>2];f=0;while(1){d=f<<2;e=F[d+(g+8|0)>>2];h=F[a+16>>2];k:{if((e|0)>(h|0)){F[d+i>>2]=h;break k}d=d+i|0;h=F[a+12>>2];if((h|0)>(e|0)){F[d>>2]=h;break k}F[d>>2]=e}f=f+1|0;e=F[a+8>>2];if((f|0)<(e|0)){continue}break}d=0;if((e|0)<=0){break j}e=j<<3;h=e+c|0;k=b+e|0;while(1){f=d<<2;e=f+h|0;f=F[f+k>>2]+F[f+i>>2]|0;F[e>>2]=f;l:{if((f|0)>F[a+16>>2]){f=f-F[a+20>>2]|0}else{if((f|0)>=F[a+12>>2]){break l}f=f+F[a+20>>2]|0}F[e>>2]=f}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}}j=j+1|0;if((n|0)!=(j|0)){continue}break}}Z=g+32|0;return 1}ta();v()}function Vh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;g=Z-32|0;Z=g;F[a+68>>2]=f;d=F[a+56>>2];e=F[d>>2];d=F[d+4>>2];F[g+24>>2]=0;F[g+16>>2]=0;F[g+20>>2]=0;a:{d=d-e|0;if((d|0)>0){m=a+60|0;d=d>>>2|0;n=d>>>0<=1?1:d;o=a+112|0;while(1){e=F[a+56>>2];d=F[e>>2];if(F[e+4>>2]-d>>2>>>0<=j>>>0){break a}Kb(m,F[d+(j<<2)>>2],g+16|0);i=F[g+20>>2];d=i>>31;h=F[g+16>>2];e=h>>31;f=(d^i)-d+((e^h)-e)|0;k=F[g+24>>2];d=k>>31;e=(d^k)-d|0;d=0;l=e;e=e+f|0;d=l>>>0>e>>>0?1:d;b:{if(!(d|e)){F[g+16>>2]=F[a+108>>2];break b}f=F[a+108>>2];l=f>>31;h=li(ki(f,l,h,h>>31),_,e,d);F[g+16>>2]=h;d=li(ki(f,l,i,i>>31),_,e,d);F[g+20>>2]=d;e=d;d=d>>31;e=(e^d)-d|0;d=h>>31;d=e+((d^h)-d|0)|0;if((k|0)>=0){F[g+24>>2]=f-d;break b}F[g+24>>2]=d-f}d=wa(o);f=F[g+16>>2];c:{if(d){F[g+24>>2]=0-F[g+24>>2];e=0-F[g+20>>2]|0;F[g+20>>2]=e;f=0-f|0;F[g+16>>2]=f;break c}e=F[g+20>>2]}d:{if((f|0)>=0){f=F[a+108>>2];d=f+F[g+24>>2]|0;f=e+f|0;break d}e:{if((e|0)<0){d=F[g+24>>2];f=d>>31;f=(d^f)-f|0;break e}d=F[g+24>>2];f=d>>31;f=F[a+100>>2]+(f-(d^f)|0)|0}if((d|0)<0){d=e>>31;d=(d^e)-d|0;break d}d=e>>31;d=F[a+100>>2]+(d-(d^e)|0)|0}e=F[a+100>>2];f:{if(!(d|f)){d=e;f=d;break f}if(!((d|0)!=(e|0)|f)){f=d;break f}if(!((e|0)!=(f|0)|d)){d=f;break f}g:{if(f){break g}i=F[a+108>>2];if((i|0)>=(d|0)){break g}d=(i<<1)-d|0;f=0;break f}h:{if((e|0)!=(f|0)){break h}i=F[a+108>>2];if((i|0)<=(d|0)){break h}d=(i<<1)-d|0;break f}i:{if((d|0)!=(e|0)){break i}e=F[a+108>>2];if((e|0)<=(f|0)){break i}f=(e<<1)-f|0;break f}if(d){break f}d=0;e=F[a+108>>2];if((e|0)>=(f|0)){break f}f=(e<<1)-f|0}F[g+12>>2]=d;F[g+8>>2]=f;j:{if(F[a+8>>2]<=0){break j}i=F[a+32>>2];f=0;while(1){d=f<<2;e=F[d+(g+8|0)>>2];h=F[a+16>>2];k:{if((e|0)>(h|0)){F[d+i>>2]=h;break k}d=d+i|0;h=F[a+12>>2];if((h|0)>(e|0)){F[d>>2]=h;break k}F[d>>2]=e}f=f+1|0;e=F[a+8>>2];if((f|0)<(e|0)){continue}break}d=0;if((e|0)<=0){break j}e=j<<3;h=e+c|0;k=b+e|0;while(1){f=d<<2;e=f+h|0;f=F[f+k>>2]+F[f+i>>2]|0;F[e>>2]=f;l:{if((f|0)>F[a+16>>2]){f=f-F[a+20>>2]|0}else{if((f|0)>=F[a+12>>2]){break l}f=f+F[a+20>>2]|0}F[e>>2]=f}d=d+1|0;if((d|0)<F[a+8>>2]){continue}break}}j=j+1|0;if((n|0)!=(j|0)){continue}break}}Z=g+32|0;return 1}ta();v()}function $a(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=Z-32|0;Z=h;a:{b:{if(F[a+8>>2]<<5>>>0>=b>>>0){break b}if((b|0)<0){break a}b=(b-1>>>5|0)+1|0;c=ka(b<<2);F[h+24>>2]=b;F[h+20>>2]=0;F[h+16>>2]=c;b=F[a>>2];F[h+12>>2]=0;F[h+8>>2]=b;c=F[a+4>>2];F[h+4>>2]=c&31;F[h>>2]=b+(c>>>3&536870908);e=Z-32|0;Z=e;i=F[h+4>>2];g=F[h+12>>2];j=F[h>>2];d=F[h+8>>2];b=(i-g|0)+(j-d<<3)|0;f=F[h+20>>2];c=b+f|0;F[h+20>>2]=c;if(!((c-1^f-1)>>>0<32?f:0)){F[F[h+16>>2]+((c>>>0>=33?c-1>>>5|0:0)<<2)>>2]=0}c=F[h+16>>2]+(f>>>3&536870908)|0;f=f&31;c:{if((f|0)==(g|0)){if((b|0)<=0){break c}if(g){i=32-g|0;f=(b|0)<(i|0)?b:i;i=-1<<g&-1>>>i-f;F[c>>2]=F[c>>2]&(i^-1)|i&F[d>>2];d=d+4|0;c=(g+f>>>3&536870908)+c|0;b=b-f|0}g=(b|0)/32|0;if(b+31>>>0>=63){pa(c,d,g<<2)}b=b-(g<<5)|0;if((b|0)<=0){break c}f=c;c=g<<2;g=f+c|0;b=-1>>>32-b|0;F[g>>2]=F[g>>2]&(b^-1)|b&F[c+d>>2];break c}F[e+28>>2]=g;F[e+24>>2]=d;F[e+20>>2]=i;F[e+16>>2]=j;F[e+12>>2]=f;F[e+8>>2]=c;b=F[e+28>>2];c=F[e+24>>2];g=(F[e+20>>2]-b|0)+(F[e+16>>2]-c<<3)|0;d:{if((g|0)<=0){b=F[e+12>>2];d=F[e+8>>2];break d}e:{if(!b){b=F[e+12>>2];break e}d=F[e+12>>2];j=32-d|0;k=32-b|0;f=(g|0)<(k|0)?g:k;i=f>>>0>j>>>0?j:f;l=F[e+8>>2];m=F[l>>2]&(-1<<d&-1>>>j-i^-1);j=F[c>>2]&(-1<<b&-1>>>k-f);F[l>>2]=m|(b>>>0<d>>>0?j<<d-b:j>>>b-d|0);c=d+i|0;b=c&31;F[e+12>>2]=b;d=l+(c>>>3&536870908)|0;F[e+8>>2]=d;c=f-i|0;if((c|0)>0){F[d>>2]=F[d>>2]&(-1>>>32-c^-1)|j>>>i+F[e+28>>2];F[e+12>>2]=c;b=c}g=g-f|0;c=F[e+24>>2]+4|0;F[e+24>>2]=c}i=-1<<b;f=32-b|0;if((g|0)>=32){j=i^-1;while(1){d=F[e+8>>2];c=F[c>>2];F[d>>2]=j&F[d>>2]|c<<b;F[e+8>>2]=d+4;F[d+4>>2]=i&F[d+4>>2]|c>>>f;c=F[e+24>>2]+4|0;F[e+24>>2]=c;d=g>>>0>63;g=g-32|0;if(d){continue}break}}d=F[e+8>>2];if((g|0)<=0){break d}j=f;f=(g|0)>(f|0)?f:g;j=F[d>>2]&(i&-1>>>j-f^-1);i=F[c>>2]&-1>>>32-g;F[d>>2]=j|i<<b;b=b+f|0;c=b&31;F[e+12>>2]=c;d=(b>>>3&536870908)+d|0;F[e+8>>2]=d;b=g-f|0;if((b|0)<=0){b=c;break d}F[d>>2]=F[d>>2]&(-1>>>32-b^-1)|i>>>f;F[e+12>>2]=b}F[e+4>>2]=b;F[e>>2]=d}Z=e+32|0;b=F[a>>2];F[a>>2]=F[h+16>>2];F[h+16>>2]=b;c=F[a+4>>2];F[a+4>>2]=F[h+20>>2];F[h+20>>2]=c;c=F[a+8>>2];F[a+8>>2]=F[h+24>>2];F[h+24>>2]=c;if(!b){break b}ja(b)}Z=h+32|0;return}na();v()}function xc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;n=$[F[F[a>>2]+44>>2]](a)|0;a:{if((n|0)<=0){break a}i=F[b+4>>2]-F[b>>2]>>2;e=Z+-64|0;Z=e;f=kb(e);d=L(F[2541],n);cc(f,F[F[a+8>>2]+56>>2],n&255,5,0,d,d>>31);f=bc(ka(96),f);D[f+84|0]=1;F[f+72>>2]=F[f+68>>2];ac(f,i);F[f+60>>2]=F[F[a+8>>2]+60>>2];d=F[a+16>>2];F[a+16>>2]=f;if(d){xa(d)}Z=e- -64|0;h=F[a+16>>2];if(!F[h+80>>2]){break a}j=F[F[h>>2]>>2];if(!j){break a}m=F[c+12>>2];e=m;d=F[c+20>>2];g=F[c+8>>2];k=F[c+16>>2];if((e|0)<=(d|0)&g>>>0<=k>>>0|(d|0)>(e|0)){break a}l=L(i,n);i=j+F[h+48>>2]|0;h=F[c>>2];j=G[h+k|0];e=k+1|0;f=e?d:d+1|0;F[c+16>>2]=e;F[c+20>>2]=f;b:{c:{if(j){if(mc(l,n,c,i)){break c}break a}if((f|0)>=(m|0)&e>>>0>=g>>>0|(f|0)>(m|0)){break a}g=G[e+h|0];f=k+2|0;d=f>>>0<2?d+1|0:d;F[c+16>>2]=f;F[c+20>>2]=d;d=F[F[a+16>>2]+64>>2];d=F[d+4>>2]-F[d>>2]|0;if((g|0)==F[2541]){e=l<<2;if(e>>>0>d>>>0){break a}g=F[c+8>>2];k=F[c+12>>2];j=F[c+20>>2];d=F[c+16>>2];f=e+d|0;j=f>>>0<e>>>0?j+1|0:j;if(f>>>0>g>>>0&(j|0)>=(k|0)|(j|0)>(k|0)){break a}la(i,d+F[c>>2]|0,e);f=F[c+20>>2];d=e+F[c+16>>2]|0;f=d>>>0<e>>>0?f+1|0:f;F[c+16>>2]=d;F[c+20>>2]=f;break c}if(d>>>0<L(g,l)>>>0){break a}d=F[c+8>>2];f=F[c+16>>2];e=d-f|0;m=d>>>0<f>>>0;d=F[c+20>>2];k=F[c+12>>2]-(m+d|0)|0;m=ki(g,0,l,0)>>>0>e>>>0;e=_;if(m&(e|0)>=(k|0)|(e|0)>(k|0)){break a}e=1;if(!l){break b}h=0;while(1){k=F[c+8>>2];j=F[c+12>>2];e=f+g|0;d=e>>>0<g>>>0?d+1|0:d;if(e>>>0>k>>>0&(d|0)>=(j|0)|(d|0)>(j|0)){return 0}la(i+(h<<2)|0,F[c>>2]+f|0,g);d=F[c+20>>2];f=g+F[c+16>>2]|0;d=f>>>0<g>>>0?d+1|0:d;F[c+16>>2]=f;F[c+20>>2]=d;h=h+1|0;if((l|0)!=(h|0)){continue}break}}e=1;if(!l){break b}d=F[a+20>>2];if(d){e=0;if($[F[F[d>>2]+32>>2]](d)|0){break b}}g=0;h=0;d:{if((l|0)<=0){break d}if((l|0)!=1){f=l&-2;while(1){e=g<<2;d=F[e+i>>2];F[e+i>>2]=0-(d&1)^d>>>1;d=e|4;e=F[d+i>>2];F[d+i>>2]=0-(e&1)^e>>>1;g=g+2|0;h=h+2|0;if((f|0)!=(h|0)){continue}break}}if(!(l&1)){break d}d=g<<2;f=F[d+i>>2];F[d+i>>2]=0-(f&1)^f>>>1}e=0}d=e;f=F[a+20>>2];e:{if(!f){break e}if(!($[F[F[f>>2]+40>>2]](f,c)|0)){break a}if(d){break e}a=F[a+20>>2];if(!($[F[F[a>>2]+44>>2]](a,i,i,l,n,F[b>>2])|0)){break a}}o=1}return o|0}function Lh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;g=Z-48|0;Z=g;d=F[a+8>>2];if(d-2>>>0<=28){F[a+76>>2]=d;e=-1<<d;d=-2-e|0;F[a+84>>2]=d;F[a+80>>2]=e^-1;F[a+92>>2]=(d|0)/2;J[a+88>>2]=M(2)/M(d|0)}F[a+52>>2]=f;d=F[a+40>>2];e=F[d>>2];d=F[d+4>>2];F[g+16>>2]=0;F[g+8>>2]=0;F[g+12>>2]=0;a:{d=d-e|0;if((d|0)>0){m=a+8|0;n=a+44|0;d=d>>>2|0;o=d>>>0<=1?1:d;p=a+96|0;while(1){e=F[a+40>>2];d=F[e>>2];if(F[e+4>>2]-d>>2>>>0<=j>>>0){break a}Mb(n,F[d+(j<<2)>>2],g+8|0);h=F[g+12>>2];d=h>>31;i=F[g+8>>2];e=i>>31;f=(d^h)-d+((e^i)-e)|0;l=F[g+16>>2];d=l>>31;e=(d^l)-d|0;d=0;k=e;e=e+f|0;d=k>>>0>e>>>0?1:d;b:{if(!(d|e)){F[g+8>>2]=F[a+92>>2];break b}f=F[a+92>>2];k=f>>31;i=li(ki(f,k,i,i>>31),_,e,d);F[g+8>>2]=i;d=li(ki(f,k,h,h>>31),_,e,d);F[g+12>>2]=d;e=d>>31;e=(d^e)-e|0;d=i>>31;d=e+((d^i)-d|0)|0;if((l|0)>=0){F[g+16>>2]=f-d;break b}F[g+16>>2]=d-f}d=wa(p);f=F[g+8>>2];c:{if(d){F[g+16>>2]=0-F[g+16>>2];e=0-F[g+12>>2]|0;F[g+12>>2]=e;f=0-f|0;F[g+8>>2]=f;break c}e=F[g+12>>2]}d:{if((f|0)>=0){f=F[a+92>>2];d=f+F[g+16>>2]|0;f=e+f|0;break d}e:{if((e|0)<0){d=F[g+16>>2];f=d>>31;f=(d^f)-f|0;break e}d=F[g+16>>2];f=d>>31;f=F[a+84>>2]+(f-(d^f)|0)|0}if((d|0)<0){d=e>>31;d=(d^e)-d|0;break d}d=e>>31;d=F[a+84>>2]+(d-(d^e)|0)|0}e=F[a+84>>2];f:{if(!(d|f)){d=e;f=d;break f}if(!((d|0)!=(e|0)|f)){f=d;break f}if(!((e|0)!=(f|0)|d)){d=f;break f}g:{if(f){break g}h=F[a+92>>2];if((h|0)>=(d|0)){break g}d=(h<<1)-d|0;f=0;break f}h:{if((e|0)!=(f|0)){break h}h=F[a+92>>2];if((h|0)<=(d|0)){break h}d=(h<<1)-d|0;break f}i:{if((d|0)!=(e|0)){break i}e=F[a+92>>2];if((e|0)<=(f|0)){break i}f=(e<<1)-f|0;break f}if(d){break f}d=0;e=F[a+92>>2];if((e|0)>=(f|0)){break f}f=(e<<1)-f|0}e=j<<3;h=e+b|0;i=F[h>>2];h=F[h+4>>2];F[g+36>>2]=d;F[g+32>>2]=f;F[g+24>>2]=i;F[g+28>>2]=h;Jb(g+40|0,m,g+32|0,g+24|0);d=c+e|0;F[d>>2]=F[g+40>>2];F[d+4>>2]=F[g+44>>2];j=j+1|0;if((o|0)!=(j|0)){continue}break}}Z=g+48|0;return 1}ta();v()}function Hh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;g=Z-48|0;Z=g;d=F[a+8>>2];if(d-2>>>0<=28){F[a+76>>2]=d;e=-1<<d;d=-2-e|0;F[a+84>>2]=d;F[a+80>>2]=e^-1;F[a+92>>2]=(d|0)/2;J[a+88>>2]=M(2)/M(d|0)}F[a+52>>2]=f;d=F[a+40>>2];e=F[d>>2];d=F[d+4>>2];F[g+16>>2]=0;F[g+8>>2]=0;F[g+12>>2]=0;a:{d=d-e|0;if((d|0)>0){m=a+8|0;n=a+44|0;d=d>>>2|0;o=d>>>0<=1?1:d;p=a+96|0;while(1){e=F[a+40>>2];d=F[e>>2];if(F[e+4>>2]-d>>2>>>0<=j>>>0){break a}Kb(n,F[d+(j<<2)>>2],g+8|0);h=F[g+12>>2];d=h>>31;i=F[g+8>>2];e=i>>31;f=(d^h)-d+((e^i)-e)|0;l=F[g+16>>2];d=l>>31;e=(d^l)-d|0;d=0;k=e;e=e+f|0;d=k>>>0>e>>>0?1:d;b:{if(!(d|e)){F[g+8>>2]=F[a+92>>2];break b}f=F[a+92>>2];k=f>>31;i=li(ki(f,k,i,i>>31),_,e,d);F[g+8>>2]=i;d=li(ki(f,k,h,h>>31),_,e,d);F[g+12>>2]=d;e=d>>31;e=(d^e)-e|0;d=i>>31;d=e+((d^i)-d|0)|0;if((l|0)>=0){F[g+16>>2]=f-d;break b}F[g+16>>2]=d-f}d=wa(p);f=F[g+8>>2];c:{if(d){F[g+16>>2]=0-F[g+16>>2];e=0-F[g+12>>2]|0;F[g+12>>2]=e;f=0-f|0;F[g+8>>2]=f;break c}e=F[g+12>>2]}d:{if((f|0)>=0){f=F[a+92>>2];d=f+F[g+16>>2]|0;f=e+f|0;break d}e:{if((e|0)<0){d=F[g+16>>2];f=d>>31;f=(d^f)-f|0;break e}d=F[g+16>>2];f=d>>31;f=F[a+84>>2]+(f-(d^f)|0)|0}if((d|0)<0){d=e>>31;d=(d^e)-d|0;break d}d=e>>31;d=F[a+84>>2]+(d-(d^e)|0)|0}e=F[a+84>>2];f:{if(!(d|f)){d=e;f=d;break f}if(!((d|0)!=(e|0)|f)){f=d;break f}if(!((e|0)!=(f|0)|d)){d=f;break f}g:{if(f){break g}h=F[a+92>>2];if((h|0)>=(d|0)){break g}d=(h<<1)-d|0;f=0;break f}h:{if((e|0)!=(f|0)){break h}h=F[a+92>>2];if((h|0)<=(d|0)){break h}d=(h<<1)-d|0;break f}i:{if((d|0)!=(e|0)){break i}e=F[a+92>>2];if((e|0)<=(f|0)){break i}f=(e<<1)-f|0;break f}if(d){break f}d=0;e=F[a+92>>2];if((e|0)>=(f|0)){break f}f=(e<<1)-f|0}e=j<<3;h=e+b|0;i=F[h>>2];h=F[h+4>>2];F[g+36>>2]=d;F[g+32>>2]=f;F[g+24>>2]=i;F[g+28>>2]=h;Jb(g+40|0,m,g+32|0,g+24|0);d=c+e|0;F[d>>2]=F[g+40>>2];F[d+4>>2]=F[g+44>>2];j=j+1|0;if((o|0)!=(j|0)){continue}break}}Z=g+48|0;return 1}ta();v()}function Nd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;a:{if(!H[b+38>>1]){break a}if(!Ta(1,a+12|0,b)){break a}d=F[b+8>>2];e=F[b+16>>2];g=d-e|0;f=F[a+12>>2];d=F[b+12>>2]-(F[b+20>>2]+(d>>>0<e>>>0)|0)|0;if(g>>>0<f>>>6>>>0&(d|0)<=0|(d|0)<0){break a}d=F[a>>2];c=F[a+4>>2]-d>>2;b:{if(c>>>0<f>>>0){qa(a,f-c|0);f=F[a+12>>2];break b}if(c>>>0<=f>>>0){break b}F[a+4>>2]=d+(f<<2)}if(!f){return 1}d=F[b+16>>2];c=F[b+20>>2];l=F[a>>2];j=F[b+8>>2];i=F[b+12>>2];g=0;while(1){if((c|0)>=(i|0)&d>>>0>=j>>>0|(c|0)>(i|0)){return 0}m=F[b>>2];k=G[m+d|0];d=d+1|0;c=d?c:c+1|0;F[b+16>>2]=d;F[b+20>>2]=c;e=k>>>2|0;h=0;c:{d:{e:{f:{n=k&3;switch(n|0){case 3:break f;case 0:break d;default:break e}}e=e+g|0;if(e>>>0>=f>>>0){return 0}ma(l+(g<<2)|0,0,(k&252)+4|0);g=e;break c}while(1){if((d|0)==(j|0)&(c|0)==(i|0)){break a}f=G[d+m|0];d=d+1|0;c=d?c:c+1|0;F[b+16>>2]=d;F[b+20>>2]=c;e=f<<(h<<3|6)|e;h=h+1|0;if((n|0)!=(h|0)){continue}break}}F[l+(g<<2)>>2]=e}f=F[a+12>>2];g=g+1|0;if(f>>>0>g>>>0){continue}break}b=a+16|0;j=F[a>>2];d=F[a+16>>2];c=F[a+20>>2]-d|0;g:{if(c>>>0<=16383){qa(b,4096-(c>>>2|0)|0);break g}if((c|0)==16384){break g}F[a+20>>2]=d+16384}c=a+28|0;g=F[c>>2];d=F[a+32>>2]-g>>3;h:{if(d>>>0<f>>>0){_a(c,f-d|0);g=F[c>>2];break h}if(d>>>0>f>>>0){F[a+32>>2]=(f<<3)+g}if(!f){break a}}d=F[b>>2];b=0;a=0;while(1){c=j+(b<<2)|0;h=F[c>>2];e=a;i=(b<<3)+g|0;F[i+4>>2]=a;F[i>>2]=h;c=F[c>>2];a=c+a|0;if(a>>>0>4096){break a}i:{if(a>>>0<=e>>>0){break i}h=0;i=c&7;if(i){while(1){F[d+(e<<2)>>2]=b;e=e+1|0;h=h+1|0;if((i|0)!=(h|0)){continue}break}}if(c-1>>>0<=6){break i}while(1){c=d+(e<<2)|0;F[c>>2]=b;F[c+28>>2]=b;F[c+24>>2]=b;F[c+20>>2]=b;F[c+16>>2]=b;F[c+12>>2]=b;F[c+8>>2]=b;F[c+4>>2]=b;e=e+8|0;if((e|0)!=(a|0)){continue}break}}b=b+1|0;if((f|0)!=(b|0)){continue}break}o=(a|0)==4096}return o}function qf(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;f=Z-32|0;Z=f;e=f+8|0;c=Z-80|0;Z=c;a=F[b+36>>2];F[c+72>>2]=F[b+32>>2];F[c+76>>2]=a;d=F[b+28>>2];a=c- -64|0;F[a>>2]=F[b+24>>2];F[a+4>>2]=d;a=F[b+20>>2];F[c+56>>2]=F[b+16>>2];F[c+60>>2]=a;a=F[b+12>>2];F[c+48>>2]=F[b+8>>2];F[c+52>>2]=a;a=F[b+4>>2];F[c+40>>2]=F[b>>2];F[c+44>>2]=a;jc(c+8|0,c+40|0,c+24|0);a=F[c+8>>2];a:{if(a){F[e>>2]=a;a=e+4|0;if(D[c+23|0]>=0){b=c+8|4;e=F[b+4>>2];F[a>>2]=F[b>>2];F[a+4>>2]=e;F[a+8>>2]=F[b+8>>2];break a}ra(a,F[c+12>>2],F[c+16>>2]);if(D[c+23|0]>=0){break a}ja(F[c+12>>2]);break a}if(D[c+23|0]<0){ja(F[c+12>>2])}a=G[c+31|0];if(a>>>0>=2){b=ka(32);D[b+26|0]=0;a=G[1475]|G[1476]<<8;D[b+24|0]=a;D[b+25|0]=a>>>8;a=G[1471]|G[1472]<<8|(G[1473]<<16|G[1474]<<24);d=G[1467]|G[1468]<<8|(G[1469]<<16|G[1470]<<24);D[b+16|0]=d;D[b+17|0]=d>>>8;D[b+18|0]=d>>>16;D[b+19|0]=d>>>24;D[b+20|0]=a;D[b+21|0]=a>>>8;D[b+22|0]=a>>>16;D[b+23|0]=a>>>24;a=G[1463]|G[1464]<<8|(G[1465]<<16|G[1466]<<24);d=G[1459]|G[1460]<<8|(G[1461]<<16|G[1462]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=a;D[b+13|0]=a>>>8;D[b+14|0]=a>>>16;D[b+15|0]=a>>>24;a=G[1455]|G[1456]<<8|(G[1457]<<16|G[1458]<<24);d=G[1451]|G[1452]<<8|(G[1453]<<16|G[1454]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=a;D[b+5|0]=a>>>8;D[b+6|0]=a>>>16;D[b+7|0]=a>>>24;F[c+8>>2]=-1;a=c+8|4;ra(a,b,26);d=D[c+23|0];F[e>>2]=F[c+8>>2];e=e+4|0;if((d|0)>=0){d=F[a+4>>2];F[e>>2]=F[a>>2];F[e+4>>2]=d;F[e+8>>2]=F[a+8>>2];ja(b);break a}ra(e,F[c+12>>2],F[c+16>>2]);if(D[c+23|0]<0){ja(F[c+12>>2])}ja(b);break a}F[e>>2]=0;F[e+4>>2]=0;F[e+16>>2]=a;F[e+8>>2]=0;F[e+12>>2]=0}Z=c+80|0;a=F[f+24>>2];if(D[f+23|0]<0){ja(F[f+12>>2])}Z=f+32|0;return a|0}function Ph(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;e=Z-32|0;Z=e;a:{if((c|0)!=3){break a}c=F[a+4>>2];f=F[a+12>>2];F[e+24>>2]=-1;F[e+16>>2]=-1;F[e+20>>2]=1065353216;F[e+8>>2]=-1;F[e+12>>2]=-1;if((b|0)==-2){break a}i=F[F[F[c+4>>2]+8>>2]+(f<<2)>>2];if(($[F[F[c>>2]+8>>2]](c)|0)==1){h=F[F[F[c+4>>2]+8>>2]+(f<<2)>>2];b:{if(($[F[F[c>>2]+8>>2]](c)|0)!=1|b-1>>>0>5){break b}g=$[F[F[c>>2]+36>>2]](c)|0;a=$[F[F[c>>2]+44>>2]](c,f)|0;if(!g|!a){break b}f=$[F[F[c>>2]+40>>2]](c,f)|0;c:{if(f){if((b|0)!=6){break b}b=F[c+44>>2];d=ka(112);F[d+4>>2]=h;c=F[e+12>>2];F[d+8>>2]=F[e+8>>2];F[d+12>>2]=c;c=F[e+20>>2];F[d+16>>2]=F[e+16>>2];F[d+20>>2]=c;F[d+24>>2]=F[e+24>>2];F[d+40>>2]=a;c=a+12|0;F[d+36>>2]=c;F[d+32>>2]=f;F[d+28>>2]=b;F[d+68>>2]=a;F[d- -64>>2]=c;F[d+60>>2]=f;F[d+56>>2]=b;F[d+48>>2]=0;F[d+52>>2]=0;F[d>>2]=5928;F[d+88>>2]=1065353216;F[d+92>>2]=-1;F[d+80>>2]=-1;F[d+84>>2]=-1;F[d+72>>2]=1;F[d+76>>2]=-1;F[d+44>>2]=6492;a=d+96|0;break c}if((b|0)!=6){break b}b=F[c+44>>2];d=ka(112);F[d+4>>2]=h;c=F[e+12>>2];F[d+8>>2]=F[e+8>>2];F[d+12>>2]=c;c=F[e+20>>2];F[d+16>>2]=F[e+16>>2];F[d+20>>2]=c;F[d+24>>2]=F[e+24>>2];F[d+40>>2]=a;c=a+12|0;F[d+36>>2]=c;F[d+32>>2]=g;F[d+28>>2]=b;F[d+68>>2]=a;F[d- -64>>2]=c;F[d+60>>2]=g;F[d+56>>2]=b;F[d+48>>2]=0;F[d+52>>2]=0;F[d>>2]=6932;F[d+88>>2]=1065353216;F[d+92>>2]=-1;F[d+80>>2]=-1;F[d+84>>2]=-1;F[d+72>>2]=1;F[d+76>>2]=-1;F[d+44>>2]=7352;a=d+96|0}F[a>>2]=0;F[a+4>>2]=0;D[a+5|0]=0;D[a+6|0]=0;D[a+7|0]=0;D[a+8|0]=0;D[a+9|0]=0;D[a+10|0]=0;D[a+11|0]=0;D[a+12|0]=0}if(d){break a}}d=ka(28);F[d+4>>2]=i;a=F[e+12>>2];F[d+8>>2]=F[e+8>>2];F[d+12>>2]=a;a=F[e+20>>2];F[d+16>>2]=F[e+16>>2];F[d+20>>2]=a;F[d+24>>2]=F[e+24>>2];F[d>>2]=7764}Z=e+32|0;return d|0}function $c(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;f=Z-80|0;Z=f;a:{if(!Wb(1,f+76|0,b)){break a}k=F[f+76>>2];if(!k){break a}c=F[b+8>>2];e=F[b+16>>2];c=ki(c-e|0,F[b+12>>2]-(F[b+20>>2]+(c>>>0<e>>>0)|0)|0,5,0);e=_;if(c>>>0<k>>>0&(e|0)<=0|(e|0)<0){break a}c=F[a+4>>2];d=F[a+8>>2]-c>>2;b:{if(d>>>0<k>>>0){qa(a+4|0,k-d|0);break b}if(d>>>0<=k>>>0){break b}F[a+8>>2]=c+(k<<2)}p=a+16|0;l=F[a+32>>2];while(1){g=F[b+12>>2];c=g;d=F[b+20>>2];h=F[b+8>>2];e=F[b+16>>2];if((c|0)<=(d|0)&h>>>0<=e>>>0|(c|0)<(d|0)){d=0;break a}m=F[b>>2];q=G[m+e|0];c=d;i=e+1|0;c=i?c:c+1|0;F[b+16>>2]=i;F[b+20>>2]=c;if(h>>>0<=i>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){d=0;break a}i=G[i+m|0];c=d;j=e+2|0;c=j>>>0<2?c+1|0:c;F[b+16>>2]=j;F[b+20>>2]=c;if(h>>>0<=j>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){d=0;break a}j=G[j+m|0];c=d;n=e+3|0;c=n>>>0<3?c+1|0:c;F[b+16>>2]=n;F[b+20>>2]=c;if(h>>>0<=n>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){d=0;break a}h=G[m+n|0];c=d;d=e+4|0;c=d>>>0<4?c+1|0:c;F[b+16>>2]=d;F[b+20>>2]=c;if(q>>>0>4){d=0;break a}if((i-12&255)>>>0<245){d=0;break a}if(!j){d=0;break a}c=kb(f+8|0);g=(h|0)!=0;d=i-1|0;if(d>>>0<=10){d=F[(d<<2)+10148>>2]}else{d=-1}d=L(d,j);cc(c,q,j,i,g,d,d>>31);if(Wb(1,f+4|0,b)){e=F[f+4>>2];F[f+68>>2]=e;d=bc(ka(96),c);$[F[F[l>>2]+8>>2]](l,F[l+12>>2]-F[l+8>>2]>>2,d);d=(F[l+12>>2]-F[l+8>>2]>>2)-1|0;h=d<<2;F[F[h+F[l+8>>2]>>2]+60>>2]=e;F[F[a+4>>2]+(o<<2)>>2]=d;c=F[a+16>>2];e=F[a+20>>2]-c>>2;c:{if((e|0)>(d|0)){break c}F[f>>2]=-1;d=d+1|0;if(d>>>0>e>>>0){Fa(p,d-e|0,f);c=F[p>>2];break c}if(d>>>0>=e>>>0){break c}F[a+20>>2]=(d<<2)+c}F[c+h>>2]=o;d=1;o=o+1|0;if((o|0)!=(k|0)){continue}break a}break}d=0}Z=f+80|0;return d|0}function Oc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;k=Z-16|0;Z=k;F[k+8>>2]=c;h=F[a+12>>2];d=F[a+8>>2];g=h-d>>2;a:{if((g|0)>(b|0)){break a}e=b+1|0;if(e>>>0>g>>>0){l=e-g|0;f=F[a+16>>2];d=F[a+12>>2];if(l>>>0<=f-d>>2>>>0){if(l){e=d;d=l<<2;d=ma(e,0,d)+d|0}F[a+12>>2]=d;break a}b:{c:{d:{m=F[a+8>>2];g=d-m>>2;i=g+l|0;if(i>>>0<1073741824){e=f-m|0;f=e>>>1|0;e=e>>>0>=2147483644?1073741823:f>>>0>i>>>0?f:i;if(e){if(e>>>0>=1073741824){break d}j=ka(e<<2)}h=(g<<2)+j|0;f=l<<2;i=ma(h,0,f);g=f+i|0;e=(e<<2)+j|0;if((d|0)==(m|0)){break c}while(1){d=d-4|0;f=F[d>>2];F[d>>2]=0;h=h-4|0;F[h>>2]=f;if((d|0)!=(m|0)){continue}break}F[a+16>>2]=e;e=F[a+12>>2];F[a+12>>2]=g;d=F[a+8>>2];F[a+8>>2]=h;if((d|0)==(e|0)){break b}while(1){e=e-4|0;f=F[e>>2];F[e>>2]=0;if(f){xa(f)}if((d|0)!=(e|0)){continue}break}break b}na();v()}oa();v()}F[a+16>>2]=e;F[a+12>>2]=g;F[a+8>>2]=i}if(d){ja(d)}break a}if(e>>>0>=g>>>0){break a}d=d+(e<<2)|0;if((d|0)!=(h|0)){while(1){h=h-4|0;c=F[h>>2];F[h>>2]=0;if(c){xa(c)}if((d|0)!=(h|0)){continue}break}c=F[k+8>>2]}F[a+12>>2]=d}e:{f:{d=F[c+56>>2];g:{if((d|0)>4){break g}j=L(d,12)+a|0;d=F[j+24>>2];if((d|0)!=F[j+28>>2]){F[d>>2]=b;F[j+24>>2]=d+4;break g}i=F[j+20>>2];g=d-i|0;f=g>>2;e=f+1|0;if(e>>>0>=1073741824){break f}d=g>>>1|0;e=g>>>0>=2147483644?1073741823:d>>>0>e>>>0?d:e;if(e){if(e>>>0>=1073741824){break e}d=ka(e<<2)}else{d=0}f=d+(f<<2)|0;F[f>>2]=b;d=pa(d,i,g);F[j+20>>2]=d;F[j+24>>2]=f+4;F[j+28>>2]=d+(e<<2);if(!i){break g}ja(i)}F[c+60>>2]=b;a=F[a+8>>2];F[k+8>>2]=0;a=a+(b<<2)|0;b=F[a>>2];F[a>>2]=c;if(b){xa(b)}a=F[k+8>>2];F[k+8>>2]=0;if(a){xa(a)}Z=k+16|0;return}na();v()}oa();v()}function Pf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;i=c;d=a;a:{if(F[a+12>>2]==(b|0)){break a}a=b;b=F[d+4>>2];e=F[d>>2];if((b|0)!=(e|0)){while(1){c=b-12|0;if(D[b-1|0]<0){ja(F[c>>2])}b=c;if((e|0)!=(b|0)){continue}break}}F[d+12>>2]=a;F[d+4>>2]=e;c=F[a>>2];j=a+4|0;if((c|0)==(j|0)){break a}while(1){a=F[d+4>>2];b:{if((a|0)!=F[d+8>>2]){c:{if(D[c+27|0]>=0){b=F[c+20>>2];F[a>>2]=F[c+16>>2];F[a+4>>2]=b;F[a+8>>2]=F[c+24>>2];break c}ra(a,F[c+16>>2],F[c+20>>2])}F[d+4>>2]=a+12;break b}g=0;d:{e:{f:{a=F[d+4>>2];e=F[d>>2];f=(a-e|0)/12|0;b=f+1|0;if(b>>>0<357913942){h=(F[d+8>>2]-e|0)/12|0;k=h<<1;b=h>>>0>=178956970?357913941:b>>>0<k>>>0?k:b;if(b){if(b>>>0>=357913942){break f}g=ka(L(b,12))}h=L(b,12);b=L(f,12)+g|0;g:{if(D[c+27|0]>=0){f=F[c+20>>2];F[b>>2]=F[c+16>>2];F[b+4>>2]=f;F[b+8>>2]=F[c+24>>2];break g}ra(b,F[c+16>>2],F[c+20>>2]);e=F[d>>2];a=F[d+4>>2]}g=g+h|0;f=b+12|0;if((a|0)==(e|0)){break e}while(1){a=a-12|0;h=F[a+4>>2];b=b-12|0;F[b>>2]=F[a>>2];F[b+4>>2]=h;F[b+8>>2]=F[a+8>>2];F[a>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;if((a|0)!=(e|0)){continue}break}F[d+8>>2]=g;a=F[d+4>>2];F[d+4>>2]=f;e=F[d>>2];F[d>>2]=b;if((a|0)==(e|0)){break d}while(1){b=a-12|0;if(D[a-1|0]<0){ja(F[b>>2])}a=b;if((e|0)!=(b|0)){continue}break}break d}na();v()}oa();v()}F[d+8>>2]=g;F[d+4>>2]=f;F[d>>2]=b}if(e){ja(e)}}b=F[c+4>>2];h:{if(b){while(1){a=b;b=F[b>>2];if(b){continue}break h}}while(1){a=F[c+8>>2];b=F[a>>2]!=(c|0);c=a;if(b){continue}break}}c=a;if((j|0)!=(a|0)){continue}break}}a=0;i:{if((i|0)<0){break i}b=F[d>>2];if((F[d+4>>2]-b|0)/12>>>0<=i>>>0){break i}a=b+L(i,12)|0;a=D[a+11|0]<0?F[a>>2]:a}return a|0}function Ad(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;i=Z-16|0;Z=i;F[i>>2]=b;f=-1;a:{if((b|0)==-1){F[i+4>>2]=-1;break a}f=b+1|0;F[i+4>>2]=(f>>>0)%3|0?f:b-2|0;if((b>>>0)%3|0){f=b-1|0;break a}f=b+2|0}F[i+8>>2]=f;n=(b>>>0)/3|0;b:{c:{d:{while(1){e:{f:{j=F[(l<<2)+i>>2];if((j|0)!=-1){f=F[F[F[a+8>>2]+12>>2]+(j<<2)>>2];if((f|0)!=-1){break f}}f=0;g=F[a+216>>2];if((g|0)==F[a+220>>2]){break e}while(1){g=L(f,144)+g|0;d=F[g+136>>2];c=F[g+140>>2];g:{if(d>>>0<c>>>0){F[d>>2]=j;F[g+136>>2]=d+4;break g}e=d;d=F[g+132>>2];k=e-d|0;e=k>>2;h=e+1|0;if(h>>>0>=1073741824){break d}m=e<<2;c=c-d|0;e=c>>>1|0;h=c>>>0>=2147483644?1073741823:h>>>0<e>>>0?e:h;if(h){if(h>>>0>=1073741824){break c}c=ka(h<<2)}else{c=0}e=m+c|0;F[e>>2]=j;c=pa(c,d,k);F[g+132>>2]=c;F[g+136>>2]=e+4;F[g+140>>2]=c+(h<<2);if(!d){break g}ja(d)}f=f+1|0;g=F[a+216>>2];if(f>>>0<(F[a+220>>2]-g|0)/144>>>0){continue}break}break e}if((b|0)==-1|(f>>>0)/3>>>0<n>>>0){break e}f=0;if(F[a+220>>2]==F[a+216>>2]){break e}while(1){h:{if(!wa(F[a+368>>2]+(f<<4)|0)){break h}g=F[a+216>>2]+L(f,144)|0;d=F[g+136>>2];c=F[g+140>>2];if(d>>>0<c>>>0){F[d>>2]=j;F[g+136>>2]=d+4;break h}e=d;d=F[g+132>>2];k=e-d|0;e=k>>2;h=e+1|0;if(h>>>0>=1073741824){break b}m=e<<2;c=c-d|0;e=c>>>1|0;h=c>>>0>=2147483644?1073741823:h>>>0<e>>>0?e:h;if(h){if(h>>>0>=1073741824){break c}c=ka(h<<2)}else{c=0}e=m+c|0;F[e>>2]=j;c=pa(c,d,k);F[g+132>>2]=c;F[g+136>>2]=e+4;F[g+140>>2]=c+(h<<2);if(!d){break h}ja(d)}f=f+1|0;if(f>>>0<(F[a+220>>2]-F[a+216>>2]|0)/144>>>0){continue}break}}l=l+1|0;if((l|0)!=3){continue}break}Z=i+16|0;return 1}na();v()}oa();v()}na();v()}function Bd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=Z-16|0;Z=h;m=-1;a:{b:{c:{if(!Da(1,h+12|0,b)){break c}j=F[h+12>>2];if(j){c=F[a+8>>2];if((F[c+4>>2]-F[c>>2]>>2>>>0)/3>>>0<j>>>0){break c}while(1){if(!Da(1,h+8|0,b)){break c}c=F[h+8>>2];if(!Da(1,h+8|0,b)){break c}g=c+g|0;c=F[h+8>>2];if(g>>>0<c>>>0){break c}e=g-c|0;c=F[a+40>>2];d:{if((c|0)!=F[a+44>>2]){F[c+4>>2]=g;F[c>>2]=e;F[a+40>>2]=c+12;j=F[h+12>>2];break d}d=c;c=F[a+36>>2];l=d-c|0;d=(l|0)/12|0;f=d+1|0;if(f>>>0>=357913942){break b}i=d<<1;f=d>>>0>=178956970?357913941:f>>>0<i>>>0?i:f;if(f){if(f>>>0>=357913942){break a}i=ka(L(f,12))}else{i=0}d=i+L(d,12)|0;F[d+4>>2]=g;F[d>>2]=e;e=pa(d+L((l|0)/-12|0,12)|0,c,l);F[a+44>>2]=i+L(f,12);F[a+40>>2]=d+12;F[a+36>>2]=e;if(!c){break d}ja(c)}k=k+1|0;if(k>>>0<j>>>0){continue}break}g=0;hc(b,0,0);if(j){while(1){c=G[b+36|0];d=H[F[a+4>>2]+36>>1];e:{f:{if(((d<<8|d>>>8)&65535)>>>0<=513){if(!c){break e}e=0;d=F[b+32>>2];k=d>>>3|0;f=F[b+24>>2];c=k+f|0;i=F[b+28>>2];g:{if(c>>>0>=i>>>0){c=d;break g}e=G[c|0];c=d+1|0;F[b+32>>2]=c;k=c>>>3|0;e=e>>>(d&7)&1}if(i>>>0>f+k>>>0){break f}break e}if(!c){break e}e=0;c=F[b+32>>2];d=F[b+24>>2]+(c>>>3|0)|0;if(d>>>0>=I[b+28>>2]){break e}e=G[d|0]>>>(c&7)&1}F[b+32>>2]=c+1}c=F[a+36>>2]+L(g,12)|0;D[c+8|0]=G[c+8|0]&254|e&1;g=g+1|0;if((j|0)!=(g|0)){continue}break}}D[b+36|0]=0;d=F[b+20>>2];a=0;e=F[b+32>>2]+7|0;a=e>>>0<7?1:a;e=a<<29|e>>>3;c=e+F[b+16>>2]|0;a=(a>>>3|0)+d|0;F[b+16>>2]=c;F[b+20>>2]=c>>>0<e>>>0?a+1|0:a}m=F[b+16>>2]}Z=h+16|0;return m}na();v()}oa();v()}function xf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;m=Z-16|0;Z=m;l=F[b+80>>2];e=G[c+24|0];a=L(l,e);a:{b:{c:{d:{b=F[c+28>>2];if(!(!G[c+84|0]|(b|0)!=1&(b|0)!=2)){b=F[c+48>>2];c=F[F[c>>2]>>2];F[m+8>>2]=0;F[m>>2]=0;F[m+4>>2]=0;if(a){if((a|0)<0){break d}f=ka(a);h=la(f,b+c|0,a)+a|0}a=F[d>>2];if(a){F[d+4>>2]=a;ja(a)}F[d+8>>2]=h;F[d+4>>2]=h;F[d>>2]=f;b=1;break a}if(e){f=ka(e);ma(f,0,e)}e:{i=F[d+4>>2];b=F[d>>2];g=i-b|0;f:{if(g>>>0<a>>>0){k=a-g|0;j=F[d+8>>2];if(k>>>0<=j-i>>>0){n=d,o=ma(i,0,k)+k|0,F[n+4>>2]=o;break f}if((a|0)<0){break e}i=j-b|0;j=i<<1;i=i>>>0>=1073741823?2147483647:a>>>0<j>>>0?j:a;j=ka(i);ma(j+g|0,0,k);g=pa(j,b,g);F[d+8>>2]=g+i;F[d+4>>2]=a+g;F[d>>2]=g;if(!b){break f}ja(b);break f}if(a>>>0>=g>>>0){break f}F[d+4>>2]=a+b}if(!l){b=1;break c}if(!e){b=0;a=0;while(1){if(!Cb(c,G[c+84|0]?a:F[F[c+68>>2]+(a<<2)>>2],D[c+24|0],f)){break c}a=a+1|0;b=l>>>0<=a>>>0;if((a|0)!=(l|0)){continue}break}break c}i=e&252;g=e&3;b=0;j=e>>>0<4;e=0;while(1){if(!Cb(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],f)){break c}b=0;a=0;k=0;if(!j){while(1){D[F[d>>2]+h|0]=G[a+f|0];D[(F[d>>2]+h|0)+1|0]=G[(a|1)+f|0];D[(F[d>>2]+h|0)+2|0]=G[(a|2)+f|0];D[(F[d>>2]+h|0)+3|0]=G[(a|3)+f|0];a=a+4|0;h=h+4|0;k=k+4|0;if((i|0)!=(k|0)){continue}break}}if(g){while(1){D[F[d>>2]+h|0]=G[a+f|0];a=a+1|0;h=h+1|0;b=b+1|0;if((g|0)!=(b|0)){continue}break}}e=e+1|0;b=l>>>0<=e>>>0;if((e|0)!=(l|0)){continue}break}break b}na();v()}na();v()}if(!f){break a}}ja(f)}Z=m+16|0;return b&1}function wf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;m=Z-16|0;Z=m;l=F[b+80>>2];e=G[c+24|0];a=L(l,e);a:{b:{c:{d:{b=F[c+28>>2];if(!(!G[c+84|0]|(b|0)!=1&(b|0)!=2)){b=F[c+48>>2];c=F[F[c>>2]>>2];F[m+8>>2]=0;F[m>>2]=0;F[m+4>>2]=0;if(a){if((a|0)<0){break d}f=ka(a);h=la(f,b+c|0,a)+a|0}a=F[d>>2];if(a){F[d+4>>2]=a;ja(a)}F[d+8>>2]=h;F[d+4>>2]=h;F[d>>2]=f;b=1;break a}if(e){f=ka(e);ma(f,0,e)}e:{i=F[d+4>>2];b=F[d>>2];g=i-b|0;f:{if(g>>>0<a>>>0){k=a-g|0;j=F[d+8>>2];if(k>>>0<=j-i>>>0){n=d,o=ma(i,0,k)+k|0,F[n+4>>2]=o;break f}if((a|0)<0){break e}i=j-b|0;j=i<<1;i=i>>>0>=1073741823?2147483647:a>>>0<j>>>0?j:a;j=ka(i);ma(j+g|0,0,k);g=pa(j,b,g);F[d+8>>2]=g+i;F[d+4>>2]=a+g;F[d>>2]=g;if(!b){break f}ja(b);break f}if(a>>>0>=g>>>0){break f}F[d+4>>2]=a+b}if(!l){b=1;break c}if(!e){b=0;a=0;while(1){if(!Bb(c,G[c+84|0]?a:F[F[c+68>>2]+(a<<2)>>2],D[c+24|0],f)){break c}a=a+1|0;b=l>>>0<=a>>>0;if((a|0)!=(l|0)){continue}break}break c}i=e&252;g=e&3;b=0;j=e>>>0<4;e=0;while(1){if(!Bb(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],f)){break c}b=0;a=0;k=0;if(!j){while(1){D[F[d>>2]+h|0]=G[a+f|0];D[(F[d>>2]+h|0)+1|0]=G[(a|1)+f|0];D[(F[d>>2]+h|0)+2|0]=G[(a|2)+f|0];D[(F[d>>2]+h|0)+3|0]=G[(a|3)+f|0];a=a+4|0;h=h+4|0;k=k+4|0;if((i|0)!=(k|0)){continue}break}}if(g){while(1){D[F[d>>2]+h|0]=G[a+f|0];a=a+1|0;h=h+1|0;b=b+1|0;if((g|0)!=(b|0)){continue}break}}e=e+1|0;b=l>>>0<=e>>>0;if((e|0)!=(l|0)){continue}break}break b}na();v()}na();v()}if(!f){break a}}ja(f)}Z=m+16|0;return b&1}function Jb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;k=F[b+16>>2];h=F[c+4>>2]-k|0;e=F[c>>2]-k|0;F[c>>2]=e;f=h;F[c+4>>2]=f;l=F[b+16>>2];f=f>>31;g=(h^f)-f|0;f=e>>31;m=l>>>0>=g+((f^e)-f|0)>>>0;a:{if(m){f=h;break a}b:{c:{if((e|0)>=0){g=1;j=1;if((h|0)>=0){break b}i=1;g=-1;j=-1;if(e){break c}break b}i=-1;g=-1;j=-1;if((h|0)<=0){break b}}g=(h|0)<=0?-1:1;j=i}n=L(j,l);f=(e<<1)-n|0;i=(L(g,j)|0)>=0;e=L(g,l);f=((i?0-f|0:f)+e|0)/2|0;F[c+4>>2]=f;e=(h<<1)-e|0;e=((i?0-e|0:e)+n|0)/2|0;F[c>>2]=e}d:{e:{f:{g:{h:{i:{j:{if(e){if((e|0)<0){break j}if((f|0)>=0){break i}break f}if(f){break h}j=1;g=0;f=0;i=0;break d}j=1;if((f|0)>0){break g}i=(f|0)>0?3:0;g=f;f=e;break d}g=0-f|0;f=0-e|0;i=2;break e}if((f|0)<=0){break f}}f=0-f|0;g=e;i=3;break e}g=0-e|0;i=1}F[c>>2]=f;F[c+4>>2]=g;j=0}e=F[d>>2]+f|0;h=F[b+16>>2];k:{if((e|0)>(h|0)){e=e-F[b+4>>2]|0;break k}if((0-h|0)<=(e|0)){break k}e=F[b+4>>2]+e|0}c=F[d+4>>2]+g|0;l:{if((h|0)<(c|0)){c=c-F[b+4>>2]|0;break l}if((0-h|0)<=(c|0)){break l}c=F[b+4>>2]+c|0}m:{if(j){b=c;break m}b=c;n:{o:{p:{d=4-i|0;switch((d>>>0<4?d:0-i|0)-1|0){case 2:break n;case 1:break o;case 0:break p;default:break m}}b=0-e|0;e=c;break m}b=0-c|0;e=0-e|0;break m}b=e;e=0-c|0}q:{if(m){c=b;break q}r:{s:{if((e|0)>=0){c=1;f=1;if((b|0)>=0){break r}d=1;c=-1;f=-1;if(e){break s}break r}d=-1;c=-1;f=-1;if((b|0)<=0){break r}}c=(b|0)<=0?-1:1;f=d}d=e<<1;e=L(f,h);d=d-e|0;f=(L(c,f)|0)>=0;g=f?0-d|0:d;d=L(c,h);c=(g+d|0)/2|0;b=(b<<1)-d|0;e=(e+(f?0-b|0:b)|0)/2|0}b=a;F[b>>2]=e+k;F[b+4>>2]=c+k}function Uh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0;F[a+8>>2]=e;m=a+32|0;h=F[m>>2];g=F[a+36>>2]-h>>2;a:{if(g>>>0<e>>>0){qa(m,e-g|0);f=F[a+8>>2];break a}f=e;if(f>>>0>=g>>>0){break a}F[a+36>>2]=h+(e<<2);f=e}g=e>>>0>1073741823?-1:e<<2;n=ma(ka(g),0,g);b:{if((f|0)<=0){break b}h=F[a+32>>2];while(1){f=i<<2;g=F[f+n>>2];j=F[a+16>>2];c:{if((g|0)>(j|0)){F[f+h>>2]=j;break c}f=f+h|0;j=F[a+12>>2];if((j|0)>(g|0)){F[f>>2]=j;break c}F[f>>2]=g}f=F[a+8>>2];i=i+1|0;if((f|0)>(i|0)){continue}break}if((f|0)<=0){break b}i=0;while(1){g=i<<2;f=g+c|0;g=F[b+g>>2]+F[g+h>>2]|0;F[f>>2]=g;d:{if((g|0)>F[a+16>>2]){g=g-F[a+20>>2]|0}else{if((g|0)>=F[a+12>>2]){break d}g=g+F[a+20>>2]|0}F[f>>2]=g}f=F[a+8>>2];i=i+1|0;if((f|0)>(i|0)){continue}break}}if(!((d|0)<=(e|0)|(f|0)<=0)){p=0-e<<2;g=e;while(1){e:{if((f|0)<=0){break e}l=g<<2;o=l+c|0;q=o+p|0;j=F[m>>2];i=0;while(1){f=i<<2;h=F[f+q>>2];k=F[a+16>>2];f:{if((h|0)>(k|0)){F[f+j>>2]=k;break f}f=f+j|0;k=F[a+12>>2];if((k|0)>(h|0)){F[f>>2]=k;break f}F[f>>2]=h}f=F[a+8>>2];i=i+1|0;if((f|0)>(i|0)){continue}break}i=0;if((f|0)<=0){break e}l=b+l|0;while(1){h=i<<2;f=h+o|0;h=F[h+l>>2]+F[h+j>>2]|0;F[f>>2]=h;g:{if((h|0)>F[a+16>>2]){h=h-F[a+20>>2]|0}else{if((h|0)>=F[a+12>>2]){break g}h=h+F[a+20>>2]|0}F[f>>2]=h}f=F[a+8>>2];i=i+1|0;if((f|0)>(i|0)){continue}break}}g=e+g|0;if((g|0)<(d|0)){continue}break}}ja(n);return 1}function yf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;j=F[b+80>>2];b=G[c+24|0];g=L(j,b);a:{if(!b){break a}h=b<<2;f=ka(h);a=f;k=b&7;if(k){while(1){F[a>>2]=-1073741824;a=a+4|0;e=e+1|0;if((k|0)!=(e|0)){continue}break}}if((b-1&1073741823)>>>0<7){break a}e=f+h|0;while(1){F[a+24>>2]=-1073741824;F[a+28>>2]=-1073741824;F[a+16>>2]=-1073741824;F[a+20>>2]=-1073741824;F[a+8>>2]=-1073741824;F[a+12>>2]=-1073741824;F[a>>2]=-1073741824;F[a+4>>2]=-1073741824;a=a+32|0;if((e|0)!=(a|0)){continue}break}}e=F[d>>2];a=F[d+4>>2]-e>>2;b:{if(a>>>0<g>>>0){qa(d,g-a|0);break b}if(a>>>0<=g>>>0){break b}F[d+4>>2]=e+(g<<2)}c:{d:{e:{if(!j){i=1;break e}if(!b){a=0;while(1){if(!lb(c,G[c+84|0]?a:F[F[c+68>>2]+(a<<2)>>2],D[c+24|0],f)){break e}a=a+1|0;i=j>>>0<=a>>>0;if((a|0)!=(j|0)){continue}break}break e}n=b&252;k=b&3;o=b>>>0<4;e=0;b=0;while(1){if(!lb(c,G[c+84|0]?b:F[F[c+68>>2]+(b<<2)>>2],D[c+24|0],f)){break e}m=F[d>>2];i=0;a=0;l=0;if(!o){while(1){g=(e<<2)+m|0;h=a<<2;J[g>>2]=J[h+f>>2];J[g+4>>2]=J[(h|4)+f>>2];J[g+8>>2]=J[(h|8)+f>>2];J[g+12>>2]=J[(h|12)+f>>2];a=a+4|0;e=e+4|0;l=l+4|0;if((n|0)!=(l|0)){continue}break}}if(k){while(1){J[(e<<2)+m>>2]=J[(a<<2)+f>>2];a=a+1|0;e=e+1|0;i=i+1|0;if((k|0)!=(i|0)){continue}break}}b=b+1|0;i=j>>>0<=b>>>0;if((b|0)!=(j|0)){continue}break}break d}if(!f){break c}}ja(f)}return i|0}function $d(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;k=Z-16|0;Z=k;c=F[b+20>>2];d=F[b+16>>2];e=d+4|0;c=e>>>0<4?c+1|0:c;g=F[b+12>>2];a:{if(I[b+8>>2]<e>>>0&(g|0)<=(c|0)|(c|0)>(g|0)){break a}d=d+F[b>>2]|0;h=G[d|0]|G[d+1|0]<<8|(G[d+2|0]<<16|G[d+3|0]<<24);F[b+16>>2]=e;F[b+20>>2]=c;if((h|0)<0){break a}Na(a+76|0,h);c=k;F[c>>2]=0;F[c+4>>2]=0;D[c+5|0]=0;D[c+6|0]=0;D[c+7|0]=0;D[c+8|0]=0;D[c+9|0]=0;D[c+10|0]=0;D[c+11|0]=0;D[c+12|0]=0;b:{if(!Aa(c,b)){break b}if(h){g=1;while(1){d=1<<i;e=wa(c);f=F[a+76>>2]+(i>>>3&536870908)|0;e=e^g;if(e&1){d=F[f>>2]&(d^-1)}else{d=d|F[f>>2]}g=e^1;F[f>>2]=d;i=i+1|0;if((h|0)!=(i|0)){continue}break}}i=0;c=F[b+8>>2];e=F[b+12>>2];f=e;e=F[b+20>>2];g=e;l=F[b+16>>2];d=l+4|0;e=d>>>0<4?e+1|0:e;h=d;if(d>>>0>c>>>0&(e|0)>=(f|0)|(e|0)>(f|0)){break b}m=F[b>>2];d=m+l|0;j=G[d|0]|G[d+1|0]<<8|(G[d+2|0]<<16|G[d+3|0]<<24);F[b+16>>2]=h;F[b+20>>2]=e;d=c;c=g;e=l+8|0;c=e>>>0<8?c+1|0:c;if(d>>>0<e>>>0&(c|0)>=(f|0)|(c|0)>(f|0)){break b}d=h+m|0;d=G[d|0]|G[d+1|0]<<8|(G[d+2|0]<<16|G[d+3|0]<<24);F[b+16>>2]=e;F[b+20>>2]=c;if((d|0)<(j|0)){break b}F[a+16>>2]=d;F[a+12>>2]=j;c=(d>>31)-((j>>31)+(d>>>0<j>>>0)|0)|0;b=d-j|0;if(!c&b>>>0>2147483646|c){break b}i=1;c=b+1|0;F[a+20>>2]=c;b=c>>>1|0;F[a+24>>2]=b;F[a+28>>2]=0-b;if(c&1){break b}F[a+24>>2]=b-1}}Z=k+16|0;return i|0}function tf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;a=0;k=Z-16|0;Z=k;j=F[b+80>>2];e=G[c+24|0];b=L(j,e);a:{b:{c:{d:{f=F[c+28>>2];if(!(!G[c+84|0]|(f|0)!=5&(f|0)!=6)){e=F[c+48>>2];c=F[F[c>>2]>>2];F[k+8>>2]=0;F[k>>2]=0;F[k+4>>2]=0;if(b){if((b|0)<0){break d}b=b<<2;a=ka(b);g=la(a,c+e|0,b)+b|0}b=F[d>>2];if(b){F[d+4>>2]=b;ja(b)}F[d+8>>2]=g;F[d+4>>2]=g;F[d>>2]=a;h=1;break a}if(e){f=e<<2;a=ka(f);ma(a,0,f)}i=F[d>>2];f=F[d+4>>2]-i>>2;e:{if(f>>>0<b>>>0){qa(d,b-f|0);break e}if(b>>>0>=f>>>0){break e}F[d+4>>2]=i+(b<<2)}if(!j){h=1;break c}if(!e){b=0;while(1){if(!xb(c,G[c+84|0]?b:F[F[c+68>>2]+(b<<2)>>2],D[c+24|0],a)){break c}b=b+1|0;h=j>>>0<=b>>>0;if((b|0)!=(j|0)){continue}break}break c}o=e&252;m=e&3;p=e>>>0<4;e=0;while(1){if(!xb(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],a)){break c}n=F[d>>2];l=0;b=0;h=0;if(!p){while(1){f=(g<<2)+n|0;i=b<<2;F[f>>2]=F[i+a>>2];F[f+4>>2]=F[(i|4)+a>>2];F[f+8>>2]=F[(i|8)+a>>2];F[f+12>>2]=F[(i|12)+a>>2];b=b+4|0;g=g+4|0;h=h+4|0;if((o|0)!=(h|0)){continue}break}}if(m){while(1){F[(g<<2)+n>>2]=F[(b<<2)+a>>2];b=b+1|0;g=g+1|0;l=l+1|0;if((l|0)!=(m|0)){continue}break}}e=e+1|0;h=j>>>0<=e>>>0;if((e|0)!=(j|0)){continue}break}break b}na();v()}if(!a){break a}}ja(a)}Z=k+16|0;return h|0}function cd(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;a=0;k=Z-16|0;Z=k;j=F[b+80>>2];e=G[c+24|0];b=L(j,e);a:{b:{c:{d:{f=F[c+28>>2];if(!(!G[c+84|0]|(f|0)!=5&(f|0)!=6)){e=F[c+48>>2];c=F[F[c>>2]>>2];F[k+8>>2]=0;F[k>>2]=0;F[k+4>>2]=0;if(b){if((b|0)<0){break d}b=b<<2;a=ka(b);g=la(a,c+e|0,b)+b|0}b=F[d>>2];if(b){F[d+4>>2]=b;ja(b)}F[d+8>>2]=g;F[d+4>>2]=g;F[d>>2]=a;h=1;break a}if(e){f=e<<2;a=ka(f);ma(a,0,f)}i=F[d>>2];f=F[d+4>>2]-i>>2;e:{if(f>>>0<b>>>0){qa(d,b-f|0);break e}if(b>>>0>=f>>>0){break e}F[d+4>>2]=i+(b<<2)}if(!j){h=1;break c}if(!e){b=0;while(1){if(!yb(c,G[c+84|0]?b:F[F[c+68>>2]+(b<<2)>>2],D[c+24|0],a)){break c}b=b+1|0;h=j>>>0<=b>>>0;if((b|0)!=(j|0)){continue}break}break c}o=e&252;m=e&3;p=e>>>0<4;e=0;while(1){if(!yb(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],a)){break c}n=F[d>>2];l=0;b=0;h=0;if(!p){while(1){f=(g<<2)+n|0;i=b<<2;F[f>>2]=F[i+a>>2];F[f+4>>2]=F[(i|4)+a>>2];F[f+8>>2]=F[(i|8)+a>>2];F[f+12>>2]=F[(i|12)+a>>2];b=b+4|0;g=g+4|0;h=h+4|0;if((o|0)!=(h|0)){continue}break}}if(m){while(1){F[(g<<2)+n>>2]=F[(b<<2)+a>>2];b=b+1|0;g=g+1|0;l=l+1|0;if((l|0)!=(m|0)){continue}break}}e=e+1|0;h=j>>>0<=e>>>0;if((e|0)!=(j|0)){continue}break}break b}na();v()}if(!a){break a}}ja(a)}Z=k+16|0;return h|0}function vf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;a=0;k=Z-16|0;Z=k;j=F[b+80>>2];e=G[c+24|0];b=L(j,e);a:{b:{c:{d:{f=F[c+28>>2];if(!(!G[c+84|0]|(f|0)!=3&(f|0)!=4)){e=F[c+48>>2];c=F[F[c>>2]>>2];F[k+8>>2]=0;F[k>>2]=0;F[k+4>>2]=0;if(b){if((b|0)<0){break d}b=b<<1;a=ka(b);g=la(a,c+e|0,b)+b|0}b=F[d>>2];if(b){F[d+4>>2]=b;ja(b)}F[d+8>>2]=g;F[d+4>>2]=g;F[d>>2]=a;h=1;break a}if(e){f=e<<1;a=ka(f);ma(a,0,f)}i=F[d>>2];f=F[d+4>>2]-i>>1;e:{if(f>>>0<b>>>0){kd(d,b-f|0);break e}if(b>>>0>=f>>>0){break e}F[d+4>>2]=i+(b<<1)}if(!j){h=1;break c}if(!e){b=0;while(1){if(!Ab(c,G[c+84|0]?b:F[F[c+68>>2]+(b<<2)>>2],D[c+24|0],a)){break c}b=b+1|0;h=j>>>0<=b>>>0;if((b|0)!=(j|0)){continue}break}break c}o=e&252;m=e&3;p=e>>>0<4;e=0;while(1){if(!Ab(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],a)){break c}n=F[d>>2];l=0;b=0;h=0;if(!p){while(1){f=(g<<1)+n|0;i=b<<1;E[f>>1]=H[i+a>>1];E[f+2>>1]=H[(i|2)+a>>1];E[f+4>>1]=H[(i|4)+a>>1];E[f+6>>1]=H[(i|6)+a>>1];b=b+4|0;g=g+4|0;h=h+4|0;if((o|0)!=(h|0)){continue}break}}if(m){while(1){E[(g<<1)+n>>1]=H[(b<<1)+a>>1];b=b+1|0;g=g+1|0;l=l+1|0;if((l|0)!=(m|0)){continue}break}}e=e+1|0;h=j>>>0<=e>>>0;if((e|0)!=(j|0)){continue}break}break b}na();v()}if(!a){break a}}ja(a)}Z=k+16|0;return h|0}function uf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0;a=0;k=Z-16|0;Z=k;j=F[b+80>>2];e=G[c+24|0];b=L(j,e);a:{b:{c:{d:{f=F[c+28>>2];if(!(!G[c+84|0]|(f|0)!=3&(f|0)!=4)){e=F[c+48>>2];c=F[F[c>>2]>>2];F[k+8>>2]=0;F[k>>2]=0;F[k+4>>2]=0;if(b){if((b|0)<0){break d}b=b<<1;a=ka(b);g=la(a,c+e|0,b)+b|0}b=F[d>>2];if(b){F[d+4>>2]=b;ja(b)}F[d+8>>2]=g;F[d+4>>2]=g;F[d>>2]=a;h=1;break a}if(e){f=e<<1;a=ka(f);ma(a,0,f)}i=F[d>>2];f=F[d+4>>2]-i>>1;e:{if(f>>>0<b>>>0){kd(d,b-f|0);break e}if(b>>>0>=f>>>0){break e}F[d+4>>2]=i+(b<<1)}if(!j){h=1;break c}if(!e){b=0;while(1){if(!zb(c,G[c+84|0]?b:F[F[c+68>>2]+(b<<2)>>2],D[c+24|0],a)){break c}b=b+1|0;h=j>>>0<=b>>>0;if((b|0)!=(j|0)){continue}break}break c}o=e&252;m=e&3;p=e>>>0<4;e=0;while(1){if(!zb(c,G[c+84|0]?e:F[F[c+68>>2]+(e<<2)>>2],D[c+24|0],a)){break c}n=F[d>>2];l=0;b=0;h=0;if(!p){while(1){f=(g<<1)+n|0;i=b<<1;E[f>>1]=H[i+a>>1];E[f+2>>1]=H[(i|2)+a>>1];E[f+4>>1]=H[(i|4)+a>>1];E[f+6>>1]=H[(i|6)+a>>1];b=b+4|0;g=g+4|0;h=h+4|0;if((o|0)!=(h|0)){continue}break}}if(m){while(1){E[(g<<1)+n>>1]=H[(b<<1)+a>>1];b=b+1|0;g=g+1|0;l=l+1|0;if((l|0)!=(m|0)){continue}break}}e=e+1|0;h=j>>>0<=e>>>0;if((e|0)!=(j|0)){continue}break}break b}na();v()}if(!a){break a}}ja(a)}Z=k+16|0;return h|0}function kc(a,b){var c=0,d=0,e=0,f=0,g=0;f=-1;d=-1;a:{if((b|0)==-1){break a}d=b+1|0;f=(d>>>0)%3|0?d:b-2|0;d=b-1|0;if((b>>>0)%3|0){break a}d=b+2|0}b:{c:{d:{switch(F[a+168>>2]){case 0:case 1:e=F[a+148>>2];c=1;b=F[a+156>>2];g=b+(((f|0)==-1?-1:F[F[e>>2]+(f<<2)>>2])<<2)|0;F[g>>2]=F[g>>2]+1;b=(((d|0)==-1?-1:F[F[e>>2]+(d<<2)>>2])<<2)+b|0;break c;case 5:e=F[a+148>>2];c=-1;c=((b|0)!=-1?F[F[e>>2]+(b<<2)>>2]:c)<<2;b=F[a+156>>2];c=c+b|0;F[c>>2]=F[c>>2]+1;c=(((f|0)==-1?-1:F[F[e>>2]+(f<<2)>>2])<<2)+b|0;F[c>>2]=F[c>>2]+1;c=2;b=(((d|0)==-1?-1:F[F[e>>2]+(d<<2)>>2])<<2)+b|0;break c;case 3:e=F[a+148>>2];c=-1;c=((b|0)!=-1?F[F[e>>2]+(b<<2)>>2]:c)<<2;b=F[a+156>>2];c=c+b|0;F[c>>2]=F[c>>2]+1;c=(((f|0)==-1?-1:F[F[e>>2]+(f<<2)>>2])<<2)+b|0;F[c>>2]=F[c>>2]+2;c=1;b=(((d|0)==-1?-1:F[F[e>>2]+(d<<2)>>2])<<2)+b|0;break c;case 7:break d;default:break b}}e=F[a+148>>2];c=-1;c=((b|0)!=-1?F[F[e>>2]+(b<<2)>>2]:c)<<2;b=F[a+156>>2];c=c+b|0;F[c>>2]=F[c>>2]+2;c=(((f|0)==-1?-1:F[F[e>>2]+(f<<2)>>2])<<2)+b|0;F[c>>2]=F[c>>2]+2;c=2;b=(((d|0)==-1?-1:F[F[e>>2]+(d<<2)>>2])<<2)+b|0}F[b>>2]=F[b>>2]+c}c=a;b=F[F[a+156>>2]+(((f|0)==-1?-1:F[F[F[a+148>>2]>>2]+(f<<2)>>2])<<2)>>2];d=F[a+180>>2];a=F[a+176>>2];F[c+172>>2]=(a|0)<=(b|0)?((b|0)<(d|0)?b:d)-a|0:0}function Dg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;a:{b=F[a+32>>2];f=F[b+8>>2];h=F[b+12>>2];g=F[b+20>>2];c=F[b+16>>2];e=0;b:{if((h|0)<=(g|0)&c>>>0>=f>>>0|(g|0)>(h|0)){break b}f=G[F[b>>2]+c|0];e=b;b=g;c=c+1|0;b=c?b:b+1|0;F[e+16>>2]=c;F[e+20>>2]=b;c:{if(!f){break c}while(1){if($[F[F[a>>2]+16>>2]](a,d)|0){d=d+1|0;if((f|0)!=(d|0)){continue}break c}break}return 0}d=F[a+8>>2];b=F[a+12>>2];if((d|0)!=(b|0)){while(1){c=F[d>>2];if(!($[F[F[c>>2]+8>>2]](c,a,F[a+4>>2])|0)){break a}d=d+4|0;if((b|0)!=(d|0)){continue}break}}d:{if(!f){break d}d=0;while(1){b=F[F[a+8>>2]+(d<<2)>>2];if(!($[F[F[b>>2]+12>>2]](b,F[a+32>>2])|0)){break a}d=d+1|0;if((f|0)!=(d|0)){continue}break}if(!f){break d}i=a+20|0;b=0;while(1){d=0;j=b<<2;c=F[j+F[a+8>>2]>>2];k=$[F[F[c>>2]+24>>2]](c)|0;if((k|0)>0){while(1){c=F[F[a+8>>2]+j>>2];c=$[F[F[c>>2]+20>>2]](c,d)|0;e=F[a+20>>2];g=F[a+24>>2]-e>>2;e:{if(c>>>0<g>>>0){break e}h=c+1|0;if(h>>>0>g>>>0){qa(i,h-g|0);e=F[i>>2];break e}if(g>>>0<=h>>>0){break e}F[a+24>>2]=(h<<2)+e}F[(c<<2)+e>>2]=b;d=d+1|0;if((k|0)!=(d|0)){continue}break}}b=b+1|0;if((f|0)!=(b|0)){continue}break}}e=0;if(!($[F[F[a>>2]+28>>2]](a)|0)){break b}e=$[F[F[a>>2]+32>>2]](a)|0}return e|0}return 0}function Ye(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;Oc(a,b,c);c=F[a+84>>2];d=F[a+88>>2]-c>>2;a:{if((d|0)>(b|0)){break a}b=b+1|0;if(b>>>0>d>>>0){b:{d=b-d|0;e=F[a+92>>2];c=F[a+88>>2];if(d>>>0<=e-c>>2>>>0){c:{if(!d){break c}b=c;e=d&7;if(e){while(1){F[b>>2]=1;b=b+4|0;f=f+1|0;if((e|0)!=(f|0)){continue}break}}c=(d<<2)+c|0;if((d-1&1073741823)>>>0<7){break c}while(1){F[b+24>>2]=1;F[b+28>>2]=1;F[b+16>>2]=1;F[b+20>>2]=1;F[b+8>>2]=1;F[b+12>>2]=1;F[b>>2]=1;F[b+4>>2]=1;b=b+32|0;if((c|0)!=(b|0)){continue}break}}F[a+88>>2]=c;break b}d:{b=c;c=F[a+84>>2];i=b-c|0;g=i>>2;b=g+d|0;if(b>>>0<1073741824){e=e-c|0;h=e>>>1|0;e=e>>>0>=2147483644?1073741823:b>>>0<h>>>0?h:b;if(e){if(e>>>0>=1073741824){break d}j=ka(e<<2)}g=(g<<2)+j|0;b=g;h=d&7;if(h){while(1){F[b>>2]=1;b=b+4|0;f=f+1|0;if((h|0)!=(f|0)){continue}break}}f=g+(d<<2)|0;if((d-1&1073741823)>>>0>=7){while(1){F[b+24>>2]=1;F[b+28>>2]=1;F[b+16>>2]=1;F[b+20>>2]=1;F[b+8>>2]=1;F[b+12>>2]=1;F[b>>2]=1;F[b+4>>2]=1;b=b+32|0;if((f|0)!=(b|0)){continue}break}}b=pa(j,c,i);F[a+88>>2]=f;F[a+84>>2]=b;F[a+92>>2]=b+(e<<2);if(c){ja(c)}break b}na();v()}oa();v()}return}if(b>>>0>=d>>>0){break a}F[a+88>>2]=c+(b<<2)}}function ab(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;d=F[a+8>>2];e=F[a+4>>2];if(d-e>>2>>>0>=b>>>0){a:{if(!b){break a}d=e;g=b&7;if(g){while(1){F[d>>2]=F[c>>2];d=d+4|0;f=f+1|0;if((g|0)!=(f|0)){continue}break}}e=(b<<2)+e|0;if((b-1&1073741823)>>>0<7){break a}while(1){F[d>>2]=F[c>>2];F[d+4>>2]=F[c>>2];F[d+8>>2]=F[c>>2];F[d+12>>2]=F[c>>2];F[d+16>>2]=F[c>>2];F[d+20>>2]=F[c>>2];F[d+24>>2]=F[c>>2];F[d+28>>2]=F[c>>2];d=d+32|0;if((e|0)!=(d|0)){continue}break}}F[a+4>>2]=e;return}b:{i=F[a>>2];f=e-i>>2;h=f+b|0;if(h>>>0<1073741824){j=d-i|0;d=j>>>1|0;h=j>>>0>=2147483644?1073741823:d>>>0>h>>>0?d:h;if(h){if(h>>>0>=1073741824){break b}k=ka(h<<2)}f=(f<<2)+k|0;d=f;j=b&7;if(j){while(1){F[d>>2]=F[c>>2];d=d+4|0;g=g+1|0;if((j|0)!=(g|0)){continue}break}}g=(b<<2)+f|0;if((b-1&1073741823)>>>0>=7){while(1){F[d>>2]=F[c>>2];F[d+4>>2]=F[c>>2];F[d+8>>2]=F[c>>2];F[d+12>>2]=F[c>>2];F[d+16>>2]=F[c>>2];F[d+20>>2]=F[c>>2];F[d+24>>2]=F[c>>2];F[d+28>>2]=F[c>>2];d=d+32|0;if((g|0)!=(d|0)){continue}break}}if((e|0)!=(i|0)){while(1){f=f-4|0;e=e-4|0;F[f>>2]=F[e>>2];if((e|0)!=(i|0)){continue}break}}F[a+8>>2]=(h<<2)+k;F[a+4>>2]=g;F[a>>2]=f;if(i){ja(i)}return}na();v()}oa();v()}function Xb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;d=F[a+8>>2];e=F[a>>2];if(d-e>>2>>>0>=b>>>0){f=F[a+4>>2];h=f-e>>2;i=b>>>0>h>>>0?h:b;a:{if(!i){break a}d=e;g=i;j=g&7;if(j){while(1){F[d>>2]=F[c>>2];g=g-1|0;d=d+4|0;k=k+1|0;if((k|0)!=(j|0)){continue}break}}if(i>>>0<8){break a}while(1){F[d>>2]=F[c>>2];F[d+4>>2]=F[c>>2];F[d+8>>2]=F[c>>2];F[d+12>>2]=F[c>>2];F[d+16>>2]=F[c>>2];F[d+20>>2]=F[c>>2];F[d+24>>2]=F[c>>2];F[d+28>>2]=F[c>>2];d=d+32|0;g=g-8|0;if(g){continue}break}}if(b>>>0>h>>>0){b=(b-h<<2)+f|0;while(1){F[f>>2]=F[c>>2];f=f+4|0;if((b|0)!=(f|0)){continue}break}F[a+4>>2]=b;return}F[a+4>>2]=e+(b<<2);return}if(e){F[a+4>>2]=e;ja(e);F[a+8>>2]=0;F[a>>2]=0;F[a+4>>2]=0;d=0}b:{if(b>>>0>=1073741824){break b}e=d>>>1|0;d=d>>>0>=2147483644?1073741823:b>>>0<e>>>0?e:b;if(d>>>0>=1073741824){break b}d=d<<2;e=ka(d);F[a>>2]=e;F[a+8>>2]=d+e;c=F[c>>2];d=e;g=b&7;if(g){while(1){F[d>>2]=c;d=d+4|0;f=f+1|0;if((g|0)!=(f|0)){continue}break}}e=e+(b<<2)|0;if((b-1&1073741823)>>>0>=7){while(1){F[d+28>>2]=c;F[d+24>>2]=c;F[d+20>>2]=c;F[d+16>>2]=c;F[d+12>>2]=c;F[d+8>>2]=c;F[d+4>>2]=c;F[d>>2]=c;d=d+32|0;if((e|0)!=(d|0)){continue}break}}F[a+4>>2]=e;return}na();v()}function Ka(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;f=(c>>>0)/3|0;j=F[(F[F[a+8>>2]+96>>2]+L(f,12)|0)+(c-L(f,3)<<2)>>2];a:{h=F[F[a+12>>2]+4>>2];e=F[h+4>>2];if((e|0)!=F[h+8>>2]){F[e>>2]=j;F[h+4>>2]=e+4;break a}b:{i=F[h>>2];f=e-i|0;g=f>>2;d=g+1|0;if(d>>>0<1073741824){k=g<<2;g=f>>>1|0;g=f>>>0>=2147483644?1073741823:d>>>0<g>>>0?g:d;if(g){if(g>>>0>=1073741824){break b}f=ka(g<<2)}else{f=0}d=k+f|0;F[d>>2]=j;j=d+4|0;if((e|0)!=(i|0)){while(1){d=d-4|0;e=e-4|0;F[d>>2]=F[e>>2];if((e|0)!=(i|0)){continue}break}}F[h+8>>2]=f+(g<<2);F[h+4>>2]=j;F[h>>2]=d;if(i){ja(i)}break a}na();v()}oa();v()}c:{d:{h=F[a+4>>2];e=F[h+4>>2];e:{if((e|0)!=F[h+8>>2]){F[e>>2]=c;F[h+4>>2]=e+4;break e}i=F[h>>2];f=e-i|0;j=f>>2;d=j+1|0;if(d>>>0>=1073741824){break d}g=f>>>1|0;g=f>>>0>=2147483644?1073741823:d>>>0<g>>>0?g:d;if(g){if(g>>>0>=1073741824){break c}f=ka(g<<2)}else{f=0}d=f+(j<<2)|0;F[d>>2]=c;c=d+4|0;if((e|0)!=(i|0)){while(1){d=d-4|0;e=e-4|0;F[d>>2]=F[e>>2];if((e|0)!=(i|0)){continue}break}}F[h+8>>2]=f+(g<<2);F[h+4>>2]=c;F[h>>2]=d;if(!i){break e}ja(i)}a=F[a+4>>2];F[F[a+12>>2]+(b<<2)>>2]=F[a+24>>2];F[a+24>>2]=F[a+24>>2]+1;return}na();v()}oa();v()}function pb(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0;h=d-c|0;if((h|0)<=0){return}a:{e=F[a+8>>2];i=F[a+4>>2];if((e-i|0)>=(h|0)){j=i-b|0;if((j|0)>=(h|0)){f=i;g=d;break a}f=i;g=c+j|0;if((g|0)!=(d|0)){e=g;while(1){D[f|0]=G[e|0];f=f+1|0;e=e+1|0;if((e|0)!=(d|0)){continue}break}}F[a+4>>2]=f;if((j|0)>0){break a}return}k=F[a>>2];g=(i-k|0)+h|0;if((g|0)>=0){j=b-k|0;f=e-k|0;e=f<<1;f=f>>>0>=1073741823?2147483647:e>>>0>g>>>0?e:g;if(f){e=ka(f)}else{e=0}g=j+e|0;if((c|0)!=(d|0)){g=la(g,c,h)+h|0}d=pa(e,k,j);c=i-b|0;b=pa(g,b,c);F[a+8>>2]=e+f;F[a+4>>2]=b+c;F[a>>2]=d;if(k){ja(k)}return}na();v()}e=f;d=e-h|0;if(i>>>0>d>>>0){while(1){D[e|0]=G[d|0];e=e+1|0;d=d+1|0;if(i>>>0>d>>>0){continue}break}}F[a+4>>2]=e;a=b+h|0;if((a|0)!=(f|0)){a=f-a|0;pa(f-a|0,b,a)}if((c|0)==(g|0)){return}f=(c^-1)+g|0;a=g-c&7;b:{if(!a){e=b;break b}d=0;e=b;while(1){D[e|0]=G[c|0];e=e+1|0;c=c+1|0;d=d+1|0;if((a|0)!=(d|0)){continue}break}}if(f>>>0<7){return}while(1){D[e|0]=G[c|0];D[e+1|0]=G[c+1|0];D[e+2|0]=G[c+2|0];D[e+3|0]=G[c+3|0];D[e+4|0]=G[c+4|0];D[e+5|0]=G[c+5|0];D[e+6|0]=G[c+6|0];D[e+7|0]=G[c+7|0];e=e+8|0;c=c+8|0;if((g|0)!=(c|0)){continue}break}}function la(a,b,c){var d=0,e=0,f=0;if(c>>>0>=512){Y(a|0,b|0,c|0);return a}e=a+c|0;a:{if(!((a^b)&3)){b:{if(!(a&3)){c=a;break b}if(!c){c=a;break b}c=a;while(1){D[c|0]=G[b|0];b=b+1|0;c=c+1|0;if(!(c&3)){break b}if(c>>>0<e>>>0){continue}break}}d=e&-4;c:{if(d>>>0<64){break c}f=d+-64|0;if(f>>>0<c>>>0){break c}while(1){F[c>>2]=F[b>>2];F[c+4>>2]=F[b+4>>2];F[c+8>>2]=F[b+8>>2];F[c+12>>2]=F[b+12>>2];F[c+16>>2]=F[b+16>>2];F[c+20>>2]=F[b+20>>2];F[c+24>>2]=F[b+24>>2];F[c+28>>2]=F[b+28>>2];F[c+32>>2]=F[b+32>>2];F[c+36>>2]=F[b+36>>2];F[c+40>>2]=F[b+40>>2];F[c+44>>2]=F[b+44>>2];F[c+48>>2]=F[b+48>>2];F[c+52>>2]=F[b+52>>2];F[c+56>>2]=F[b+56>>2];F[c+60>>2]=F[b+60>>2];b=b- -64|0;c=c- -64|0;if(f>>>0>=c>>>0){continue}break}}if(c>>>0>=d>>>0){break a}while(1){F[c>>2]=F[b>>2];b=b+4|0;c=c+4|0;if(d>>>0>c>>>0){continue}break}break a}if(e>>>0<4){c=a;break a}d=e-4|0;if(d>>>0<a>>>0){c=a;break a}c=a;while(1){D[c|0]=G[b|0];D[c+1|0]=G[b+1|0];D[c+2|0]=G[b+2|0];D[c+3|0]=G[b+3|0];b=b+4|0;c=c+4|0;if(d>>>0>=c>>>0){continue}break}}if(c>>>0<e>>>0){while(1){D[c|0]=G[b|0];b=b+1|0;c=c+1|0;if((e|0)!=(c|0)){continue}break}}return a}function sd(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;F[a>>2]=8336;d=a+232|0;b=F[d+196>>2];if(b){F[d+200>>2]=b;ja(b)}c=F[d+184>>2];if(c){b=c;e=F[d+188>>2];if((b|0)!=(e|0)){while(1){b=e-12|0;f=F[b>>2];if(f){F[e-8>>2]=f;ja(f)}e=b;if((b|0)!=(c|0)){continue}break}b=F[d+184>>2]}F[d+188>>2]=c;ja(b)}b=F[d+156>>2];if(b){F[d+160>>2]=b;ja(b)}c=F[d+136>>2];F[d+136>>2]=0;if(c){e=c-4|0;b=F[e>>2];if(b){b=c+(b<<4)|0;while(1){b=b-16|0;if((c|0)!=(b|0)){continue}break}}ja(e)}td(a+216|0);b=F[a+196>>2];if(b){F[a+200>>2]=b;ja(b)}b=F[a+184>>2];if(b){F[a+188>>2]=b;ja(b)}b=F[a+172>>2];if(b){F[a+176>>2]=b;ja(b)}b=F[a+160>>2];if(b){F[a+164>>2]=b;ja(b)}b=F[a+144>>2];if(b){while(1){c=F[b>>2];ja(b);b=c;if(b){continue}break}}b=F[a+136>>2];F[a+136>>2]=0;if(b){ja(b)}b=F[a+120>>2];if(b){ja(b)}b=F[a+108>>2];if(b){ja(b)}b=F[a+96>>2];if(b){ja(b)}b=F[a+72>>2];if(b){F[a+76>>2]=b;ja(b)}b=F[a+60>>2];if(b){ja(b)}b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}b=F[a+36>>2];if(b){F[a+40>>2]=b;ja(b)}b=F[a+24>>2];if(b){F[a+28>>2]=b;ja(b)}b=F[a+12>>2];if(b){F[a+16>>2]=b;ja(b)}b=F[a+8>>2];F[a+8>>2]=0;if(b){Za(b)}return a|0}function Fa(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;d=F[a+8>>2];e=F[a+4>>2];if(d-e>>2>>>0>=b>>>0){a:{if(!b){break a}d=e;f=b&7;if(f){while(1){F[d>>2]=F[c>>2];d=d+4|0;h=h+1|0;if((f|0)!=(h|0)){continue}break}}e=(b<<2)+e|0;if((b-1&1073741823)>>>0<7){break a}while(1){F[d>>2]=F[c>>2];F[d+4>>2]=F[c>>2];F[d+8>>2]=F[c>>2];F[d+12>>2]=F[c>>2];F[d+16>>2]=F[c>>2];F[d+20>>2]=F[c>>2];F[d+24>>2]=F[c>>2];F[d+28>>2]=F[c>>2];d=d+32|0;if((e|0)!=(d|0)){continue}break}}F[a+4>>2]=e;return}b:{i=F[a>>2];j=e-i|0;f=j>>2;g=f+b|0;if(g>>>0<1073741824){d=d-i|0;e=d>>>1|0;g=d>>>0>=2147483644?1073741823:e>>>0>g>>>0?e:g;if(g){if(g>>>0>=1073741824){break b}k=ka(g<<2)}f=(f<<2)+k|0;d=f;e=b&7;if(e){while(1){F[d>>2]=F[c>>2];d=d+4|0;h=h+1|0;if((e|0)!=(h|0)){continue}break}}e=f+(b<<2)|0;if((b-1&1073741823)>>>0>=7){while(1){F[d>>2]=F[c>>2];F[d+4>>2]=F[c>>2];F[d+8>>2]=F[c>>2];F[d+12>>2]=F[c>>2];F[d+16>>2]=F[c>>2];F[d+20>>2]=F[c>>2];F[d+24>>2]=F[c>>2];F[d+28>>2]=F[c>>2];d=d+32|0;if((e|0)!=(d|0)){continue}break}}b=pa(k,i,j);F[a+4>>2]=e;F[a>>2]=b;F[a+8>>2]=b+(g<<2);if(i){ja(i)}return}na();v()}oa();v()}function Sb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;if(G[a+11|0]>>>7|0){d=F[a+4>>2]}else{d=G[a+11|0]&127}if(d>>>0<b>>>0){h=Z-16|0;Z=h;b=b-d|0;if(b){g=G[a+11|0]>>>7|0?(F[a+8>>2]&2147483647)-1|0:10;if(G[a+11|0]>>>7|0){d=F[a+4>>2]}else{d=G[a+11|0]&127}i=d+b|0;if(g-d>>>0<b>>>0){a:{e=Z-16|0;Z=e;c=i-g|0;if(c>>>0<=2147483631-g>>>0){if(G[a+11|0]>>>7|0){f=F[a>>2]}else{f=a}if(g>>>0<1073741799){F[e+12>>2]=g<<1;F[e>>2]=c+g;c=Z-16|0;Z=c;Z=c+16|0;c=e+12|0;c=F[(I[e>>2]<I[c>>2]?c:e)>>2];if(c>>>0>=11){j=c+16&-16;c=j-1|0;c=(c|0)==11?j:c}else{c=10}c=c+1|0}else{c=2147483631}sb(e,c);c=F[e>>2];if(d){db(c,f,d)}if((g|0)!=10){ja(f)}F[a>>2]=c;F[a+8>>2]=F[a+8>>2]&-2147483648|F[e+4>>2]&2147483647;F[a+8>>2]=F[a+8>>2]|-2147483648;Z=e+16|0;break a}za();v()}}f=d;if(G[a+11|0]>>>7|0){d=F[a>>2]}else{d=a}f=f+d|0;e=Z-16|0;Z=e;D[e+15|0]=0;while(1){if(b){D[f|0]=G[e+15|0];b=b-1|0;f=f+1|0;continue}break}Z=e+16|0;Ic(a,i);D[h+15|0]=0;D[d+i|0]=G[h+15|0]}Z=h+16|0;return}if(G[a+11|0]>>>7|0){d=F[a>>2]}else{d=a}f=Z-16|0;Z=f;Ic(a,b);D[f+15|0]=0;D[b+d|0]=G[f+15|0];Z=f+16|0}function Zc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;g=Z-16|0;Z=g;a:{b:{if(b){F[a+88>>2]=0;F[a+92>>2]=0;d=F[a+84>>2];F[a+84>>2]=0;if(d){ja(d)}F[a+76>>2]=0;F[a+80>>2]=0;d=F[a+72>>2];F[a+72>>2]=0;if(d){ja(d)}d=F[b>>2];c=F[b+4>>2];D[g+15|0]=0;Ea(a,c-d>>2,g+15|0);d=F[b+28>>2];c=F[b+24>>2];D[g+14|0]=0;Ea(a+12|0,d-c>>2,g+14|0);Xb(a+28|0,F[b+4>>2]-F[b>>2]>>2,10284);c=F[b+28>>2]-F[b+24>>2]|0;f=c>>2;e=F[a+52>>2];c:{if(f>>>0<=F[a+60>>2]-e>>2>>>0){break c}if((c|0)<0){break b}d=F[a+56>>2];c=ka(c);f=c+(f<<2)|0;h=c+(d-e&-4)|0;c=h;if((d|0)!=(e|0)){while(1){c=c-4|0;d=d-4|0;F[c>>2]=F[d>>2];if((d|0)!=(e|0)){continue}break}}F[a+60>>2]=f;F[a+56>>2]=h;F[a+52>>2]=c;if(!e){break c}ja(e)}c=F[b+28>>2]-F[b+24>>2]|0;f=c>>2;e=F[a+40>>2];d:{if(f>>>0<=F[a+48>>2]-e>>2>>>0){break d}if((c|0)<0){break a}d=F[a+44>>2];c=ka(c);f=c+(f<<2)|0;h=c+(d-e&-4)|0;c=h;if((d|0)!=(e|0)){while(1){c=c-4|0;d=d-4|0;F[c>>2]=F[d>>2];if((d|0)!=(e|0)){continue}break}}F[a+48>>2]=f;F[a+44>>2]=h;F[a+40>>2]=c;if(!e){break d}ja(e)}D[a+24|0]=1;F[a+64>>2]=b}Z=g+16|0;return}na();v()}na();v()}function nb(a,b){var c=0,d=0,e=0;c=(a|0)==(b|0);D[b+12|0]=c;a:{if(c){break a}while(1){d=F[b+8>>2];if(G[d+12|0]){break a}b:{c=F[d+8>>2];e=F[c>>2];if((e|0)==(d|0)){e=F[c+4>>2];if(!(!e|G[e+12|0])){break b}c:{if(F[d>>2]==(b|0)){b=d;break c}b=F[d+4>>2];a=F[b>>2];F[d+4>>2]=a;if(a){F[a+8>>2]=d;c=F[d+8>>2]}F[b+8>>2]=c;a=F[d+8>>2];F[((F[a>>2]!=(d|0))<<2)+a>>2]=b;F[b>>2]=d;F[d+8>>2]=b;c=F[b+8>>2];d=F[c>>2]}D[b+12|0]=1;D[c+12|0]=0;a=F[d+4>>2];F[c>>2]=a;if(a){F[a+8>>2]=c}F[d+8>>2]=F[c+8>>2];a=F[c+8>>2];F[((F[a>>2]!=(c|0))<<2)+a>>2]=d;F[d+4>>2]=c;F[c+8>>2]=d;return}if(!(G[e+12|0]|!e)){break b}d:{if(F[d>>2]!=(b|0)){b=d;break d}a=F[b+4>>2];F[d>>2]=a;if(a){F[a+8>>2]=d;c=F[d+8>>2]}F[b+8>>2]=c;a=F[d+8>>2];F[((F[a>>2]!=(d|0))<<2)+a>>2]=b;F[b+4>>2]=d;F[d+8>>2]=b;c=F[b+8>>2]}D[b+12|0]=1;D[c+12|0]=0;a=F[c+4>>2];b=F[a>>2];F[c+4>>2]=b;if(b){F[b+8>>2]=c}F[a+8>>2]=F[c+8>>2];b=F[c+8>>2];F[((F[b>>2]!=(c|0))<<2)+b>>2]=a;F[a>>2]=c;F[c+8>>2]=a;break a}D[d+12|0]=1;D[c+12|0]=(a|0)==(c|0);D[e+12|0]=1;b=c;if((c|0)!=(a|0)){continue}break}}}function mi(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{k:{if(b){if(!c){break k}if(!d){break j}e=O(d)-O(b)|0;if(e>>>0<=31){break i}break c}if((d|0)==1|d>>>0>1){break c}_=0;a=(a>>>0)/(c>>>0)|0;break a}if(!a){break h}if(!d|d-1&d){break g}a=b>>>ji(d)|0;_=0;break a}if(!(c-1&c)){break f}h=(O(c)+33|0)-O(b)|0;g=0-h|0;break d}h=e+1|0;g=63-e|0;break d}_=0;a=(b>>>0)/(d>>>0)|0;break a}e=O(d)-O(b)|0;if(e>>>0<31){break e}break c}if((c|0)==1){break b}d=ji(c);c=d&31;if((d&63)>>>0>=32){a=b>>>c|0}else{e=b>>>c|0;a=((1<<c)-1&b)<<32-c|a>>>c}_=e;break a}h=e+1|0;g=63-e|0}e=h&63;f=e&31;if(e>>>0>=32){e=0;i=b>>>f|0}else{e=b>>>f|0;i=((1<<f)-1&b)<<32-f|a>>>f}g=g&63;f=g&31;if(g>>>0>=32){b=a<<f;a=0}else{b=(1<<f)-1&a>>>32-f|b<<f;a=a<<f}if(h){f=d-1|0;g=c-1|0;m=(g|0)!=-1?f+1|0:f;while(1){j=e<<1|i>>>31;e=i<<1|b>>>31;f=m-(j+(e>>>0>g>>>0)|0)>>31;k=c&f;i=e-k|0;e=j-((d&f)+(e>>>0<k>>>0)|0)|0;b=b<<1|a>>>31;a=l|a<<1;l=f&1;h=h-1|0;if(h){continue}break}}_=b<<1|a>>>31;a=l|a<<1;break a}a=0;b=0}_=b}return a}function yh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;c=F[b+88>>2];if(!(!c|F[c>>2]!=1)){e=F[c+8>>2];F[a+4>>2]=G[e|0]|G[e+1|0]<<8|(G[e+2|0]<<16|G[e+3|0]<<24);f=a+8|0;d=G[b+24|0];h=F[a+8>>2];g=F[a+12>>2]-h>>2;a:{if(d>>>0>g>>>0){qa(f,d-g|0);d=G[b+24|0];e=F[c+8>>2];break a}if(d>>>0>=g>>>0){break a}F[a+12>>2]=h+(d<<2)}b:{if(!d){b=4;break b}h=d&3;f=F[f>>2];c:{if(d-1>>>0<3){b=4;d=0;break c}k=d&252;d=0;b=4;while(1){g=d<<2;c=b+e|0;F[g+f>>2]=G[c|0]|G[c+1|0]<<8|(G[c+2|0]<<16|G[c+3|0]<<24);F[f+(g|4)>>2]=G[c+4|0]|G[c+5|0]<<8|(G[c+6|0]<<16|G[c+7|0]<<24);F[f+(g|8)>>2]=G[c+8|0]|G[c+9|0]<<8|(G[c+10|0]<<16|G[c+11|0]<<24);F[f+(g|12)>>2]=G[c+12|0]|G[c+13|0]<<8|(G[c+14|0]<<16|G[c+15|0]<<24);d=d+4|0;b=b+16|0;i=i+4|0;if((k|0)!=(i|0)){continue}break}}if(!h){break b}while(1){c=b+e|0;F[f+(d<<2)>>2]=G[c|0]|G[c+1|0]<<8|(G[c+2|0]<<16|G[c+3|0]<<24);d=d+1|0;b=b+4|0;j=j+1|0;if((h|0)!=(j|0)){continue}break}}d=a;a=b+e|0;F[d+20>>2]=G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24);d=1}return d|0}function Yg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0;g=Z-16|0;Z=g;e=F[a+4>>2];d=F[e>>2];a:{b=F[a+12>>2];c=F[b+28>>2]-F[b+24>>2]|0;f=c>>2;b:{if(f>>>0<=F[e+8>>2]-d>>2>>>0){break b}if((c|0)<0){break a}b=F[e+4>>2];c=ka(c);f=c+(f<<2)|0;h=c+(b-d&-4)|0;c=h;if((b|0)!=(d|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(d|0)){continue}break}}F[e+8>>2]=f;F[e+4>>2]=h;F[e>>2]=c;if(!d){break b}ja(d)}b=F[a+12>>2];c=F[b+28>>2];b=F[b+24>>2];F[g+12>>2]=0;b=c-b>>2;d=a+96|0;e=F[d>>2];c=F[a+100>>2]-e>>2;c:{if(b>>>0>c>>>0){Fa(d,b-c|0,g+12|0);break c}if(b>>>0>=c>>>0){break c}F[a+100>>2]=e+(b<<2)}e=a+8|0;b=F[a+116>>2];d:{if(b){d=F[b>>2];if((d|0)==F[b+4>>2]){c=1;break d}b=0;while(1){c=rd(e,F[(b<<2)+d>>2]);if(!c){break d}f=F[a+116>>2];d=F[f>>2];b=b+1|0;if(b>>>0<F[f+4>>2]-d>>2>>>0){continue}break}break d}c=1;a=F[a+12>>2];a=F[a+4>>2]-F[a>>2]|0;if(a>>>0<12){break d}a=(a>>2>>>0)/3|0;b=0;while(1){c=rd(e,L(b,3));if(!c){break d}b=b+1|0;if((a|0)!=(b|0)){continue}break}}Z=g+16|0;return c|0}na();v()}function md(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0;a:{b:{c:{if(!b){if((d|0)<0){break a}f=F[a+4>>2];b=F[a>>2];d=f-b|0;if(c>>>0>d>>>0){g=c-d|0;e=F[a+8>>2];if(g>>>0<=e-f>>>0){i=a,j=ma(f,0,g)+g|0,F[i+4>>2]=j;break c}if((c|0)<0){break b}f=e-b|0;e=f<<1;f=f>>>0>=1073741823?2147483647:c>>>0<e>>>0?e:c;e=ka(f);ma(e+d|0,0,g);d=pa(e,b,d);F[a+8>>2]=d+f;F[a+4>>2]=c+d;F[a>>2]=d;if(!b){break c}ja(b);break c}if(c>>>0>=d>>>0){break c}F[a+4>>2]=b+c;break c}if((d|0)<0){break a}e=F[a+4>>2];f=F[a>>2];g=e-f|0;d:{if((d|0)<=0&c>>>0<=g>>>0|(d|0)<0){break d}if(c>>>0>g>>>0){d=c-g|0;h=F[a+8>>2];if(d>>>0<=h-e>>>0){i=a,j=ma(e,0,d)+d|0,F[i+4>>2]=j;break d}if((c|0)<0){break b}e=h-f|0;h=e<<1;e=e>>>0>=1073741823?2147483647:c>>>0<h>>>0?h:c;h=ka(e);ma(h+g|0,0,d);d=pa(h,f,g);F[a+8>>2]=d+e;F[a+4>>2]=c+d;F[a>>2]=d;if(!f){break d}ja(f);break d}if(c>>>0>=g>>>0){break d}F[a+4>>2]=c+f}if(!c){break c}pa(F[a>>2],b,c)}b=F[a+28>>2];c=F[a+24>>2]+1|0;b=c?b:b+1|0;F[a+24>>2]=c;F[a+28>>2]=b;g=1;break a}na();v()}return g}function Lg(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;k=F[a+12>>2];c=F[a+68>>2];d=F[c+80>>2];D[b+84|0]=0;n=b+68|0;i=F[b+68>>2];e=F[b+72>>2]-i>>2;a:{if(e>>>0<d>>>0){ab(n,d-e|0,9124);c=F[a+68>>2];d=F[c+80>>2];break a}if(d>>>0>=e>>>0){break a}F[b+72>>2]=i+(d<<2)}b=F[c+100>>2];e=F[c+96>>2];i=(b-e|0)/12|0;m=1;b:{if((b|0)==(e|0)){break b}k=F[k+28>>2];f=F[k>>2];if((f|0)==-1){return 0}o=i>>>0<=1?1:i;c=e;b=0;m=0;while(1){g=F[c>>2];if(g>>>0>=d>>>0){break b}j=F[F[a+72>>2]+12>>2];h=F[j+(f<<2)>>2];if(h>>>0>=d>>>0){break b}f=F[n>>2];F[f+(g<<2)>>2]=h;g=k+(l<<2)|0;h=F[g+4>>2];if((h|0)==-1){break b}l=F[c+4>>2];if(l>>>0>=d>>>0){break b}h=F[(h<<2)+j>>2];if(h>>>0>=d>>>0){break b}F[f+(l<<2)>>2]=h;g=F[g+8>>2];if((g|0)==-1){break b}c=F[c+8>>2];if(c>>>0>=d>>>0){break b}j=F[(g<<2)+j>>2];if(j>>>0>=d>>>0){break b}F[f+(c<<2)>>2]=j;b=b+1|0;m=i>>>0<=b>>>0;if((b|0)==(o|0)){break b}c=e+L(b,12)|0;l=L(b,3);f=F[k+(l<<2)>>2];if((f|0)!=-1){continue}break}}return m|0}function ag(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;h=F[d+80>>2];e=Z-48|0;Z=e;a=F[a+4>>2];m=a-2|0;a:{if(m>>>0>28){break a}j=F[F[d>>2]>>2]+F[d+48>>2]|0;F[e+16>>2]=a;a=-1<<a;F[e+20>>2]=a^-1;a=-2-a|0;F[e+24>>2]=a;F[e+32>>2]=(a|0)/2;J[e+28>>2]=M(2)/M(a|0);f=F[c>>2];if((f|0)!=F[c+4>>2]){a=0;d=0;while(1){g=F[(d<<2)+f>>2];h=e+36|0;k=F[F[b>>2]>>2];l=F[b+48>>2];f=F[b+40>>2];i=F[b+44>>2];if(!G[b+84|0]){g=F[F[b+68>>2]+(g<<2)>>2]}g=ki(f,i,g,0);i=g;g=g+l|0;la(h,g+k|0,f);Kc(e+16|0,h,e+12|0,e+8|0);f=a<<2;F[f+j>>2]=F[e+12>>2];F[(f|4)+j>>2]=F[e+8>>2];a=a+2|0;d=d+1|0;f=F[c>>2];if(d>>>0<F[c+4>>2]-f>>2>>>0){continue}break}break a}if(!h){break a}d=0;a=0;while(1){k=e+36|0;l=F[F[b>>2]>>2];i=F[b+48>>2];c=F[b+40>>2];f=ki(c,F[b+44>>2],G[b+84|0]?a:F[F[b+68>>2]+(a<<2)>>2],0);g=f;f=f+i|0;la(k,f+l|0,c);Kc(e+16|0,k,e+12|0,e+8|0);c=d<<2;F[c+j>>2]=F[e+12>>2];F[(c|4)+j>>2]=F[e+8>>2];d=d+2|0;a=a+1|0;if((h|0)!=(a|0)){continue}break}}Z=e+48|0;return m>>>0<29|0}function Zg(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;c=F[a+12>>2];d=F[a+108>>2];e=F[d+80>>2];D[b+84|0]=0;m=b+68|0;h=F[b+68>>2];f=F[b+72>>2]-h>>2;a:{if(f>>>0<e>>>0){ab(m,e-f|0,9124);d=F[a+108>>2];e=F[d+80>>2];break a}if(e>>>0>=f>>>0){break a}F[b+72>>2]=h+(e<<2)}b=F[d+100>>2];f=F[d+96>>2];h=(b-f|0)/12|0;k=1;b:{if((b|0)==(f|0)){break b}n=h>>>0<=1?1:h;o=F[c>>2];c=0;d=f;b=0;k=0;while(1){c=(c<<2)+o|0;i=F[c>>2];if((i|0)==-1){break b}g=F[d>>2];if(g>>>0>=e>>>0){break b}l=F[F[a+112>>2]+12>>2];j=F[l+(i<<2)>>2];if(j>>>0>=e>>>0){break b}i=F[m>>2];F[i+(g<<2)>>2]=j;g=F[c+4>>2];if((g|0)==-1){break b}j=F[d+4>>2];if(j>>>0>=e>>>0){break b}g=F[(g<<2)+l>>2];if(g>>>0>=e>>>0){break b}F[i+(j<<2)>>2]=g;c=F[c+8>>2];if((c|0)==-1){break b}d=F[d+8>>2];if(d>>>0>=e>>>0){break b}c=F[(c<<2)+l>>2];if(c>>>0>=e>>>0){break b}F[i+(d<<2)>>2]=c;b=b+1|0;k=h>>>0<=b>>>0;if((b|0)==(n|0)){break b}c=L(b,3);d=f+L(b,12)|0;if((b|0)!=1431655765){continue}break}}return k|0}function xd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=M(0),j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0;k=Z-16|0;Z=k;if(F[c+28>>2]==9){d=F[a+4>>2];h=G[c+24|0];e=h<<2;f=ka(e);l=k+8|0;F[l>>2]=1065353216;i=J[a+20>>2];d=-1<<d^-1;if((d|0)>0){J[l>>2]=i/M(d|0)}o=(d|0)>0;a:{if(!o){break a}j=F[c+80>>2];if(!j){break a}if(h){p=F[F[b>>2]>>2]+F[b+48>>2]|0;t=h&254;u=h&1;b=0;while(1){m=F[a+8>>2];i=J[l>>2];d=0;n=0;if((h|0)!=1){while(1){g=d<<2;q=(b<<2)+p|0;J[g+f>>2]=M(i*M(F[q>>2]))+J[g+m>>2];g=g|4;J[g+f>>2]=M(i*M(F[q+4>>2]))+J[g+m>>2];d=d+2|0;b=b+2|0;n=n+2|0;if((t|0)!=(n|0)){continue}break}}if(u){d=d<<2;J[d+f>>2]=M(i*M(F[(b<<2)+p>>2]))+J[d+m>>2];b=b+1|0}la(F[F[c+64>>2]>>2]+r|0,f,e);r=e+r|0;s=s+1|0;if((s|0)!=(j|0)){continue}break}break a}b=0;if((j|0)!=1){a=j&-2;d=0;while(1){la(F[F[c+64>>2]>>2]+b|0,f,e);b=b+e|0;la(b+F[F[c+64>>2]>>2]|0,f,e);b=b+e|0;d=d+2|0;if((a|0)!=(d|0)){continue}break}}if(!(j&1)){break a}la(F[F[c+64>>2]>>2]+b|0,f,e)}ja(f)}Z=k+16|0;return o|0}function Rg(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;c=F[a+12>>2];d=F[a+68>>2];e=F[d+80>>2];D[b+84|0]=0;m=b+68|0;h=F[b+68>>2];f=F[b+72>>2]-h>>2;a:{if(f>>>0<e>>>0){ab(m,e-f|0,9124);d=F[a+68>>2];e=F[d+80>>2];break a}if(e>>>0>=f>>>0){break a}F[b+72>>2]=h+(e<<2)}b=F[d+100>>2];f=F[d+96>>2];h=(b-f|0)/12|0;k=1;b:{if((b|0)==(f|0)){break b}n=h>>>0<=1?1:h;o=F[c>>2];c=0;d=f;b=0;k=0;while(1){c=(c<<2)+o|0;i=F[c>>2];if((i|0)==-1){break b}g=F[d>>2];if(g>>>0>=e>>>0){break b}l=F[F[a+72>>2]+12>>2];j=F[l+(i<<2)>>2];if(j>>>0>=e>>>0){break b}i=F[m>>2];F[i+(g<<2)>>2]=j;g=F[c+4>>2];if((g|0)==-1){break b}j=F[d+4>>2];if(j>>>0>=e>>>0){break b}g=F[(g<<2)+l>>2];if(g>>>0>=e>>>0){break b}F[i+(j<<2)>>2]=g;c=F[c+8>>2];if((c|0)==-1){break b}d=F[d+8>>2];if(d>>>0>=e>>>0){break b}c=F[(c<<2)+l>>2];if(c>>>0>=e>>>0){break b}F[i+(d<<2)>>2]=c;b=b+1|0;k=h>>>0<=b>>>0;if((b|0)==(n|0)){break b}c=L(b,3);d=f+L(b,12)|0;if((b|0)!=1431655765){continue}break}}return k|0}function Na(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;d=Z-16|0;Z=d;a:{f=F[a+4>>2];b:{if(f>>>0<b>>>0){e=b-f|0;c=F[a+8>>2];g=c<<5;c:{if(!(e>>>0>g>>>0|f>>>0>g-e>>>0)){F[a+4>>2]=b;h=f&31;b=F[a>>2]+(f>>>3&536870908)|0;break c}F[d+8>>2]=0;F[d>>2]=0;F[d+4>>2]=0;if((b|0)<0){break a}if(g>>>0<=1073741822){c=c<<6;b=b+31&-32;b=b>>>0<c>>>0?c:b}else{b=2147483647}$a(d,b);f=F[a+4>>2];F[d+4>>2]=f+e;i=F[a>>2];b=F[d>>2];d:{if((f|0)<=0){break d}c=f>>>5|0;if(f>>>0>=32){pa(b,i,c<<2)}g=c<<2;b=g+b|0;h=f&31;if(h){c=-1>>>32-h|0;F[b>>2]=F[b>>2]&(c^-1)|F[i+g>>2]&c}i=F[a>>2]}F[a>>2]=F[d>>2];F[d>>2]=i;c=F[a+4>>2];F[a+4>>2]=F[d+4>>2];F[d+4>>2]=c;c=F[a+8>>2];F[a+8>>2]=F[d+8>>2];F[d+8>>2]=c;if(!i){break c}ja(i)}if(!e){break b}if(h){c=32-h|0;a=c>>>0<e>>>0?c:e;F[b>>2]=F[b>>2]&(-1<<h&-1>>>c-a^-1);e=e-a|0;b=b+4|0}a=e>>>5|0;if(e>>>0>=32){ma(b,0,a<<2)}if((e&-32)==(e|0)){break b}a=(a<<2)+b|0;F[a>>2]=F[a>>2]&(-1>>>32-(e&31)^-1);break b}F[a+4>>2]=b}Z=d+16|0;return}na();v()}function Aa(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;i=Z-16|0;Z=i;f=F[b+20>>2];h=F[b+12>>2];c=F[b+16>>2];a:{if((f|0)>=(h|0)&c>>>0>=I[b+8>>2]|(f|0)>(h|0)){break a}D[a+12|0]=G[c+F[b>>2]|0];c=F[b+20>>2];f=F[b+16>>2]+1|0;c=f?c:c+1|0;F[b+16>>2]=f;F[b+20>>2]=c;if(!Qd(1,i+12|0,b)){break a}h=F[b+8>>2];f=F[b+16>>2];g=h-f|0;c=F[i+12>>2];d=f>>>0>h>>>0;h=F[b+20>>2];e=F[b+12>>2]-(d+h|0)|0;if(g>>>0<c>>>0&(e|0)<=0|(e|0)<0|(c|0)<=0){break a}g=f+F[b>>2]|0;F[a>>2]=g;b:{c:{e=c-1|0;j=e+g|0;d=G[j|0];d:{if(d>>>0<=63){F[a+4>>2]=e;d=G[j|0]&63;break d}e:{switch((d>>>6|0)-1|0){case 1:break c;case 0:break e;default:break a}}if(c>>>0<2){break a}e=c-2|0;F[a+4>>2]=e;g=g+e|0;d=G[g+1|0]<<8&16128|G[g|0]}F[a+8>>2]=d+4096;break b}if(c>>>0<3){break a}e=c-3|0;F[a+4>>2]=e;d=a;a=g+e|0;a=G[a+1|0]<<8|G[a+2|0]<<16&4128768|G[a|0];F[d+8>>2]=a+4096;if(a>>>0>1044479){break a}}a=h;d=c;c=c+f|0;a=d>>>0>c>>>0?a+1|0:a;F[b+16>>2]=c;F[b+20>>2]=a;k=1}Z=i+16|0;return k}function qd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;e=F[a+12>>2];i=F[a+8>>2];d=e-i>>2;b=G[b+24|0];a:{if(d>>>0<b>>>0){qa(a+8|0,b-d|0);i=F[a+8>>2];e=F[a+12>>2];break a}if(b>>>0>=d>>>0){break a}e=(b<<2)+i|0;F[a+12>>2]=e}b=0;f=F[c+8>>2];h=F[c+12>>2];j=F[c+20>>2];e=e-i|0;d=F[c+16>>2];g=e+d|0;j=e>>>0>g>>>0?j+1|0:j;b:{if(f>>>0<g>>>0&(h|0)<=(j|0)|(h|0)<(j|0)){break b}la(i,d+F[c>>2]|0,e);d=F[c+20>>2];g=e;e=e+F[c+16>>2]|0;d=g>>>0>e>>>0?d+1|0:d;F[c+16>>2]=e;F[c+20>>2]=d;f=F[c+8>>2];h=F[c+12>>2];g=e+4|0;d=g>>>0<4?d+1|0:d;if(f>>>0<g>>>0&(d|0)>=(h|0)|(d|0)>(h|0)){break b}d=e+F[c>>2]|0;F[a+20>>2]=G[d|0]|G[d+1|0]<<8|(G[d+2|0]<<16|G[d+3|0]<<24);d=F[c+20>>2];g=d;f=d;e=F[c+16>>2];d=e+4|0;f=d>>>0<4?f+1|0:f;F[c+16>>2]=d;F[c+20>>2]=f;h=F[c+12>>2];if((f|0)>=(h|0)&d>>>0>=I[c+8>>2]|(f|0)>(h|0)){break b}f=G[d+F[c>>2]|0];d=g;e=e+5|0;d=e>>>0<5?d+1|0:d;F[c+16>>2]=e;F[c+20>>2]=d;if(f-1>>>0>29){break b}F[a+4>>2]=f;b=1}return b|0}function Kc(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;j=+J[b>>2];k=+J[b+4>>2];l=+J[b+8>>2];g=N(j)+N(k)+N(l);a:{if(!(g>1e-6)){j=1;k=0;e=0;break a}g=1/g;k=g*k;j=g*j;e=g*l<0}h=F[a+16>>2];l=+(h|0);g=R(j*l+.5);b:{if(N(g)<2147483648){m=~~g;break b}m=-2147483648}f=m>>31;i=(f^m)-f|0;g=R(k*l+.5);c:{if(N(g)<2147483648){f=~~g;break c}f=-2147483648}b=f>>31;b=h-(i+((f^b)-b|0)|0)|0;i=(b|0)>0?b:0;e=e?0-i|0:i;f=f+(b>>31&((f|0)>0?b:0-b|0))|0;d:{if((m|0)>=0){b=e+h|0;a=F[a+8>>2];e=h+f|0;break d}b=f>>31;b=(b^f)-b|0;a=F[a+8>>2];b=(e|0)<0?b:a-b|0;e=(f|0)<0?i:a-i|0}e:{if(!(b|e)){b=a;break e}if(!((a|0)!=(b|0)|e)){b=a;break e}if(!((a|0)!=(e|0)|b)){b=a;break e}if(!((b|0)<=(h|0)|e)){b=(h<<1)-b|0;a=0;break e}if(!((a|0)!=(e|0)|(b|0)>=(h|0))){b=(h<<1)-b|0;break e}if(!((a|0)!=(b|0)|(e|0)>=(h|0))){b=a;a=(h<<1)-e|0;break e}if(b){a=e;break e}b=0;if((e|0)<=(h|0)){a=e;break e}a=(h<<1)-e|0}F[c>>2]=a;F[d>>2]=b}function ye(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;a:{if(!$c(a,b)){break a}h=a+36|0;g=$[F[F[a>>2]+24>>2]](a)|0;e=F[a+40>>2];d=F[a+36>>2];c=e-d>>2;b:{if(g>>>0>c>>>0){Pb(h,g-c|0);break b}if(c>>>0<=g>>>0){break b}d=d+(g<<2)|0;if((d|0)!=(e|0)){while(1){e=e-4|0;c=F[e>>2];F[e>>2]=0;if(c){$[F[F[c>>2]+4>>2]](c)}if((d|0)!=(e|0)){continue}break}}F[a+40>>2]=d}c=1;if((g|0)<=0){break a}e=0;while(1){c:{c=F[b+20>>2];f=F[b+12>>2];d=F[b+16>>2];if((c|0)>=(f|0)&d>>>0>=I[b+8>>2]|(c|0)>(f|0)){break c}f=G[F[b>>2]+d|0];d=d+1|0;c=d?c:c+1|0;F[b+16>>2]=d;F[b+20>>2]=c;d=$[F[F[a>>2]+48>>2]](a,f)|0;f=e<<2;i=f+F[a+36>>2]|0;c=F[i>>2];F[i>>2]=d;if(c){$[F[F[c>>2]+4>>2]](c)}c=F[F[h>>2]+f>>2];if(!c){break c}if(!(k=c,l=$[F[F[a>>2]+28>>2]](a)|0,m=$[F[F[a>>2]+20>>2]](a,e)|0,j=F[F[c>>2]+8>>2],$[j](k|0,l|0,m|0)|0)){break c}c=1;e=e+1|0;if((g|0)!=(e|0)){continue}break a}break}c=0}return c|0}function Xc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;g=F[a>>2];c=g+(b>>>3&536870908)|0;F[c>>2]=F[c>>2]|1<<b;f=F[a+64>>2];e=(b|0)==-1;d=-1;a:{if(e){break a}c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;d=-1;if((c|0)==-1){break a}d=F[F[f>>2]+(c<<2)>>2]}c=F[a+12>>2];h=(d>>>3&536870908)+c|0;F[h>>2]=F[h>>2]|1<<d;b:{c:{if(!e){d:{e:{if((b>>>0)%3|0){e=b-1|0;break e}e=b+2|0;d=-1;if((e|0)==-1){break d}}d=F[F[f>>2]+(e<<2)>>2]}e=(d>>>3&536870908)+c|0;F[e>>2]=F[e>>2]|1<<d;d=-1;b=F[F[f+12>>2]+(b<<2)>>2];if((b|0)==-1){break b}D[a+24|0]=0;a=(b>>>3&536870908)+g|0;F[a>>2]=F[a>>2]|1<<b;a=b+1|0;a=(a>>>0)%3|0?a:b-2|0;if((a|0)!=-1){d=F[F[f>>2]+(a<<2)>>2]}a=c+(d>>>3&536870908)|0;F[a>>2]=F[a>>2]|1<<d;f:{g:{if((b>>>0)%3|0){b=b-1|0;break g}b=b+2|0;a=-1;if((b|0)==-1){break f}}a=F[F[f>>2]+(b<<2)>>2]}b=1<<a;a=c+(a>>>3&536870908)|0;c=F[a>>2];break c}a=c+536870908|0;b=F[c+536870908>>2];c=-2147483648}F[a>>2]=b|c}}function zc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=M(0),f=M(0),g=M(0),h=M(0),i=M(0),j=0,k=M(0),l=M(0),m=M(0),n=M(0),o=0;a:{if(F[c+28>>2]!=9|G[c+24|0]!=3){break a}a=F[a+4>>2];if(a-2>>>0>28){break a}o=1;j=F[c+80>>2];if(!j){break a}k=M(M(2)/M((1<<a)-2|0));c=F[F[c>>2]>>2]+F[c+48>>2]|0;a=F[F[b>>2]>>2]+F[b+48>>2]|0;b=0;while(1){g=M(0);l=M(0);m=M(0);e=M(M(M(F[a>>2])*k)+M(-1));f=M(M(M(F[a+4>>2])*k)+M(-1));i=M(M(M(1)-M(N(e)))-M(N(f)));h=M(Q(M(-i),M(0)));n=M(-h);f=M(f+(f<M(0)?h:n));e=M(e+(e<M(0)?h:n));h=M(M(f*f)+M(M(i*i)+M(e*e)));if(!(+h<1e-6)){g=M(M(1)/M(U(h)));m=M(f*g);l=M(e*g);g=M(i*g)}a=a+8|0;d=(w(m),y(2));D[c+8|0]=d;D[c+9|0]=d>>>8;D[c+10|0]=d>>>16;D[c+11|0]=d>>>24;d=(w(l),y(2));D[c+4|0]=d;D[c+5|0]=d>>>8;D[c+6|0]=d>>>16;D[c+7|0]=d>>>24;d=(w(g),y(2));D[c|0]=d;D[c+1|0]=d>>>8;D[c+2|0]=d>>>16;D[c+3|0]=d>>>24;c=c+12|0;b=b+1|0;if((j|0)!=(b|0)){continue}break}}return o|0}function Md(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;g=Z-16|0;Z=g;a:{if(!Sa(1,g+8|0,b)){break a}d=F[b+8>>2];e=F[b+16>>2];f=d-e|0;h=F[g+12>>2];c=d>>>0<e>>>0;d=F[b+20>>2];i=F[b+12>>2]-(c+d|0)|0;c=F[g+8>>2];if((h|0)==(i|0)&c>>>0>f>>>0|h>>>0>i>>>0){break a}d=d+h|0;f=c+e|0;d=f>>>0<e>>>0?d+1|0:d;F[b+16>>2]=f;F[b+20>>2]=d;if((c|0)<=0){break a}b=F[b>>2]+e|0;F[a+40>>2]=b;e=c-1|0;d=b+e|0;f=G[d|0];b:{if(f>>>0<=63){F[a+44>>2]=e;b=G[d|0]&63;break b}c:{switch((f>>>6|0)-1|0){case 0:if(c>>>0<2){break a}c=c-2|0;F[a+44>>2]=c;b=b+c|0;b=G[b+1|0]<<8&16128|G[b|0];break b;case 1:if(c>>>0<3){break a}c=c-3|0;F[a+44>>2]=c;b=b+c|0;b=G[b+1|0]<<8|G[b+2|0]<<16&4128768|G[b|0];break b;default:break c}}c=c-4|0;F[a+44>>2]=c;b=b+c|0;b=(G[b|0]|G[b+1|0]<<8|(G[b+2|0]<<16|G[b+3|0]<<24))&1073741823}F[a+48>>2]=b+16384;j=b>>>0<4177920}Z=g+16|0;return j}function Tf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;a:{a=Z-32|0;Z=a;e=ya(c);if(e>>>0<2147483632){b:{c:{if(e>>>0>=11){g=(e|15)+1|0;f=ka(g);F[a+24>>2]=g|-2147483648;F[a+16>>2]=f;F[a+20>>2]=e;g=e+f|0;break c}D[a+27|0]=e;f=a+16|0;g=e+f|0;if(!e){break b}}la(f,c,e)}D[g|0]=0;F[a+8>>2]=0;F[a>>2]=0;F[a+4>>2]=0;d:{c=Ya(b,a+16|0);if((c|0)==(b+4|0)){break d}b=F[c+28>>2];e=F[c+32>>2];if((b|0)==(e|0)){break d}b=e-b|0;if(b&3){break d}e=b>>>2|0;f=F[a+4>>2];b=F[a>>2];g=f-b>>2;e:{if(e>>>0>g>>>0){qa(a,e-g|0);b=F[a>>2];f=F[a+4>>2];break e}if(e>>>0>=g>>>0){break e}f=(e<<2)+b|0;F[a+4>>2]=f}if((b|0)!=(f|0)){e=b;b=F[c+28>>2];la(e,b,F[c+32>>2]-b|0);break d}ta();v()}b=F[d>>2];if(b){F[d+4>>2]=b;ja(b)}F[d>>2]=F[a>>2];F[d+4>>2]=F[a+4>>2];F[d+8>>2]=F[a+8>>2];if(D[a+27|0]<0){ja(F[a+16>>2])}Z=a+32|0;break a}za();v()}}function ud(a){a=a|0;var b=0,c=0,d=0,e=0;F[a>>2]=8284;d=F[a+368>>2];F[a+368>>2]=0;if(d){e=d-4|0;b=F[e>>2];if(b){c=(b<<4)+d|0;while(1){c=c-16|0;if((d|0)!=(c|0)){continue}break}}ja(e)}td(a+216|0);b=F[a+196>>2];if(b){F[a+200>>2]=b;ja(b)}b=F[a+184>>2];if(b){F[a+188>>2]=b;ja(b)}b=F[a+172>>2];if(b){F[a+176>>2]=b;ja(b)}b=F[a+160>>2];if(b){F[a+164>>2]=b;ja(b)}c=F[a+144>>2];if(c){while(1){b=F[c>>2];ja(c);c=b;if(b){continue}break}}b=F[a+136>>2];F[a+136>>2]=0;if(b){ja(b)}b=F[a+120>>2];if(b){ja(b)}b=F[a+108>>2];if(b){ja(b)}b=F[a+96>>2];if(b){ja(b)}b=F[a+72>>2];if(b){F[a+76>>2]=b;ja(b)}b=F[a+60>>2];if(b){ja(b)}b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}b=F[a+36>>2];if(b){F[a+40>>2]=b;ja(b)}b=F[a+24>>2];if(b){F[a+28>>2]=b;ja(b)}b=F[a+12>>2];if(b){F[a+16>>2]=b;ja(b)}b=F[a+8>>2];F[a+8>>2]=0;if(b){Za(b)}return a|0}function Vf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;d=Z-16|0;Z=d;a:{e=ya(c);if(e>>>0<2147483632){b:{c:{if(e>>>0>=11){f=(e|15)+1|0;a=ka(f);F[d+8>>2]=f|-2147483648;F[d>>2]=a;F[d+4>>2]=e;f=a+e|0;break c}D[d+11|0]=e;f=d+e|0;a=d;if(!e){break b}}la(a,c,e)}D[f|0]=0;c=G[d+11|0];e=c<<24>>24;b=F[b+4>>2];a=0;d:{if(!b){break d}a=c;c=(e|0)<0;a=c?F[d+4>>2]:a;f=c?F[d>>2]:d;while(1){c=G[b+27|0];g=c<<24>>24<0;c=g?F[b+20>>2]:c;i=c>>>0<a>>>0;e:{f:{g:{h:{i:{j:{h=i?c:a;if(h){g=g?F[b+16>>2]:b+16|0;j=sa(f,g,h);if(j){break j}if(a>>>0>=c>>>0){break i}break e}if(a>>>0>=c>>>0){break h}break e}if((j|0)<0){break e}}c=sa(g,f,h);if(c){break g}}if(i){break f}a=1;break d}if((c|0)<0){break f}a=1;break d}b=b+4|0}b=F[b>>2];if(b){continue}break}a=0}if((e|0)<0){ja(F[d>>2])}Z=d+16|0;break a}za();v()}return a|0}function lc(a,b){var c=0,d=0;c=F[b+8>>2];F[a+4>>2]=F[b+4>>2];F[a+8>>2]=c;F[a+20>>2]=F[b+20>>2];c=F[b+16>>2];F[a+12>>2]=F[b+12>>2];F[a+16>>2]=c;a:{b:{if((a|0)!=(b|0)){c=F[b+28>>2];if(c){d=F[a+24>>2];if(F[a+32>>2]<<5>>>0<c>>>0){if(d){ja(d);F[a+32>>2]=0;F[a+24>>2]=0;F[a+28>>2]=0;c=F[b+28>>2]}if((c|0)<0){break b}c=(c-1>>>5|0)+1|0;d=ka(c<<2);F[a+32>>2]=c;F[a+28>>2]=0;F[a+24>>2]=d;c=F[b+28>>2]}pa(d,F[b+24>>2],(c-1>>>3&536870908)+4|0);c=F[b+28>>2]}else{c=0}F[a+28>>2]=c;c=F[b+40>>2];if(c){d=F[a+36>>2];if(F[a+44>>2]<<5>>>0<c>>>0){if(d){ja(d);F[a+44>>2]=0;F[a+36>>2]=0;F[a+40>>2]=0;c=F[b+40>>2]}if((c|0)<0){break a}c=(c-1>>>5|0)+1|0;d=ka(c<<2);F[a+44>>2]=c;F[a+40>>2]=0;F[a+36>>2]=d;c=F[b+40>>2]}pa(d,F[b+36>>2],(c-1>>>3&536870908)+4|0);b=F[b+40>>2]}else{b=0}F[a+40>>2]=b}return}na();v()}na();v()}function nc(a){var b=0,c=0,d=0;b=F[a+8>>2];d=F[a>>2];a:{if(G[a+12|0]){b:{c:{d:{e:{if((b|0)==-1){break e}c=b+1|0;b=(c>>>0)%3|0?c:b-2|0;if((b|0)==-1){break e}b=F[F[d+12>>2]+(b<<2)>>2];if((b|0)!=-1){break d}}F[a+8>>2]=-1;break c}c=b+1|0;b=(c>>>0)%3|0?c:b-2|0;F[a+8>>2]=b;if((b|0)!=-1){break b}}c=F[a+4>>2];b=-1;f:{if((c|0)==-1){break f}g:{if((c>>>0)%3|0){c=c-1|0;break g}c=c+2|0;b=-1;if((c|0)==-1){break f}}c=F[F[d+12>>2]+(c<<2)>>2];b=-1;if((c|0)==-1){break f}b=c-1|0;if((c>>>0)%3|0){break f}b=c+2|0}D[a+12|0]=0;F[a+8>>2]=b;return}if((b|0)!=F[a+4>>2]){break a}F[a+8>>2]=-1;return}c=-1;h:{if((b|0)==-1){break h}i:{if((b>>>0)%3|0){b=b-1|0;break i}b=b+2|0;c=-1;if((b|0)==-1){break h}}b=F[F[d+12>>2]+(b<<2)>>2];c=-1;if((b|0)==-1){break h}c=b-1|0;if((b>>>0)%3|0){break h}c=b+2|0}F[a+8>>2]=c}}function Od(a){var b=0,c=0,d=0;b=ka(32);D[b+26|0]=0;c=G[1475]|G[1476]<<8;D[b+24|0]=c;D[b+25|0]=c>>>8;c=G[1471]|G[1472]<<8|(G[1473]<<16|G[1474]<<24);d=G[1467]|G[1468]<<8|(G[1469]<<16|G[1470]<<24);D[b+16|0]=d;D[b+17|0]=d>>>8;D[b+18|0]=d>>>16;D[b+19|0]=d>>>24;D[b+20|0]=c;D[b+21|0]=c>>>8;D[b+22|0]=c>>>16;D[b+23|0]=c>>>24;c=G[1463]|G[1464]<<8|(G[1465]<<16|G[1466]<<24);d=G[1459]|G[1460]<<8|(G[1461]<<16|G[1462]<<24);D[b+8|0]=d;D[b+9|0]=d>>>8;D[b+10|0]=d>>>16;D[b+11|0]=d>>>24;D[b+12|0]=c;D[b+13|0]=c>>>8;D[b+14|0]=c>>>16;D[b+15|0]=c>>>24;c=G[1455]|G[1456]<<8|(G[1457]<<16|G[1458]<<24);d=G[1451]|G[1452]<<8|(G[1453]<<16|G[1454]<<24);D[b|0]=d;D[b+1|0]=d>>>8;D[b+2|0]=d>>>16;D[b+3|0]=d>>>24;D[b+4|0]=c;D[b+5|0]=c>>>8;D[b+6|0]=c>>>16;D[b+7|0]=c>>>24;F[a>>2]=-1;ra(a+4|0,b,26);ja(b)}function Kg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0;e=F[a+4>>2];d=F[e>>2];a:{b=F[a+12>>2];c=F[b+56>>2]-F[b+52>>2]|0;f=c>>2;b:{if(f>>>0<=F[e+8>>2]-d>>2>>>0){break b}if((c|0)<0){break a}b=F[e+4>>2];c=ka(c);f=c+(f<<2)|0;g=c+(b-d&-4)|0;c=g;if((b|0)!=(d|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(d|0)){continue}break}}F[e+8>>2]=f;F[e+4>>2]=g;F[e>>2]=c;if(!d){break b}ja(d)}e=a+8|0;b=F[a+76>>2];c:{if(b){d=F[b>>2];if((d|0)==F[b+4>>2]){return 1}b=0;while(1){c=od(e,F[(b<<2)+d>>2]);if(!c){break c}f=F[a+76>>2];d=F[f>>2];b=b+1|0;if(b>>>0<F[f+4>>2]-d>>2>>>0){continue}break}break c}c=1;a=F[F[a+12>>2]+64>>2];a=F[a+4>>2]-F[a>>2]|0;if(a>>>0<12){break c}a=(a>>2>>>0)/3|0;b=0;while(1){c=od(e,L(b,3));if(!c){break c}b=b+1|0;if((a|0)!=(b|0)){continue}break}}return c|0}na();v()}function Qg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0;e=F[a+4>>2];d=F[e>>2];a:{b=F[a+12>>2];c=F[b+28>>2]-F[b+24>>2]|0;f=c>>2;b:{if(f>>>0<=F[e+8>>2]-d>>2>>>0){break b}if((c|0)<0){break a}b=F[e+4>>2];c=ka(c);f=c+(f<<2)|0;g=c+(b-d&-4)|0;c=g;if((b|0)!=(d|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(d|0)){continue}break}}F[e+8>>2]=f;F[e+4>>2]=g;F[e>>2]=c;if(!d){break b}ja(d)}e=a+8|0;b=F[a+76>>2];c:{if(b){d=F[b>>2];if((d|0)==F[b+4>>2]){return 1}b=0;while(1){c=pd(e,F[(b<<2)+d>>2]);if(!c){break c}f=F[a+76>>2];d=F[f>>2];b=b+1|0;if(b>>>0<F[f+4>>2]-d>>2>>>0){continue}break}break c}c=1;a=F[a+12>>2];a=F[a+4>>2]-F[a>>2]|0;if(a>>>0<12){break c}a=(a>>2>>>0)/3|0;b=0;while(1){c=pd(e,L(b,3));if(!c){break c}b=b+1|0;if((a|0)!=(b|0)){continue}break}}return c|0}na();v()}function pa(a,b,c){var d=0,e=0;a:{if((a|0)==(b|0)){break a}e=a+c|0;if(b-e>>>0<=0-(c<<1)>>>0){return la(a,b,c)}d=(a^b)&3;b:{c:{if(a>>>0<b>>>0){if(d){d=a;break b}if(!(a&3)){d=a;break c}d=a;while(1){if(!c){break a}D[d|0]=G[b|0];b=b+1|0;c=c-1|0;d=d+1|0;if(d&3){continue}break}break c}d:{if(d){break d}if(e&3){while(1){if(!c){break a}c=c-1|0;d=c+a|0;D[d|0]=G[b+c|0];if(d&3){continue}break}}if(c>>>0<=3){break d}while(1){c=c-4|0;F[c+a>>2]=F[b+c>>2];if(c>>>0>3){continue}break}}if(!c){break a}while(1){c=c-1|0;D[c+a|0]=G[b+c|0];if(c){continue}break}break a}if(c>>>0<=3){break b}while(1){F[d>>2]=F[b>>2];b=b+4|0;d=d+4|0;c=c-4|0;if(c>>>0>3){continue}break}}if(!c){break a}while(1){D[d|0]=G[b|0];d=d+1|0;b=b+1|0;c=c-1|0;if(c){continue}break}}return a}function Pb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;d=F[a+8>>2];c=F[a+4>>2];if(d-c>>2>>>0>=b>>>0){if(b){b=b<<2;c=ma(c,0,b)+b|0}F[a+4>>2]=c;return}a:{b:{c:{g=F[a>>2];f=c-g>>2;e=f+b|0;if(e>>>0<1073741824){d=d-g|0;h=d>>>1|0;e=d>>>0>=2147483644?1073741823:e>>>0<h>>>0?h:e;if(e){if(e>>>0>=1073741824){break c}i=ka(e<<2)}d=(f<<2)+i|0;f=b<<2;b=ma(d,0,f);f=b+f|0;e=(e<<2)+i|0;if((c|0)==(g|0)){break b}while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;d=d-4|0;F[d>>2]=b;if((c|0)!=(g|0)){continue}break}F[a+8>>2]=e;b=F[a+4>>2];F[a+4>>2]=f;c=F[a>>2];F[a>>2]=d;if((b|0)==(c|0)){break a}while(1){b=b-4|0;a=F[b>>2];F[b>>2]=0;if(a){$[F[F[a>>2]+4>>2]](a)}if((b|0)!=(c|0)){continue}break}break a}na();v()}oa();v()}F[a+8>>2]=e;F[a+4>>2]=f;F[a>>2]=b}if(c){ja(c)}}function Yd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;e=F[b+8>>2];d=F[b+12>>2];g=d;d=F[b+20>>2];k=d;h=F[b+16>>2];c=h+4|0;d=c>>>0<4?d+1|0:d;i=c;a:{if(c>>>0>e>>>0&(d|0)>=(g|0)|(d|0)>(g|0)){break a}j=F[b>>2];c=j+h|0;f=G[c|0]|G[c+1|0]<<8|(G[c+2|0]<<16|G[c+3|0]<<24);F[b+16>>2]=i;F[b+20>>2]=d;c=e;e=k;d=h+8|0;e=d>>>0<8?e+1|0:e;if(c>>>0<d>>>0&(e|0)>=(g|0)|(e|0)>(g|0)){break a}c=i+j|0;c=G[c|0]|G[c+1|0]<<8|(G[c+2|0]<<16|G[c+3|0]<<24);F[b+16>>2]=d;F[b+20>>2]=e;if((c|0)<(f|0)){break a}F[a+16>>2]=c;F[a+12>>2]=f;d=(c>>31)-((f>>31)+(c>>>0<f>>>0)|0)|0;e=c-f|0;if(!d&e>>>0>2147483646|d){break a}d=e+1|0;F[a+20>>2]=d;e=d>>>1|0;F[a+24>>2]=e;F[a+28>>2]=0-e;if(!(d&1)){F[a+24>>2]=e-1}l=Aa(a+112|0,b)}return l|0}function Wc(a,b){var c=0,d=0,e=0,f=0;d=-1;e=-1;f=-1;a:{b:{if((b|0)==-1){break b}e=F[F[F[a+4>>2]+12>>2]+(b<<2)>>2];c=b+1|0;c=(c>>>0)%3|0?c:b-2|0;if((c|0)>=0){f=(c>>>0)/3|0;f=F[(F[F[a>>2]+96>>2]+L(f,12)|0)+(c-L(f,3)<<2)>>2]}c:{if((e|0)==-1){break c}c=((e>>>0)%3|0?-1:2)+e|0;if((c|0)<0){break c}d=(c>>>0)/3|0;d=F[(F[F[a>>2]+96>>2]+L(d,12)|0)+(c-L(d,3)<<2)>>2]}c=-1;if((d|0)!=(f|0)){break a}f=-1;d:{b=((b>>>0)%3|0?-1:2)+b|0;if((b|0)>=0){d=(b>>>0)/3|0;d=F[(F[F[a>>2]+96>>2]+L(d,12)|0)+(b-L(d,3)<<2)>>2];if((e|0)==-1){break b}break d}d=-1;if((e|0)!=-1){break d}break b}b=e+1|0;b=(b>>>0)%3|0?b:e-2|0;if((b|0)<0){break b}c=F[F[a>>2]+96>>2];a=(b>>>0)/3|0;f=F[(c+L(a,12)|0)+(b-L(a,3)<<2)>>2]}c=(d|0)!=(f|0)?-1:e}return c}function Fc(a,b){var c=0,d=0,e=0;c=Z+-64|0;Z=c;d=F[a>>2];e=F[d-4>>2];d=F[d-8>>2];F[c+32>>2]=0;F[c+36>>2]=0;F[c+40>>2]=0;F[c+44>>2]=0;F[c+48>>2]=0;F[c+52>>2]=0;D[c+55|0]=0;D[c+56|0]=0;D[c+57|0]=0;D[c+58|0]=0;D[c+59|0]=0;D[c+60|0]=0;D[c+61|0]=0;D[c+62|0]=0;F[c+24>>2]=0;F[c+28>>2]=0;F[c+20>>2]=0;F[c+16>>2]=11020;F[c+12>>2]=a;F[c+8>>2]=b;a=a+d|0;d=0;a:{if(La(e,b,0)){F[c+56>>2]=1;$[F[F[e>>2]+20>>2]](e,c+8|0,a,a,1,0);d=F[c+32>>2]==1?a:0;break a}$[F[F[e>>2]+24>>2]](e,c+8|0,a,1,0);b:{switch(F[c+44>>2]){case 0:d=F[c+48>>2]==1?F[c+36>>2]==1?F[c+40>>2]==1?F[c+28>>2]:0:0:0;break a;case 1:break b;default:break a}}if(F[c+32>>2]!=1){if(F[c+48>>2]|F[c+36>>2]!=1|F[c+40>>2]!=1){break a}}d=F[c+24>>2]}Z=c- -64|0;return d}function ma(a,b,c){var d=0,e=0,f=0,g=0;a:{if(!c){break a}D[a|0]=b;d=a+c|0;D[d-1|0]=b;if(c>>>0<3){break a}D[a+2|0]=b;D[a+1|0]=b;D[d-3|0]=b;D[d-2|0]=b;if(c>>>0<7){break a}D[a+3|0]=b;D[d-4|0]=b;if(c>>>0<9){break a}d=0-a&3;e=d+a|0;b=L(b&255,16843009);F[e>>2]=b;d=c-d&-4;c=d+e|0;F[c-4>>2]=b;if(d>>>0<9){break a}F[e+8>>2]=b;F[e+4>>2]=b;F[c-8>>2]=b;F[c-12>>2]=b;if(d>>>0<25){break a}F[e+24>>2]=b;F[e+20>>2]=b;F[e+16>>2]=b;F[e+12>>2]=b;F[c-16>>2]=b;F[c-20>>2]=b;F[c-24>>2]=b;F[c-28>>2]=b;g=e&4|24;c=d-g|0;if(c>>>0<32){break a}d=ki(b,0,1,1);f=_;b=e+g|0;while(1){F[b+24>>2]=d;F[b+28>>2]=f;F[b+16>>2]=d;F[b+20>>2]=f;F[b+8>>2]=d;F[b+12>>2]=f;F[b>>2]=d;F[b+4>>2]=f;b=b+32|0;c=c-32|0;if(c>>>0>31){continue}break}}return a}function ie(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;d=F[b+8>>2];e=F[b+12>>2];g=e;e=F[b+20>>2];k=e;h=F[b+16>>2];c=h+4|0;e=c>>>0<4?e+1|0:e;i=c;a:{if(c>>>0>d>>>0&(e|0)>=(g|0)|(e|0)>(g|0)){break a}j=F[b>>2];c=j+h|0;f=G[c|0]|G[c+1|0]<<8|(G[c+2|0]<<16|G[c+3|0]<<24);F[b+16>>2]=i;F[b+20>>2]=e;c=d;d=k;e=h+8|0;d=e>>>0<8?d+1|0:d;if(c>>>0<e>>>0&(d|0)>=(g|0)|(d|0)>(g|0)){break a}c=i+j|0;c=G[c|0]|G[c+1|0]<<8|(G[c+2|0]<<16|G[c+3|0]<<24);F[b+16>>2]=e;F[b+20>>2]=d;if((c|0)<(f|0)){break a}F[a+16>>2]=c;F[a+12>>2]=f;d=(c>>31)-((f>>31)+(c>>>0<f>>>0)|0)|0;b=c-f|0;if(!d&b>>>0>2147483646|d){break a}l=1;d=b+1|0;F[a+20>>2]=d;b=d>>>1|0;F[a+24>>2]=b;F[a+28>>2]=0-b;if(d&1){break a}F[a+24>>2]=b-1}return l|0}function Uc(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;d=Z-16|0;Z=d;f=F[a+24>>2];k=F[a+28>>2];a:{if((f|0)!=(k|0)){while(1){F[d+8>>2]=0;F[d>>2]=0;F[d+4>>2]=0;a=Sc(F[f>>2],b,d);g=G[d+11|0];h=g<<24>>24;i=3;b:{c:{d:{if(!a){break d}i=0;a=G[c+11|0];e=a<<24>>24;j=(h|0)<0?F[d+4>>2]:g;if((j|0)!=(((e|0)<0?F[c+4>>2]:a)|0)){break d}a=(e|0)<0?F[c>>2]:c;e=(h|0)<0;e:{if(!e){e=d;if(!h){break e}while(1){if(G[e|0]!=G[a|0]){break d}a=a+1|0;e=e+1|0;g=g-1|0;if(g){continue}break}break e}if(!j){break e}if(sa(e?F[d>>2]:d,a,j)){break c}}l=F[f>>2];i=1}if((h|0)>=0){break b}}ja(F[d>>2])}f:{switch(i|0){case 0:case 3:break f;default:break a}}f=f+4|0;if((k|0)!=(f|0)){continue}break}}l=0}Z=d+16|0;return l}function gb(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0;f=c-b|0;h=f>>2;d=F[a+8>>2];e=F[a>>2];if(h>>>0<=d-e>>2>>>0){d=F[a+4>>2];g=d-e|0;f=g+b|0;i=g>>2;g=i>>>0<h>>>0?f:c;if((g|0)!=(b|0)){while(1){F[e>>2]=F[b>>2];e=e+4|0;b=b+4|0;if((g|0)!=(b|0)){continue}break}}if(h>>>0>i>>>0){if((c|0)!=(g|0)){while(1){F[d>>2]=F[f>>2];d=d+4|0;f=f+4|0;if((f|0)!=(c|0)){continue}break}}F[a+4>>2]=d;return}F[a+4>>2]=e;return}if(e){F[a+4>>2]=e;ja(e);F[a+8>>2]=0;F[a>>2]=0;F[a+4>>2]=0;d=0}a:{if((f|0)<0){break a}e=d>>>1|0;d=d>>>0>=2147483644?1073741823:e>>>0>h>>>0?e:h;if(d>>>0>=1073741824){break a}e=d<<2;d=ka(e);F[a>>2]=d;F[a+8>>2]=d+e;if((b|0)!=(c|0)){c=b;b=(f-4&-4)+4|0;d=la(d,c,b)+b|0}F[a+4>>2]=d;return}na();v()}function Ea(a,b,c){var d=0,e=0,f=0;e=Z-16|0;Z=e;F[a+4>>2]=0;a:{b:{if(!b){break b}f=F[a+8>>2];d=f<<5;c:{if(d>>>0>=b>>>0){F[a+4>>2]=b;break c}F[e+8>>2]=0;F[e>>2]=0;F[e+4>>2]=0;if((b|0)<0){break a}if(d>>>0<=1073741822){f=f<<6;d=b+31&-32;d=d>>>0<f>>>0?f:d}else{d=2147483647}$a(e,d);f=F[a>>2];F[a>>2]=F[e>>2];F[e>>2]=f;d=F[a+4>>2];F[a+4>>2]=b;F[e+4>>2]=d;d=F[a+8>>2];F[a+8>>2]=F[e+8>>2];F[e+8>>2]=d;if(!f){break c}ja(f)}d=b>>>5|0;a=F[a>>2];if(G[c|0]){if(b>>>0>=32){ma(a,255,d<<2)}if((b&-32)==(b|0)){break b}a=a+(d<<2)|0;F[a>>2]=F[a>>2]|-1>>>32-(b&31);break b}if(b>>>0>=32){ma(a,0,d<<2)}if((b&-32)==(b|0)){break b}a=a+(d<<2)|0;F[a>>2]=F[a>>2]&(-1>>>32-(b&31)^-1)}Z=e+16|0;return}na();v()}function If(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=Z-32|0;Z=e;a:{b:{f=ya(c);if(f>>>0<2147483632){c:{d:{if(f>>>0>=11){a=(f|15)+1|0;g=ka(a);F[e+24>>2]=a|-2147483648;F[e+16>>2]=g;F[e+20>>2]=f;a=f+g|0;break d}D[e+27|0]=f;g=e+16|0;a=f+g|0;if(!f){break c}}la(g,c,f)}D[a|0]=0;c=ya(d);if(c>>>0>=2147483632){break b}e:{f:{if(c>>>0>=11){f=(c|15)+1|0;a=ka(f);F[e+8>>2]=f|-2147483648;F[e>>2]=a;F[e+4>>2]=c;g=a+c|0;break f}D[e+11|0]=c;g=c+e|0;a=e;if(!c){break e}}la(a,d,c)}D[g|0]=0;c=F[b+4>>2];a=-1;g:{if(!c){break g}c=Uc(c,e+16|0,e);a=-1;if(!c){break g}a=Pc(b,F[c+24>>2])}if(D[e+11|0]<0){ja(F[e>>2])}if(D[e+27|0]<0){ja(F[e+16>>2])}Z=e+32|0;break a}za();v()}za();v()}return a|0}function se(a,b){a=a|0;b=b|0;a=0;a:{switch(b|0){case 0:a=ka(20);F[a+12>>2]=-1;F[a+16>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a>>2]=1920;return a|0;case 1:a=ka(24);F[a+12>>2]=-1;F[a+16>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a>>2]=1920;F[a+20>>2]=0;F[a>>2]=2136;return a|0;case 2:a=ka(48);F[a+12>>2]=-1;F[a+16>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a>>2]=1920;F[a+20>>2]=0;F[a>>2]=2136;F[a+24>>2]=1624;F[a>>2]=7948;F[a+32>>2]=0;F[a+36>>2]=0;F[a+28>>2]=-1;F[a+40>>2]=0;F[a+44>>2]=0;return a|0;case 3:a=ka(32);F[a+12>>2]=-1;F[a+16>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a>>2]=1920;F[a+20>>2]=0;F[a>>2]=2136;F[a+24>>2]=1032;F[a>>2]=5812;F[a+28>>2]=-1;break;default:break a}}return a|0}function Be(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;f=F[b>>2];b=F[b+4>>2];d=F[F[a+8>>2]+40>>2];j=d;m=ka((d|0)<0?-1:d);i=b-f|0;e=1;a:{if((i|0)<4){break a}b=0;g=F[c+16>>2];k=d;f=g+d|0;d=0+F[c+20>>2]|0;d=f>>>0<k>>>0?d+1|0:d;h=F[c+12>>2];e=0;if(I[c+8>>2]<f>>>0&(d|0)>=(h|0)|(d|0)>(h|0)){break a}e=i>>2;i=(e|0)<=1?1:e;while(1){b:{g=la(m,F[c>>2]+g|0,j);F[c+16>>2]=f;F[c+20>>2]=d;la(F[F[F[a+8>>2]+64>>2]>>2]+b|0,g,j);l=l+1|0;if((i|0)==(l|0)){break b}b=b+j|0;d=n+F[c+20>>2]|0;g=F[c+16>>2];f=k+g|0;d=f>>>0<k>>>0?d+1|0:d;h=F[c+12>>2];if((d|0)<=(h|0)&I[c+8>>2]>=f>>>0|(d|0)<(h|0)){continue}}break}e=(e|0)<=(l|0)}ja(m);return e|0}function mh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;F[b>>2]=1;f=b+8|0;c=F[b+8>>2];d=F[b+12>>2]-c|0;if(d>>>0<=4294967291){Db(f,d+4|0);c=F[f>>2]}c=c+d|0;d=F[a+4>>2];D[c|0]=d;D[c+1|0]=d>>>8;D[c+2|0]=d>>>16;D[c+3|0]=d>>>24;c=F[a+8>>2];if((c|0)!=F[a+12>>2]){d=0;while(1){g=(d<<2)+c|0;c=F[b+8>>2];e=F[b+12>>2]-c|0;if(e>>>0<=4294967291){Db(f,e+4|0);c=F[f>>2]}c=c+e|0;e=F[g>>2];D[c|0]=e;D[c+1|0]=e>>>8;D[c+2|0]=e>>>16;D[c+3|0]=e>>>24;d=d+1|0;c=F[a+8>>2];if(d>>>0<F[a+12>>2]-c>>2>>>0){continue}break}}c=F[b+12>>2];b=F[b+8>>2];c=c-b|0;if(c>>>0<=4294967291){Db(f,c+4|0);b=F[f>>2]}b=b+c|0;a=F[a+20>>2];D[b|0]=a;D[b+1|0]=a>>>8;D[b+2|0]=a>>>16;D[b+3|0]=a>>>24}function mb(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;c=F[a+4>>2];if((c|0)!=F[a+8>>2]){e=F[b+4>>2];F[c>>2]=F[b>>2];F[c+4>>2]=e;F[c+8>>2]=F[b+8>>2];F[a+4>>2]=c+12;return}a:{g=F[a>>2];d=(c-g|0)/12|0;e=d+1|0;if(e>>>0<357913942){f=d<<1;f=d>>>0>=178956970?357913941:e>>>0<f>>>0?f:e;if(f){if(f>>>0>=357913942){break a}e=ka(L(f,12))}else{e=0}d=e+L(d,12)|0;h=F[b+4>>2];F[d>>2]=F[b>>2];F[d+4>>2]=h;F[d+8>>2]=F[b+8>>2];b=d+12|0;if((c|0)!=(g|0)){while(1){c=c-12|0;h=F[c+4>>2];d=d-12|0;F[d>>2]=F[c>>2];F[d+4>>2]=h;F[d+8>>2]=F[c+8>>2];if((c|0)!=(g|0)){continue}break}c=F[a>>2]}F[a+8>>2]=e+L(f,12);F[a+4>>2]=b;F[a>>2]=d;if(c){ja(c)}return}na();v()}oa();v()}function ne(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;h=F[c+12>>2];f=h;e=F[c+20>>2];i=F[c+8>>2];g=F[c+16>>2];a:{if((f|0)<=(e|0)&i>>>0<=g>>>0|(e|0)>(f|0)){break a}j=F[c>>2];k=D[j+g|0];d=e;f=g+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;b:{if((k|0)==-2){break b}if((d|0)>=(h|0)&f>>>0>=i>>>0|(d|0)>(h|0)){break a}d=D[f+j|0];g=g+2|0;e=g>>>0<2?e+1|0:e;F[c+16>>2]=g;F[c+20>>2]=e;if((d-4&255)>>>0<251){break a}e=$[F[F[a>>2]+40>>2]](a,k,d)|0;d=F[a+20>>2];F[a+20>>2]=e;if(!d){break b}$[F[F[d>>2]+4>>2]](d)}d=F[a+20>>2];if(d){if(!($[F[F[a>>2]+28>>2]](a,d)|0)){break a}}l=$[F[F[a>>2]+36>>2]](a,b,c)|0}return l|0}function Bf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0;a:{if(I[b+80>>2]>65535){break a}a=F[b+100>>2];b=F[b+96>>2];e=(a-b|0)/12|0;f=L(e,6);g=(f|0)==(c|0);if((a|0)==(b|0)|(c|0)!=(f|0)){break a}g=1;c=e>>>0<=1?1:e;i=c&1;a=0;if(e>>>0>=2){j=c&-2;c=0;while(1){f=L(a,6);h=f+d|0;e=b+L(a,12)|0;E[h>>1]=F[e>>2];E[(f|2)+d>>1]=F[e+4>>2];E[h+4>>1]=F[e+8>>2];f=a|1;e=L(f,6)+d|0;f=b+L(f,12)|0;E[e>>1]=F[f>>2];E[e+2>>1]=F[f+4>>2];E[e+4>>1]=F[f+8>>2];a=a+2|0;c=c+2|0;if((j|0)!=(c|0)){continue}break}}if(!i){break a}c=L(a,6)+d|0;a=b+L(a,12)|0;E[c>>1]=F[a>>2];E[c+2>>1]=F[a+4>>2];E[c+4>>1]=F[a+8>>2]}return g|0}function Gh(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;f=Z-32|0;Z=f;h=e>>>0>1073741823?-1:e<<2;h=ma(ka(h),0,h);g=F[b>>2];i=F[b+4>>2];k=F[h+4>>2];F[f+16>>2]=F[h>>2];F[f+20>>2]=k;F[f+8>>2]=g;F[f+12>>2]=i;i=a+8|0;Jb(f+24|0,i,f+16|0,f+8|0);F[c>>2]=F[f+24>>2];F[c+4>>2]=F[f+28>>2];if((d|0)>(e|0)){k=0-e<<2;a=e;while(1){g=a<<2;j=g+b|0;m=F[j>>2];j=F[j+4>>2];g=c+g|0;l=g+k|0;n=F[l+4>>2];F[f+16>>2]=F[l>>2];F[f+20>>2]=n;F[f+8>>2]=m;F[f+12>>2]=j;Jb(f+24|0,i,f+16|0,f+8|0);F[g>>2]=F[f+24>>2];F[g+4>>2]=F[f+28>>2];a=a+e|0;if((d|0)>(a|0)){continue}break}}ja(h);Z=f+32|0;return 1}function Sf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;a=Z-32|0;Z=a;F[a+24>>2]=0;F[a+28>>2]=0;a:{d=ya(c);if(d>>>0<2147483632){b:{c:{if(d>>>0>=11){e=(d|15)+1|0;f=ka(e);F[a+16>>2]=e|-2147483648;F[a+8>>2]=f;F[a+12>>2]=d;e=d+f|0;break c}D[a+19|0]=d;f=a+8|0;e=f+d|0;if(!d){break b}}la(f,c,d)}D[e|0]=0;c=b+4|0;b=Ya(b,a+8|0);d:{if((c|0)==(b|0)){break d}c=F[b+32>>2];b=F[b+28>>2];if((c-b|0)!=8){break d}c=G[b+4|0]|G[b+5|0]<<8|(G[b+6|0]<<16|G[b+7|0]<<24);F[a+24>>2]=G[b|0]|G[b+1|0]<<8|(G[b+2|0]<<16|G[b+3|0]<<24);F[a+28>>2]=c}g=K[a+24>>3];if(D[a+19|0]<0){ja(F[a+8>>2])}Z=a+32|0;break a}za();v()}return+g}function Gc(a,b,c,d,e,f,g){var h=0,i=0,j=0;h=Z-16|0;Z=h;if((b^-1)+2147483631>>>0>=c>>>0){if(G[a+11|0]>>>7|0){i=F[a>>2]}else{i=a}if(b>>>0<1073741799){F[h+12>>2]=b<<1;F[h>>2]=b+c;c=Z-16|0;Z=c;Z=c+16|0;c=h+12|0;c=F[(I[h>>2]<I[c>>2]?c:h)>>2];if(c>>>0>=11){j=c+16&-16;c=j-1|0;c=(c|0)==11?j:c}else{c=10}c=c+1|0}else{c=2147483631}sb(h,c);c=F[h>>2];if(f){db(c,g,f)}g=d-e|0;if((d|0)!=(e|0)){db(c+f|0,e+i|0,g)}if((b|0)!=10){ja(i)}F[a>>2]=c;F[a+8>>2]=F[a+8>>2]&-2147483648|F[h+4>>2]&2147483647;F[a+8>>2]=F[a+8>>2]|-2147483648;b=a;a=f+g|0;F[b+4>>2]=a;D[h+12|0]=0;D[a+c|0]=G[h+12|0];Z=h+16|0;return}za();v()}function _c(a,b,c){var d=0,e=0,f=0,g=0;a:{f=b>>>0<1431655766&(b|c)>=0;b:{if(!f){break b}b=L(b,3);Xb(a,b,10224);Xb(a+12|0,b,10228);d=F[a+24>>2];c:{if(F[a+32>>2]-d>>2>>>0>=c>>>0){break c}if(c>>>0>=1073741824){break a}b=F[a+28>>2];e=c<<2;c=ka(e);e=c+e|0;g=c+(b-d&-4)|0;c=g;if((b|0)!=(d|0)){while(1){c=c-4|0;b=b-4|0;F[c>>2]=F[b>>2];if((b|0)!=(d|0)){continue}break}}F[a+32>>2]=e;F[a+28>>2]=g;F[a+24>>2]=c;if(!d){break c}ja(d)}F[a+80>>2]=0;F[a+84>>2]=0;b=F[a+76>>2];F[a+76>>2]=0;if(b){ja(b)}F[a+68>>2]=0;F[a+72>>2]=0;b=a- -64|0;a=F[b>>2];F[b>>2]=0;if(!a){break b}ja(a)}return f}na();v()}function yd(a){var b=0,c=0,d=0,e=0,f=0;f=1;c=F[a+140>>2];a:{if((c|0)<=0){break a}b=c<<4;d=ka(c>>>0>268435455?-1:b|4);F[d>>2]=c;d=d+4|0;c=d+b|0;b=d;while(1){F[b>>2]=0;F[b+4>>2]=0;D[b+5|0]=0;D[b+6|0]=0;D[b+7|0]=0;D[b+8|0]=0;D[b+9|0]=0;D[b+10|0]=0;D[b+11|0]=0;D[b+12|0]=0;b=b+16|0;if((c|0)!=(b|0)){continue}break}e=F[a+136>>2];F[a+136>>2]=d;if(e){c=e-4|0;d=F[c>>2];if(d){b=(d<<4)+e|0;while(1){b=b-16|0;if((e|0)!=(b|0)){continue}break}}ja(c)}b=0;if(F[a+140>>2]<=0){break a}while(1){f=Aa(F[a+136>>2]+(b<<4)|0,a);if(!f){break a}b=b+1|0;if((b|0)<F[a+140>>2]){continue}break}}return f}
function Sd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;d=F[b+8>>2];c=F[b+12>>2];g=c;c=F[b+20>>2];i=c;h=F[b+16>>2];f=h+4|0;c=f>>>0<4?c+1|0:c;a:{if(d>>>0<f>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){break a}e=h+F[b>>2]|0;e=G[e|0]|G[e+1|0]<<8|(G[e+2|0]<<16|G[e+3|0]<<24);F[b+16>>2]=f;F[b+20>>2]=c;f=d;d=i;c=h+8|0;d=c>>>0<8?d+1|0:d;if(c>>>0>f>>>0&(d|0)>=(g|0)|(d|0)>(g|0)){break a}F[b+16>>2]=c;F[b+20>>2]=d;if(!(e&1)){break a}d=O(e)^31;if(d-1>>>0>28){break a}F[a+8>>2]=d+1;d=-2<<d;c=d^-2;F[a+16>>2]=c;F[a+12>>2]=d^-1;F[a+24>>2]=c>>1;J[a+20>>2]=M(2)/M(c|0);j=Aa(a+96|0,b)}return j|0}function bc(a,b){var c=0;c=F[b+4>>2];F[a>>2]=F[b>>2];F[a+4>>2]=c;c=F[b+60>>2];F[a+56>>2]=F[b+56>>2];F[a+60>>2]=c;c=F[b+52>>2];F[a+48>>2]=F[b+48>>2];F[a+52>>2]=c;c=F[b+44>>2];F[a+40>>2]=F[b+40>>2];F[a+44>>2]=c;c=F[b+36>>2];F[a+32>>2]=F[b+32>>2];F[a+36>>2]=c;c=F[b+28>>2];F[a+24>>2]=F[b+24>>2];F[a+28>>2]=c;c=F[b+20>>2];F[a+16>>2]=F[b+16>>2];F[a+20>>2]=c;c=F[b+12>>2];F[a+8>>2]=F[b+8>>2];F[a+12>>2]=c;F[a+88>>2]=0;F[a+64>>2]=0;F[a+68>>2]=0;F[a+72>>2]=0;F[a+76>>2]=0;D[a+77|0]=0;D[a+78|0]=0;D[a+79|0]=0;D[a+80|0]=0;D[a+81|0]=0;D[a+82|0]=0;D[a+83|0]=0;D[a+84|0]=0;return a}function ac(a,b){var c=0,d=0,e=0,f=0,g=0;a:{if(F[a+64>>2]){break a}c=ka(32);F[c+16>>2]=0;F[c+20>>2]=0;F[c+8>>2]=0;F[c>>2]=0;F[c+4>>2]=0;F[c+24>>2]=0;F[c+28>>2]=0;d=F[a+64>>2];F[a+64>>2]=c;if(!d){break a}c=F[d>>2];if(c){F[d+4>>2]=c;ja(c)}ja(d)}d=F[a+64>>2];c=F[a+28>>2]-1|0;if(c>>>0<=10){c=F[(c<<2)+10148>>2]}else{c=-1}c=L(c,G[a+24|0]);f=c>>31;g=md(d,0,ki(c,f,b,0),_);if(g){d=F[a+64>>2];F[a>>2]=d;e=F[d+20>>2];F[a+8>>2]=F[d+16>>2];F[a+12>>2]=e;e=F[d+24>>2];d=F[d+28>>2];F[a+48>>2]=0;F[a+52>>2]=0;F[a+40>>2]=c;F[a+44>>2]=f;F[a+16>>2]=e;F[a+20>>2]=d;F[a+80>>2]=b}return g}function Af(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0,i=0,j=0,k=0;a=F[b+100>>2];b=F[b+96>>2];h=a-b|0;a:{if((h|0)!=(c|0)|(a|0)==(b|0)){break a}g=(c|0)/12|0;e=g>>>0<=1?1:g;j=e&1;a=0;if(g>>>0>=2){k=e&-2;g=0;while(1){e=L(a,12);i=e+d|0;f=b+e|0;F[i>>2]=F[f>>2];F[(e|4)+d>>2]=F[f+4>>2];F[i+8>>2]=F[f+8>>2];f=L(a|1,12);e=f+d|0;f=b+f|0;F[e>>2]=F[f>>2];F[e+4>>2]=F[f+4>>2];F[e+8>>2]=F[f+8>>2];a=a+2|0;g=g+2|0;if((k|0)!=(g|0)){continue}break}}if(!j){break a}e=d;d=L(a,12);a=e+d|0;b=b+d|0;F[a>>2]=F[b>>2];F[a+4>>2]=F[b+4>>2];F[a+8>>2]=F[b+8>>2]}return(c|0)==(h|0)|0}function Kh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;c=F[b+8>>2];d=F[b+12>>2];g=d;d=F[b+20>>2];i=d;h=F[b+16>>2];f=h+4|0;d=f>>>0<4?d+1|0:d;a:{if(c>>>0<f>>>0&(d|0)>=(g|0)|(d|0)>(g|0)){break a}e=h+F[b>>2]|0;e=G[e|0]|G[e+1|0]<<8|(G[e+2|0]<<16|G[e+3|0]<<24);F[b+16>>2]=f;F[b+20>>2]=d;f=c;c=i;d=h+8|0;c=d>>>0<8?c+1|0:c;if(d>>>0>f>>>0&(c|0)>=(g|0)|(c|0)>(g|0)){break a}F[b+16>>2]=d;F[b+20>>2]=c;if(!(e&1)){break a}b=O(e)^31;if(b-1>>>0>28){break a}j=1;F[a+8>>2]=b+1;b=-2<<b;c=b^-2;F[a+16>>2]=c;F[a+12>>2]=b^-1;F[a+24>>2]=c>>1;J[a+20>>2]=M(2)/M(c|0)}return j|0}function Ya(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;f=a+4|0;a=F[a+4>>2];a:{b:{if(!a){break b}d=G[b+11|0];c=d<<24>>24<0;g=c?F[b>>2]:b;d=c?F[b+4>>2]:d;b=f;while(1){e=G[a+27|0];c=e<<24>>24<0;e=c?F[a+20>>2]:e;h=e>>>0>d>>>0;i=h?d:e;c:{if(i){c=sa(c?F[a+16>>2]:a+16|0,g,i);if(c){break c}}c=d>>>0>e>>>0?-1:h}c=(c|0)<0;b=c?b:a;a=F[(c?a+4|0:a)>>2];if(a){continue}break}if((b|0)==(f|0)){break b}c=G[b+27|0];a=c<<24>>24<0;d:{c=a?F[b+20>>2]:c;e=c>>>0<d>>>0?c:d;if(e){a=sa(g,a?F[b+16>>2]:b+16|0,e);if(a){break d}}if(c>>>0>d>>>0){break b}break a}if((a|0)>=0){break a}}b=f}return b}function Oe(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(La(a,F[b+8>>2],e)){if(!(F[b+28>>2]==1|F[b+4>>2]!=(c|0))){F[b+28>>2]=d}return}a:{if(La(a,F[b>>2],e)){if(!(F[b+16>>2]!=(c|0)&F[b+20>>2]!=(c|0))){if((d|0)!=1){break a}F[b+32>>2]=1;return}F[b+32>>2]=d;b:{if(F[b+44>>2]==4){break b}E[b+52>>1]=0;a=F[a+8>>2];$[F[F[a>>2]+20>>2]](a,b,c,c,1,e);if(G[b+53|0]){F[b+44>>2]=3;if(!G[b+52|0]){break b}break a}F[b+44>>2]=4}F[b+20>>2]=c;F[b+40>>2]=F[b+40>>2]+1;if(F[b+36>>2]!=1|F[b+24>>2]!=2){break a}D[b+54|0]=1;return}a=F[a+8>>2];$[F[F[a>>2]+24>>2]](a,b,c,d,e)}}function Ig(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0;f=ka(64);c=ka(12);F[c+8>>2]=F[F[a+4>>2]+80>>2];F[c>>2]=9968;F[c+4>>2]=0;f=yc(f,c);a:{b:{if((b|0)<0){c=f;break b}h=a+8|0;c=F[a+12>>2];e=F[a+8>>2];g=c-e>>2;c:{if((g|0)>(b|0)){break c}d=b+1|0;if(b>>>0>=g>>>0){Pb(h,d-g|0);break c}if(d>>>0>=g>>>0){break c}e=e+(d<<2)|0;if((e|0)!=(c|0)){while(1){c=c-4|0;d=F[c>>2];F[c>>2]=0;if(d){$[F[F[d>>2]+4>>2]](d)}if((c|0)!=(e|0)){continue}break}}F[a+12>>2]=e}a=F[h>>2]+(b<<2)|0;c=F[a>>2];F[a>>2]=f;if(!c){break a}}$[F[F[c>>2]+4>>2]](c)}return(b^-1)>>>31|0}function we(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;c=F[a+60>>2];a:{if(!c){break a}F[c+4>>2]=a+48;if(!($[F[F[c>>2]+12>>2]](c)|0)){break a}b:{c=$[F[F[a>>2]+24>>2]](a)|0;if((c|0)<=0){break b}while(1){c:{f=F[($[F[F[a>>2]+28>>2]](a)|0)+4>>2];g=$[F[F[a>>2]+20>>2]](a,d)|0;e=F[a+60>>2];if(!($[F[F[e>>2]+8>>2]](e,F[F[f+8>>2]+(g<<2)>>2])|0)){break c}d=d+1|0;if((c|0)!=(d|0)){continue}break b}break}return 0}d=0;if(!($[F[F[a>>2]+36>>2]](a,b)|0)){break a}if(!($[F[F[a>>2]+40>>2]](a,b)|0)){break a}d=$[F[F[a>>2]+44>>2]](a)|0}return d|0}function Id(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;c=F[a+216>>2];if((c|0)!=F[a+220>>2]){while(1){a:{c=F[L(e,144)+c>>2];if((c|0)<0){break a}d=F[a+4>>2];f=F[d+8>>2];if((c|0)>=F[d+12>>2]-f>>2){break a}d=0;c=F[(c<<2)+f>>2];if(($[F[F[c>>2]+24>>2]](c)|0)<=0){break a}while(1){if(($[F[F[c>>2]+20>>2]](c,d)|0)!=(b|0)){d=d+1|0;if(($[F[F[c>>2]+24>>2]](c)|0)>(d|0)){continue}break a}break}a=F[a+216>>2]+L(e,144)|0;return(G[a+100|0]?a+4|0:0)|0}e=e+1|0;c=F[a+216>>2];if(e>>>0<(F[a+220>>2]-c|0)/144>>>0){continue}break}}return 0}function nd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;c=F[a+8>>2];d=F[a+4>>2];if(c-d>>2>>>0>=b>>>0){if(b){b=b<<2;d=ma(d,0,b)+b|0}F[a+4>>2]=d;return}a:{f=F[a>>2];g=d-f>>2;e=g+b|0;if(e>>>0<1073741824){c=c-f|0;h=c>>>1|0;e=c>>>0>=2147483644?1073741823:e>>>0<h>>>0?h:e;if(e){if(e>>>0>=1073741824){break a}i=ka(e<<2)}c=(g<<2)+i|0;b=b<<2;b=ma(c,0,b)+b|0;if((d|0)!=(f|0)){while(1){c=c-4|0;d=d-4|0;F[c>>2]=F[d>>2];if((d|0)!=(f|0)){continue}break}}F[a+8>>2]=(e<<2)+i;F[a+4>>2]=b;F[a>>2]=c;if(f){ja(f)}return}na();v()}oa();v()}function bb(a){var b=0,c=0,d=0,e=0,f=0;d=F[a+8>>2];a:{if(G[d+84|0]){break a}b=F[a+16>>2];if(!b|!G[b+84|0]){break a}c=F[d+72>>2];e=F[d+68>>2];D[b+84|0]=0;c=c-e>>2;f=F[b+68>>2];e=F[b+72>>2]-f>>2;b:{if(c>>>0>e>>>0){ab(b+68|0,c-e|0,2004);d=F[a+8>>2];break b}if(c>>>0>=e>>>0){break b}F[b+72>>2]=f+(c<<2)}if(G[d+84|0]){break a}c=F[d+68>>2];if((c|0)==F[d+72>>2]){break a}e=F[F[a+16>>2]+68>>2];b=0;while(1){f=b<<2;F[f+e>>2]=F[c+f>>2];b=b+1|0;c=F[d+68>>2];if(b>>>0<F[d+72>>2]-c>>2>>>0){continue}break}}return F[a+16>>2]}function Lf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=Z+-64|0;Z=e;f=Ja(e+8|0);F[f+16>>2]=0;F[f+20>>2]=0;F[f>>2]=b;F[f+8>>2]=c;F[f+12>>2]=0;b=e+48|0;Pd(b,a,f,d);F[a+24>>2]=F[e+48>>2];f=a+24|0;a:{if((f|0)==(b|0)){break a}b=a+28|0;c=e+48|4;g=G[e+63|0];d=g<<24>>24;if(D[a+39|0]>=0){if((d|0)>=0){a=F[c+4>>2];F[b>>2]=F[c>>2];F[b+4>>2]=a;F[b+8>>2]=F[c+8>>2];break a}qb(b,F[e+52>>2],F[e+56>>2]);break a}a=(d|0)<0;rb(b,a?F[e+52>>2]:c,a?F[e+56>>2]:g)}if(D[e+63|0]<0){ja(F[e+52>>2])}Z=e- -64|0;return f|0}function Jf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0;a=Z-32|0;Z=a;a:{d=ya(c);if(d>>>0<2147483632){b:{c:{if(d>>>0>=11){e=(d|15)+1|0;f=ka(e);F[a+24>>2]=e|-2147483648;F[a+16>>2]=f;F[a+20>>2]=d;e=d+f|0;break c}D[a+27|0]=d;f=a+16|0;e=f+d|0;if(!d){break b}}la(f,c,d)}D[e|0]=0;D[a+4|0]=0;F[a>>2]=1701667182;D[a+11|0]=4;d=F[b+4>>2];c=-1;d:{if(!d){break d}d=Uc(d,a,a+16|0);c=-1;if(!d){break d}c=Pc(b,F[d+24>>2])}b=c;if(D[a+11|0]<0){ja(F[a>>2])}if(D[a+27|0]<0){ja(F[a+16>>2])}Z=a+32|0;break a}za();v()}return b|0}function Hd(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;c=F[a+216>>2];if((c|0)!=F[a+220>>2]){while(1){a:{c=F[L(e,144)+c>>2];if((c|0)<0){break a}d=F[a+4>>2];f=F[d+8>>2];if((c|0)>=F[d+12>>2]-f>>2){break a}d=0;c=F[(c<<2)+f>>2];if(($[F[F[c>>2]+24>>2]](c)|0)<=0){break a}while(1){if(($[F[F[c>>2]+20>>2]](c,d)|0)!=(b|0)){d=d+1|0;if(($[F[F[c>>2]+24>>2]](c)|0)>(d|0)){continue}break a}break}return(F[a+216>>2]+L(e,144)|0)+104|0}e=e+1|0;c=F[a+216>>2];if(e>>>0<(F[a+220>>2]-c|0)/144>>>0){continue}break}}return a+184|0}function Uf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0;d=Z-16|0;Z=d;F[d+12>>2]=0;a:{e=ya(c);if(e>>>0<2147483632){b:{c:{if(e>>>0>=11){f=(e|15)+1|0;a=ka(f);F[d+8>>2]=f|-2147483648;F[d>>2]=a;F[d+4>>2]=e;f=a+e|0;break c}D[d+11|0]=e;f=d+e|0;a=d;if(!e){break b}}la(a,c,e)}D[f|0]=0;a=Ya(b,d);d:{if((a|0)==(b+4|0)){break d}b=F[a+32>>2];a=F[a+28>>2];if((b-a|0)!=4){break d}F[d+12>>2]=G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24)}a=F[d+12>>2];if(D[d+11|0]<0){ja(F[d>>2])}Z=d+16|0;break a}za();v()}return a|0}function Mf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;d=Z+-64|0;Z=d;e=Ja(d+8|0);F[e+16>>2]=0;F[e+20>>2]=0;F[e>>2]=b;F[e+8>>2]=c;F[e+12>>2]=0;b=d+48|0;Od(b);F[a+24>>2]=F[d+48>>2];f=a+24|0;a:{if((b|0)==(f|0)){break a}b=a+28|0;c=d+48|4;g=G[d+63|0];e=g<<24>>24;if(D[a+39|0]>=0){if((e|0)>=0){a=F[c+4>>2];F[b>>2]=F[c>>2];F[b+4>>2]=a;F[b+8>>2]=F[c+8>>2];break a}qb(b,F[d+52>>2],F[d+56>>2]);break a}a=(e|0)<0;rb(b,a?F[d+52>>2]:c,a?F[d+56>>2]:g)}if(D[d+63|0]<0){ja(F[d+52>>2])}Z=d- -64|0;return f|0}function Ce(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0;e=1;a:{if(($[F[F[b>>2]+20>>2]](b)|0)<=0){break a}while(1){e=0;d=Qc(F[F[a+4>>2]+4>>2],$[F[F[b>>2]+24>>2]](b,f)|0);if((d|0)==-1){break a}g=F[a+4>>2];c=0;b:{if((d|0)<0){break b}h=F[g+4>>2];if((d|0)>=F[h+12>>2]-F[h+8>>2]>>2){break b}c=F[F[g+8>>2]+(F[F[g+20>>2]+(d<<2)>>2]<<2)>>2];c=$[F[F[c>>2]+32>>2]](c,d)|0}if(!c){break a}if(!($[F[F[b>>2]+28>>2]](b,c)|0)){break a}e=1;f=f+1|0;if(($[F[F[b>>2]+20>>2]](b)|0)>(f|0)){continue}break}}return e|0}function Db(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;a:{c=F[a+4>>2];e=F[a>>2];d=c-e|0;b:{if(d>>>0<b>>>0){g=b-d|0;f=F[a+8>>2];if(g>>>0<=f-c>>>0){h=a,i=ma(c,0,g)+g|0,F[h+4>>2]=i;break b}if((b|0)<0){break a}c=f-e|0;f=c<<1;c=c>>>0>=1073741823?2147483647:b>>>0<f>>>0?f:b;f=ka(c);ma(f+d|0,0,g);d=pa(f,e,d);F[a+8>>2]=d+c;F[a+4>>2]=b+d;F[a>>2]=d;if(!e){break b}ja(e);break b}if(b>>>0>=d>>>0){break b}F[a+4>>2]=b+e}b=F[a+28>>2];c=b;d=b+1|0;b=F[a+24>>2]+1|0;e=b?c:d;F[a+24>>2]=b;F[a+28>>2]=e;return}na();v()}function Ma(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;e=F[a+4>>2];if((e|0)!=F[a+8>>2]){F[e>>2]=F[b>>2];F[a+4>>2]=e+4;return}a:{g=F[a>>2];f=e-g|0;c=f>>2;d=c+1|0;if(d>>>0<1073741824){h=c<<2;c=f>>>1|0;c=f>>>0>=2147483644?1073741823:c>>>0>d>>>0?c:d;if(c){if(c>>>0>=1073741824){break a}f=ka(c<<2)}else{f=0}d=h+f|0;F[d>>2]=F[b>>2];b=d+4|0;if((e|0)!=(g|0)){while(1){d=d-4|0;e=e-4|0;F[d>>2]=F[e>>2];if((e|0)!=(g|0)){continue}break}}F[a+8>>2]=f+(c<<2);F[a+4>>2]=b;F[a>>2]=d;if(g){ja(g)}return}na();v()}oa();v()}function va(a){F[a>>2]=-1;F[a+4>>2]=0;F[a+8>>2]=0;F[a+32>>2]=0;F[a+36>>2]=0;D[a+28|0]=1;F[a+20>>2]=0;F[a+24>>2]=0;F[a+12>>2]=0;F[a+16>>2]=0;F[a+40>>2]=0;F[a+44>>2]=0;F[a+48>>2]=0;F[a+52>>2]=0;F[a+56>>2]=0;F[a+60>>2]=0;F[a+64>>2]=0;F[a+68>>2]=0;F[a+76>>2]=0;F[a+80>>2]=0;F[a+84>>2]=0;F[a+88>>2]=0;F[a+92>>2]=0;F[a+96>>2]=0;F[a+72>>2]=a+4;F[a+104>>2]=0;F[a+108>>2]=0;D[a+100|0]=1;F[a+112>>2]=0;F[a+116>>2]=0;F[a+120>>2]=0;F[a+124>>2]=0;F[a+128>>2]=0;F[a+132>>2]=0;F[a+136>>2]=0;F[a+140>>2]=0}function Hb(a,b){var c=0,d=0,e=0,f=0;d=F[a+12>>2];c=F[a+16>>2]-d>>2;a:{if(c>>>0<b>>>0){qa(a+12|0,b-c|0);break a}if(b>>>0>=c>>>0){break a}F[a+16>>2]=d+(b<<2)}b:{c=F[a>>2];c:{if(F[a+8>>2]-c>>2>>>0>=b>>>0){break c}if(b>>>0>=1073741824){break b}d=F[a+4>>2];e=b<<2;b=ka(e);e=b+e|0;f=b+(d-c&-4)|0;b=f;if((c|0)!=(d|0)){while(1){b=b-4|0;d=d-4|0;F[b>>2]=F[d>>2];if((c|0)!=(d|0)){continue}break}}F[a+8>>2]=e;F[a+4>>2]=f;F[a>>2]=b;if(!c){break c}ja(c)}return}na();v()}function tb(a){a=a|0;var b=0,c=0,d=0;F[a>>2]=10300;b=F[a+68>>2];if(b){F[a+72>>2]=b;ja(b)}b=F[a+56>>2];if(b){F[a+60>>2]=b;ja(b)}b=F[a+44>>2];if(b){F[a+48>>2]=b;ja(b)}b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}b=F[a+20>>2];if(b){F[a+24>>2]=b;ja(b)}b=F[a+8>>2];if(b){d=b;c=F[a+12>>2];if((b|0)!=(c|0)){while(1){c=c-4|0;d=F[c>>2];F[c>>2]=0;if(d){xa(d)}if((b|0)!=(c|0)){continue}break}d=F[a+8>>2]}F[a+12>>2]=b;ja(d)}b=F[a+4>>2];F[a+4>>2]=0;if(b){ic(b)}return a|0}function qa(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;e=F[a+8>>2];c=F[a+4>>2];if(e-c>>2>>>0>=b>>>0){if(b){b=b<<2;c=ma(c,0,b)+b|0}F[a+4>>2]=c;return}a:{f=c;c=F[a>>2];g=f-c|0;h=g>>2;d=h+b|0;if(d>>>0<1073741824){e=e-c|0;f=e>>>1|0;d=e>>>0>=2147483644?1073741823:d>>>0<f>>>0?f:d;if(d){if(d>>>0>=1073741824){break a}i=ka(d<<2)}b=b<<2;e=ma((h<<2)+i|0,0,b);f=d<<2;d=pa(i,c,g);F[a+8>>2]=f+d;F[a+4>>2]=b+e;F[a>>2]=d;if(c){ja(c)}return}na();v()}oa();v()}function gc(a,b){var c=0,d=0,e=0,f=0;c=a+4|0;a=Ya(a,b);a:{if((c|0)==(a|0)){break a}b=a+28|0;b=D[a+39|0]<0?F[b>>2]:b;while(1){a=b;b=a+1|0;c=D[a|0];if((c|0)==32|c-9>>>0<5){continue}break}b:{c:{d:{c=D[a|0];switch(c-43|0){case 0:break c;case 2:break d;default:break b}}e=1}c=D[b|0];a=b}if(c-48>>>0<10){while(1){d=(L(d,10)-D[a|0]|0)+48|0;b=D[a+1|0];a=a+1|0;if(b-48>>>0<10){continue}break}}a=e?d:0-d|0;if((a|0)==-1){break a}f=(a|0)!=0}return f}function Qa(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;a=F[a>>2];c=F[a+4>>2];e=F[a+8>>2];if(c>>>0<e>>>0){F[c>>2]=F[b>>2];F[a+4>>2]=c+4;return}a:{d=c;c=F[a>>2];g=d-c|0;d=g>>2;f=d+1|0;if(f>>>0<1073741824){h=d<<2;e=e-c|0;d=e>>>1|0;f=e>>>0>=2147483644?1073741823:f>>>0<d>>>0?d:f;if(f){if(f>>>0>=1073741824){break a}e=ka(f<<2)}else{e=0}d=h+e|0;F[d>>2]=F[b>>2];b=pa(e,c,g);F[a+8>>2]=b+(f<<2);F[a+4>>2]=d+4;F[a>>2]=b;if(c){ja(c)}return}na();v()}oa();v()}function db(a,b,c){var d=0,e=0,f=0,g=0,h=0;f=Z-16|0;Z=f;d=Z-32|0;Z=d;e=Z-16|0;Z=e;F[e+12>>2]=b;F[e+8>>2]=b+c;F[d+24>>2]=F[e+12>>2];F[d+28>>2]=F[e+8>>2];Z=e+16|0;c=Z-16|0;Z=c;h=F[d+28>>2];e=F[d+24>>2];g=h-e|0;if((e|0)!=(h|0)){pa(a,e,g)}F[c+12>>2]=e+g;F[c+8>>2]=a+g;F[d+16>>2]=F[c+12>>2];F[d+20>>2]=F[c+8>>2];Z=c+16|0;F[d+12>>2]=(F[d+16>>2]-b|0)+b;F[d+8>>2]=(F[d+20>>2]-a|0)+a;F[f+8>>2]=F[d+12>>2];F[f+12>>2]=F[d+8>>2];Z=d+32|0;Z=f+16|0}function _a(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;e=F[a+8>>2];c=F[a+4>>2];if(e-c>>3>>>0>=b>>>0){if(b){b=b<<3;c=ma(c,0,b)+b|0}F[a+4>>2]=c;return}a:{f=c;c=F[a>>2];g=f-c|0;h=g>>3;d=h+b|0;if(d>>>0<536870912){e=e-c|0;f=e>>>2|0;d=e>>>0>=2147483640?536870911:d>>>0<f>>>0?f:d;if(d){if(d>>>0>=536870912){break a}i=ka(d<<3)}b=b<<3;e=ma((h<<3)+i|0,0,b);f=d<<3;d=pa(i,c,g);F[a+8>>2]=f+d;F[a+4>>2]=b+e;F[a>>2]=d;if(c){ja(c)}return}na();v()}oa();v()}function re(a){a=a|0;var b=0,c=0,d=0;F[a>>2]=2016;b=F[a+60>>2];F[a+60>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}d=F[a+36>>2];if(d){c=F[a+40>>2];b=d;if((c|0)!=(b|0)){while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}if((c|0)!=(d|0)){continue}break}b=F[a+36>>2]}F[a+40>>2]=d;ja(b)}F[a>>2]=1776;b=F[a+16>>2];if(b){F[a+20>>2]=b;ja(b)}b=F[a+4>>2];if(b){F[a+8>>2]=b;ja(b)}return a|0}function qe(a){a=a|0;var b=0,c=0,d=0;F[a>>2]=2016;b=F[a+60>>2];F[a+60>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}d=F[a+36>>2];if(d){c=F[a+40>>2];b=d;if((c|0)!=(b|0)){while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}if((c|0)!=(d|0)){continue}break}b=F[a+36>>2]}F[a+40>>2]=d;ja(b)}F[a>>2]=1776;b=F[a+16>>2];if(b){F[a+20>>2]=b;ja(b)}b=F[a+4>>2];if(b){F[a+8>>2]=b;ja(b)}ja(a)}function Eg(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;a:{b=F[a+8>>2];b:{if((b|0)<0){break b}c=F[a+4>>2];e=F[c>>2];d=F[c+4>>2]-e>>2;c:{if(d>>>0<b>>>0){nd(c,b-d|0);f=F[a+8>>2];break c}f=b;if(b>>>0>=d>>>0){break c}F[c+4>>2]=e+(b<<2);f=b}d=f;if((d|0)<=0){break b}a=F[a+4>>2];c=F[a>>2];e=F[a+4>>2]-c>>2;a=0;while(1){if((a|0)==(e|0)){break a}F[c+(a<<2)>>2]=a;a=a+1|0;if((d|0)!=(a|0)){continue}break}}return(b^-1)>>>31|0}ta();v()}function fh(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;d=Z-16|0;Z=d;e=F[a+4>>2];a:{if((e|0)==-1){break a}c=F[b+20>>2];if(!!F[b+16>>2]&(c|0)>=0|(c|0)>0){break a}pb(b,F[b+4>>2],F[a+8>>2],F[a+12>>2]);c=F[b+20>>2];if(!!F[b+16>>2]&(c|0)>=0|(c|0)>0){break a}pb(b,F[b+4>>2],a+20|0,a+24|0);c=F[b+20>>2];f=F[b+16>>2];D[d+15|0]=F[a+4>>2];if(!!f&(c|0)>=0|(c|0)>0){break a}pb(b,F[b+4>>2],d+15|0,d+16|0)}Z=d+16|0;return(e|0)!=-1|0}function kd(a,b){var c=0,d=0,e=0,f=0,g=0,h=0;e=F[a+8>>2];c=F[a+4>>2];if(e-c>>1>>>0>=b>>>0){if(b){b=b<<1;c=ma(c,0,b)+b|0}F[a+4>>2]=c;return}a:{f=c;c=F[a>>2];g=f-c|0;f=g>>1;d=f+b|0;if((d|0)>=0){e=e-c|0;d=e>>>0>=2147483646?2147483647:d>>>0<e>>>0?e:d;if(d){if((d|0)<0){break a}h=ka(d<<1)}b=b<<1;e=ma((f<<1)+h|0,0,b);f=d<<1;d=pa(h,c,g);F[a+8>>2]=f+d;F[a+4>>2]=b+e;F[a>>2]=d;if(c){ja(c)}return}na();v()}oa();v()}function of(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;d=Z-16|0;Z=d;Pd(d,a,b,c);F[a+24>>2]=F[d>>2];e=a+24|0;a:{if((e|0)==(d|0)){break a}b=a+28|0;c=d|4;f=G[d+15|0];g=f<<24>>24;if(D[a+39|0]>=0){if((g|0)>=0){a=F[c+4>>2];F[b>>2]=F[c>>2];F[b+4>>2]=a;F[b+8>>2]=F[c+8>>2];break a}qb(b,F[d+4>>2],F[d+8>>2]);break a}a=(g|0)<0;rb(b,a?F[d+4>>2]:c,a?F[d+8>>2]:f)}if(D[d+15|0]<0){ja(F[d+4>>2])}Z=d+16|0;return e|0}function ra(a,b,c){var d=0,e=0,f=0,g=0;e=Z-16|0;Z=e;a:{b:{if(c>>>0<11){d=a;D[a+11|0]=G[a+11|0]&128|c;D[a+11|0]=G[a+11|0]&127;break b}if(c>>>0>2147483631){break a}g=e+8|0;if(c>>>0>=11){f=c+16&-16;d=f-1|0;d=(d|0)==11?f:d}else{d=10}sb(g,d+1|0);d=F[e+8>>2];F[a>>2]=d;F[a+8>>2]=F[a+8>>2]&-2147483648|F[e+12>>2]&2147483647;F[a+8>>2]=F[a+8>>2]|-2147483648;F[a+4>>2]=c}db(d,b,c+1|0);Z=e+16|0;return}za();v()}function pf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;b=Z-16|0;Z=b;Od(b);F[a+24>>2]=F[b>>2];e=a+24|0;a:{if((e|0)==(b|0)){break a}c=a+28|0;d=b|4;f=G[b+15|0];g=f<<24>>24;if(D[a+39|0]>=0){if((g|0)>=0){a=F[d+4>>2];F[c>>2]=F[d>>2];F[c+4>>2]=a;F[c+8>>2]=F[d+8>>2];break a}qb(c,F[b+4>>2],F[b+8>>2]);break a}a=(g|0)<0;rb(c,a?F[b+4>>2]:d,a?F[b+8>>2]:f)}if(D[b+15|0]<0){ja(F[b+4>>2])}Z=b+16|0;return e|0}function Rf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;d=Z-16|0;Z=d;a:{e=ya(c);if(e>>>0<2147483632){b:{c:{if(e>>>0>=11){g=(e|15)+1|0;f=ka(g);F[d+8>>2]=g|-2147483648;F[d>>2]=f;F[d+4>>2]=e;g=e+f|0;break c}D[d+11|0]=e;g=d+e|0;f=d;if(!e){break b}}la(f,c,e)}D[g|0]=0;f=a+16|0;c=Sc(b,d,f);b=F[a+16>>2];a=D[a+27|0];if(D[d+11|0]<0){ja(F[d>>2])}Z=d+16|0;a=c?(a|0)<0?b:f:0;break a}za();v()}return a|0}function Yb(a,b){var c=0,d=0,e=0;c=F[a+4>>2];d=c+b|0;F[a+4>>2]=d;if(!((d-1^c-1)>>>0<32?c:0)){F[F[a>>2]+((d>>>0>=33?d-1>>>5|0:0)<<2)>>2]=0}a:{if(!b){break a}a=F[a>>2]+(c>>>3&536870908)|0;c=c&31;if(c){d=32-c|0;e=b>>>0>d>>>0?d:b;F[a>>2]=F[a>>2]&(-1<<c&-1>>>d-e^-1);b=b-e|0;a=a+4|0}c=b>>>5|0;if(b>>>0>=32){ma(a,0,c<<2)}if((b&-32)==(b|0)){break a}a=(c<<2)+a|0;F[a>>2]=F[a>>2]&(-1>>>32-(b&31)^-1)}}function ld(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>10){break a}d=F[c+20>>2];f=F[c+12>>2];e=F[c+16>>2];if((d|0)>=(f|0)&e>>>0>=I[c+8>>2]|(d|0)>(f|0)){break a}f=D[e+F[c>>2]|0];e=e+1|0;d=e?d:d+1|0;F[c+16>>2]=e;F[c+20>>2]=d;d=f;b:{if((d|0)<0){if(!ld(a+1|0,b,c)){break a}a=F[b>>2];d=d&127|a<<7;a=F[b+4>>2]<<7|a>>>25;break b}d=d&255;a=0}F[b>>2]=d;F[b+4>>2]=a;g=1}return g}function Sa(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>10){break a}d=F[c+20>>2];f=F[c+12>>2];e=F[c+16>>2];if((d|0)>=(f|0)&e>>>0>=I[c+8>>2]|(d|0)>(f|0)){break a}f=D[e+F[c>>2]|0];e=e+1|0;d=e?d:d+1|0;F[c+16>>2]=e;F[c+20>>2]=d;d=f;b:{if((d|0)<0){if(!Sa(a+1|0,b,c)){break a}a=F[b>>2];d=d&127|a<<7;a=F[b+4>>2]<<7|a>>>25;break b}d=d&255;a=0}F[b>>2]=d;F[b+4>>2]=a;g=1}return g}function Ne(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(La(a,F[b+8>>2],e)){if(!(F[b+28>>2]==1|F[b+4>>2]!=(c|0))){F[b+28>>2]=d}return}a:{if(!La(a,F[b>>2],e)){break a}if(!(F[b+16>>2]!=(c|0)&F[b+20>>2]!=(c|0))){if((d|0)!=1){break a}F[b+32>>2]=1;return}F[b+20>>2]=c;F[b+32>>2]=d;F[b+40>>2]=F[b+40>>2]+1;if(!(F[b+36>>2]!=1|F[b+24>>2]!=2)){D[b+54|0]=1}F[b+44>>2]=4}}function jg(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;e=Z+-64|0;Z=e;d=$[F[F[a>>2]+44>>2]](a,b)|0;a=$[F[F[a>>2]+40>>2]](a,b)|0;f=kb(e);g=F[b+56>>2];h=d&255;i=a;a=a-1|0;if(a>>>0<=10){a=F[(a<<2)+10148>>2]}else{a=-1}d=L(a,d);cc(f,g,h,i,0,d,d>>31);a=bc(ka(96),f);ac(a,c);D[a+84|0]=1;F[a+72>>2]=F[a+68>>2];F[a+60>>2]=F[b+60>>2];Z=e- -64|0;return a|0}function rh(a){a=a|0;var b=0,c=0,d=0;F[a>>2]=8176;b=F[a+48>>2];F[a+48>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}F[a>>2]=10032;b=F[a+20>>2];if(b){F[a+24>>2]=b;ja(b)}d=F[a+8>>2];if(d){c=F[a+12>>2];b=d;if((c|0)!=(b|0)){while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}if((c|0)!=(d|0)){continue}break}b=F[a+8>>2]}F[a+12>>2]=d;ja(b)}return a|0}function Dc(a,b,c,d){D[a+53|0]=1;a:{if(F[a+4>>2]!=(c|0)){break a}D[a+52|0]=1;c=F[a+16>>2];b:{if(!c){F[a+36>>2]=1;F[a+24>>2]=d;F[a+16>>2]=b;if((d|0)!=1){break a}if(F[a+48>>2]==1){break b}break a}if((b|0)==(c|0)){c=F[a+24>>2];if((c|0)==2){F[a+24>>2]=d;c=d}if(F[a+48>>2]!=1){break a}if((c|0)==1){break b}break a}F[a+36>>2]=F[a+36>>2]+1}D[a+54|0]=1}}function qh(a){a=a|0;var b=0,c=0,d=0;F[a>>2]=8176;b=F[a+48>>2];F[a+48>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}F[a>>2]=10032;b=F[a+20>>2];if(b){F[a+24>>2]=b;ja(b)}d=F[a+8>>2];if(d){c=F[a+12>>2];b=d;if((c|0)!=(b|0)){while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}if((c|0)!=(d|0)){continue}break}b=F[a+8>>2]}F[a+12>>2]=d;ja(b)}ja(a)}function Se(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;e=Z+-64|0;Z=e;d=1;a:{if(La(a,b,0)){break a}d=0;if(!b){break a}b=Fc(b,11068);d=0;if(!b){break a}d=e+8|0;ma(d|4,0,52);F[e+56>>2]=1;F[e+20>>2]=-1;F[e+16>>2]=a;F[e+8>>2]=b;$[F[F[b>>2]+28>>2]](b,d,F[c>>2],1);a=F[e+32>>2];if((a|0)==1){F[c>>2]=F[e+24>>2]}d=(a|0)==1}Z=e- -64|0;return d|0}function Fd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=Z-16|0;Z=d;F[a+4>>2]=b;b=F[b+64>>2];e=F[b>>2];b=F[b+4>>2];D[d+15|0]=0;Ea(a+24|0,(b-e>>2>>>0)/3|0,d+15|0);b=F[a+4>>2];e=F[b+56>>2];b=F[b+52>>2];D[d+14|0]=0;Ea(a+36|0,e-b>>2,d+14|0);b=F[c+12>>2];F[a+16>>2]=F[c+8>>2];F[a+20>>2]=b;b=F[c+4>>2];F[a+8>>2]=F[c>>2];F[a+12>>2]=b;Z=d+16|0}function zf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;a=Z-16|0;Z=a;f=D[b+24|0];e=F[2555];F[a+8>>2]=F[2554];F[a+12>>2]=e;e=F[2553];F[a>>2]=F[2552];F[a+4>>2]=e;e=lb(b,c,f,a);if(e){b=0;if(f){c=(f&255)<<2;b=ka(c);g=la(b,a,c)+c|0}c=F[d>>2];if(c){F[d+4>>2]=c;ja(c)}F[d+8>>2]=g;F[d+4>>2]=g;F[d>>2]=b}Z=a+16|0;return e|0}function wd(a,b){var c=0,d=0;a:{c=F[a+4>>2];d=F[a+8>>2];if((c|0)==d<<5){if((c+1|0)<0){break a}if(c>>>0<=1073741822){d=d<<6;c=(c&-32)+32|0;c=c>>>0<d>>>0?d:c}else{c=2147483647}$a(a,c);c=F[a+4>>2]}F[a+4>>2]=c+1;d=1<<c;a=F[a>>2]+(c>>>3&536870908)|0;if(G[b|0]){F[a>>2]=d|F[a>>2];return}F[a>>2]=F[a>>2]&(d^-1);return}na();v()}function Zb(a){var b=0;F[a>>2]=0;F[a+4>>2]=0;F[a+56>>2]=0;F[a+48>>2]=0;F[a+52>>2]=0;F[a+40>>2]=0;F[a+44>>2]=0;F[a+32>>2]=0;F[a+36>>2]=0;F[a+24>>2]=0;F[a+28>>2]=0;F[a+16>>2]=0;F[a+20>>2]=0;F[a+8>>2]=0;F[a+12>>2]=0;b=a- -64|0;F[b>>2]=0;F[b+4>>2]=0;F[a+72>>2]=0;F[a+76>>2]=0;F[a+80>>2]=0;F[a+84>>2]=0;F[a+60>>2]=a;return a}function ve(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;f=$[F[F[a>>2]+24>>2]](a)|0;c=1;a:{if((f|0)<=0){break a}d=F[F[a+36>>2]>>2];g=a+48|0;c=0;if(!($[F[F[d>>2]+16>>2]](d,g,b)|0)){break a}while(1){e=e+1|0;if((f|0)!=(e|0)){d=F[F[a+36>>2]+(e<<2)>>2];if($[F[F[d>>2]+16>>2]](d,g,b)|0){continue}}break}c=(e|0)>=(f|0)}return c|0}function ue(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;f=$[F[F[a>>2]+24>>2]](a)|0;c=1;a:{if((f|0)<=0){break a}d=F[F[a+36>>2]>>2];g=a+48|0;c=0;if(!($[F[F[d>>2]+20>>2]](d,g,b)|0)){break a}while(1){e=e+1|0;if((f|0)!=(e|0)){d=F[F[a+36>>2]+(e<<2)>>2];if($[F[F[d>>2]+20>>2]](d,g,b)|0){continue}}break}c=(e|0)>=(f|0)}return c|0}function bh(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=Z-16|0;Z=d;F[a+4>>2]=b;e=F[b>>2];b=F[b+4>>2];D[d+15|0]=0;Ea(a+24|0,(b-e>>2>>>0)/3|0,d+15|0);b=F[a+4>>2];e=F[b+28>>2];b=F[b+24>>2];D[d+14|0]=0;Ea(a+36|0,e-b>>2,d+14|0);b=F[c+12>>2];F[a+16>>2]=F[c+8>>2];F[a+20>>2]=b;b=F[c+4>>2];F[a+8>>2]=F[c>>2];F[a+12>>2]=b;Z=d+16|0}function hb(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>5){break a}d=F[c+20>>2];e=F[c+12>>2];f=F[c+16>>2];if((d|0)>=(e|0)&f>>>0>=I[c+8>>2]|(d|0)>(e|0)){break a}e=G[F[c>>2]+f|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;d=e<<24>>24;if((d|0)<0){if(!hb(a+1|0,b,c)){break a}e=d&127|F[b>>2]<<7}F[b>>2]=e;g=1}return g}function fb(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>5){break a}d=F[c+20>>2];e=F[c+12>>2];f=F[c+16>>2];if((d|0)>=(e|0)&f>>>0>=I[c+8>>2]|(d|0)>(e|0)){break a}e=G[F[c>>2]+f|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;d=e<<24>>24;if((d|0)<0){if(!fb(a+1|0,b,c)){break a}e=d&127|F[b>>2]<<7}F[b>>2]=e;g=1}return g}function Wb(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>5){break a}d=F[c+20>>2];e=F[c+12>>2];f=F[c+16>>2];if((d|0)>=(e|0)&f>>>0>=I[c+8>>2]|(d|0)>(e|0)){break a}e=G[F[c>>2]+f|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;d=e<<24>>24;if((d|0)<0){if(!Wb(a+1|0,b,c)){break a}e=d&127|F[b>>2]<<7}F[b>>2]=e;g=1}return g}function Ta(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>5){break a}d=F[c+20>>2];e=F[c+12>>2];f=F[c+16>>2];if((d|0)>=(e|0)&f>>>0>=I[c+8>>2]|(d|0)>(e|0)){break a}e=G[F[c>>2]+f|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;d=e<<24>>24;if((d|0)<0){if(!Ta(a+1|0,b,c)){break a}e=d&127|F[b>>2]<<7}F[b>>2]=e;g=1}return g}function Qd(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>5){break a}d=F[c+20>>2];e=F[c+12>>2];f=F[c+16>>2];if((d|0)>=(e|0)&f>>>0>=I[c+8>>2]|(d|0)>(e|0)){break a}e=G[F[c>>2]+f|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;d=e<<24>>24;if((d|0)<0){if(!Qd(a+1|0,b,c)){break a}e=d&127|F[b>>2]<<7}F[b>>2]=e;g=1}return g}function Oa(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>5){break a}d=F[c+20>>2];e=F[c+12>>2];f=F[c+16>>2];if((d|0)>=(e|0)&f>>>0>=I[c+8>>2]|(d|0)>(e|0)){break a}e=G[F[c>>2]+f|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;d=e<<24>>24;if((d|0)<0){if(!Oa(a+1|0,b,c)){break a}e=d&127|F[b>>2]<<7}F[b>>2]=e;g=1}return g}function Da(a,b,c){var d=0,e=0,f=0,g=0;a:{if(a>>>0>5){break a}d=F[c+20>>2];e=F[c+12>>2];f=F[c+16>>2];if((d|0)>=(e|0)&f>>>0>=I[c+8>>2]|(d|0)>(e|0)){break a}e=G[F[c>>2]+f|0];f=f+1|0;d=f?d:d+1|0;F[c+16>>2]=f;F[c+20>>2]=d;d=e<<24>>24;if((d|0)<0){if(!Da(a+1|0,b,c)){break a}e=d&127|F[b>>2]<<7}F[b>>2]=e;g=1}return g}function sa(a,b,c){var d=0,e=0;a:{b:{if(c>>>0>=4){if((a|b)&3){break b}while(1){if(F[a>>2]!=F[b>>2]){break b}b=b+4|0;a=a+4|0;c=c-4|0;if(c>>>0>3){continue}break}}if(!c){break a}}while(1){d=G[a|0];e=G[b|0];if((d|0)==(e|0)){b=b+1|0;a=a+1|0;c=c-1|0;if(c){continue}break a}break}return d-e|0}return 0}function td(a){var b=0,c=0,d=0,e=0;d=F[a>>2];if(d){e=d;c=F[a+4>>2];if((d|0)!=(c|0)){while(1){e=c-144|0;b=F[e+132>>2];if(b){F[c-8>>2]=b;ja(b)}b=F[c-28>>2];if(b){F[c-24>>2]=b;ja(b)}b=F[c-40>>2];if(b){F[c-36>>2]=b;ja(b)}Gb(c-140|0);c=e;if((d|0)!=(c|0)){continue}break}e=F[a>>2]}F[a+4>>2]=d;ja(e)}}function Ef(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=F[b+4>>2];a:{if(!d){break a}b=F[F[F[b+8>>2]+(c<<2)>>2]+60>>2];if((b|0)<0){break a}a=F[d+24>>2];c=F[d+28>>2];if((a|0)==(c|0)){break a}b:{while(1){e=F[a>>2];if((b|0)==F[e+24>>2]){break b}a=a+4|0;if((c|0)!=(a|0)){continue}break}e=0}}return e|0}function ic(a){var b=0,c=0,d=0;if(a){d=F[a+24>>2];if(d){b=d;c=F[a+28>>2];if((b|0)!=(c|0)){while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;if(b){Ca(b+12|0,F[b+16>>2]);Ba(b,F[b+4>>2]);ja(b)}if((c|0)!=(d|0)){continue}break}b=F[a+24>>2]}F[a+28>>2]=d;ja(b)}Ca(a+12|0,F[a+16>>2]);Ba(a,F[a+4>>2]);ja(a)}}function $g(a){a=a|0;var b=0;F[a+8>>2]=9136;F[a>>2]=8924;b=F[a+96>>2];if(b){F[a+100>>2]=b;ja(b)}b=F[a+80>>2];if(b){F[a+84>>2]=b;ja(b)}b=F[a+68>>2];if(b){F[a+72>>2]=b;ja(b)}b=F[a+56>>2];if(b){F[a+60>>2]=b;ja(b)}F[a+8>>2]=9372;b=F[a+44>>2];if(b){ja(b)}b=F[a+32>>2];if(b){ja(b)}return a|0}function _g(a){a=a|0;var b=0;F[a+8>>2]=9136;F[a>>2]=8924;b=F[a+96>>2];if(b){F[a+100>>2]=b;ja(b)}b=F[a+80>>2];if(b){F[a+84>>2]=b;ja(b)}b=F[a+68>>2];if(b){F[a+72>>2]=b;ja(b)}b=F[a+56>>2];if(b){F[a+60>>2]=b;ja(b)}F[a+8>>2]=9372;b=F[a+44>>2];if(b){ja(b)}b=F[a+32>>2];if(b){ja(b)}ja(a)}function wh(a){a=a|0;var b=0,c=0,d=0;F[a>>2]=10032;b=F[a+20>>2];if(b){F[a+24>>2]=b;ja(b)}d=F[a+8>>2];if(d){c=F[a+12>>2];b=d;if((c|0)!=(b|0)){while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}if((c|0)!=(d|0)){continue}break}b=F[a+8>>2]}F[a+12>>2]=d;ja(b)}return a|0}function uc(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;h=F[c+8>>2];e=F[c+16>>2];g=F[c+12>>2];f=g;d=F[c+20>>2];if(h>>>0>e>>>0&(f|0)>=(d|0)|(d|0)<(f|0)){b=G[F[c>>2]+e|0];i=e+1|0;f=i?d:d+1|0;F[c+16>>2]=i;F[c+20>>2]=f;F[a+4>>2]=b}return e>>>0<h>>>0&(d|0)<=(g|0)|(d|0)<(g|0)}function La(a,b,c){var d=0;if(!c){return F[a+4>>2]==F[b+4>>2]}if((a|0)==(b|0)){return 1}d=F[a+4>>2];a=G[d|0];c=F[b+4>>2];b=G[c|0];a:{if(!a|(b|0)!=(a|0)){break a}while(1){b=G[c+1|0];a=G[d+1|0];if(!a){break a}c=c+1|0;d=d+1|0;if((a|0)==(b|0)){continue}break}}return(a|0)==(b|0)}function Gg(a){a=a|0;var b=0,c=0,d=0;F[a>>2]=10032;b=F[a+20>>2];if(b){F[a+24>>2]=b;ja(b)}d=F[a+8>>2];if(d){c=F[a+12>>2];b=d;if((c|0)!=(b|0)){while(1){c=c-4|0;b=F[c>>2];F[c>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}if((c|0)!=(d|0)){continue}break}b=F[a+8>>2]}F[a+12>>2]=d;ja(b)}ja(a)}function Gf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=F[b+12>>2];b=F[b+8>>2];a=0;a:{if((d|0)==(b|0)){break a}a=d-b>>2;d=a>>>0<=1?1:a;a=0;b:{while(1){e=F[b+(a<<2)>>2];if(F[e+60>>2]==(c|0)){break b}a=a+1|0;if((d|0)!=(a|0)){continue}break}a=0;break a}a=(a|0)!=-1?e:0}return a|0}function ah(a){a=a|0;var b=0;F[a>>2]=9136;b=F[a+88>>2];if(b){F[a+92>>2]=b;ja(b)}b=F[a+72>>2];if(b){F[a+76>>2]=b;ja(b)}b=F[a+60>>2];if(b){F[a- -64>>2]=b;ja(b)}b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}F[a>>2]=9372;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}return a|0}function Tc(a,b){var c=0,d=0,e=0;F[a+8>>2]=0;F[a>>2]=0;F[a+4>>2]=0;a:{c=F[b+4>>2];d=F[b>>2];b:{if((c|0)==(d|0)){a=c;break b}c=c-d|0;if((c|0)<0){break a}d=c;e=ka(c);c=ma(e,0,c);d=d+c|0;F[a+8>>2]=d;F[a+4>>2]=d;F[a>>2]=c;c=F[b>>2];a=F[b+4>>2]}la(e,c,a-c|0);return}na();v()}function Dd(a){var b=0,c=0,d=0,e=0;c=F[a+4>>2];d=F[a>>2];if((c|0)!=(d|0)){while(1){e=c-144|0;b=F[e+132>>2];if(b){F[c-8>>2]=b;ja(b)}b=F[c-28>>2];if(b){F[c-24>>2]=b;ja(b)}b=F[c-40>>2];if(b){F[c-36>>2]=b;ja(b)}Gb(c-140|0);c=e;if((d|0)!=(c|0)){continue}break}}F[a+4>>2]=d}function Xg(a){a=a|0;var b=0;F[a>>2]=9136;b=F[a+88>>2];if(b){F[a+92>>2]=b;ja(b)}b=F[a+72>>2];if(b){F[a+76>>2]=b;ja(b)}b=F[a+60>>2];if(b){F[a- -64>>2]=b;ja(b)}b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}F[a>>2]=9372;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}ja(a)}function Za(a){var b=0;if(a){b=F[a+76>>2];if(b){F[a+80>>2]=b;ja(b)}b=F[a- -64>>2];if(b){F[a+68>>2]=b;ja(b)}b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}b=F[a+24>>2];if(b){F[a+28>>2]=b;ja(b)}b=F[a+12>>2];if(b){F[a+16>>2]=b;ja(b)}b=F[a>>2];if(b){F[a+4>>2]=b;ja(b)}ja(a)}}function Gb(a){var b=0;b=F[a+84>>2];if(b){F[a+88>>2]=b;ja(b)}b=F[a+72>>2];if(b){F[a+76>>2]=b;ja(b)}b=F[a+52>>2];if(b){F[a+56>>2]=b;ja(b)}b=F[a+40>>2];if(b){F[a+44>>2]=b;ja(b)}b=F[a+28>>2];if(b){F[a+32>>2]=b;ja(b)}b=F[a+12>>2];if(b){ja(b)}a=F[a>>2];if(a){ja(a)}}function Lc(a,b,c){var d=0,e=0,f=0,g=0;f=Z-16|0;Z=f;d=Z-16|0;Z=d;b=b-a>>2;while(1){if(b){F[d+12>>2]=a;e=b>>>1|0;F[d+12>>2]=F[d+12>>2]+(e<<2);g=(e^-1)+b|0;b=e;e=I[F[d+12>>2]>>2]<I[c>>2];b=e?g:b;a=e?F[d+12>>2]+4|0:a;continue}break}Z=d+16|0;Z=f+16|0;return a}function id(a,b){var c=0,d=0;d=ka(40);F[d>>2]=-1;c=d+8|0;F[c+16>>2]=0;F[c+20>>2]=0;F[c+8>>2]=0;F[c>>2]=0;F[c+4>>2]=0;F[c+24>>2]=0;F[c+28>>2]=0;$[F[F[a>>2]+16>>2]](a,d);a=F[b+88>>2];F[b+88>>2]=d;if(a){b=F[a+8>>2];if(b){F[a+12>>2]=b;ja(b)}ja(a)}return 1}function ya(a){var b=0,c=0,d=0;b=a;a:{if(b&3){while(1){if(!G[b|0]){break a}b=b+1|0;if(b&3){continue}break}}while(1){c=b;b=b+4|0;d=F[c>>2];if(!((d^-1)&d-16843009&-2139062144)){continue}break}while(1){b=c;c=b+1|0;if(G[b|0]){continue}break}}return b-a|0}function wa(a){var b=0,c=0,d=0,e=0,f=0;d=G[a+12|0];c=F[a+8>>2];a:{if(c>>>0>4095){break a}b=F[a+4>>2];if((b|0)<=0){break a}b=b-1|0;F[a+4>>2]=b;c=G[b+F[a>>2]|0]|c<<8}d=0-d&255;b=L(d,c>>>8|0);e=c&255;f=e>>>0<d>>>0;F[a+8>>2]=f?b+e|0:c-(b+d|0)|0;return f}function yc(a,b){F[a+4>>2]=0;F[a+8>>2]=0;F[a>>2]=1776;F[a+12>>2]=0;F[a+16>>2]=0;F[a+20>>2]=0;F[a+24>>2]=0;F[a+28>>2]=0;F[a+32>>2]=0;F[a+36>>2]=0;F[a+40>>2]=0;F[a>>2]=2016;F[a+60>>2]=b;F[a+44>>2]=0;F[a+48>>2]=0;F[a+52>>2]=0;F[a+56>>2]=0;return a}function Eb(a,b){var c=0,d=0,e=0;c=ya(b);if(c>>>0<2147483632){a:{b:{if(c>>>0>=11){d=(c|15)+1|0;e=ka(d);F[a+8>>2]=d|-2147483648;F[a>>2]=e;F[a+4>>2]=c;d=c+e|0;break b}D[a+11|0]=c;d=a+c|0;e=a;if(!c){break a}}pa(e,b,c)}D[d|0]=0;return a}za();v()}function Of(a){a=a|0;var b=0,c=0,d=0;if(a){if(D[a+27|0]<0){ja(F[a+16>>2])}b=F[a>>2];if(b){c=b;d=F[a+4>>2];if((b|0)!=(d|0)){while(1){c=d-12|0;if(D[d-1|0]<0){ja(F[c>>2])}d=c;if((d|0)!=(b|0)){continue}break}c=F[a>>2]}F[a+4>>2]=b;ja(c)}ja(a)}}function xa(a){a=a|0;var b=0,c=0;if(a){b=F[a+88>>2];F[a+88>>2]=0;if(b){c=F[b+8>>2];if(c){F[b+12>>2]=c;ja(c)}ja(b)}b=F[a+68>>2];if(b){F[a+72>>2]=b;ja(b)}b=F[a+64>>2];F[a+64>>2]=0;if(b){c=F[b>>2];if(c){F[b+4>>2]=c;ja(c)}ja(b)}ja(a)}}function Ib(a,b){var c=0,d=0,e=0;a:{c=F[a>>2];b:{if(F[a+8>>2]-c>>2>>>0>=b>>>0){break b}if(b>>>0>=1073741824){break a}d=F[a+4>>2]-c|0;e=b<<2;b=pa(ka(e),c,d);F[a+8>>2]=b+e;F[a+4>>2]=b+d;F[a>>2]=b;if(!c){break b}ja(c)}return}na();v()}function Df(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;b=F[b+96>>2];a=ka(12);b=b+L(c,12)|0;c=F[b+4>>2];F[a>>2]=F[b>>2];F[a+4>>2]=c;F[a+8>>2]=F[b+8>>2];b=F[d>>2];if(b){F[d+4>>2]=b;ja(b)}F[d>>2]=a;a=a+12|0;F[d+8>>2]=a;F[d+4>>2]=a;return 1}function Ah(a){a=a|0;var b=0;F[a+24>>2]=1624;F[a>>2]=7948;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}F[a>>2]=2136;b=F[a+20>>2];F[a+20>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}F[a>>2]=1920;b=F[a+16>>2];F[a+16>>2]=0;if(b){xa(b)}return a|0}function li(a,b,c,d){var e=0,f=0,g=0,h=0;f=b^d;g=f>>31;e=b>>31;a=a^e;h=a-e|0;e=(b^e)-((a>>>0<e>>>0)+e|0)|0;a=d>>31;b=c^a;f=f>>31;a=mi(h,e,b-a|0,(a^d)-((a>>>0>b>>>0)+a|0)|0)^f;b=a-f|0;_=(g^_)-((a>>>0<f>>>0)+g|0)|0;return b}function zh(a){a=a|0;var b=0;F[a+24>>2]=1624;F[a>>2]=7948;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}F[a>>2]=2136;b=F[a+20>>2];F[a+20>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}F[a>>2]=1920;b=F[a+16>>2];F[a+16>>2]=0;if(b){xa(b)}ja(a)}function rb(a,b,c){var d=0,e=0,f=0;e=Z-16|0;Z=e;d=F[a+8>>2]&2147483647;a:{if(d>>>0>c>>>0){d=F[a>>2];F[a+4>>2]=c;db(d,b,c);D[e+15|0]=0;D[c+d|0]=G[e+15|0];break a}f=a;a=F[a+4>>2];Gc(f,d-1|0,(c-d|0)+1|0,a,a,c,b)}Z=e+16|0}function xe(a,b){a=a|0;b=b|0;var c=0,d=0;c=Z-16|0;Z=c;a=F[a+4>>2];a:{if((a|0)==-1){break a}D[c+15|0]=a;d=F[b+20>>2];if(!!F[b+16>>2]&(d|0)>=0|(d|0)>0){break a}pb(b,F[b+4>>2],c+15|0,c+16|0)}Z=c+16|0;return(a|0)!=-1|0}function ki(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0;e=c>>>16|0;f=a>>>16|0;j=L(e,f);g=c&65535;h=a&65535;i=L(g,h);f=(i>>>16|0)+L(f,g)|0;e=(f&65535)+L(e,h)|0;_=(L(b,c)+j|0)+L(a,d)+(f>>>16)+(e>>>16)|0;return i&65535|e<<16}function qb(a,b,c){var d=0,e=0;d=Z-16|0;Z=d;a:{if(c>>>0<=10){D[a+11|0]=G[a+11|0]&128|c;D[a+11|0]=G[a+11|0]&127;db(a,b,c);D[d+15|0]=0;D[a+c|0]=G[d+15|0];break a}e=a;a=G[a+11|0]&127;Gc(e,10,c-10|0,a,a,c,b)}Z=d+16|0}function Ec(a,b,c){var d=0;d=F[a+16>>2];if(!d){F[a+36>>2]=1;F[a+24>>2]=c;F[a+16>>2]=b;return}a:{if((b|0)==(d|0)){if(F[a+24>>2]!=2){break a}F[a+24>>2]=c;return}D[a+54|0]=1;F[a+24>>2]=2;F[a+36>>2]=F[a+36>>2]+1}}function vg(){var a=0;a=kb(ka(96));F[a+64>>2]=0;F[a+68>>2]=0;F[a+88>>2]=0;F[a+72>>2]=0;F[a+76>>2]=0;D[a+77|0]=0;D[a+78|0]=0;D[a+79|0]=0;D[a+80|0]=0;D[a+81|0]=0;D[a+82|0]=0;D[a+83|0]=0;D[a+84|0]=0;return a|0}function jh(a,b){a=a|0;b=b|0;var c=0,d=0;F[b>>2]=2;c=F[b+8>>2];d=F[b+12>>2]-c|0;if(d>>>0<=4294967291){Db(b+8|0,d+4|0);c=F[b+8>>2]}b=c+d|0;a=F[a+4>>2];D[b|0]=a;D[b+1|0]=a>>>8;D[b+2|0]=a>>>16;D[b+3|0]=a>>>24}function ge(a){a=a|0;var b=0;F[a>>2]=3016;b=F[a+96>>2];if(b){ja(b)}b=F[a+84>>2];if(b){ja(b)}b=F[a+72>>2];if(b){ja(b)}b=F[a+60>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}return a|0}function ci(a){a=a|0;var b=0;F[a>>2]=4580;b=F[a+96>>2];if(b){ja(b)}b=F[a+84>>2];if(b){ja(b)}b=F[a+72>>2];if(b){ja(b)}b=F[a+60>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}return a|0}function Cg(a){a=a|0;var b=0,c=0,d=0;b=F[a+8>>2];d=F[a+12>>2];if((b|0)==(d|0)){return 1}while(1){c=F[b>>2];c=$[F[F[c>>2]+16>>2]](c,F[a+32>>2])|0;if(c){b=b+4|0;if((d|0)!=(b|0)){continue}}break}return c|0}function Pc(a,b){var c=0,d=0;c=F[a+8>>2];a=F[a+12>>2];if((c|0)!=(a|0)){a=a-c>>2;d=a>>>0<=1?1:a;a=0;while(1){if(F[F[(a<<2)+c>>2]+60>>2]==(b|0)){return a}a=a+1|0;if((d|0)!=(a|0)){continue}break}}return-1}function fe(a){a=a|0;var b=0;F[a>>2]=3016;b=F[a+96>>2];if(b){ja(b)}b=F[a+84>>2];if(b){ja(b)}b=F[a+72>>2];if(b){ja(b)}b=F[a+60>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}ja(a)}function bi(a){a=a|0;var b=0;F[a>>2]=4580;b=F[a+96>>2];if(b){ja(b)}b=F[a+84>>2];if(b){ja(b)}b=F[a+72>>2];if(b){ja(b)}b=F[a+60>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}ja(a)}function Sc(a,b,c){var d=0,e=0;d=a+4|0;a=Ya(a,b);a:{if((d|0)==(a|0)){break a}b=F[a+32>>2];d=F[a+28>>2];if((b|0)==(d|0)){break a}Sb(c,b-d|0);c=Tb(c);b=F[a+28>>2];la(c,b,F[a+32>>2]-b|0);e=1}return e}function Kd(a){F[a+40>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;F[a>>2]=10032;F[a+12>>2]=0;F[a+16>>2]=0;F[a+20>>2]=0;F[a+24>>2]=0;F[a+28>>2]=0;F[a+32>>2]=0;E[a+36>>1]=0;F[a+44>>2]=0;F[a>>2]=8080;return a}function kb(a){F[a+8>>2]=0;F[a+12>>2]=0;F[a>>2]=0;F[a+40>>2]=0;F[a+44>>2]=0;F[a+28>>2]=9;D[a+24|0]=1;F[a+56>>2]=-1;F[a+60>>2]=0;F[a+16>>2]=0;F[a+20>>2]=0;F[a+48>>2]=0;F[a+52>>2]=0;return a}function pe(a,b){a=a|0;b=b|0;var c=0,d=0;d=F[a+16>>2];c=0;a:{if(F[a+20>>2]-d>>2<=(b|0)){break a}b=F[(b<<2)+d>>2];c=0;if((b|0)<0){break a}c=bb(F[F[a+36>>2]+(b<<2)>>2])}return c|0}function Nf(){var a=0,b=0;a=ka(40);F[a+4>>2]=0;F[a+8>>2]=0;F[a+24>>2]=0;F[a+28>>2]=0;b=a+16|0;F[b>>2]=0;F[b+4>>2]=0;F[a>>2]=a+4;F[a+12>>2]=b;F[a+32>>2]=0;F[a+36>>2]=0;return a|0}function Xe(a,b){a=a|0;b=b|0;var c=0,d=0;Nc(a,b);a:{if((b|0)<0){break a}d=F[a+88>>2];c=F[a+84>>2];if(d-c>>2<=(b|0)){break a}c=(b<<2)+c|0;b=c+4|0;pa(c,b,d-b|0);F[a+88>>2]=d-4}}function eb(a){var b=0,c=0;b=F[2909];c=a+7&-8;a=b+c|0;a:{if(a>>>0<=b>>>0?c:0){break a}if(a>>>0>aa()<<16>>>0){if(!(X(a|0)|0)){break a}}F[2909]=a;return b}F[2940]=48;return-1}function Th(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;F[a+4>>2]=b;b=F[F[F[b+4>>2]+8>>2]+(c<<2)>>2];F[a+12>>2]=c;F[a+8>>2]=b;a=F[a+8>>2];if(G[a+24|0]==3){d=F[a+28>>2]==9}return d|0}function Tg(a){a=a|0;var b=0;F[a+8>>2]=9556;F[a>>2]=9392;b=F[a+56>>2];if(b){F[a+60>>2]=b;ja(b)}F[a+8>>2]=9372;b=F[a+44>>2];if(b){ja(b)}b=F[a+32>>2];if(b){ja(b)}return a|0}function Ng(a){a=a|0;var b=0;F[a+8>>2]=8624;F[a>>2]=9684;b=F[a+56>>2];if(b){F[a+60>>2]=b;ja(b)}F[a+8>>2]=8876;b=F[a+44>>2];if(b){ja(b)}b=F[a+32>>2];if(b){ja(b)}return a|0}function Ee(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=F[a+8>>2];a:{if(!G[d+24|0]){break a}if(!ac(d,F[b+4>>2]-F[b>>2]>>2)){break a}e=$[F[F[a>>2]+32>>2]](a,b,c)|0}return e|0}function Fh(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;F[a+4>>2]=b;d=F[F[F[b+4>>2]+8>>2]+(c<<2)>>2];F[a+12>>2]=c;F[a+8>>2]=d;return F[F[F[F[b+4>>2]+8>>2]+(c<<2)>>2]+28>>2]==9|0}function Ca(a,b){if(b){Ca(a,F[b>>2]);Ca(a,F[b+4>>2]);a=F[b+28>>2];F[b+28>>2]=0;if(a){Ca(a+12|0,F[a+16>>2]);Ba(a,F[a+4>>2]);ja(a)}if(D[b+27|0]<0){ja(F[b+16>>2])}ja(b)}}function Sg(a){a=a|0;var b=0;F[a+8>>2]=9556;F[a>>2]=9392;b=F[a+56>>2];if(b){F[a+60>>2]=b;ja(b)}F[a+8>>2]=9372;b=F[a+44>>2];if(b){ja(b)}b=F[a+32>>2];if(b){ja(b)}ja(a)}function Mg(a){a=a|0;var b=0;F[a+8>>2]=8624;F[a>>2]=9684;b=F[a+56>>2];if(b){F[a+60>>2]=b;ja(b)}F[a+8>>2]=8876;b=F[a+44>>2];if(b){ja(b)}b=F[a+32>>2];if(b){ja(b)}ja(a)}function Hc(a,b){var c=0,d=0,e=0,f=0;F[a>>2]=11356;F[a>>2]=11468;c=ya(b);d=ka(c+13|0);F[d+8>>2]=0;F[d+4>>2]=c;F[d>>2]=c;e=a,f=la(d+12|0,b,c+1|0),F[e+4>>2]=f;return a}function hc(a,b,c){a:{if(b){b=0;if(!ld(1,c,a)){break a}}D[a+36|0]=1;F[a+32>>2]=0;b=F[a+16>>2];c=b+F[a>>2]|0;F[a+24>>2]=c;F[a+28>>2]=c+(F[a+8>>2]-b|0);b=1}return b}function Ue(a,b){a=a|0;b=b|0;var c=0;a:{if(!($[F[F[a>>2]+36>>2]](a,b)|0)){break a}if(!($[F[F[a>>2]+40>>2]](a,b)|0)){break a}c=$[F[F[a>>2]+44>>2]](a)|0}return c|0}function _d(a){a=a|0;var b=0;a:{if(!F[a- -64>>2]|!F[a+68>>2]|(!F[a+44>>2]|!F[a+48>>2])){break a}if(!F[a+52>>2]|!F[a+56>>2]){break a}b=F[a+92>>2]!=-1}return b|0}function ii(a,b){a=a|0;b=b|0;var c=0;b=F[b+88>>2];if(!(!b|F[b>>2]!=2)){c=a;a=F[b+8>>2];F[c+4>>2]=G[a|0]|G[a+1|0]<<8|(G[a+2|0]<<16|G[a+3|0]<<24);c=1}return c|0}function wc(a){a=a|0;var b=0;F[a>>2]=2136;b=F[a+20>>2];F[a+20>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}F[a>>2]=1920;b=F[a+16>>2];F[a+16>>2]=0;if(b){xa(b)}return a|0}function Ud(a){a=a|0;var b=0;a:{if(!F[a+48>>2]|!F[a+52>>2]|(!F[a+28>>2]|!F[a+32>>2])){break a}if(!F[a+36>>2]|!F[a+40>>2]){break a}b=F[a+76>>2]!=-1}return b|0}function Ug(a){a=a|0;var b=0;F[a>>2]=9556;b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}F[a>>2]=9372;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}return a|0}function Ed(a){a=a|0;var b=0;F[a>>2]=8624;b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}F[a>>2]=8876;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}return a|0}function vc(a){a=a|0;var b=0;F[a>>2]=2136;b=F[a+20>>2];F[a+20>>2]=0;if(b){$[F[F[b>>2]+4>>2]](b)}F[a>>2]=1920;b=F[a+16>>2];F[a+16>>2]=0;if(b){xa(b)}ja(a)}function yg(){var a=0,b=0;b=ka(40);F[b>>2]=-1;a=b+8|0;F[a+16>>2]=0;F[a+20>>2]=0;F[a+8>>2]=0;F[a>>2]=0;F[a+4>>2]=0;F[a+24>>2]=0;F[a+28>>2]=0;return b|0}function eh(a){a=a|0;var b=0;F[a>>2]=8624;b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}F[a>>2]=8876;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}ja(a)}function Og(a){a=a|0;var b=0;F[a>>2]=9556;b=F[a+48>>2];if(b){F[a+52>>2]=b;ja(b)}F[a>>2]=9372;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}ja(a)}function Ja(a){F[a+8>>2]=0;F[a+12>>2]=0;F[a>>2]=0;F[a+16>>2]=0;F[a+20>>2]=0;F[a+32>>2]=0;F[a+24>>2]=0;F[a+28>>2]=0;E[a+38>>1]=0;D[a+36|0]=0;return a}function Me(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(La(a,F[b+8>>2],f)){Dc(b,c,d,e);return}a=F[a+8>>2];$[F[F[a>>2]+20>>2]](a,b,c,d,e,f)}function Eh(a,b,c){a=a|0;b=b|0;c=c|0;a:{if(G[F[a+4>>2]+36|0]>=2){b=0;if(!($[F[F[a>>2]+52>>2]](a)|0)){break a}}b=id(a+24|0,F[a+16>>2])}return b|0}function hg(){var a=0;a=Rc(ka(108));F[a+84>>2]=0;F[a+88>>2]=0;F[a>>2]=10240;F[a+92>>2]=0;F[a+96>>2]=0;F[a+100>>2]=0;F[a+104>>2]=0;return a|0}function Qc(a,b){var c=0;c=-1;a:{if((b|0)==-1|(b|0)>4){break a}b=L(b,12)+a|0;a=F[b+20>>2];if((F[b+24>>2]-a|0)<=0){break a}c=F[a>>2]}return c}function cc(a,b,c,d,e,f,g){F[a>>2]=0;F[a+56>>2]=b;F[a+48>>2]=0;F[a+52>>2]=0;F[a+40>>2]=f;F[a+44>>2]=g;D[a+32|0]=e;F[a+28>>2]=d;D[a+24|0]=c}function Sh(a,b,c){a=a|0;b=b|0;c=c|0;a:{if(G[F[a+4>>2]+36|0]>=2){b=0;if(!uc(a+24|0,bb(a),c)){break a}}b=id(a+24|0,F[a+16>>2])}return b|0}function _e(a){a=a|0;var b=0;F[a>>2]=10240;b=F[a+96>>2];if(b){F[a+100>>2]=b;ja(b)}b=F[a+84>>2];if(b){F[a+88>>2]=b;ja(b)}return tb(a)|0}function Tb(a){var b=0;if(G[a+11|0]>>>7|0){b=F[a+4>>2]}else{b=G[a+11|0]&127}if(!b){sc(1222);v()}if(G[a+11|0]>>>7|0){a=F[a>>2]}return a}function Ze(a){a=a|0;var b=0;F[a>>2]=10240;b=F[a+96>>2];if(b){F[a+100>>2]=b;ja(b)}b=F[a+84>>2];if(b){F[a+88>>2]=b;ja(b)}ja(tb(a))}function ce(a){a=a|0;var b=0;F[a>>2]=3264;b=F[a+76>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}return a|0}function _h(a){a=a|0;var b=0;F[a>>2]=4816;b=F[a+76>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}return a|0}function Ba(a,b){if(b){Ba(a,F[b>>2]);Ba(a,F[b+4>>2]);a=F[b+28>>2];if(a){F[b+32>>2]=a;ja(a)}if(D[b+27|0]<0){ja(F[b+16>>2])}ja(b)}}function Wf(){var a=0;a=ka(28);F[a>>2]=0;F[a+4>>2]=0;F[a+24>>2]=0;F[a+16>>2]=0;F[a+20>>2]=0;F[a+8>>2]=0;F[a+12>>2]=0;return a|0}function We(a){a=a|0;var b=0;F[a>>2]=1776;b=F[a+16>>2];if(b){F[a+20>>2]=b;ja(b)}b=F[a+4>>2];if(b){F[a+8>>2]=b;ja(b)}return a|0}function fg(){var a=0,b=0;a=ka(24);F[a+4>>2]=0;F[a+8>>2]=0;b=a+16|0;F[b>>2]=0;F[b+4>>2]=0;F[a>>2]=a+4;F[a+12>>2]=b;return a|0}function Pe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(La(a,F[b+8>>2],0)){Ec(b,c,d);return}a=F[a+8>>2];$[F[F[a>>2]+28>>2]](a,b,c,d)}function be(a){a=a|0;var b=0;F[a>>2]=3264;b=F[a+76>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}ja(a)}function Zh(a){a=a|0;var b=0;F[a>>2]=4816;b=F[a+76>>2];if(b){ja(b)}F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}ja(a)}function ka(a){var b=0;a=a?a:1;a:{while(1){b=Ub(a);if(b){break a}b=F[3065];if(b){$[b|0]();continue}break}V();v()}return b}function ib(a,b){if(b){ib(a,F[b>>2]);ib(a,F[b+4>>2]);if(D[b+39|0]<0){ja(F[b+28>>2])}if(D[b+27|0]<0){ja(F[b+16>>2])}ja(b)}}function Cc(a){a=a|0;var b=0,c=0;F[a>>2]=11468;b=F[a+4>>2]-12|0;c=F[b+8>>2]-1|0;F[b+8>>2]=c;if((c|0)<0){ja(b)}return a|0}function ng(){var a=0;a=ka(24);F[a+8>>2]=0;F[a+12>>2]=0;F[a+4>>2]=-1;F[a>>2]=1624;F[a+16>>2]=0;F[a+20>>2]=0;return a|0}function Ac(a,b,c){a=a|0;b=b|0;c=c|0;F[a+4>>2]=b;b=F[F[F[b+4>>2]+8>>2]+(c<<2)>>2];F[a+12>>2]=c;F[a+8>>2]=b;return 1}function pc(a){a=a|0;var b=0;if(!(!F[a+60>>2]|!F[a+44>>2]|(!F[a+48>>2]|!F[a+52>>2]))){b=F[a+56>>2]!=0}return b|0}function Ic(a,b){if(G[a+11|0]>>>7|0){F[a+4>>2]=b;return}D[a+11|0]=G[a+11|0]&128|b;D[a+11|0]=G[a+11|0]&127}function gi(a){a=a|0;var b=0;F[a>>2]=3500;F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}return a|0}function Xh(a){a=a|0;var b=0;F[a>>2]=5040;F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}return a|0}function nf(a){a=a|0;if(a){if(D[a+39|0]<0){ja(F[a+28>>2])}$b(a+12|0,F[a+16>>2]);ib(a,F[a+4>>2]);ja(a)}}function dh(a){a=a|0;var b=0;F[a>>2]=8876;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}return a|0}function Wg(a){a=a|0;var b=0;F[a>>2]=9372;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}return a|0}function ob(a){a=a|0;var b=0;if(!(!F[a+52>>2]|(!F[a+44>>2]|!F[a+48>>2]))){b=F[a+56>>2]!=0}return b|0}function oc(a,b){a=a|0;b=b|0;var c=0;if(!(F[b+56>>2]|!b|G[b+24|0]!=3)){F[a+60>>2]=b;c=1}return c|0}function fi(a){a=a|0;var b=0;F[a>>2]=3500;F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}ja(a)}function Wh(a){a=a|0;var b=0;F[a>>2]=5040;F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}ja(a)}function zg(a,b,c){a=a|0;b=b|0;c=c|0;F[a+16>>2]=0;F[a+20>>2]=0;F[a>>2]=b;F[a+8>>2]=c;F[a+12>>2]=0}function ch(a){a=a|0;var b=0;F[a>>2]=8876;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}ja(a)}function Zd(a,b){a=a|0;b=b|0;var c=0;if(!(F[b+56>>2]|G[b+24|0]!=3)){F[a- -64>>2]=b;c=1}return c|0}function Vg(a){a=a|0;var b=0;F[a>>2]=9372;b=F[a+36>>2];if(b){ja(b)}b=F[a+24>>2];if(b){ja(b)}ja(a)}function Td(a,b){a=a|0;b=b|0;var c=0;if(!(F[b+56>>2]|G[b+24|0]!=3)){F[a+48>>2]=b;c=1}return c|0}function Le(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(La(a,F[b+8>>2],f)){Dc(b,c,d,e)}}function oa(){var a=0;a=Rb(4);F[a>>2]=11356;F[a>>2]=11316;F[a>>2]=11336;W(a|0,11448,14);v()}function je(a){a=a|0;var b=0;F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}return a|0}function Ae(a){a=a|0;var b=0;F[a>>2]=1920;b=F[a+16>>2];F[a+16>>2]=0;if(b){xa(b)}return a|0}function Pg(a){a=a|0;var b=0;F[a>>2]=1624;b=F[a+8>>2];if(b){F[a+12>>2]=b;ja(b)}return a|0}function Ch(a){a=a|0;var b=0;b=bb(a);return qd(a+24|0,b?b:F[a+8>>2],F[F[a+4>>2]+32>>2])|0}function ze(a){a=a|0;var b=0;F[a>>2]=1920;b=F[a+16>>2];F[a+16>>2]=0;if(b){xa(b)}ja(a)}function Ob(a){a=a|0;var b=0;F[a>>2]=2960;b=F[a+32>>2];if(b){F[a+36>>2]=b;ja(b)}ja(a)}function Hg(a){a=a|0;var b=0;F[a>>2]=1624;b=F[a+8>>2];if(b){F[a+12>>2]=b;ja(b)}ja(a)}function oe(a,b){a=a|0;b=b|0;return $[F[F[a>>2]+48>>2]](a,F[b+4>>2]-F[b>>2]>>2)|0}function $b(a,b){if(b){$b(a,F[b>>2]);$b(a,F[b+4>>2]);ib(b+20|0,F[b+24>>2]);ja(b)}}function xg(a){a=a|0;var b=0;if(a){b=F[a+8>>2];if(b){F[a+12>>2]=b;ja(b)}ja(a)}}function xh(a){a=a|0;if(!F[a+44>>2]){return 0}return $[F[F[a>>2]+48>>2]](a)|0}function ni(a){var b=0;while(1){if(a){a=a-1&a;b=b+1|0;continue}break}return b}function Qe(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(La(a,F[b+8>>2],0)){Ec(b,c,d)}}function vh(a,b){a=a|0;b=b|0;a=F[a+48>>2];return $[F[F[a>>2]+20>>2]](a,b)|0}function oi(a){var b=0;b=a&31;a=0-a&31;return(-1>>>b&-2)<<b|(-1<<a&-2)>>>a}
function oh(a,b){a=a|0;b=b|0;a=F[a+48>>2];return $[F[F[a>>2]+12>>2]](a,b)|0}function nh(a,b){a=a|0;b=b|0;a=F[a+48>>2];return $[F[F[a>>2]+16>>2]](a,b)|0}function Xa(){var a=0;a=ka(12);F[a>>2]=0;F[a+4>>2]=0;F[a+8>>2]=0;return a|0}function Wa(a){a=a|0;var b=0;if(a){b=F[a>>2];if(b){F[a+4>>2]=b;ja(b)}ja(a)}}function mf(a,b,c){a=a|0;b=b|0;c=c|0;F[a+32>>2]=c;F[a+28>>2]=b;return 1}function eg(a){a=a|0;if(a){Ca(a+12|0,F[a+16>>2]);Ba(a,F[a+4>>2]);ja(a)}}function Lb(a,b){a=a|0;b=b|0;if(b>>>0<=1){F[a+28>>2]=b}return b>>>0<2|0}function Fg(a,b){a=a|0;b=b|0;D[b+84|0]=1;F[b+72>>2]=F[b+68>>2];return 1}function kg(){var a=0;a=ka(8);F[a+4>>2]=-1;F[a>>2]=1032;return a|0}function Hf(a,b,c){a=a|0;b=b|0;c=c|0;return F[F[b+8>>2]+(c<<2)>>2]}function th(a){a=a|0;a=F[a+48>>2];return $[F[F[a>>2]+24>>2]](a)|0}function sh(a){a=a|0;a=F[a+48>>2];return $[F[F[a>>2]+28>>2]](a)|0}function ph(a){a=a|0;a=F[a+48>>2];return $[F[F[a>>2]+36>>2]](a)|0}function Rh(a,b){a=a|0;b=b|0;return zc(a+24|0,bb(a),F[a+8>>2])|0}function Bh(a,b){a=a|0;b=b|0;return xd(a+24|0,bb(a),F[a+8>>2])|0}function bg(a){a=a|0;if(a){if(D[a+15|0]<0){ja(F[a+4>>2])}ja(a)}}function Ke(a){a=a|0;if(!a){return 0}return(Fc(a,11164)|0)!=0|0}function Fe(a,b){a=a|0;b=b|0;F[a+12>>2]=-1;F[a+8>>2]=b;return 1}function hd(a,b){a=a|0;b=b|0;return $[F[F[a>>2]+12>>2]](a,b)|0}function Dh(a,b){a=a|0;b=b|0;return $[F[F[a>>2]+56>>2]](a,b)|0}function sc(a){a=Hc(Rb(8),a);F[a>>2]=11568;W(a|0,11600,1);v()}function mg(a,b){a=a|0;b=b|0;return M(J[F[a+8>>2]+(b<<2)>>2])}function Rd(a){a=Hc(Rb(8),a);F[a>>2]=11516;W(a|0,11548,1);v()}function $f(a,b){a=a|0;b=b|0;return M(J[F[a>>2]+(b<<2)>>2])}function gg(a){a=a|0;return(F[a+100>>2]-F[a+96>>2]|0)/12|0}function cg(a){a=a|0;return(D[a+15|0]<0?F[a+4>>2]:a+4|0)|0}function Re(a,b){a=a|0;b=b|0;return F[F[a+4>>2]+(b<<2)>>2]}function dd(a,b){a=a|0;b=b|0;return F[F[a>>2]+(b<<2)>>2]}function Yf(a,b){a=a|0;b=b|0;return E[F[a>>2]+(b<<1)>>1]}function Xf(a,b){a=a|0;b=b|0;return H[F[a>>2]+(b<<1)>>1]}function Vd(a,b,c){a=a|0;b=b|0;c=c|0;return xc(a,b,c)|0}function sb(a,b){var c=0;c=ka(b);F[a+4>>2]=b;F[a>>2]=c}function Kf(a,b,c){a=a|0;b=b|0;c=c|0;return Qc(b,c)|0}function Rc(a){F[a>>2]=10300;ma(a+4|0,0,80);return a}function ji(a){if(a){return 31-O(a-1^a)|0}return 32}function gd(a){a=a|0;return F[a+12>>2]-F[a+8>>2]>>2}function _f(a,b){a=a|0;b=b|0;return D[F[a>>2]+b|0]}function Zf(a,b){a=a|0;b=b|0;return G[F[a>>2]+b|0]}function Ie(a){a=a|0;return F[a+8>>2]-F[a+4>>2]>>2}function wb(a){a=a|0;if(a){$[F[F[a>>2]+4>>2]](a)}}function Jd(a,b){a=a|0;b=b|0;F[a+4>>2]=b;return 1}function ed(a){a=a|0;return F[a+4>>2]-F[a>>2]>>1}function dc(a){a=a|0;return F[a+4>>2]-F[a>>2]>>2}function fd(a){a=a|0;return F[a+4>>2]-F[a>>2]|0}function ke(a){a=a|0;return G[F[a+8>>2]+24|0]}function Qf(a,b){a=a|0;b=b|0;return F[b+8>>2]}function Ff(a,b){a=a|0;b=b|0;return F[b+4>>2]}function De(a,b,c){a=a|0;b=b|0;c=c|0;return 1}function Bg(a,b){a=a|0;b=b|0;return G[b+24|0]}function Oh(a){a=a|0;F[a>>2]=5928;return a|0}function Jh(a){a=a|0;F[a>>2]=6932;return a|0}function lg(a){a=a|0;return M(J[a+20>>2])}function Nh(a){a=a|0;F[a>>2]=5928;ja(a)}function Ih(a){a=a|0;F[a>>2]=6932;ja(a)}function ug(a){a=a|0;return F[a+88>>2]}function tg(a){a=a|0;return F[a+56>>2]}function qg(a){a=a|0;return F[a+40>>2]}function pg(a){a=a|0;return F[a+48>>2]}function og(a){a=a|0;return F[a+60>>2]}function ec(a){a=a|0;return F[a+80>>2]}function cb(a){a=a|0;return F[a+28>>2]}function vd(a){a=a|0;return F[a+8>>2]}function tc(a,b){a=a|0;b=b|0;return-1}function sg(a){a=a|0;return D[a+24|0]}function rg(a){a=a|0;return G[a+32|0]}function dg(a){a=a|0;return!F[a>>2]|0}function Wd(a,b){a=a|0;b=b|0;return 6}function Va(a){a=a|0;return F[a+4>>2]}function Rb(a){return Ub(a+80|0)+80|0}function Qh(a,b){a=a|0;b=b|0;return 2}function Ia(a,b){a=a|0;b=b|0;return 0}function Bc(a,b){a=a|0;b=b|0;return 1}function jd(a){a=a|0;return F[a>>2]}function wg(){return kb(ka(64))|0}function ig(){return Rc(ka(84))|0}function fc(a){a=a|0;if(a){ja(a)}}function Ag(){return Ja(ka(40))|0}function Qb(a){a=a|0;Cc(a);ja(a)}function Je(a){a=a|0;return 1161}function He(a){a=a|0;return 1235}function Ge(a){a=a|0;return 1201}function Pa(a){a=a|0;return a|0}function hh(a){a=a|0;ja(ud(a))}function gh(a){a=a|0;ja(sd(a))}function Ve(a){a=a|0;ja(tb(a))}function ua(a){a=a|0;return 1}function rc(a){a=a|0;return 4}function qc(a){a=a|0;return 5}function Xd(a){a=a|0;return 2}function Ua(a){a=a|0;return 0}function Nb(a){a=a|0;return 6}function Mh(a){a=a|0;return 3}function za(){Rd(1222);v()}function ta(){sc(1154);v()}function na(){Rd(1154);v()}function Ha(a){a=a|0;ja(a)}function Ra(a){a=a|0;v()}function ff(){return 10}function ef(){return 11}function df(){return 12}function vb(){return-1}function ub(){return 1}function lf(){return 5}function kf(){return 6}function jf(){return 7}function jb(){return 0}function hf(){return 8}function gf(){return 9}function cf(){return-2}function bf(){return-3}function bd(){return 3}function af(){return-4}function ad(){return 4}function _b(){return 2}function $e(){return-5}function Te(){V();v()}function Jc(a){a=a|0}function ae(){}
// EMSCRIPTEN_END_FUNCS
e=G;p(q);var $=c([null,Cc,Pa,Ha,Xd,ii,jh,ag,zc,xe,uc,jg,Wd,Qh,Pa,Pg,Hg,ua,yh,mh,kh,xd,fh,qd,Wd,Bg,We,Ra,mf,$c,Ue,Re,Ie,cb,Ia,Te,Bc,ua,Ae,ze,Ac,Fe,Ee,De,Bc,Ce,Be,re,qe,ye,we,pe,ve,ue,te,se,wc,vc,Ac,oe,ne,xc,me,ke,le,je,Ob,ua,Va,ob,Ua,tc,Ia,Ua,ua,ie,he,Ra,Ra,ge,fe,rc,ob,ee,de,ce,be,qc,pc,ua,Ia,oc,$d,hi,gi,fi,Nb,_d,ua,Ia,Zd,Yd,ei,Pa,Ha,Lb,cb,Mb,Ra,Ob,ua,ob,di,Ra,ci,bi,rc,ob,ai,$h,_h,Zh,qc,pc,ua,Ia,oc,$d,Yh,Xh,Wh,Nb,_d,ua,Ia,Zd,Yd,Vh,Pa,Ha,Lb,cb,Kb,Ra,Ob,Ua,ua,Uh,wc,vc,Th,Sh,Vd,Ph,Xd,Rh,Oh,Nh,Nb,Va,Ud,ua,Ia,Td,ua,Mh,Sd,Lh,Pa,Ha,Lb,cb,Mb,Jh,Ih,Nb,Ud,ua,Ia,Td,Sd,Hh,Pa,Ha,Lb,cb,Kb,Pa,Ha,Ua,ua,Ua,tc,Ia,Kh,Gh,Ah,zh,Fh,Eh,Vd,Dh,Ch,Bh,wh,Ra,ua,ua,xh,Dg,Cg,ua,Ua,Ia,Ia,rh,qh,uh,vh,sh,ph,oh,nh,th,ud,hh,Jd,Id,Hd,Gd,lh,ua,Va,vd,sd,gh,Jd,Id,Hd,Gd,ih,ua,Va,vd,Ed,eh,Fd,dh,ch,$g,_g,Zg,Yg,ah,Xg,bh,Wg,Vg,Tg,Sg,Rg,Qg,Ug,Og,Ng,Mg,Lg,Kg,Gg,Ig,Jg,Pa,Ha,Fg,Eg,Ra,Ua,ua,_e,Ze,Ye,Xe,tb,Ve,Oc,Nc,Pa,Ha,Jc,Jc,Se,Le,Ne,Qe,Ha,Me,Oe,Pe,Ha,He,Ha,Ge,Ha,Je,Qb,Va,Qb,Qb]);function aa(){return C.byteLength/65536|0}function fa(ga){ga=ga|0;var ba=aa()|0;var ca=ba+ga|0;if(ba<ca&&ca<65536){var da=new ArrayBuffer(L(ca,65536));var ea=new Int8Array(da);ea.set(D);D=new Int8Array(da);E=new Int16Array(da);F=new Int32Array(da);G=new Uint8Array(da);H=new Uint16Array(da);I=new Uint32Array(da);J=new Float32Array(da);K=new Float64Array(da);C=da;B.buffer=C;e=G}return ba}return{"f":ae,"g":$,"h":fc,"i":Ag,"j":zg,"k":fc,"l":yg,"m":jd,"n":xg,"o":wg,"p":fc,"q":vg,"r":ec,"s":ug,"t":tg,"u":cb,"v":sg,"w":rg,"x":qg,"y":pg,"z":og,"A":xa,"B":ng,"C":hd,"D":Va,"E":mg,"F":lg,"G":wb,"H":kg,"I":hd,"J":Va,"K":wb,"L":ig,"M":gd,"N":ec,"O":wb,"P":hg,"Q":gg,"R":gd,"S":ec,"T":wb,"U":fg,"V":eg,"W":jd,"X":dg,"Y":cg,"Z":bg,"_":Xa,"$":$f,"aa":dc,"ba":Wa,"ca":Xa,"da":_f,"ea":fd,"fa":Wa,"ga":Xa,"ha":Zf,"ia":fd,"ja":Wa,"ka":Xa,"la":Yf,"ma":ed,"na":Wa,"oa":Xa,"pa":Xf,"qa":ed,"ra":Wa,"sa":Xa,"ta":dd,"ua":dc,"va":Wa,"wa":Xa,"xa":dd,"ya":dc,"za":Wa,"Aa":Wf,"Ba":Vf,"Ca":Uf,"Da":Tf,"Ea":Sf,"Fa":Rf,"Ga":Qf,"Ha":Pf,"Ia":Of,"Ja":Nf,"Ka":Mf,"La":Lf,"Ma":Kf,"Na":Jf,"Oa":If,"Pa":Hf,"Qa":Gf,"Ra":Ff,"Sa":Ef,"Ta":Df,"Ua":Cf,"Va":Bf,"Wa":Af,"Xa":zf,"Ya":yf,"Za":cd,"_a":xf,"$a":wf,"ab":vf,"bb":uf,"cb":cd,"db":tf,"eb":sf,"fb":rf,"gb":qf,"hb":pf,"ib":of,"jb":nf,"kb":vb,"lb":jb,"mb":ub,"nb":_b,"ob":vb,"pb":jb,"qb":ub,"rb":_b,"sb":bd,"tb":ad,"ub":vb,"vb":jb,"wb":ub,"xb":jb,"yb":ub,"zb":_b,"Ab":bd,"Bb":ad,"Cb":lf,"Db":kf,"Eb":jf,"Fb":hf,"Gb":gf,"Hb":ff,"Ib":ef,"Jb":df,"Kb":jb,"Lb":vb,"Mb":cf,"Nb":bf,"Ob":af,"Pb":$e,"Qb":Ub,"Rb":ja,"Sb":Ke}}return ha(ia)}
// EMSCRIPTEN_END_ASM


)(info)},instantiate:function(binary,info){return{then:function(ok){var module=new WebAssembly.Module(binary);ok({"instance":new WebAssembly.Instance(module,info)})}}},RuntimeError:Error};wasmBinary=[];if(typeof WebAssembly!="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heapOrArray,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;assert(INITIAL_MEMORY>=65536,"INITIAL_MEMORY should be larger than STACK_SIZE, was "+INITIAL_MEMORY+"! (STACK_SIZE="+65536+")");if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"]}else{wasmMemory=new WebAssembly.Memory({"initial":INITIAL_MEMORY/65536,"maximum":2147483648/65536})}updateMemoryViews();INITIAL_MEMORY=wasmMemory.buffer.byteLength;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function keepRuntimeAlive(){return noExitRuntime}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}function isFileURI(filename){return filename.startsWith("file://")}var wasmBinaryFile;wasmBinaryFile="draco_decoder_gltf.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}var binary=tryParseAsDataURI(file);if(binary){return binary}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch=="function"&&!isFileURI(wasmBinaryFile)){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}else{if(readAsync){return new Promise(function(resolve,reject){readAsync(wasmBinaryFile,function(response){resolve(new Uint8Array(response))},reject)})}}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":wasmImports};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmTable=Module["asm"]["g"];addOnInit(Module["asm"]["f"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(wasmBinaryFile)&&!isFileURI(wasmBinaryFile)&&!ENVIRONMENT_IS_NODE&&typeof fetch=="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);readyPromiseReject(e)}}instantiateAsync().catch(readyPromiseReject);return{}}function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){callbacks.shift()(Module)}}function intArrayToString(array){var ret=[];for(var i=0;i<array.length;i++){var chr=array[i];if(chr>255){chr&=255}ret.push(String.fromCharCode(chr))}return ret.join("")}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=prev-1;return prev===1};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function _abort(){abort("")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function getHeapMax(){return 2147483648}function emscripten_realloc_buffer(size){var b=wasmMemory.buffer;try{wasmMemory.grow(size-b.byteLength+65535>>>16);updateMemoryViews();return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}let alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var decodeBase64=typeof atob=="function"?atob:function(input){var keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var output="";var chr1,chr2,chr3;var enc1,enc2,enc3,enc4;var i=0;input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");do{enc1=keyStr.indexOf(input.charAt(i++));enc2=keyStr.indexOf(input.charAt(i++));enc3=keyStr.indexOf(input.charAt(i++));enc4=keyStr.indexOf(input.charAt(i++));chr1=enc1<<2|enc2>>4;chr2=(enc2&15)<<4|enc3>>2;chr3=(enc3&3)<<6|enc4;output=output+String.fromCharCode(chr1);if(enc3!==64){output=output+String.fromCharCode(chr2)}if(enc4!==64){output=output+String.fromCharCode(chr3)}}while(i<input.length);return output};function intArrayFromBase64(s){if(typeof ENVIRONMENT_IS_NODE=="boolean"&&ENVIRONMENT_IS_NODE){var buf=Buffer.from(s,"base64");return new Uint8Array(buf["buffer"],buf["byteOffset"],buf["byteLength"])}try{var decoded=decodeBase64(s);var bytes=new Uint8Array(decoded.length);for(var i=0;i<decoded.length;++i){bytes[i]=decoded.charCodeAt(i)}return bytes}catch(_){throw new Error("Converting base64 string to bytes failed.")}}function tryParseAsDataURI(filename){if(!isDataURI(filename)){return}return intArrayFromBase64(filename.slice(dataURIPrefix.length))}var wasmImports={"c":___cxa_throw,"b":_abort,"e":_emscripten_memcpy_big,"d":_emscripten_resize_heap,"a":wasmMemory};var asm=createWasm();var ___wasm_call_ctors=function(){return(___wasm_call_ctors=Module["asm"]["f"]).apply(null,arguments)};var _emscripten_bind_VoidPtr___destroy___0=Module["_emscripten_bind_VoidPtr___destroy___0"]=function(){return(_emscripten_bind_VoidPtr___destroy___0=Module["_emscripten_bind_VoidPtr___destroy___0"]=Module["asm"]["h"]).apply(null,arguments)};var _emscripten_bind_DecoderBuffer_DecoderBuffer_0=Module["_emscripten_bind_DecoderBuffer_DecoderBuffer_0"]=function(){return(_emscripten_bind_DecoderBuffer_DecoderBuffer_0=Module["_emscripten_bind_DecoderBuffer_DecoderBuffer_0"]=Module["asm"]["i"]).apply(null,arguments)};var _emscripten_bind_DecoderBuffer_Init_2=Module["_emscripten_bind_DecoderBuffer_Init_2"]=function(){return(_emscripten_bind_DecoderBuffer_Init_2=Module["_emscripten_bind_DecoderBuffer_Init_2"]=Module["asm"]["j"]).apply(null,arguments)};var _emscripten_bind_DecoderBuffer___destroy___0=Module["_emscripten_bind_DecoderBuffer___destroy___0"]=function(){return(_emscripten_bind_DecoderBuffer___destroy___0=Module["_emscripten_bind_DecoderBuffer___destroy___0"]=Module["asm"]["k"]).apply(null,arguments)};var _emscripten_bind_AttributeTransformData_AttributeTransformData_0=Module["_emscripten_bind_AttributeTransformData_AttributeTransformData_0"]=function(){return(_emscripten_bind_AttributeTransformData_AttributeTransformData_0=Module["_emscripten_bind_AttributeTransformData_AttributeTransformData_0"]=Module["asm"]["l"]).apply(null,arguments)};var _emscripten_bind_AttributeTransformData_transform_type_0=Module["_emscripten_bind_AttributeTransformData_transform_type_0"]=function(){return(_emscripten_bind_AttributeTransformData_transform_type_0=Module["_emscripten_bind_AttributeTransformData_transform_type_0"]=Module["asm"]["m"]).apply(null,arguments)};var _emscripten_bind_AttributeTransformData___destroy___0=Module["_emscripten_bind_AttributeTransformData___destroy___0"]=function(){return(_emscripten_bind_AttributeTransformData___destroy___0=Module["_emscripten_bind_AttributeTransformData___destroy___0"]=Module["asm"]["n"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute_GeometryAttribute_0=Module["_emscripten_bind_GeometryAttribute_GeometryAttribute_0"]=function(){return(_emscripten_bind_GeometryAttribute_GeometryAttribute_0=Module["_emscripten_bind_GeometryAttribute_GeometryAttribute_0"]=Module["asm"]["o"]).apply(null,arguments)};var _emscripten_bind_GeometryAttribute___destroy___0=Module["_emscripten_bind_GeometryAttribute___destroy___0"]=function(){return(_emscripten_bind_GeometryAttribute___destroy___0=Module["_emscripten_bind_GeometryAttribute___destroy___0"]=Module["asm"]["p"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_PointAttribute_0=Module["_emscripten_bind_PointAttribute_PointAttribute_0"]=function(){return(_emscripten_bind_PointAttribute_PointAttribute_0=Module["_emscripten_bind_PointAttribute_PointAttribute_0"]=Module["asm"]["q"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_size_0=Module["_emscripten_bind_PointAttribute_size_0"]=function(){return(_emscripten_bind_PointAttribute_size_0=Module["_emscripten_bind_PointAttribute_size_0"]=Module["asm"]["r"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_GetAttributeTransformData_0=Module["_emscripten_bind_PointAttribute_GetAttributeTransformData_0"]=function(){return(_emscripten_bind_PointAttribute_GetAttributeTransformData_0=Module["_emscripten_bind_PointAttribute_GetAttributeTransformData_0"]=Module["asm"]["s"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_attribute_type_0=Module["_emscripten_bind_PointAttribute_attribute_type_0"]=function(){return(_emscripten_bind_PointAttribute_attribute_type_0=Module["_emscripten_bind_PointAttribute_attribute_type_0"]=Module["asm"]["t"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_data_type_0=Module["_emscripten_bind_PointAttribute_data_type_0"]=function(){return(_emscripten_bind_PointAttribute_data_type_0=Module["_emscripten_bind_PointAttribute_data_type_0"]=Module["asm"]["u"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_num_components_0=Module["_emscripten_bind_PointAttribute_num_components_0"]=function(){return(_emscripten_bind_PointAttribute_num_components_0=Module["_emscripten_bind_PointAttribute_num_components_0"]=Module["asm"]["v"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_normalized_0=Module["_emscripten_bind_PointAttribute_normalized_0"]=function(){return(_emscripten_bind_PointAttribute_normalized_0=Module["_emscripten_bind_PointAttribute_normalized_0"]=Module["asm"]["w"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_byte_stride_0=Module["_emscripten_bind_PointAttribute_byte_stride_0"]=function(){return(_emscripten_bind_PointAttribute_byte_stride_0=Module["_emscripten_bind_PointAttribute_byte_stride_0"]=Module["asm"]["x"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_byte_offset_0=Module["_emscripten_bind_PointAttribute_byte_offset_0"]=function(){return(_emscripten_bind_PointAttribute_byte_offset_0=Module["_emscripten_bind_PointAttribute_byte_offset_0"]=Module["asm"]["y"]).apply(null,arguments)};var _emscripten_bind_PointAttribute_unique_id_0=Module["_emscripten_bind_PointAttribute_unique_id_0"]=function(){return(_emscripten_bind_PointAttribute_unique_id_0=Module["_emscripten_bind_PointAttribute_unique_id_0"]=Module["asm"]["z"]).apply(null,arguments)};var _emscripten_bind_PointAttribute___destroy___0=Module["_emscripten_bind_PointAttribute___destroy___0"]=function(){return(_emscripten_bind_PointAttribute___destroy___0=Module["_emscripten_bind_PointAttribute___destroy___0"]=Module["asm"]["A"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=Module["_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=Module["_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0"]=Module["asm"]["B"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1"]=Module["asm"]["C"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=Module["_emscripten_bind_AttributeQuantizationTransform_quantization_bits_0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=Module["_emscripten_bind_AttributeQuantizationTransform_quantization_bits_0"]=Module["asm"]["D"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_min_value_1=Module["_emscripten_bind_AttributeQuantizationTransform_min_value_1"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_min_value_1=Module["_emscripten_bind_AttributeQuantizationTransform_min_value_1"]=Module["asm"]["E"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform_range_0=Module["_emscripten_bind_AttributeQuantizationTransform_range_0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform_range_0=Module["_emscripten_bind_AttributeQuantizationTransform_range_0"]=Module["asm"]["F"]).apply(null,arguments)};var _emscripten_bind_AttributeQuantizationTransform___destroy___0=Module["_emscripten_bind_AttributeQuantizationTransform___destroy___0"]=function(){return(_emscripten_bind_AttributeQuantizationTransform___destroy___0=Module["_emscripten_bind_AttributeQuantizationTransform___destroy___0"]=Module["asm"]["G"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=Module["_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0"]=function(){return(_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=Module["_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0"]=Module["asm"]["H"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1"]=function(){return(_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=Module["_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1"]=Module["asm"]["I"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=Module["_emscripten_bind_AttributeOctahedronTransform_quantization_bits_0"]=function(){return(_emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=Module["_emscripten_bind_AttributeOctahedronTransform_quantization_bits_0"]=Module["asm"]["J"]).apply(null,arguments)};var _emscripten_bind_AttributeOctahedronTransform___destroy___0=Module["_emscripten_bind_AttributeOctahedronTransform___destroy___0"]=function(){return(_emscripten_bind_AttributeOctahedronTransform___destroy___0=Module["_emscripten_bind_AttributeOctahedronTransform___destroy___0"]=Module["asm"]["K"]).apply(null,arguments)};var _emscripten_bind_PointCloud_PointCloud_0=Module["_emscripten_bind_PointCloud_PointCloud_0"]=function(){return(_emscripten_bind_PointCloud_PointCloud_0=Module["_emscripten_bind_PointCloud_PointCloud_0"]=Module["asm"]["L"]).apply(null,arguments)};var _emscripten_bind_PointCloud_num_attributes_0=Module["_emscripten_bind_PointCloud_num_attributes_0"]=function(){return(_emscripten_bind_PointCloud_num_attributes_0=Module["_emscripten_bind_PointCloud_num_attributes_0"]=Module["asm"]["M"]).apply(null,arguments)};var _emscripten_bind_PointCloud_num_points_0=Module["_emscripten_bind_PointCloud_num_points_0"]=function(){return(_emscripten_bind_PointCloud_num_points_0=Module["_emscripten_bind_PointCloud_num_points_0"]=Module["asm"]["N"]).apply(null,arguments)};var _emscripten_bind_PointCloud___destroy___0=Module["_emscripten_bind_PointCloud___destroy___0"]=function(){return(_emscripten_bind_PointCloud___destroy___0=Module["_emscripten_bind_PointCloud___destroy___0"]=Module["asm"]["O"]).apply(null,arguments)};var _emscripten_bind_Mesh_Mesh_0=Module["_emscripten_bind_Mesh_Mesh_0"]=function(){return(_emscripten_bind_Mesh_Mesh_0=Module["_emscripten_bind_Mesh_Mesh_0"]=Module["asm"]["P"]).apply(null,arguments)};var _emscripten_bind_Mesh_num_faces_0=Module["_emscripten_bind_Mesh_num_faces_0"]=function(){return(_emscripten_bind_Mesh_num_faces_0=Module["_emscripten_bind_Mesh_num_faces_0"]=Module["asm"]["Q"]).apply(null,arguments)};var _emscripten_bind_Mesh_num_attributes_0=Module["_emscripten_bind_Mesh_num_attributes_0"]=function(){return(_emscripten_bind_Mesh_num_attributes_0=Module["_emscripten_bind_Mesh_num_attributes_0"]=Module["asm"]["R"]).apply(null,arguments)};var _emscripten_bind_Mesh_num_points_0=Module["_emscripten_bind_Mesh_num_points_0"]=function(){return(_emscripten_bind_Mesh_num_points_0=Module["_emscripten_bind_Mesh_num_points_0"]=Module["asm"]["S"]).apply(null,arguments)};var _emscripten_bind_Mesh___destroy___0=Module["_emscripten_bind_Mesh___destroy___0"]=function(){return(_emscripten_bind_Mesh___destroy___0=Module["_emscripten_bind_Mesh___destroy___0"]=Module["asm"]["T"]).apply(null,arguments)};var _emscripten_bind_Metadata_Metadata_0=Module["_emscripten_bind_Metadata_Metadata_0"]=function(){return(_emscripten_bind_Metadata_Metadata_0=Module["_emscripten_bind_Metadata_Metadata_0"]=Module["asm"]["U"]).apply(null,arguments)};var _emscripten_bind_Metadata___destroy___0=Module["_emscripten_bind_Metadata___destroy___0"]=function(){return(_emscripten_bind_Metadata___destroy___0=Module["_emscripten_bind_Metadata___destroy___0"]=Module["asm"]["V"]).apply(null,arguments)};var _emscripten_bind_Status_code_0=Module["_emscripten_bind_Status_code_0"]=function(){return(_emscripten_bind_Status_code_0=Module["_emscripten_bind_Status_code_0"]=Module["asm"]["W"]).apply(null,arguments)};var _emscripten_bind_Status_ok_0=Module["_emscripten_bind_Status_ok_0"]=function(){return(_emscripten_bind_Status_ok_0=Module["_emscripten_bind_Status_ok_0"]=Module["asm"]["X"]).apply(null,arguments)};var _emscripten_bind_Status_error_msg_0=Module["_emscripten_bind_Status_error_msg_0"]=function(){return(_emscripten_bind_Status_error_msg_0=Module["_emscripten_bind_Status_error_msg_0"]=Module["asm"]["Y"]).apply(null,arguments)};var _emscripten_bind_Status___destroy___0=Module["_emscripten_bind_Status___destroy___0"]=function(){return(_emscripten_bind_Status___destroy___0=Module["_emscripten_bind_Status___destroy___0"]=Module["asm"]["Z"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=Module["_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0"]=function(){return(_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=Module["_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0"]=Module["asm"]["_"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array_GetValue_1=Module["_emscripten_bind_DracoFloat32Array_GetValue_1"]=function(){return(_emscripten_bind_DracoFloat32Array_GetValue_1=Module["_emscripten_bind_DracoFloat32Array_GetValue_1"]=Module["asm"]["$"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array_size_0=Module["_emscripten_bind_DracoFloat32Array_size_0"]=function(){return(_emscripten_bind_DracoFloat32Array_size_0=Module["_emscripten_bind_DracoFloat32Array_size_0"]=Module["asm"]["aa"]).apply(null,arguments)};var _emscripten_bind_DracoFloat32Array___destroy___0=Module["_emscripten_bind_DracoFloat32Array___destroy___0"]=function(){return(_emscripten_bind_DracoFloat32Array___destroy___0=Module["_emscripten_bind_DracoFloat32Array___destroy___0"]=Module["asm"]["ba"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array_DracoInt8Array_0=Module["_emscripten_bind_DracoInt8Array_DracoInt8Array_0"]=function(){return(_emscripten_bind_DracoInt8Array_DracoInt8Array_0=Module["_emscripten_bind_DracoInt8Array_DracoInt8Array_0"]=Module["asm"]["ca"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array_GetValue_1=Module["_emscripten_bind_DracoInt8Array_GetValue_1"]=function(){return(_emscripten_bind_DracoInt8Array_GetValue_1=Module["_emscripten_bind_DracoInt8Array_GetValue_1"]=Module["asm"]["da"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array_size_0=Module["_emscripten_bind_DracoInt8Array_size_0"]=function(){return(_emscripten_bind_DracoInt8Array_size_0=Module["_emscripten_bind_DracoInt8Array_size_0"]=Module["asm"]["ea"]).apply(null,arguments)};var _emscripten_bind_DracoInt8Array___destroy___0=Module["_emscripten_bind_DracoInt8Array___destroy___0"]=function(){return(_emscripten_bind_DracoInt8Array___destroy___0=Module["_emscripten_bind_DracoInt8Array___destroy___0"]=Module["asm"]["fa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=Module["_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0"]=function(){return(_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0=Module["_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0"]=Module["asm"]["ga"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array_GetValue_1=Module["_emscripten_bind_DracoUInt8Array_GetValue_1"]=function(){return(_emscripten_bind_DracoUInt8Array_GetValue_1=Module["_emscripten_bind_DracoUInt8Array_GetValue_1"]=Module["asm"]["ha"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array_size_0=Module["_emscripten_bind_DracoUInt8Array_size_0"]=function(){return(_emscripten_bind_DracoUInt8Array_size_0=Module["_emscripten_bind_DracoUInt8Array_size_0"]=Module["asm"]["ia"]).apply(null,arguments)};var _emscripten_bind_DracoUInt8Array___destroy___0=Module["_emscripten_bind_DracoUInt8Array___destroy___0"]=function(){return(_emscripten_bind_DracoUInt8Array___destroy___0=Module["_emscripten_bind_DracoUInt8Array___destroy___0"]=Module["asm"]["ja"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array_DracoInt16Array_0=Module["_emscripten_bind_DracoInt16Array_DracoInt16Array_0"]=function(){return(_emscripten_bind_DracoInt16Array_DracoInt16Array_0=Module["_emscripten_bind_DracoInt16Array_DracoInt16Array_0"]=Module["asm"]["ka"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array_GetValue_1=Module["_emscripten_bind_DracoInt16Array_GetValue_1"]=function(){return(_emscripten_bind_DracoInt16Array_GetValue_1=Module["_emscripten_bind_DracoInt16Array_GetValue_1"]=Module["asm"]["la"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array_size_0=Module["_emscripten_bind_DracoInt16Array_size_0"]=function(){return(_emscripten_bind_DracoInt16Array_size_0=Module["_emscripten_bind_DracoInt16Array_size_0"]=Module["asm"]["ma"]).apply(null,arguments)};var _emscripten_bind_DracoInt16Array___destroy___0=Module["_emscripten_bind_DracoInt16Array___destroy___0"]=function(){return(_emscripten_bind_DracoInt16Array___destroy___0=Module["_emscripten_bind_DracoInt16Array___destroy___0"]=Module["asm"]["na"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=Module["_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0"]=function(){return(_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0=Module["_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0"]=Module["asm"]["oa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array_GetValue_1=Module["_emscripten_bind_DracoUInt16Array_GetValue_1"]=function(){return(_emscripten_bind_DracoUInt16Array_GetValue_1=Module["_emscripten_bind_DracoUInt16Array_GetValue_1"]=Module["asm"]["pa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array_size_0=Module["_emscripten_bind_DracoUInt16Array_size_0"]=function(){return(_emscripten_bind_DracoUInt16Array_size_0=Module["_emscripten_bind_DracoUInt16Array_size_0"]=Module["asm"]["qa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt16Array___destroy___0=Module["_emscripten_bind_DracoUInt16Array___destroy___0"]=function(){return(_emscripten_bind_DracoUInt16Array___destroy___0=Module["_emscripten_bind_DracoUInt16Array___destroy___0"]=Module["asm"]["ra"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array_DracoInt32Array_0=Module["_emscripten_bind_DracoInt32Array_DracoInt32Array_0"]=function(){return(_emscripten_bind_DracoInt32Array_DracoInt32Array_0=Module["_emscripten_bind_DracoInt32Array_DracoInt32Array_0"]=Module["asm"]["sa"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array_GetValue_1=Module["_emscripten_bind_DracoInt32Array_GetValue_1"]=function(){return(_emscripten_bind_DracoInt32Array_GetValue_1=Module["_emscripten_bind_DracoInt32Array_GetValue_1"]=Module["asm"]["ta"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array_size_0=Module["_emscripten_bind_DracoInt32Array_size_0"]=function(){return(_emscripten_bind_DracoInt32Array_size_0=Module["_emscripten_bind_DracoInt32Array_size_0"]=Module["asm"]["ua"]).apply(null,arguments)};var _emscripten_bind_DracoInt32Array___destroy___0=Module["_emscripten_bind_DracoInt32Array___destroy___0"]=function(){return(_emscripten_bind_DracoInt32Array___destroy___0=Module["_emscripten_bind_DracoInt32Array___destroy___0"]=Module["asm"]["va"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=Module["_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0"]=function(){return(_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0=Module["_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0"]=Module["asm"]["wa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array_GetValue_1=Module["_emscripten_bind_DracoUInt32Array_GetValue_1"]=function(){return(_emscripten_bind_DracoUInt32Array_GetValue_1=Module["_emscripten_bind_DracoUInt32Array_GetValue_1"]=Module["asm"]["xa"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array_size_0=Module["_emscripten_bind_DracoUInt32Array_size_0"]=function(){return(_emscripten_bind_DracoUInt32Array_size_0=Module["_emscripten_bind_DracoUInt32Array_size_0"]=Module["asm"]["ya"]).apply(null,arguments)};var _emscripten_bind_DracoUInt32Array___destroy___0=Module["_emscripten_bind_DracoUInt32Array___destroy___0"]=function(){return(_emscripten_bind_DracoUInt32Array___destroy___0=Module["_emscripten_bind_DracoUInt32Array___destroy___0"]=Module["asm"]["za"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_MetadataQuerier_0=Module["_emscripten_bind_MetadataQuerier_MetadataQuerier_0"]=function(){return(_emscripten_bind_MetadataQuerier_MetadataQuerier_0=Module["_emscripten_bind_MetadataQuerier_MetadataQuerier_0"]=Module["asm"]["Aa"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_HasEntry_2=Module["_emscripten_bind_MetadataQuerier_HasEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_HasEntry_2=Module["_emscripten_bind_MetadataQuerier_HasEntry_2"]=Module["asm"]["Ba"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetIntEntry_2=Module["_emscripten_bind_MetadataQuerier_GetIntEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetIntEntry_2=Module["_emscripten_bind_MetadataQuerier_GetIntEntry_2"]=Module["asm"]["Ca"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetIntEntryArray_3=Module["_emscripten_bind_MetadataQuerier_GetIntEntryArray_3"]=function(){return(_emscripten_bind_MetadataQuerier_GetIntEntryArray_3=Module["_emscripten_bind_MetadataQuerier_GetIntEntryArray_3"]=Module["asm"]["Da"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetDoubleEntry_2=Module["_emscripten_bind_MetadataQuerier_GetDoubleEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetDoubleEntry_2=Module["_emscripten_bind_MetadataQuerier_GetDoubleEntry_2"]=Module["asm"]["Ea"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetStringEntry_2=Module["_emscripten_bind_MetadataQuerier_GetStringEntry_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetStringEntry_2=Module["_emscripten_bind_MetadataQuerier_GetStringEntry_2"]=Module["asm"]["Fa"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_NumEntries_1=Module["_emscripten_bind_MetadataQuerier_NumEntries_1"]=function(){return(_emscripten_bind_MetadataQuerier_NumEntries_1=Module["_emscripten_bind_MetadataQuerier_NumEntries_1"]=Module["asm"]["Ga"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier_GetEntryName_2=Module["_emscripten_bind_MetadataQuerier_GetEntryName_2"]=function(){return(_emscripten_bind_MetadataQuerier_GetEntryName_2=Module["_emscripten_bind_MetadataQuerier_GetEntryName_2"]=Module["asm"]["Ha"]).apply(null,arguments)};var _emscripten_bind_MetadataQuerier___destroy___0=Module["_emscripten_bind_MetadataQuerier___destroy___0"]=function(){return(_emscripten_bind_MetadataQuerier___destroy___0=Module["_emscripten_bind_MetadataQuerier___destroy___0"]=Module["asm"]["Ia"]).apply(null,arguments)};var _emscripten_bind_Decoder_Decoder_0=Module["_emscripten_bind_Decoder_Decoder_0"]=function(){return(_emscripten_bind_Decoder_Decoder_0=Module["_emscripten_bind_Decoder_Decoder_0"]=Module["asm"]["Ja"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeArrayToPointCloud_3=Module["_emscripten_bind_Decoder_DecodeArrayToPointCloud_3"]=function(){return(_emscripten_bind_Decoder_DecodeArrayToPointCloud_3=Module["_emscripten_bind_Decoder_DecodeArrayToPointCloud_3"]=Module["asm"]["Ka"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeArrayToMesh_3=Module["_emscripten_bind_Decoder_DecodeArrayToMesh_3"]=function(){return(_emscripten_bind_Decoder_DecodeArrayToMesh_3=Module["_emscripten_bind_Decoder_DecodeArrayToMesh_3"]=Module["asm"]["La"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeId_2=Module["_emscripten_bind_Decoder_GetAttributeId_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeId_2=Module["_emscripten_bind_Decoder_GetAttributeId_2"]=Module["asm"]["Ma"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeIdByName_2=Module["_emscripten_bind_Decoder_GetAttributeIdByName_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeIdByName_2=Module["_emscripten_bind_Decoder_GetAttributeIdByName_2"]=Module["asm"]["Na"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=Module["_emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=Module["_emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3"]=Module["asm"]["Oa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttribute_2=Module["_emscripten_bind_Decoder_GetAttribute_2"]=function(){return(_emscripten_bind_Decoder_GetAttribute_2=Module["_emscripten_bind_Decoder_GetAttribute_2"]=Module["asm"]["Pa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeByUniqueId_2=Module["_emscripten_bind_Decoder_GetAttributeByUniqueId_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeByUniqueId_2=Module["_emscripten_bind_Decoder_GetAttributeByUniqueId_2"]=Module["asm"]["Qa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetMetadata_1=Module["_emscripten_bind_Decoder_GetMetadata_1"]=function(){return(_emscripten_bind_Decoder_GetMetadata_1=Module["_emscripten_bind_Decoder_GetMetadata_1"]=Module["asm"]["Ra"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeMetadata_2=Module["_emscripten_bind_Decoder_GetAttributeMetadata_2"]=function(){return(_emscripten_bind_Decoder_GetAttributeMetadata_2=Module["_emscripten_bind_Decoder_GetAttributeMetadata_2"]=Module["asm"]["Sa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetFaceFromMesh_3=Module["_emscripten_bind_Decoder_GetFaceFromMesh_3"]=function(){return(_emscripten_bind_Decoder_GetFaceFromMesh_3=Module["_emscripten_bind_Decoder_GetFaceFromMesh_3"]=Module["asm"]["Ta"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=Module["_emscripten_bind_Decoder_GetTriangleStripsFromMesh_2"]=function(){return(_emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=Module["_emscripten_bind_Decoder_GetTriangleStripsFromMesh_2"]=Module["asm"]["Ua"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetTrianglesUInt16Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt16Array_3"]=function(){return(_emscripten_bind_Decoder_GetTrianglesUInt16Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt16Array_3"]=Module["asm"]["Va"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetTrianglesUInt32Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt32Array_3"]=function(){return(_emscripten_bind_Decoder_GetTrianglesUInt32Array_3=Module["_emscripten_bind_Decoder_GetTrianglesUInt32Array_3"]=Module["asm"]["Wa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeFloat_3=Module["_emscripten_bind_Decoder_GetAttributeFloat_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeFloat_3=Module["_emscripten_bind_Decoder_GetAttributeFloat_3"]=Module["asm"]["Xa"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3"]=Module["asm"]["Ya"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3"]=Module["asm"]["Za"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3"]=Module["asm"]["_a"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3"]=Module["asm"]["$a"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3"]=Module["asm"]["ab"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3"]=Module["asm"]["bb"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3"]=Module["asm"]["cb"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3"]=function(){return(_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3=Module["_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3"]=Module["asm"]["db"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5"]=function(){return(_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5=Module["_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5"]=Module["asm"]["eb"]).apply(null,arguments)};var _emscripten_bind_Decoder_SkipAttributeTransform_1=Module["_emscripten_bind_Decoder_SkipAttributeTransform_1"]=function(){return(_emscripten_bind_Decoder_SkipAttributeTransform_1=Module["_emscripten_bind_Decoder_SkipAttributeTransform_1"]=Module["asm"]["fb"]).apply(null,arguments)};var _emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=Module["_emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1"]=function(){return(_emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1=Module["_emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1"]=Module["asm"]["gb"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeBufferToPointCloud_2=Module["_emscripten_bind_Decoder_DecodeBufferToPointCloud_2"]=function(){return(_emscripten_bind_Decoder_DecodeBufferToPointCloud_2=Module["_emscripten_bind_Decoder_DecodeBufferToPointCloud_2"]=Module["asm"]["hb"]).apply(null,arguments)};var _emscripten_bind_Decoder_DecodeBufferToMesh_2=Module["_emscripten_bind_Decoder_DecodeBufferToMesh_2"]=function(){return(_emscripten_bind_Decoder_DecodeBufferToMesh_2=Module["_emscripten_bind_Decoder_DecodeBufferToMesh_2"]=Module["asm"]["ib"]).apply(null,arguments)};var _emscripten_bind_Decoder___destroy___0=Module["_emscripten_bind_Decoder___destroy___0"]=function(){return(_emscripten_bind_Decoder___destroy___0=Module["_emscripten_bind_Decoder___destroy___0"]=Module["asm"]["jb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM"]=Module["asm"]["kb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM"]=Module["asm"]["lb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM"]=Module["asm"]["mb"]).apply(null,arguments)};var _emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM"]=function(){return(_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=Module["_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM"]=Module["asm"]["nb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_INVALID=Module["_emscripten_enum_draco_GeometryAttribute_Type_INVALID"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_INVALID=Module["_emscripten_enum_draco_GeometryAttribute_Type_INVALID"]=Module["asm"]["ob"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_POSITION=Module["_emscripten_enum_draco_GeometryAttribute_Type_POSITION"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_POSITION=Module["_emscripten_enum_draco_GeometryAttribute_Type_POSITION"]=Module["asm"]["pb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_NORMAL=Module["_emscripten_enum_draco_GeometryAttribute_Type_NORMAL"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_NORMAL=Module["_emscripten_enum_draco_GeometryAttribute_Type_NORMAL"]=Module["asm"]["qb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_COLOR=Module["_emscripten_enum_draco_GeometryAttribute_Type_COLOR"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_COLOR=Module["_emscripten_enum_draco_GeometryAttribute_Type_COLOR"]=Module["asm"]["rb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=Module["_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=Module["_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD"]=Module["asm"]["sb"]).apply(null,arguments)};var _emscripten_enum_draco_GeometryAttribute_Type_GENERIC=Module["_emscripten_enum_draco_GeometryAttribute_Type_GENERIC"]=function(){return(_emscripten_enum_draco_GeometryAttribute_Type_GENERIC=Module["_emscripten_enum_draco_GeometryAttribute_Type_GENERIC"]=Module["asm"]["tb"]).apply(null,arguments)};var _emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=Module["_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE"]=function(){return(_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=Module["_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE"]=Module["asm"]["ub"]).apply(null,arguments)};var _emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=Module["_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD"]=function(){return(_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=Module["_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD"]=Module["asm"]["vb"]).apply(null,arguments)};var _emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=Module["_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH"]=function(){return(_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=Module["_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH"]=Module["asm"]["wb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INVALID=Module["_emscripten_enum_draco_DataType_DT_INVALID"]=function(){return(_emscripten_enum_draco_DataType_DT_INVALID=Module["_emscripten_enum_draco_DataType_DT_INVALID"]=Module["asm"]["xb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT8=Module["_emscripten_enum_draco_DataType_DT_INT8"]=function(){return(_emscripten_enum_draco_DataType_DT_INT8=Module["_emscripten_enum_draco_DataType_DT_INT8"]=Module["asm"]["yb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT8=Module["_emscripten_enum_draco_DataType_DT_UINT8"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT8=Module["_emscripten_enum_draco_DataType_DT_UINT8"]=Module["asm"]["zb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT16=Module["_emscripten_enum_draco_DataType_DT_INT16"]=function(){return(_emscripten_enum_draco_DataType_DT_INT16=Module["_emscripten_enum_draco_DataType_DT_INT16"]=Module["asm"]["Ab"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT16=Module["_emscripten_enum_draco_DataType_DT_UINT16"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT16=Module["_emscripten_enum_draco_DataType_DT_UINT16"]=Module["asm"]["Bb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT32=Module["_emscripten_enum_draco_DataType_DT_INT32"]=function(){return(_emscripten_enum_draco_DataType_DT_INT32=Module["_emscripten_enum_draco_DataType_DT_INT32"]=Module["asm"]["Cb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT32=Module["_emscripten_enum_draco_DataType_DT_UINT32"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT32=Module["_emscripten_enum_draco_DataType_DT_UINT32"]=Module["asm"]["Db"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_INT64=Module["_emscripten_enum_draco_DataType_DT_INT64"]=function(){return(_emscripten_enum_draco_DataType_DT_INT64=Module["_emscripten_enum_draco_DataType_DT_INT64"]=Module["asm"]["Eb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_UINT64=Module["_emscripten_enum_draco_DataType_DT_UINT64"]=function(){return(_emscripten_enum_draco_DataType_DT_UINT64=Module["_emscripten_enum_draco_DataType_DT_UINT64"]=Module["asm"]["Fb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_FLOAT32=Module["_emscripten_enum_draco_DataType_DT_FLOAT32"]=function(){return(_emscripten_enum_draco_DataType_DT_FLOAT32=Module["_emscripten_enum_draco_DataType_DT_FLOAT32"]=Module["asm"]["Gb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_FLOAT64=Module["_emscripten_enum_draco_DataType_DT_FLOAT64"]=function(){return(_emscripten_enum_draco_DataType_DT_FLOAT64=Module["_emscripten_enum_draco_DataType_DT_FLOAT64"]=Module["asm"]["Hb"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_BOOL=Module["_emscripten_enum_draco_DataType_DT_BOOL"]=function(){return(_emscripten_enum_draco_DataType_DT_BOOL=Module["_emscripten_enum_draco_DataType_DT_BOOL"]=Module["asm"]["Ib"]).apply(null,arguments)};var _emscripten_enum_draco_DataType_DT_TYPES_COUNT=Module["_emscripten_enum_draco_DataType_DT_TYPES_COUNT"]=function(){return(_emscripten_enum_draco_DataType_DT_TYPES_COUNT=Module["_emscripten_enum_draco_DataType_DT_TYPES_COUNT"]=Module["asm"]["Jb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_OK=Module["_emscripten_enum_draco_StatusCode_OK"]=function(){return(_emscripten_enum_draco_StatusCode_OK=Module["_emscripten_enum_draco_StatusCode_OK"]=Module["asm"]["Kb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_DRACO_ERROR=Module["_emscripten_enum_draco_StatusCode_DRACO_ERROR"]=function(){return(_emscripten_enum_draco_StatusCode_DRACO_ERROR=Module["_emscripten_enum_draco_StatusCode_DRACO_ERROR"]=Module["asm"]["Lb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_IO_ERROR=Module["_emscripten_enum_draco_StatusCode_IO_ERROR"]=function(){return(_emscripten_enum_draco_StatusCode_IO_ERROR=Module["_emscripten_enum_draco_StatusCode_IO_ERROR"]=Module["asm"]["Mb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_INVALID_PARAMETER=Module["_emscripten_enum_draco_StatusCode_INVALID_PARAMETER"]=function(){return(_emscripten_enum_draco_StatusCode_INVALID_PARAMETER=Module["_emscripten_enum_draco_StatusCode_INVALID_PARAMETER"]=Module["asm"]["Nb"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=Module["_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION"]=function(){return(_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=Module["_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION"]=Module["asm"]["Ob"]).apply(null,arguments)};var _emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=Module["_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION"]=function(){return(_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=Module["_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION"]=Module["asm"]["Pb"]).apply(null,arguments)};var ___errno_location=function(){return(___errno_location=Module["asm"]["__errno_location"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["Qb"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["Rb"]).apply(null,arguments)};var ___cxa_is_pointer_type=function(){return(___cxa_is_pointer_type=Module["asm"]["Sb"]).apply(null,arguments)};var ___start_em_js=Module["___start_em_js"]=11660;var ___stop_em_js=Module["___stop_em_js"]=11758;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();function WrapperObject(){}WrapperObject.prototype=Object.create(WrapperObject.prototype);WrapperObject.prototype.constructor=WrapperObject;WrapperObject.prototype.__class__=WrapperObject;WrapperObject.__cache__={};Module["WrapperObject"]=WrapperObject;function getCache(__class__){return(__class__||WrapperObject).__cache__}Module["getCache"]=getCache;function wrapPointer(ptr,__class__){var cache=getCache(__class__);var ret=cache[ptr];if(ret)return ret;ret=Object.create((__class__||WrapperObject).prototype);ret.ptr=ptr;return cache[ptr]=ret}Module["wrapPointer"]=wrapPointer;function castObject(obj,__class__){return wrapPointer(obj.ptr,__class__)}Module["castObject"]=castObject;Module["NULL"]=wrapPointer(0);function destroy(obj){if(!obj["__destroy__"])throw"Error: Cannot destroy object. (Did you create it yourself?)";obj["__destroy__"]();delete getCache(obj.__class__)[obj.ptr]}Module["destroy"]=destroy;function compare(obj1,obj2){return obj1.ptr===obj2.ptr}Module["compare"]=compare;function getPointer(obj){return obj.ptr}Module["getPointer"]=getPointer;function getClass(obj){return obj.__class__}Module["getClass"]=getClass;var ensureCache={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ensureCache.needed){for(var i=0;i<ensureCache.temps.length;i++){Module["_free"](ensureCache.temps[i])}ensureCache.temps.length=0;Module["_free"](ensureCache.buffer);ensureCache.buffer=0;ensureCache.size+=ensureCache.needed;ensureCache.needed=0}if(!ensureCache.buffer){ensureCache.size+=128;ensureCache.buffer=Module["_malloc"](ensureCache.size);assert(ensureCache.buffer)}ensureCache.pos=0},alloc:function(array,view){assert(ensureCache.buffer);var bytes=view.BYTES_PER_ELEMENT;var len=array.length*bytes;len=len+7&-8;var ret;if(ensureCache.pos+len>=ensureCache.size){assert(len>0);ensureCache.needed+=len;ret=Module["_malloc"](len);ensureCache.temps.push(ret)}else{ret=ensureCache.buffer+ensureCache.pos;ensureCache.pos+=len}return ret},copy:function(array,view,offset){offset>>>=0;var bytes=view.BYTES_PER_ELEMENT;switch(bytes){case 2:offset>>>=1;break;case 4:offset>>>=2;break;case 8:offset>>>=3;break}for(var i=0;i<array.length;i++){view[offset+i]=array[i]}}};function ensureString(value){if(typeof value==="string"){var intArray=intArrayFromString(value);var offset=ensureCache.alloc(intArray,HEAP8);ensureCache.copy(intArray,HEAP8,offset);return offset}return value}function ensureInt8(value){if(typeof value==="object"){var offset=ensureCache.alloc(value,HEAP8);ensureCache.copy(value,HEAP8,offset);return offset}return value}function VoidPtr(){throw"cannot construct a VoidPtr, no constructor in IDL"}VoidPtr.prototype=Object.create(WrapperObject.prototype);VoidPtr.prototype.constructor=VoidPtr;VoidPtr.prototype.__class__=VoidPtr;VoidPtr.__cache__={};Module["VoidPtr"]=VoidPtr;VoidPtr.prototype["__destroy__"]=VoidPtr.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_VoidPtr___destroy___0(self)};function DecoderBuffer(){this.ptr=_emscripten_bind_DecoderBuffer_DecoderBuffer_0();getCache(DecoderBuffer)[this.ptr]=this}DecoderBuffer.prototype=Object.create(WrapperObject.prototype);DecoderBuffer.prototype.constructor=DecoderBuffer;DecoderBuffer.prototype.__class__=DecoderBuffer;DecoderBuffer.__cache__={};Module["DecoderBuffer"]=DecoderBuffer;DecoderBuffer.prototype["Init"]=DecoderBuffer.prototype.Init=function(data,data_size){var self=this.ptr;ensureCache.prepare();if(typeof data=="object"){data=ensureInt8(data)}if(data_size&&typeof data_size==="object")data_size=data_size.ptr;_emscripten_bind_DecoderBuffer_Init_2(self,data,data_size)};DecoderBuffer.prototype["__destroy__"]=DecoderBuffer.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DecoderBuffer___destroy___0(self)};function AttributeTransformData(){this.ptr=_emscripten_bind_AttributeTransformData_AttributeTransformData_0();getCache(AttributeTransformData)[this.ptr]=this}AttributeTransformData.prototype=Object.create(WrapperObject.prototype);AttributeTransformData.prototype.constructor=AttributeTransformData;AttributeTransformData.prototype.__class__=AttributeTransformData;AttributeTransformData.__cache__={};Module["AttributeTransformData"]=AttributeTransformData;AttributeTransformData.prototype["transform_type"]=AttributeTransformData.prototype.transform_type=function(){var self=this.ptr;return _emscripten_bind_AttributeTransformData_transform_type_0(self)};AttributeTransformData.prototype["__destroy__"]=AttributeTransformData.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_AttributeTransformData___destroy___0(self)};function GeometryAttribute(){this.ptr=_emscripten_bind_GeometryAttribute_GeometryAttribute_0();getCache(GeometryAttribute)[this.ptr]=this}GeometryAttribute.prototype=Object.create(WrapperObject.prototype);GeometryAttribute.prototype.constructor=GeometryAttribute;GeometryAttribute.prototype.__class__=GeometryAttribute;GeometryAttribute.__cache__={};Module["GeometryAttribute"]=GeometryAttribute;GeometryAttribute.prototype["__destroy__"]=GeometryAttribute.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_GeometryAttribute___destroy___0(self)};function PointAttribute(){this.ptr=_emscripten_bind_PointAttribute_PointAttribute_0();getCache(PointAttribute)[this.ptr]=this}PointAttribute.prototype=Object.create(WrapperObject.prototype);PointAttribute.prototype.constructor=PointAttribute;PointAttribute.prototype.__class__=PointAttribute;PointAttribute.__cache__={};Module["PointAttribute"]=PointAttribute;PointAttribute.prototype["size"]=PointAttribute.prototype.size=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_size_0(self)};PointAttribute.prototype["GetAttributeTransformData"]=PointAttribute.prototype.GetAttributeTransformData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_PointAttribute_GetAttributeTransformData_0(self),AttributeTransformData)};PointAttribute.prototype["attribute_type"]=PointAttribute.prototype.attribute_type=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_attribute_type_0(self)};PointAttribute.prototype["data_type"]=PointAttribute.prototype.data_type=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_data_type_0(self)};PointAttribute.prototype["num_components"]=PointAttribute.prototype.num_components=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_num_components_0(self)};PointAttribute.prototype["normalized"]=PointAttribute.prototype.normalized=function(){var self=this.ptr;return!!_emscripten_bind_PointAttribute_normalized_0(self)};PointAttribute.prototype["byte_stride"]=PointAttribute.prototype.byte_stride=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_byte_stride_0(self)};PointAttribute.prototype["byte_offset"]=PointAttribute.prototype.byte_offset=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_byte_offset_0(self)};PointAttribute.prototype["unique_id"]=PointAttribute.prototype.unique_id=function(){var self=this.ptr;return _emscripten_bind_PointAttribute_unique_id_0(self)};PointAttribute.prototype["__destroy__"]=PointAttribute.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_PointAttribute___destroy___0(self)};function AttributeQuantizationTransform(){this.ptr=_emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0();getCache(AttributeQuantizationTransform)[this.ptr]=this}AttributeQuantizationTransform.prototype=Object.create(WrapperObject.prototype);AttributeQuantizationTransform.prototype.constructor=AttributeQuantizationTransform;AttributeQuantizationTransform.prototype.__class__=AttributeQuantizationTransform;AttributeQuantizationTransform.__cache__={};Module["AttributeQuantizationTransform"]=AttributeQuantizationTransform;AttributeQuantizationTransform.prototype["InitFromAttribute"]=AttributeQuantizationTransform.prototype.InitFromAttribute=function(att){var self=this.ptr;if(att&&typeof att==="object")att=att.ptr;return!!_emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1(self,att)};AttributeQuantizationTransform.prototype["quantization_bits"]=AttributeQuantizationTransform.prototype.quantization_bits=function(){var self=this.ptr;return _emscripten_bind_AttributeQuantizationTransform_quantization_bits_0(self)};AttributeQuantizationTransform.prototype["min_value"]=AttributeQuantizationTransform.prototype.min_value=function(axis){var self=this.ptr;if(axis&&typeof axis==="object")axis=axis.ptr;return _emscripten_bind_AttributeQuantizationTransform_min_value_1(self,axis)};AttributeQuantizationTransform.prototype["range"]=AttributeQuantizationTransform.prototype.range=function(){var self=this.ptr;return _emscripten_bind_AttributeQuantizationTransform_range_0(self)};AttributeQuantizationTransform.prototype["__destroy__"]=AttributeQuantizationTransform.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_AttributeQuantizationTransform___destroy___0(self)};function AttributeOctahedronTransform(){this.ptr=_emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0();getCache(AttributeOctahedronTransform)[this.ptr]=this}AttributeOctahedronTransform.prototype=Object.create(WrapperObject.prototype);AttributeOctahedronTransform.prototype.constructor=AttributeOctahedronTransform;AttributeOctahedronTransform.prototype.__class__=AttributeOctahedronTransform;AttributeOctahedronTransform.__cache__={};Module["AttributeOctahedronTransform"]=AttributeOctahedronTransform;AttributeOctahedronTransform.prototype["InitFromAttribute"]=AttributeOctahedronTransform.prototype.InitFromAttribute=function(att){var self=this.ptr;if(att&&typeof att==="object")att=att.ptr;return!!_emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1(self,att)};AttributeOctahedronTransform.prototype["quantization_bits"]=AttributeOctahedronTransform.prototype.quantization_bits=function(){var self=this.ptr;return _emscripten_bind_AttributeOctahedronTransform_quantization_bits_0(self)};AttributeOctahedronTransform.prototype["__destroy__"]=AttributeOctahedronTransform.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_AttributeOctahedronTransform___destroy___0(self)};function PointCloud(){this.ptr=_emscripten_bind_PointCloud_PointCloud_0();getCache(PointCloud)[this.ptr]=this}PointCloud.prototype=Object.create(WrapperObject.prototype);PointCloud.prototype.constructor=PointCloud;PointCloud.prototype.__class__=PointCloud;PointCloud.__cache__={};Module["PointCloud"]=PointCloud;PointCloud.prototype["num_attributes"]=PointCloud.prototype.num_attributes=function(){var self=this.ptr;return _emscripten_bind_PointCloud_num_attributes_0(self)};PointCloud.prototype["num_points"]=PointCloud.prototype.num_points=function(){var self=this.ptr;return _emscripten_bind_PointCloud_num_points_0(self)};PointCloud.prototype["__destroy__"]=PointCloud.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_PointCloud___destroy___0(self)};function Mesh(){this.ptr=_emscripten_bind_Mesh_Mesh_0();getCache(Mesh)[this.ptr]=this}Mesh.prototype=Object.create(WrapperObject.prototype);Mesh.prototype.constructor=Mesh;Mesh.prototype.__class__=Mesh;Mesh.__cache__={};Module["Mesh"]=Mesh;Mesh.prototype["num_faces"]=Mesh.prototype.num_faces=function(){var self=this.ptr;return _emscripten_bind_Mesh_num_faces_0(self)};Mesh.prototype["num_attributes"]=Mesh.prototype.num_attributes=function(){var self=this.ptr;return _emscripten_bind_Mesh_num_attributes_0(self)};Mesh.prototype["num_points"]=Mesh.prototype.num_points=function(){var self=this.ptr;return _emscripten_bind_Mesh_num_points_0(self)};Mesh.prototype["__destroy__"]=Mesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Mesh___destroy___0(self)};function Metadata(){this.ptr=_emscripten_bind_Metadata_Metadata_0();getCache(Metadata)[this.ptr]=this}Metadata.prototype=Object.create(WrapperObject.prototype);Metadata.prototype.constructor=Metadata;Metadata.prototype.__class__=Metadata;Metadata.__cache__={};Module["Metadata"]=Metadata;Metadata.prototype["__destroy__"]=Metadata.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Metadata___destroy___0(self)};function Status(){throw"cannot construct a Status, no constructor in IDL"}Status.prototype=Object.create(WrapperObject.prototype);Status.prototype.constructor=Status;Status.prototype.__class__=Status;Status.__cache__={};Module["Status"]=Status;Status.prototype["code"]=Status.prototype.code=function(){var self=this.ptr;return _emscripten_bind_Status_code_0(self)};Status.prototype["ok"]=Status.prototype.ok=function(){var self=this.ptr;return!!_emscripten_bind_Status_ok_0(self)};Status.prototype["error_msg"]=Status.prototype.error_msg=function(){var self=this.ptr;return UTF8ToString(_emscripten_bind_Status_error_msg_0(self))};Status.prototype["__destroy__"]=Status.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Status___destroy___0(self)};function DracoFloat32Array(){this.ptr=_emscripten_bind_DracoFloat32Array_DracoFloat32Array_0();getCache(DracoFloat32Array)[this.ptr]=this}DracoFloat32Array.prototype=Object.create(WrapperObject.prototype);DracoFloat32Array.prototype.constructor=DracoFloat32Array;DracoFloat32Array.prototype.__class__=DracoFloat32Array;DracoFloat32Array.__cache__={};Module["DracoFloat32Array"]=DracoFloat32Array;DracoFloat32Array.prototype["GetValue"]=DracoFloat32Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoFloat32Array_GetValue_1(self,index)};DracoFloat32Array.prototype["size"]=DracoFloat32Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoFloat32Array_size_0(self)};DracoFloat32Array.prototype["__destroy__"]=DracoFloat32Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoFloat32Array___destroy___0(self)};function DracoInt8Array(){this.ptr=_emscripten_bind_DracoInt8Array_DracoInt8Array_0();getCache(DracoInt8Array)[this.ptr]=this}DracoInt8Array.prototype=Object.create(WrapperObject.prototype);DracoInt8Array.prototype.constructor=DracoInt8Array;DracoInt8Array.prototype.__class__=DracoInt8Array;DracoInt8Array.__cache__={};Module["DracoInt8Array"]=DracoInt8Array;DracoInt8Array.prototype["GetValue"]=DracoInt8Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoInt8Array_GetValue_1(self,index)};DracoInt8Array.prototype["size"]=DracoInt8Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoInt8Array_size_0(self)};DracoInt8Array.prototype["__destroy__"]=DracoInt8Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoInt8Array___destroy___0(self)};function DracoUInt8Array(){this.ptr=_emscripten_bind_DracoUInt8Array_DracoUInt8Array_0();getCache(DracoUInt8Array)[this.ptr]=this}DracoUInt8Array.prototype=Object.create(WrapperObject.prototype);DracoUInt8Array.prototype.constructor=DracoUInt8Array;DracoUInt8Array.prototype.__class__=DracoUInt8Array;DracoUInt8Array.__cache__={};Module["DracoUInt8Array"]=DracoUInt8Array;DracoUInt8Array.prototype["GetValue"]=DracoUInt8Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoUInt8Array_GetValue_1(self,index)};DracoUInt8Array.prototype["size"]=DracoUInt8Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoUInt8Array_size_0(self)};DracoUInt8Array.prototype["__destroy__"]=DracoUInt8Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoUInt8Array___destroy___0(self)};function DracoInt16Array(){this.ptr=_emscripten_bind_DracoInt16Array_DracoInt16Array_0();getCache(DracoInt16Array)[this.ptr]=this}DracoInt16Array.prototype=Object.create(WrapperObject.prototype);DracoInt16Array.prototype.constructor=DracoInt16Array;DracoInt16Array.prototype.__class__=DracoInt16Array;DracoInt16Array.__cache__={};Module["DracoInt16Array"]=DracoInt16Array;DracoInt16Array.prototype["GetValue"]=DracoInt16Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoInt16Array_GetValue_1(self,index)};DracoInt16Array.prototype["size"]=DracoInt16Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoInt16Array_size_0(self)};DracoInt16Array.prototype["__destroy__"]=DracoInt16Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoInt16Array___destroy___0(self)};function DracoUInt16Array(){this.ptr=_emscripten_bind_DracoUInt16Array_DracoUInt16Array_0();getCache(DracoUInt16Array)[this.ptr]=this}DracoUInt16Array.prototype=Object.create(WrapperObject.prototype);DracoUInt16Array.prototype.constructor=DracoUInt16Array;DracoUInt16Array.prototype.__class__=DracoUInt16Array;DracoUInt16Array.__cache__={};Module["DracoUInt16Array"]=DracoUInt16Array;DracoUInt16Array.prototype["GetValue"]=DracoUInt16Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoUInt16Array_GetValue_1(self,index)};DracoUInt16Array.prototype["size"]=DracoUInt16Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoUInt16Array_size_0(self)};DracoUInt16Array.prototype["__destroy__"]=DracoUInt16Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoUInt16Array___destroy___0(self)};function DracoInt32Array(){this.ptr=_emscripten_bind_DracoInt32Array_DracoInt32Array_0();getCache(DracoInt32Array)[this.ptr]=this}DracoInt32Array.prototype=Object.create(WrapperObject.prototype);DracoInt32Array.prototype.constructor=DracoInt32Array;DracoInt32Array.prototype.__class__=DracoInt32Array;DracoInt32Array.__cache__={};Module["DracoInt32Array"]=DracoInt32Array;DracoInt32Array.prototype["GetValue"]=DracoInt32Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoInt32Array_GetValue_1(self,index)};DracoInt32Array.prototype["size"]=DracoInt32Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoInt32Array_size_0(self)};DracoInt32Array.prototype["__destroy__"]=DracoInt32Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoInt32Array___destroy___0(self)};function DracoUInt32Array(){this.ptr=_emscripten_bind_DracoUInt32Array_DracoUInt32Array_0();getCache(DracoUInt32Array)[this.ptr]=this}DracoUInt32Array.prototype=Object.create(WrapperObject.prototype);DracoUInt32Array.prototype.constructor=DracoUInt32Array;DracoUInt32Array.prototype.__class__=DracoUInt32Array;DracoUInt32Array.__cache__={};Module["DracoUInt32Array"]=DracoUInt32Array;DracoUInt32Array.prototype["GetValue"]=DracoUInt32Array.prototype.GetValue=function(index){var self=this.ptr;if(index&&typeof index==="object")index=index.ptr;return _emscripten_bind_DracoUInt32Array_GetValue_1(self,index)};DracoUInt32Array.prototype["size"]=DracoUInt32Array.prototype.size=function(){var self=this.ptr;return _emscripten_bind_DracoUInt32Array_size_0(self)};DracoUInt32Array.prototype["__destroy__"]=DracoUInt32Array.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DracoUInt32Array___destroy___0(self)};function MetadataQuerier(){this.ptr=_emscripten_bind_MetadataQuerier_MetadataQuerier_0();getCache(MetadataQuerier)[this.ptr]=this}MetadataQuerier.prototype=Object.create(WrapperObject.prototype);MetadataQuerier.prototype.constructor=MetadataQuerier;MetadataQuerier.prototype.__class__=MetadataQuerier;MetadataQuerier.__cache__={};Module["MetadataQuerier"]=MetadataQuerier;MetadataQuerier.prototype["HasEntry"]=MetadataQuerier.prototype.HasEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return!!_emscripten_bind_MetadataQuerier_HasEntry_2(self,metadata,entry_name)};MetadataQuerier.prototype["GetIntEntry"]=MetadataQuerier.prototype.GetIntEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return _emscripten_bind_MetadataQuerier_GetIntEntry_2(self,metadata,entry_name)};MetadataQuerier.prototype["GetIntEntryArray"]=MetadataQuerier.prototype.GetIntEntryArray=function(metadata,entry_name,out_values){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);if(out_values&&typeof out_values==="object")out_values=out_values.ptr;_emscripten_bind_MetadataQuerier_GetIntEntryArray_3(self,metadata,entry_name,out_values)};MetadataQuerier.prototype["GetDoubleEntry"]=MetadataQuerier.prototype.GetDoubleEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return _emscripten_bind_MetadataQuerier_GetDoubleEntry_2(self,metadata,entry_name)};MetadataQuerier.prototype["GetStringEntry"]=MetadataQuerier.prototype.GetStringEntry=function(metadata,entry_name){var self=this.ptr;ensureCache.prepare();if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_name&&typeof entry_name==="object")entry_name=entry_name.ptr;else entry_name=ensureString(entry_name);return UTF8ToString(_emscripten_bind_MetadataQuerier_GetStringEntry_2(self,metadata,entry_name))};MetadataQuerier.prototype["NumEntries"]=MetadataQuerier.prototype.NumEntries=function(metadata){var self=this.ptr;if(metadata&&typeof metadata==="object")metadata=metadata.ptr;return _emscripten_bind_MetadataQuerier_NumEntries_1(self,metadata)};MetadataQuerier.prototype["GetEntryName"]=MetadataQuerier.prototype.GetEntryName=function(metadata,entry_id){var self=this.ptr;if(metadata&&typeof metadata==="object")metadata=metadata.ptr;if(entry_id&&typeof entry_id==="object")entry_id=entry_id.ptr;return UTF8ToString(_emscripten_bind_MetadataQuerier_GetEntryName_2(self,metadata,entry_id))};MetadataQuerier.prototype["__destroy__"]=MetadataQuerier.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_MetadataQuerier___destroy___0(self)};function Decoder(){this.ptr=_emscripten_bind_Decoder_Decoder_0();getCache(Decoder)[this.ptr]=this}Decoder.prototype=Object.create(WrapperObject.prototype);Decoder.prototype.constructor=Decoder;Decoder.prototype.__class__=Decoder;Decoder.__cache__={};Module["Decoder"]=Decoder;Decoder.prototype["DecodeArrayToPointCloud"]=Decoder.prototype.DecodeArrayToPointCloud=function(data,data_size,out_point_cloud){var self=this.ptr;ensureCache.prepare();if(typeof data=="object"){data=ensureInt8(data)}if(data_size&&typeof data_size==="object")data_size=data_size.ptr;if(out_point_cloud&&typeof out_point_cloud==="object")out_point_cloud=out_point_cloud.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeArrayToPointCloud_3(self,data,data_size,out_point_cloud),Status)};Decoder.prototype["DecodeArrayToMesh"]=Decoder.prototype.DecodeArrayToMesh=function(data,data_size,out_mesh){var self=this.ptr;ensureCache.prepare();if(typeof data=="object"){data=ensureInt8(data)}if(data_size&&typeof data_size==="object")data_size=data_size.ptr;if(out_mesh&&typeof out_mesh==="object")out_mesh=out_mesh.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeArrayToMesh_3(self,data,data_size,out_mesh),Status)};Decoder.prototype["GetAttributeId"]=Decoder.prototype.GetAttributeId=function(pc,type){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(type&&typeof type==="object")type=type.ptr;return _emscripten_bind_Decoder_GetAttributeId_2(self,pc,type)};Decoder.prototype["GetAttributeIdByName"]=Decoder.prototype.GetAttributeIdByName=function(pc,name){var self=this.ptr;ensureCache.prepare();if(pc&&typeof pc==="object")pc=pc.ptr;if(name&&typeof name==="object")name=name.ptr;else name=ensureString(name);return _emscripten_bind_Decoder_GetAttributeIdByName_2(self,pc,name)};Decoder.prototype["GetAttributeIdByMetadataEntry"]=Decoder.prototype.GetAttributeIdByMetadataEntry=function(pc,name,value){var self=this.ptr;ensureCache.prepare();if(pc&&typeof pc==="object")pc=pc.ptr;if(name&&typeof name==="object")name=name.ptr;else name=ensureString(name);if(value&&typeof value==="object")value=value.ptr;else value=ensureString(value);return _emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3(self,pc,name,value)};Decoder.prototype["GetAttribute"]=Decoder.prototype.GetAttribute=function(pc,att_id){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(att_id&&typeof att_id==="object")att_id=att_id.ptr;return wrapPointer(_emscripten_bind_Decoder_GetAttribute_2(self,pc,att_id),PointAttribute)};Decoder.prototype["GetAttributeByUniqueId"]=Decoder.prototype.GetAttributeByUniqueId=function(pc,unique_id){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(unique_id&&typeof unique_id==="object")unique_id=unique_id.ptr;return wrapPointer(_emscripten_bind_Decoder_GetAttributeByUniqueId_2(self,pc,unique_id),PointAttribute)};Decoder.prototype["GetMetadata"]=Decoder.prototype.GetMetadata=function(pc){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;return wrapPointer(_emscripten_bind_Decoder_GetMetadata_1(self,pc),Metadata)};Decoder.prototype["GetAttributeMetadata"]=Decoder.prototype.GetAttributeMetadata=function(pc,att_id){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(att_id&&typeof att_id==="object")att_id=att_id.ptr;return wrapPointer(_emscripten_bind_Decoder_GetAttributeMetadata_2(self,pc,att_id),Metadata)};Decoder.prototype["GetFaceFromMesh"]=Decoder.prototype.GetFaceFromMesh=function(m,face_id,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(face_id&&typeof face_id==="object")face_id=face_id.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetFaceFromMesh_3(self,m,face_id,out_values)};Decoder.prototype["GetTriangleStripsFromMesh"]=Decoder.prototype.GetTriangleStripsFromMesh=function(m,strip_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(strip_values&&typeof strip_values==="object")strip_values=strip_values.ptr;return _emscripten_bind_Decoder_GetTriangleStripsFromMesh_2(self,m,strip_values)};Decoder.prototype["GetTrianglesUInt16Array"]=Decoder.prototype.GetTrianglesUInt16Array=function(m,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetTrianglesUInt16Array_3(self,m,out_size,out_values)};Decoder.prototype["GetTrianglesUInt32Array"]=Decoder.prototype.GetTrianglesUInt32Array=function(m,out_size,out_values){var self=this.ptr;if(m&&typeof m==="object")m=m.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetTrianglesUInt32Array_3(self,m,out_size,out_values)};Decoder.prototype["GetAttributeFloat"]=Decoder.prototype.GetAttributeFloat=function(pa,att_index,out_values){var self=this.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(att_index&&typeof att_index==="object")att_index=att_index.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeFloat_3(self,pa,att_index,out_values)};Decoder.prototype["GetAttributeFloatForAllPoints"]=Decoder.prototype.GetAttributeFloatForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeIntForAllPoints"]=Decoder.prototype.GetAttributeIntForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeIntForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeInt8ForAllPoints"]=Decoder.prototype.GetAttributeInt8ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeInt8ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeUInt8ForAllPoints"]=Decoder.prototype.GetAttributeUInt8ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeUInt8ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeInt16ForAllPoints"]=Decoder.prototype.GetAttributeInt16ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeInt16ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeUInt16ForAllPoints"]=Decoder.prototype.GetAttributeUInt16ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeUInt16ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeInt32ForAllPoints"]=Decoder.prototype.GetAttributeInt32ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeInt32ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeUInt32ForAllPoints"]=Decoder.prototype.GetAttributeUInt32ForAllPoints=function(pc,pa,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeUInt32ForAllPoints_3(self,pc,pa,out_values)};Decoder.prototype["GetAttributeDataArrayForAllPoints"]=Decoder.prototype.GetAttributeDataArrayForAllPoints=function(pc,pa,data_type,out_size,out_values){var self=this.ptr;if(pc&&typeof pc==="object")pc=pc.ptr;if(pa&&typeof pa==="object")pa=pa.ptr;if(data_type&&typeof data_type==="object")data_type=data_type.ptr;if(out_size&&typeof out_size==="object")out_size=out_size.ptr;if(out_values&&typeof out_values==="object")out_values=out_values.ptr;return!!_emscripten_bind_Decoder_GetAttributeDataArrayForAllPoints_5(self,pc,pa,data_type,out_size,out_values)};Decoder.prototype["SkipAttributeTransform"]=Decoder.prototype.SkipAttributeTransform=function(att_type){var self=this.ptr;if(att_type&&typeof att_type==="object")att_type=att_type.ptr;_emscripten_bind_Decoder_SkipAttributeTransform_1(self,att_type)};Decoder.prototype["GetEncodedGeometryType_Deprecated"]=Decoder.prototype.GetEncodedGeometryType_Deprecated=function(in_buffer){var self=this.ptr;if(in_buffer&&typeof in_buffer==="object")in_buffer=in_buffer.ptr;return _emscripten_bind_Decoder_GetEncodedGeometryType_Deprecated_1(self,in_buffer)};Decoder.prototype["DecodeBufferToPointCloud"]=Decoder.prototype.DecodeBufferToPointCloud=function(in_buffer,out_point_cloud){var self=this.ptr;if(in_buffer&&typeof in_buffer==="object")in_buffer=in_buffer.ptr;if(out_point_cloud&&typeof out_point_cloud==="object")out_point_cloud=out_point_cloud.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeBufferToPointCloud_2(self,in_buffer,out_point_cloud),Status)};Decoder.prototype["DecodeBufferToMesh"]=Decoder.prototype.DecodeBufferToMesh=function(in_buffer,out_mesh){var self=this.ptr;if(in_buffer&&typeof in_buffer==="object")in_buffer=in_buffer.ptr;if(out_mesh&&typeof out_mesh==="object")out_mesh=out_mesh.ptr;return wrapPointer(_emscripten_bind_Decoder_DecodeBufferToMesh_2(self,in_buffer,out_mesh),Status)};Decoder.prototype["__destroy__"]=Decoder.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Decoder___destroy___0(self)};(function(){function setupEnums(){Module["ATTRIBUTE_INVALID_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM();Module["ATTRIBUTE_NO_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM();Module["ATTRIBUTE_QUANTIZATION_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM();Module["ATTRIBUTE_OCTAHEDRON_TRANSFORM"]=_emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM();Module["INVALID"]=_emscripten_enum_draco_GeometryAttribute_Type_INVALID();Module["POSITION"]=_emscripten_enum_draco_GeometryAttribute_Type_POSITION();Module["NORMAL"]=_emscripten_enum_draco_GeometryAttribute_Type_NORMAL();Module["COLOR"]=_emscripten_enum_draco_GeometryAttribute_Type_COLOR();Module["TEX_COORD"]=_emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD();Module["GENERIC"]=_emscripten_enum_draco_GeometryAttribute_Type_GENERIC();Module["INVALID_GEOMETRY_TYPE"]=_emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE();Module["POINT_CLOUD"]=_emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD();Module["TRIANGULAR_MESH"]=_emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH();Module["DT_INVALID"]=_emscripten_enum_draco_DataType_DT_INVALID();Module["DT_INT8"]=_emscripten_enum_draco_DataType_DT_INT8();Module["DT_UINT8"]=_emscripten_enum_draco_DataType_DT_UINT8();Module["DT_INT16"]=_emscripten_enum_draco_DataType_DT_INT16();Module["DT_UINT16"]=_emscripten_enum_draco_DataType_DT_UINT16();Module["DT_INT32"]=_emscripten_enum_draco_DataType_DT_INT32();Module["DT_UINT32"]=_emscripten_enum_draco_DataType_DT_UINT32();Module["DT_INT64"]=_emscripten_enum_draco_DataType_DT_INT64();Module["DT_UINT64"]=_emscripten_enum_draco_DataType_DT_UINT64();Module["DT_FLOAT32"]=_emscripten_enum_draco_DataType_DT_FLOAT32();Module["DT_FLOAT64"]=_emscripten_enum_draco_DataType_DT_FLOAT64();Module["DT_BOOL"]=_emscripten_enum_draco_DataType_DT_BOOL();Module["DT_TYPES_COUNT"]=_emscripten_enum_draco_DataType_DT_TYPES_COUNT();Module["OK"]=_emscripten_enum_draco_StatusCode_OK();Module["DRACO_ERROR"]=_emscripten_enum_draco_StatusCode_DRACO_ERROR();Module["IO_ERROR"]=_emscripten_enum_draco_StatusCode_IO_ERROR();Module["INVALID_PARAMETER"]=_emscripten_enum_draco_StatusCode_INVALID_PARAMETER();Module["UNSUPPORTED_VERSION"]=_emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION();Module["UNKNOWN_VERSION"]=_emscripten_enum_draco_StatusCode_UNKNOWN_VERSION()}if(runtimeInitialized)setupEnums();else addOnInit(setupEnums)})();if(typeof Module["onModuleParsed"]==="function"){Module["onModuleParsed"]()}Module["Decoder"].prototype.GetEncodedGeometryType=function(array){if(array.__class__&&array.__class__===Module.DecoderBuffer){return Module.Decoder.prototype.GetEncodedGeometryType_Deprecated(array)}if(array.byteLength<8)return Module.INVALID_GEOMETRY_TYPE;switch(array[7]){case 0:return Module.POINT_CLOUD;case 1:return Module.TRIANGULAR_MESH;default:return Module.INVALID_GEOMETRY_TYPE}};


  return DracoDecoderModule.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = DracoDecoderModule;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return DracoDecoderModule; });
else if (typeof exports === 'object')
  exports["DracoDecoderModule"] = DracoDecoderModule;
