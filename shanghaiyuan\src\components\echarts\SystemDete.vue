<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      var stages = [
        { name: "环境温湿度", max: 2 },
        { name: "房间压差", max: 8 },
        { name: "甲烷气体浓度监测", max: 15 },
        { name: "甲烷气体浓度监测", max: 70 },
        { name: "摄像头", max: 70 },
        { name: "环境O2浓度", max: 70 },
        { name: "环境CO2浓度", max:20 },
        { name: "培养箱温度", max: 20 },
        { name: "冰箱温度及门开关", max: 20 },
        { name: "防爆环境温湿度", max: 20 },
      ];

      var scores = [
        {
          name: "在线数",
          value: [2, 4, 15, 55, 50, 13, 13, 13, 13, 13],
        },
        {
          name: "离线数",
          value: [2, 4, 15, 65, 60, 15, 15, 15, 15, 15],
        },
      ];
      function contains(arr, obj) {
        var i = arr.length;
        while (i--) {
          if (arr[i].name === obj) {
            return i;
          }
        }
        return false;
      }
      const option = {
        color: ["#00FFB4", "#DB8F2C"],
        legend: {
          bottom: 0,
          // right: 0,
          icon: "circle",
          itemWidth: 10, // 图例标记的图形宽度。[ default: 25 ]
          itemHeight: 10, // 图例标记的图形高度。[ default: 14 ]
          itemGap: 9, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
          textStyle: {
            fontSize: 12,
            color: "#fff",
          },
          data: ["正常运行数", "故障数"],
        },
        radar: {
          radius: "50%",

          triggerEvent: true,
          // name: {
          //   textStyle: {
          //     color: '#fff',
          //     fontSize: '16',
          //     borderRadius: 3,
          //     padding: [3, 5],
          //   },
          // },
          center: ["50%", "50%"],
          name: {
            rich: {
              a: {
                fontSize: 10,
                color: "#91D2FB",
                lineHeight: "40",
                padding: [10, 10, 10, 10],
              },
              b: {
                color: "#00FFB4",
                fontSize: 10,
                padding: [-10, 0, 10, 20],
              },
              c: {
                color: "#91D2FB",
                fontSize: 10,
                padding: [-10, 0, 10, 0],
              },
              d: {
                color: "#DB8F2C",
                fontSize: 10,
                padding: [-10, 15, 10, 0],
              },
              triggerEvent: true,
            },
            formatter: (a) => {
              let i = contains(stages, a); // 处理对应要显示的样式
              return `{a| ${a}}\n{b| ${scores[0]["value"][i]}}{c|  / }{d| ${scores[1]["value"][i]}}`;
            },
          },
          nameGap: "2",
          indicator: stages,
          splitArea: {
            // 坐标轴在 grid 区域中的分隔区域，默认不显示。
            show: true,
            areaStyle: {
              // 分隔区域的样式设置。
              color: ["#092645", "#092645"], // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
            },
          },
          // axisLabel:{//展示刻度
          //     show: true
          // },
          axisLine: {
            //指向外圈文本的分隔线样式
            lineStyle: {
              color: "rgba(0,0,0,0)",
            },
          },
          splitLine: {
            lineStyle: {
              color: "#144F79", // 分隔线颜色
              width: 1, // 分隔线线宽
            },
          },
        },

        series: [
          {
            name: "正常运行数",
            type: "radar",
            // 使用线性渐变
            areaStyle: {
              normal: {
                color: "#00FFB4",
                opacity: 0.6, // 透明度
              },
            },

            symbolSize: 0, // 标记的大小，设为0表示不显示标记
            lineStyle: {
              normal: {
                color: "#00FFB4", // 线条颜色
                width: 1, // 线条宽度
              },
            },
            data: [scores[0]], // 指定数据
          },
          {
            name: "故障数",
            type: "radar",
            areaStyle: {
              normal: {
                color: "#DB8F2C",
                opacity: 0.6, // 透明度
              },
            },
            itemStyle: {
              normal: {
                //图形悬浮效果
                borderColor: "#DB8F2C",
                borderWidth: 2.5,
              },
            },
            symbolSize: 0, // 同上
            lineStyle: {
              normal: {
                color: "#DB8F2C", // 线条颜色
                width: 1, // 线条宽度
              },
            },
            data: [scores[1]], // 指定数据
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 100% !important;
  }
}
</style>