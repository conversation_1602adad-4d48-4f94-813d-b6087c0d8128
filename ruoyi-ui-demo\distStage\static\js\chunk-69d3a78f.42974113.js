(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-69d3a78f"],{"01e0":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAFoTx1HAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAADqADAAQAAAABAAAADgAAAAC98Dn6AAABWUlEQVQoFXVSPS8EURQ99xnJEhQ7CrtC1MJP4BdQilaj9VP0ejodlUrlD0hoNug2sh8JK7tYmevcOzvjbXbdZN6ce+be8+47b1Br6y0fDRCkAgzEMhEMAxiqmEWto/eWWBhOHLDMGa7e48nEYmpFuStS4NSrCtXVru7V23plpOGxHbKMrODRZBwzdRmr5pQ3zVQ2SRxZbvHvKJUZbIQkYJuHvG4ui4SA/QL/KBax1tWtXGR8Nf5fWVP8Gwg44VDrcX/ZaXvah8IR63QnnezoiygauaV5v5gT1D6eAx5yKl8zoSVTYqC4SwSHPk8QfDRSee8rLgaCXcOfGc6/gJ2nqrzFmFrmM82zm1ZMtWHKhk6NORAX8bjdSgUrHPYy5mNcWheRrfkq6s8L8sq7PWDzWfSthBON3C30e7zGUdChpQLH74Tn68QE/88U32jxXpwe/dZlCYWHdLX3C8vIkJ6IkBvJAAAAAElFTkSuQmCC"},1548:function(t,e,a){t.exports=a.p+"static/img/co2_carbon.faabf5cc.png"},"26ad":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{class:t.className,style:{height:t.height,width:t.width}})},n=[],o=a("5403"),r=a("2ef0");a("a524");var l={mixins:[o["a"]],props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"100%"},autoResize:{type:Boolean,default:!0},optionData:{type:Object,require:!1,default:function(){}}},computed:{option:function(){return Object(r["merge"])({},this.defatulOpts,this.optionData)}},data:function(){return{chart:null,defatulOpts:{darkMode:!0,backgroundColor:"transparent",grid:{top:30,left:30,bottom:20,right:10},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{right:0,itemHeight:1,textStyle:{color:"#ccc",fontSize:12}},xAxis:{type:"category",boundaryGap:!1,data:[],axisTick:{show:!1},splitLine:{show:!1},axisLine:{show:!1},axisLabel:{color:"#D7FFFE",fontSize:12}},yAxis:{type:"value",nameGap:8,nameTextStyle:{color:"#D7FFFE",fontSize:12,padding:[0,0,0,10]},axisTick:{show:!1},splitLine:{show:!1},axisLine:{show:!1},axisLabel:{textStyle:{color:"#D7FFFE",fontSize:12}}}}}},watch:{optionData:{deep:!0,handler:function(t){var e=this;console.log(t,"11111"),this.$nextTick((function(){e.initChart()}))}}},mounted:function(){var t=this;this.$nextTick((function(){t.initChart()}))},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{initChart:function(){var t=this;this.chart=this.$echarts.init(this.$el,null,{devicePixelRatio:2.5}),console.log("BaseChart --------\x3e ",this.option),this.chart.setOption(this.option,!0),this.$nextTick((function(){t.resize()}))}}},s=l,c=a("2877"),d=Object(c["a"])(s,i,n,!1,null,null,null);e["default"]=d.exports},"2bdf":function(t,e,a){},3463:function(t,e,a){"use strict";a("2bdf")},"35c8":function(t,e,a){},"45b6":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAANCAYAAACZ3F9/AAAAAXNSR0IArs4c6QAAAfJJREFUKBWFUT1rFEEYfj9m9y5EFFIlIAhpJIUK2gmK4g+wkGgnSLBJKQjiB0ZEESwEO5u0QgrBTgXRLm06CQRNEBH0uJhwy+3ufLw+c3dFCsHdfWeZeef5mGeY/vM8ftMsuDIul6r1TLd8cOMi1xnC/8KtfDLX29m7XBZ6vut4u1PI807J2i1kc2a2PHP9FFdyELj4qn/k6stf97996W+0Id2LyT4+vXbohY+JMac22vFqQNMZ4/KwuGb6Z/PnpcFuewysXjjdJc9vmcNrtKfbmIRZCOtEg4wgkpO3t6/sbHxfbWI6F2JaakN4FqItoCgEoxUz8fj7PEdNcORCjCeC8G7j4xJoD4vIuzbGr0xKIlD4TJJtMtIQ1MgnFF0IRC0sqLiHPqQ5YbsAyFEpsKaI4DeNFFWgiqqqkVNymc3QZx8fGdEcswJg7ykmksC0R6TZpnjMs2xnbNa1KW8QwvfEmUTs997zD6axQq8mMZCrKMERzji+CCjCOzYxhVtsMo8iBHiHoKY4Y+F7UiGcnCjjnZqkgzNCAhZEeF0irWugvjjayooKQH/fKVuDPjyrHkg1K+Zwcoqc5kFwmn3aQg6ZjIYmUniQ8Fi1MzUJp/bmYS1f+lmOMpuvQD0XgXkowYZNqtCyYUhwhZCqqv5wc3V/+S9oVgHyxZEolwAAAABJRU5ErkJggg=="},4943:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAQCAYAAAGHNqTJAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEaADAAQAAAABAAAAEAAAAACz87qxAAABoUlEQVQ4EbVTzS5DURD+5ioJQqKtRf0kEhHbJmptIbHwBuIBPICNV/ECbO2FRFiJEhuJoBGhFnoTbVDa3nt8czjtaZWdk3Tmm7/vzMy5BTrPrDFDbT7JlIxpesYrJjUWmpJzBAriGtSx05bpMqxm5ESBlit2pE/0jfKXtzzfBhUgKnjT2QCwoDiWL5/iaoy9x7TkEmqwu+wrUFaMVqdq5VU4asWQABvFFALSb1nHt7DXZUJTJ4NlbQsKdospWfJ9XbFkns3cYIQrjfoN1wTZ+xE5sL1cp6TyBhQ+EkgUklJ+N7iJBC9aZO/WPerODfdKrBMCDegG2b93pI/bE9x6rta4HKmKCJNrSUx3TSBrPxPWN0NK7wQ9goqzmbTqsNN2WRNlMxM1cNFtYS6xU4u3SDtIZDBMgnM+pgR9oET4F+Z+ctxG0hH/eKqHYQkZTGvCb9gVO+2TzOnuXUA1Z92nOKRznp0uN2MNi+wXpqjtXZpJBHyxlWJaFklySXOKI9b9uI9/JTExtvlnqLGDI3Z0zB57/UIf++P4fodPWXznjH/Vn/l/lT6eC3QcAAAAAElFTkSuQmCC"},5158:function(t,e,a){t.exports=a.p+"static/img/co2_daily.e20c4fb4.png"},5403:function(t,e,a){"use strict";var i=a("ed08");e["a"]={data:function(){return{$_sidebarElm:null,$_resizeHandler:null}},mounted:function(){this.initListener()},activated:function(){this.$_resizeHandler||this.initListener(),this.resize()},beforeDestroy:function(){this.destroyListener()},deactivated:function(){this.destroyListener()},methods:{$_sidebarResizeHandler:function(t){"width"===t.propertyName&&this.$_resizeHandler()},initListener:function(){var t=this;this.$_resizeHandler=Object(i["c"])((function(){t.resize()}),100),window.addEventListener("resize",this.$_resizeHandler),this.$_sidebarElm=document.getElementsByClassName("sidebar-container")[0],this.$_sidebarElm&&this.$_sidebarElm.addEventListener("transitionend",this.$_sidebarResizeHandler)},destroyListener:function(){window.removeEventListener("resize",this.$_resizeHandler),this.$_resizeHandler=null,this.$_sidebarElm&&this.$_sidebarElm.removeEventListener("transitionend",this.$_sidebarResizeHandler)},resize:function(){var t=this.chart;t&&t.resize()}}}},"5e2f":function(t,e,a){"use strict";a("35c8")},"823d":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"co2_body"},[i("div",{staticClass:"fl"},[i("div",{staticClass:"co2_main_left"},[i("div",{staticClass:"co2_main_left_content co2_energy_left"},[i("div",[i("Weather"),i("div",{staticClass:"page_nav"},[i("span",{staticClass:"text"},[t._v("建筑概况 -- "),i("BuildingGroup")],1),t._m(0)]),i("ul",{staticClass:"co2_survey"},[i("li",[t._m(1),i("div",[i("b",{domProps:{textContent:t._s(t.buildingInfo.area||"N/A")}}),i("i",[t._v(" m²")]),i("p",[t._v("建筑面积")])]),i("div",[i("b",{domProps:{textContent:t._s(t.buildingInfo.person||"N/A")}}),i("i",[t._v(" 人")]),i("p",[t._v("用能人数")])]),t._m(2)]),i("li",[t._m(3),i("div",[i("b",{domProps:{textContent:t._s(t.avgCarbonPerson||"N/A")}},[t._v("0")]),i("i",[t._v(" kgCO2e/(人)")]),i("p",[t._v("人均碳排放量")])]),i("div",[i("b",{domProps:{textContent:t._s(t.avgCarbonArea||"N/A")}}),i("i",[t._v(" kgce/m²")]),i("p",[t._v("单位面积能耗")])]),i("div",[i("b",{domProps:{textContent:t._s(t.avgEnergySaveRate)}}),i("p",[t._v("平均节能率")])])]),i("li",[t._m(4),i("div",[i("el-popover",{attrs:{placement:"top-start",width:"fit-content",trigger:"hover"}},[i("div",{staticClass:"pop"},[i("p",[i("em",[t._v("单位面积能耗")]),t._v(" "),i("b",[t._v("5")]),t._v(" "),i("i",[t._v("kgce/㎡")])]),i("p",[i("em",[t._v("人均碳排放")]),t._v(" "),i("b",[t._v("540")]),t._v(" "),i("i",[t._v("kgCO2e/人")])]),i("p",[i("em",[t._v("可再生能")]),t._v(" "),i("b",[t._v("30")]),t._v(" "),i("i",[t._v("%")])])]),i("span",{staticClass:"h",attrs:{slot:"reference"},slot:"reference"},[t._v("近零碳公共建筑")])])],1),i("div",[i("el-popover",{attrs:{placement:"top-start",width:"fit-content",trigger:"hover"}},[i("div",{staticClass:"pop"},[i("p",[i("em",[t._v("建筑体型系数")]),t._v(" "),i("b",[t._v("0.4")]),t._v(" "),i("i")]),i("p",[i("em",[t._v("屋面热传导系数")]),t._v(" "),i("b",[t._v("0.4")]),t._v(" "),i("i",[t._v("W/(㎡*K)")])]),i("p",[i("em",[t._v("外墙热传导系数")]),t._v(" "),i("b",[t._v("0.6")]),t._v(" "),i("i",[t._v("W/(㎡*K)")])]),i("p",[i("em",[t._v("户门热传导系数")]),t._v(" "),i("b",[t._v("1")]),t._v(" "),i("i",[t._v("W/(㎡*K)")])])]),i("span",{staticClass:"h",attrs:{slot:"reference"},slot:"reference"},[t._v("围护结构")])])],1)])])],1),i("div",{staticClass:"charts_box"},[i("div",{staticClass:"electricity_charts_box"},[t._m(5),i("div",{staticClass:"electricity_analysis"},[i("div",{staticClass:"page_nav2 flex-1"},[i("img",{attrs:{src:a("4943")}}),t._v(" 年度累计用电 "),i("div",{staticClass:"pull-right"},[i("el-popover",{attrs:{placement:"top-start",width:"fit-content",trigger:"hover"}},[i("div",{staticClass:"pop"},[i("p",[i("em",[t._v("年度外购市电")]),t._v(" "),i("b",{domProps:{textContent:t._s(t.electricityTotal)}}),t._v(" "),i("i",[t._v("kWh")])]),i("p",{staticStyle:{"font-size":"80%","padding-left":"10px","list-style":"disc"}},[i("em",[t._v("年度替代电能")]),t._v(" "),i("b",{domProps:{textContent:t._s(t.replaceAbleTotal)}},[t._v("0")]),t._v(" "),i("i",[t._v("kWh")])]),i("p",[i("em",[t._v("年度光伏发电")]),t._v(" "),i("b",{domProps:{textContent:t._s(t.pvPlantTotal)}}),t._v(" "),i("i",[t._v("kWh")])])]),i("span",{attrs:{slot:"reference"},slot:"reference"},[i("b",{domProps:{textContent:t._s((t.electricityTotal||0)+(t.pvPlantTotal||0))}}),t._v("kWh")])])],1)]),i("div",{staticClass:"electricity_analysis_charts"},[i("div",{staticClass:"electricity_pie",staticStyle:{height:"100%"}},[i("BaseChart",{attrs:{optionData:t.electricityPieOption}})],1)])])]),i("div",{staticClass:"water_charts_box"},[t._m(6),i("div",{staticClass:"water_analysis"},[i("div",{staticClass:"page_nav2"},[i("img",{attrs:{src:a("01e0")}}),t._v(" 年度累计用水 "),i("span",[i("b",{domProps:{textContent:t._s(t.waterTotal)}}),t._v("吨")])]),i("div",{staticClass:"water_analysis_charts"},[i("div",{staticClass:"water_chart_pie"},[i("BaseChart",{attrs:{optionData:t.waterOptOption}})],1),i("div",{staticClass:"water_chart_bar"},[i("BaseChart",{attrs:{optionData:t.waterBarOption}})],1)])])])])])])]),i("div",{staticClass:"co2_main_center co2_energy_center"},[i("div",{staticClass:"points"},[i("span",{staticClass:"point pointMz"}),i("span",{staticClass:"point pointJs"}),i("span",{staticClass:"point pointRt"}),i("span",{staticClass:"point pointBj"}),i("span",{staticClass:"point pointJx"}),i("span",{staticClass:"point pointHy"}),i("a",{staticClass:"link linkMz",attrs:{target:"_blank"},on:{click:function(e){return t.openLink("明洲大厦")}}},[t._v("明洲大厦")]),i("a",{staticClass:"link linkJs",attrs:{target:"_blank"},on:{click:function(e){return t.openLink("嘉善大楼")}}},[t._v("嘉善大楼")]),i("a",{staticClass:"link linkRt",attrs:{target:"_blank"},on:{click:function(e){return t.openLink("融通大厦")}}},[t._v("融通大厦")]),i("a",{staticClass:"link linkBj",attrs:{target:"_blank"},on:{click:function(e){return t.openLink("滨海大楼")}}},[t._v("滨海大楼")]),i("a",{staticClass:"link linkJx",attrs:{target:"_blank"},on:{click:function(e){return t.openLink("本部大楼")}}},[t._v("本部大楼")]),i("a",{staticClass:"link linkHy",attrs:{target:"_blank"},on:{click:function(e){return t.openLink("海盐大楼")}}},[t._v("海盐大楼")]),i("img",{staticStyle:{margin:"80px 8.5%"},attrs:{src:"/uploads/map.png",width:"800"}})]),i("div",{staticClass:"ft"},[i("div",{staticClass:"page_fixed_nav_box"},[i("el-dropdown",{staticClass:"avatar-container hover-effect",attrs:{trigger:"hover"}},[i("div",[i("img",{staticClass:"mb10",attrs:{src:a("5158")}}),i("p",[t._v("建筑列表")])]),i("el-dropdown-menu",{staticClass:"nav_wrap",attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.subLinks,(function(e,a){return i("a",{staticClass:"router-link",on:{click:function(i){return t.openLink(e,a)}}},[t._v(t._s(e.name))])})),0)],1)],1)]),i("div",{staticClass:"fb"},[i("div",{staticClass:"co2_energy_carbon"},[i("div",{staticClass:"page_nav page_nav1"},[i("span",{staticClass:"text"},[t._v("能耗概览")]),i("div",{staticClass:"select_form pull-right"},[i("el-select",{staticClass:"co2_select",attrs:{placeholder:"请选择",size:"small"},on:{change:t.handleEnergyRanking},model:{value:t.energyRankingValue,callback:function(e){t.energyRankingValue=e},expression:"energyRankingValue"}},t._l(t.energyRankingSelectOption,(function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)]),i("ul",{staticClass:"co2_survey"},[i("li",[t._m(7),i("div",[i("i",[t._v("用电")]),i("b",{domProps:{textContent:t._s(t.energyRanking["electricityTotal"]||"N/A")}}),i("i",[t._v("kWh")])]),i("div",[i("i",[t._v("用水")]),i("b",{domProps:{textContent:t._s(t.energyRanking["waterTotal"]||"N/A")}}),i("i",[t._v("吨")])]),i("div",[i("i",[t._v("光伏发电")]),i("b",{domProps:{textContent:t._s(t.energyRanking["pvPlantTotal"]||"N/A")}}),i("i",[t._v("kWh")])])])]),i("div",{staticClass:"carbon_line_chart flex"},[i("div",{staticClass:"energy_ranking flex-1",staticStyle:{"min-height":"285px"}},[t._m(8),i("p",{staticClass:"clearfix"}),i("div",{staticStyle:{height:"230px",overflow:"auto"}},t._l(t.energyRanking["electricityRanking"],(function(e,a){return i("div",{key:a,staticClass:"co2_progress"},[i("div",{staticClass:"text_cont"},[i("span",{domProps:{textContent:t._s(e.name)}}),i("i",[t._v(t._s(e.total||0)+" "),i("b",{domProps:{textContent:t._s((e.percent||"0")+"%")}})])]),i("div",{staticClass:"progress_bar"},[i("span",{class:"progress_fill_"+a,style:{width:e.percent+"%"}})])])})),0)]),i("div",{staticClass:"energy_ranking flex-1"},[t._m(9),i("p",{staticClass:"clearfix"}),i("div",{staticStyle:{height:"230px",overflow:"auto"}},t._l(t.energyRanking["waterRanking"],(function(e,a){return i("div",{key:a,staticClass:"co2_progress"},[i("div",{staticClass:"text_cont"},[i("span",{domProps:{textContent:t._s(e.name)}}),i("i",[t._v(t._s(e.total||0)+" "),i("b",{domProps:{textContent:t._s((e.percent||"0")+"%")}})])]),i("div",{staticClass:"progress_bar"},[i("span",{class:"progress_fill_"+a,style:{width:e.percent+"%"}})])])})),0)]),i("div",{staticClass:"energy_ranking flex-1"},[t._m(10),i("p",{staticClass:"clearfix"}),i("div",{staticStyle:{height:"230px",overflow:"auto"}},t._l(t.energyRanking["分项用电SubOpt"],(function(e,a){return i("div",{key:a,staticClass:"co2_progress"},[i("div",{staticClass:"text_cont"},[i("span",{domProps:{textContent:t._s(a)}}),i("i",[t._v(t._s(e.total||0)+" "),i("b",{domProps:{textContent:t._s((e.percent||"0")+"%")}})])]),i("div",{staticClass:"progress_bar"},[i("span",{staticClass:"progress_fill_1",style:{width:e.percent+"%"}})])])})),0)])])])])]),i("div",{staticClass:"fr"},[i("div",{staticClass:"co2_main_right co2_energy_right"},[i("DateTime"),i("div",{staticClass:"co2__carbon_member"},[t._m(11),i("div",{staticClass:"co2__carbon_pie"},[i("BaseChart",{attrs:{optionData:t.carbonPieOption}})],1)]),i("div",{staticClass:"co2__carbon_reduce"},[t._m(12),i("div",{staticClass:"co2__carbon_reduce_inner"},[i("div",{staticClass:"page_nav2"},[i("img",{attrs:{src:a("a956")}}),t._v(" 减碳总量 "),i("span",[i("b",{domProps:{textContent:t._s(t.carbonReduceTotal)}}),t._v("tco2")])]),i("div",{staticClass:"clearfix"}),i("div",{staticClass:"page_nav2"},[i("img",{attrs:{src:a("45b6")}}),t._v(" 零碳指数 "),i("span",[i("b",{domProps:{textContent:t._s("96")}})])]),i("div",{staticClass:"clearfix"}),i("div",{staticClass:"co2_carbon_reduce_pie"},[i("div",{staticClass:"reduce_chart_pie"},[i("BaseChart",{attrs:{optionData:t.reducePieOption}})],1),i("ul",t._l(t.reducePieData,(function(e,a){return i("li",{key:a,class:"color_"+a},[i("h4",[t._v(t._s(e.name))]),i("div",{staticClass:"text"},[t._v(t._s(e.value)+" ")]),i("div",[t._v("tco2")])])})),0)])])]),i("div",{staticClass:"co2__carbon_reduce"},[i("div",{staticClass:"page_nav"},[i("span",{staticClass:"text"},[t._v("碳排放分析预测")]),i("div",{staticClass:"select_form pull-right"},[i("el-select",{staticClass:"co2_select",attrs:{placeholder:"请选择",size:"small"},on:{change:t.handleCarbonLine},model:{value:t.carbonLineValue,callback:function(e){t.carbonLineValue=e},expression:"carbonLineValue"}},t._l(t.carbonLineSelectOption,(function(t){return i("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)]),i("div",{staticClass:"page_nav2 flex",staticStyle:{padding:"10px 20px 0px"}},[i("span",{staticClass:"flex-1"},[t._v("实现碳中和缺口")]),i("span",[i("b",{staticStyle:{"margin-padding":"30px"},domProps:{textContent:t._s(parseInt(t.carbonTotal/1e3))}}),t._v(" tco2")])]),i("div",{staticClass:"co2__carbon_reduce_inner"},[i("BaseChart",{attrs:{optionData:t.carbonLineOption}})],1),i("div",{staticClass:"co2_carbon_results",staticStyle:{display:"none"}},[i("div",{staticClass:"page_nav2"},[i("img",{attrs:{src:a("a956")}}),t._v(" 等效植树 "),i("span",[i("b",{domProps:{textContent:t._s(t.reduceTree)}}),t._v("棵")])]),i("p",{staticClass:"clearfix"}),t._l(t.carbonResults,(function(e,a){return i("div",{key:a,staticClass:"co2_progress"},[i("div",{staticClass:"text_cont"},[i("span",{domProps:{textContent:t._s(e.name)}}),i("i",[t._v(t._s(e.value||0)+" "),i("b",{domProps:{textContent:t._s(e.unit)}})])]),i("div",{staticClass:"progress_bar"},[i("span",{class:"progress_fill_"+a,style:{width:e.percent+"%"}})])])}))],2)])],1)])])])},n=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("span",{staticClass:"page_nav_right"},[i("img",{attrs:{src:a("45b6")}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"img_box"},[i("img",{attrs:{src:a("1548")}})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("span",[t._v("夏热冬冷")]),a("p",[t._v("热工分区")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"img_box"},[i("img",{attrs:{src:a("95f6")}})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-left":"15px"}},[a("span",[t._v("甲类办公")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page_nav"},[a("span",{staticClass:"text"},[t._v("年度用电数据")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page_nav"},[a("span",{staticClass:"text"},[t._v("年度用水数据")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"img_box"},[i("img",{attrs:{src:a("1548")}}),t._v(" 园区用能 ")])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"page_nav2"},[i("img",{attrs:{src:a("a956")}}),t._v(" 区域用电排名 (kWh) ")])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"page_nav2"},[i("img",{attrs:{src:a("a956")}}),t._v(" 区域用水排名 (吨) ")])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"page_nav2"},[i("img",{attrs:{src:a("a956")}}),t._v(" 分项用电排名 (kWh) ")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page_nav"},[a("span",{staticClass:"text"},[t._v("本年度累计碳排放")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page_nav"},[a("span",{staticClass:"text"},[t._v("节能降碳")])])}],o=(a("4de4"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("4e82"),a("b0c0"),a("e9c4"),a("b680"),a("b64b"),a("d3b7"),a("25f0"),a("0643"),a("2382"),a("a573"),a("ed38")),r=a("918c"),l=a("26ad"),s=a("0c6a"),c=a("b6ee"),d=a("31d7"),p={components:{BuildingGroup:s["a"],Weather:o["default"],DateTime:r["default"],BaseChart:l["default"]},data:function(){return{loading:!1,curBuilding:this.gf.getCurBuilding(),buildingInfo:this.gf.projectInfo(),buildingList:this.gf.getBuildingList(),buildingIds:[],electricityDeviceIds:[],waterDeviceIds:[],pvPlantDeviceIds:[],electricityCarbonRate:.86,waterCarbonRate:3423643163629789e-20,carbonReduceDeviceSaveRate:.25,carbonReduceOtherRate:.05,replaceableElecByDay:200,replaceAbleTotal:0,pvFakeData:361123,carbonRates:[.95,.95,.95,.9,.9,.85,.8,.85,.9,.9,.95,.95],links:[{name:"能流图",path:"/energyMgt/energyFlow"},{name:"碳流图",path:"/energyMgt/carbonFlow"},{name:"用能监测",path:"/energyMgt/summary/energy"},{name:"分区记量",path:"/energyMgt/partition"},{name:"用能分项",path:"/energyMgt/subOption"},{name:"能源分析",path:"/energyMgt/analysis"},{name:"智能报表",path:"/energyMgt/report"},{name:"调峰降费",path:"/energyMgt/feeReduction"},{name:"节能检测",path:"/energyMgt/saveCheck"},{name:"报告下载",path:"/energyMgt/report/download"}],eTotal:0,pvPlantTotal:0,carbonTotal:0,carbonReduceTotal:0,carbonReduceTreeTotal:0,reduceTree:0,carbonDirectRate:.16,carbonIndirectRate:.84,carbonSepticTankRate:.0094,carbonCardRate:.0832,carbonFireFighterRate:.038,carbonCoolCoalRate:.0321,carbonElectricityRate:.5581,carbonEmployeeCommuteRate:.2098,carbonOfficeSuppliesRate:.0092,carbonWasteRate:.0231,carbonBusinessTravelRate:.0358,carbonOtherRate:.0013,avgCarbonPerson:0,avgCarbonArea:0,avgEnergySaveRate:"72.84%",electricityTotal:0,electricityPieOption:{},electricityLineOption:{},waterTotal:0,waterPieOption:{},waterTreemapOption:{},waterOptOption:{},waterBarOption:{},carbonPieOption:{},reducePieData:[],reducePieOption:{},carbonResults:[],baseSelectOpt:[{label:"年度累计",value:"年度累计",display:"year",from:this.$moment().startOf("year").format("YYYY-MM-DD"),to:this.$moment().format("YYYY-MM-DD")},{label:"去年",value:"去年",display:"month",from:this.$moment().add(-1,"year").startOf("year").format("YYYY-MM-DD"),to:this.$moment().add(-1,"year").endOf("year").format("YYYY-MM-DD")},{label:"上月",value:"上月",display:"day",from:this.$moment().add(-1,"month").startOf("month").format("YYYY-MM-DD"),to:this.$moment().add(-1,"month").endOf("month").format("YYYY-MM-DD")},{label:"上周",value:"上周",display:"day",from:this.$moment().add(-1,"week").startOf("week").format("YYYY-MM-DD"),to:this.$moment().add(-1,"week").endOf("week").format("YYYY-MM-DD")},{label:"今日",value:"今日",display:"hour",from:this.$moment().format("YYYY-MM-DD"),to:this.$moment().format("YYYY-MM-DD")}],carbonLineValue:"月度累计",carbonLineSelectOption:[{label:"月度累计",value:"月度累计",display:"month",from:this.$moment().startOf("year").format("YYYY-MM-DD"),to:this.$moment().format("YYYY-MM-DD")},{label:"年度累计",value:"年度累计",display:"year",from:this.$moment().add(-2,"year").startOf("year").format("YYYY-MM-DD"),to:this.$moment().add(3,"year").format("YYYY-MM-DD")}],carbonLineOption:{},energyRankingValue:"年度累计",energyRankingSelectOption:[],energyRanking:{electricityTotal:null,waterTotal:null,pvPlantTotal:null,"区域用电":{},"区域用水":{},"分项用电":{},"分项用水":{}},unitMap:{"区域用电":"kwh","区域用水":"吨","分项用电":"kwh","分项用水":"吨"}}},computed:{subLinks:function(){var t=[];return t=this.buildingList.map((function(t){return{name:t.name,path:"/index",building:t}})),t}},created:function(){var t=this;this.energyRankingSelectOption=JSON.parse(JSON.stringify(this.baseSelectOpt)),this.buildingDeviceList().then((function(e){t.electricityUse(),t.waterUse(),t.handleEnergyRanking(),t.handleCarbonLine()}))},mounted:function(){},methods:{openWindow:function(t){this.$forceUpdate(),window.open(this.gf.getFullPath()+t.path,"innerPage")},openLink:function(t,e){this.curBuilding=this.buildingList[e],this.gf.changeBuilding(this.curBuilding),setTimeout((function(){window.open("/#/","innerPage")}),1500)},buildingDeviceList:function(){var t=this;return this.buildingIds=this.buildingList.map((function(t){return t.id})),this.buildingNames={},this.buildingList.map((function(e){t.buildingNames[e.id]=e.name})),new Promise((function(e,a){Object(d["s"])({buildingIds:t.buildingIds.join(","),deviceType:"electricity"}).then((function(a){t.electricityDeviceIds=a.data,Object(d["s"])({buildingIds:t.buildingIds.join(","),deviceType:"water"}).then((function(a){t.waterDeviceIds=a.data,Object(d["s"])({buildingIds:t.buildingIds.join(","),deviceType:"pvPlant"}).then((function(a){t.pvPlantDeviceIds=a.data,e()}))}))}))}))},targetButtom:function(t){console.log(this.$refs.co2_energy_carbon);var e=this.$refs.co2_energy_carbon,a=e.attributes.class.nodeValue;a.indexOf("_hidden")>=0?e.className="co2_energy_carbon":e.className="co2_energy_carbon _hidden"},electricityUse:function(){var t=this;this.electricityTotal=0;var e=[{value:0,name:"年度外购市电"},{value:0,name:"年度光伏发电",selected:!0}],a=this.gf.workdayCount(this.$moment().startOf("year"),this.$moment())||0;this.replaceAbleTotal=a*this.replaceableElecByDay,a=0,Object(d["d"])({buildingId:this.curBuilding.id,deviceType:"electricity",type:"electricity",displayType:"year",from:this.$moment().startOf("year").format("YYYY-MM-DD"),to:this.$moment().format("YYYY-MM-DD"),year:this.$moment().format("YYYY"),compareYear:this.$moment().format("YYYY")}).then((function(i){i.data.datas.map((function(i){e[0].value=parseInt(i.totalVal)-a,t.electricityTotal=parseInt(i.totalVal)}))})).then((function(){Object(d["d"])({buildingId:t.curBuilding.id,deviceType:"pvPlant",type:"pvPlant",displayType:"year",from:t.$moment().startOf("year").format("YYYY-MM-DD"),to:t.$moment().format("YYYY-MM-DD"),year:t.$moment().format("YYYY"),compareYear:t.$moment().format("YYYY")}).then((function(a){a.data.datas.map((function(a){e[1].value=parseInt(a.totalVal),e[0].value=parseInt(t.electricityTotal),t.pvPlantTotal=parseInt(a.totalVal)}))})).then((function(){console.log(t.electricityTotal),t.carbonTotal=.551*t.electricityTotal/.5206,t.avgCarbonPerson=(t.carbonTotal/t.buildingInfo.person).toFixed(2),t.avgCarbonArea=(t.carbonTotal/t.buildingInfo.area).toFixed(2),t.carbonTotal=t.carbonTotal.toFixed(2),t.electricityPieOption=Object(c["electricityPieOption"])(e),console.log("---------",t.electricityPieOption)})).then((function(){t.carbonAnalysisComposition(),t.energySave()}))}));var i=[{visualMap:[0,0,0]},{name:"峰谷电价",data:[]},{name:"实时用电功率",data:[]},{name:"实时发电功率",data:[]},{name:"日均用电量",data:[]},{name:"日均发电量",data:[]}],n={};this.getDicts("energy_electricity_ratios").then((function(t){t.data.map((function(t){var e=t.dictValue.split(",");e.map((function(e){n[e]=parseFloat(t.remark)})),"g"==t.dictLabel?i[0].visualMap[0]=parseFloat(t.remark):"f"==t.dictLabel?i[0].visualMap[1]=parseFloat(t.remark):"j"==t.dictLabel&&(i[0].visualMap[2]=parseFloat(t.remark))})),i[1].data.push(n["00"]);for(var e=0;e<24;e++){var a=(100+e).toString().slice(1);i[1].data.push(n[a])}})).then((function(){Object(d["d"])({buildingId:t.curBuilding.id,deviceType:"electricity",type:"electricity",displayType:"hour",from:t.$moment().format("YYYY-MM-DD"),to:t.$moment().format("YYYY-MM-DD"),year:t.$moment().format("YYYY"),compareYear:t.$moment().format("YYYY")}).then((function(e){e.data.datas.map((function(e){var a=e.recordedAt.split(" ")[1],n=t.$moment().format("HH");a<=n&&i[2].data.push(e.totalVal)}))})).then((function(){Object(d["d"])({buildingId:t.curBuilding.id,deviceType:"electricity",type:"electricity",displayType:"hour",from:t.$moment().add(-1,"day").format("YYYY-MM-DD"),to:t.$moment().add(-1,"day").format("YYYY-MM-DD"),year:t.$moment().format("YYYY"),compareYear:t.$moment().format("YYYY")}).then((function(t){t.data.datas.map((function(t){i[4].data.push(t.totalVal)}))})).then((function(){Object(d["c"])({buildingId:t.curBuilding.id,deviceType:"electricity",type:"electricity",displayType:"hour",from:t.$moment().add(-1,"day").format("YYYY-MM-DD"),to:t.$moment().add(-1,"day").format("YYYY-MM-DD")}).then((function(t){t.data.datas.map((function(t){i[4].data.push(t.totalVal)}))})).then((function(){t.electricityLineOption=Object(c["electricityLineOption"])(i),console.log(t.electricityLineOption)}))}))}))}))},waterUse:function(){var t=this;this.waterTotal=0;var e=[],a={xAxis:[],data:[]};Object(d["j"])({buildingId:this.curBuilding.id,from:this.$moment().startOf("year").format("YYYY-MM-DD"),to:this.$moment().endOf("year").format("YYYY-MM-DD"),deviceTypes:"分项用水,区域用水,光伏发电",displayType:"year"}).then((function(i){var n=i.data;for(var o in n)if("分项用水"==o)for(var r in n[o])e.push({value:parseFloat(n[o][r].total.toFixed(2)),name:r}),t.waterTotal+=n[o][r].total;else if("区域用水"==o)for(var l in n[o])a.xAxis.unshift(l),a.data.unshift(parseFloat(n[o][l].total).toFixed(2))})).then((function(){t.waterTotal=t.waterTotal.toFixed(2),t.waterPieOption=Object(c["waterPieOption"])(e),t.waterBarOption=Object(c["waterBarOption"])(a);var i=["#0DD3A3","#F56C6C","#5628EE"],n=e.map((function(t,e){return t.itemStyle={color:i[e]},t})),o=[{type:"treemap",left:"15%",right:"15%",top:"15%",bottom:"15%",itemStyle:{gapWidth:2},data:[{name:"用水结构",value:t.waterTotal,children:n}]}];t.waterTreemapOption=Object(c["waterTreemapOption"])(o),console.log("waterTreemapOption",JSON.stringify(t.waterTreemapOption));var r=[],l={data:e.map((function(e,a){return e.name=e.name+parseInt(100*e.value/t.waterTotal)+"%",e.data=[e.value],e.type="bar",e.barWidth="100%",e.stack="total",e.label={show:!1,formatter:"{c}"},r.unshift(e.name),e}))};l.legend=r,t.waterOptOption=Object(c["waterOptOption"])(l)}))},handleCarbonLine:function(){this.carbonAnalysisPrediction()},carbonAnalysisPrediction:function(){var t=this,e={data:[{name:"实际",data:[]},{name:"目标",data:[]},{name:"预测",data:[]}],xAxis:[]},a=this.carbonLineSelectOption.filter((function(e){return e.value==t.carbonLineValue})).pop();if("month"==a.display)e.xAxis=["01","02","03","04","05","06","07","08","09","10","11","12"];else if("year"==a.display)for(var i=this.$moment(a.from).format("YYYY");i<this.$moment(a.to).format("YYYY");i++)e.xAxis.push(i);Object(d["d"])({buildingId:this.curBuilding.id,deviceType:"electricity",type:"electricity",displayType:a.display,from:a.from,to:a.to,year:this.$moment().format("YYYY"),compareYear:this.$moment().format("YYYY")}).then((function(a){var i=0;a.data.datas.map((function(a,n){var o=a.totalVal/t.carbonElectricityRate*t.electricityCarbonRate;i+=o,e.data[0].data.push([a.recordedAt,o.toFixed(2)])}));var n=i/a.data.datas.length;a.data.datas.map((function(a,i){var o=(n*t.carbonRates[i]).toFixed(2);e.data[1].data.push([a.recordedAt,o])}))})).then((function(){Object(d["c"])({buildingId:t.curBuilding.id,deviceType:"electricity",type:"electricity",displayType:a.display,from:a.from,to:a.to,year:t.$moment().format("YYYY"),compareYear:t.$moment().format("YYYY")}).then((function(a){a.data.datas.map((function(a,i){var n=a.totalVal/t.carbonElectricityRate*t.electricityCarbonRate;e.data[2].data.push([a.recordedAt,n.toFixed(2)])}))})).then((function(){t.carbonLineOption=Object(c["carbonLineOption"])(e)}))}))},carbonAnalysisComposition:function(){var t=.001,e={data:[{name:"间接",value:(this.carbonTotal*this.carbonIndirectRate*t).toFixed(2)},{name:"直接",value:(this.carbonTotal*this.carbonDirectRate*t).toFixed(2)}],other:[{name:"外购电力",value:(.5206*this.carbonTotal*t).toFixed(2)},{name:"外购自来水",value:(this.waterTotal*this.waterCarbonRate*t).toFixed(2)},{name:"自有燃油车",value:(this.carbonTotal*this.carbonCardRate*t).toFixed(2)},{name:"灭火器逸散",value:(this.carbonTotal*this.carbonFireFighterRate*t).toFixed(2)},{name:"冷媒逸散",value:(this.carbonTotal*this.carbonCoolCoalRate*t).toFixed(2)},{name:"化粪池逸散",value:(this.carbonTotal*this.carbonSepticTankRate*t).toFixed(2)},{name:"员工通勤",value:(this.carbonTotal*this.carbonEmployeeCommuteRate*t).toFixed(2)},{name:"办公用品",value:(this.carbonTotal*this.carbonOfficeSuppliesRate*t).toFixed(2)},{name:"废弃物处理",value:(this.carbonTotal*this.carbonWasteRate*t).toFixed(2)},{name:"商务差旅",value:(this.carbonTotal*this.carbonBusinessTravelRate*t).toFixed(2)},{name:"其他",value:(this.carbonTotal*this.carbonOtherRate*t).toFixed(2)}]};this.carbonPieOption=Object(c["carbonPieOption"])(e)},energySave:function(){var t=.001,e=[{name:"可再生资源",value:(this.pvPlantTotal*this.electricityCarbonRate*t).toFixed(2)},{name:"设备节能",value:(this.carbonTotal*this.carbonReduceDeviceSaveRate*t).toFixed(2)},{name:"围护结构改造",value:0},{name:"柔性调峰",value:(this.carbonTotal*this.carbonReduceOtherRate*t).toFixed(2)}];this.carbonReduceTotal=(this.pvPlantTotal*this.electricityCarbonRate*t+this.carbonTotal*this.carbonReduceDeviceSaveRate*t+this.carbonTotal*this.carbonReduceOtherRate*t).toFixed(2),this.carbonReduceTreeTotal=(this.carbonReduceTotal/18.334).toFixed(2),this.reducePieOption=Object(c["reducePieOption"])(e),this.reducePieData=e;var a=parseFloat(this.carbonReduceTotal)/this.electricityCarbonRate;this.carbonResults=[{name:"等效二氧化硫减排",value:(.03*a).toFixed(2),percent:80,unit:"tso2"},{name:"等效标煤减排",value:(.36*a).toFixed(2),percent:80,unit:"tc"},{name:"等效烟尘减排",value:(.272*a).toFixed(2),percent:80,unit:"tsmoke"}],this.reduceTree=(.997*a/18.334).toFixed(2)},handleEnergyRanking:function(){var t=this,e=this.energyRankingSelectOption.filter((function(e){return e.value==t.energyRankingValue})).pop();this.energyRanking["electricityTotal"]=0,this.energyRanking["waterTotal"]=0,this.energyRanking["pvPlantTotal"]=0,this.energyRanking["electricityRanking"]=[],this.energyRanking["waterRanking"]=[],this.energyRanking["分项用电_subOpt"]={},this.energyRanking["分项用水_subOpt"]={},this.energyRanking["分项用电SubOpt"]={},this.energyRanking["分项用水SubOpt"]={};var a=1;this.buildingIds.map((function(i){Object(d["j"])({buildingId:i,from:e.from,to:e.to,deviceTypes:"分项用电,分项用水"}).then((function(e){var n=e.data;for(var o in a+=1,n){for(var r in t.energyRanking[o][i]||(t.energyRanking[o][i]={total:0,unit:t.unitMap[o],data:[]}),t.energyRanking[o][i]["total"]=0,t.energyRanking[o][i]["data"]=[],n[o])t.energyRanking[o][i].data.push({name:r,value:n[o][r].total}),t.energyRanking[o][i]["total"]+=n[o][r].total;for(var l in n[o]){t.energyRanking[o+"_subOpt"]||(t.energyRanking[o+"_subOpt"]={}),t.energyRanking[o+"_subOpt"][l]||(t.energyRanking[o+"_subOpt"][l]=0);var s=t.gf.getLocalObject("buildingConfig_"+i);t.energyRanking[o+"_subOpt"][l]+=s.hasOwnProperty("dashboard_"+o+"_"+l)?t.energyRanking[o][i]["total"]*s["dashboard_"+o+"_"+l]:n[o][l].total}}if(t.energyRanking["electricityTotal"]+=t.energyRanking["分项用电"][i].total,t.energyRanking["electricityRanking"].push({buildingId:i,name:t.buildingNames[i],total:t.energyRanking["分项用电"][i].total}),t.energyRanking["waterTotal"]+=t.energyRanking["分项用水"][i].total,t.energyRanking["waterRanking"].push({buildingId:i,name:t.buildingNames[i],total:t.energyRanking["分项用水"][i].total}),a>t.buildingIds.length){for(var c in t.energyRanking["electricityTotal"]=parseFloat(t.energyRanking["electricityTotal"].toFixed(2)),t.energyRanking["electricityRanking"].map((function(e){return e.percent=parseFloat((100*e.total/t.energyRanking["electricityTotal"]).toFixed(2)),e.total=parseFloat(e.total.toFixed(2)),e})),t.energyRanking["waterRanking"].map((function(e){return e.percent=parseFloat((100*e.total/t.energyRanking["waterTotal"]).toFixed(2)),e.total=parseFloat(e.total.toFixed(2)),e})),t.energyRanking["electricityRanking"].sort((function(t,e){return e.total-t.total})),t.energyRanking["waterRanking"].sort((function(t,e){return e.total-t.total})),t.energyRanking["分项用电SubOpt"]={},t.energyRanking["分项用水SubOpt"]={},t.energyRanking["分项用电_subOpt"]){var d=parseFloat(t.energyRanking["分项用电_subOpt"][c].toFixed(2));t.energyRanking["分项用电SubOpt"][c]={total:d,percent:t.energyRanking["electricityTotal"]>0?parseFloat((100*d/t.energyRanking["electricityTotal"]).toFixed(2)):0}}for(var p in console.log(t.energyRanking),t.energyRanking["分项用水_subOpt"]){var u=parseFloat(t.energyRanking["分项用水_subOpt"][p].toFixed(2));t.energyRanking["分项用水SubOpt"][p]={total:u,percent:t.energyRanking["waterTotal"]>0?parseFloat((100*u/t.energyRanking["waterTotal"]).toFixed(2)):0}}console.log(t.energyRanking),t.$forceUpdate()}}))}))}}},u=p,m=(a("3463"),a("2877")),f=Object(m["a"])(u,i,n,!1,null,"1e9bcc7e",null);e["default"]=f.exports},"918c":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"co2_datetime"},[a("div",{staticClass:"co2_time"},[a("span",[t._v(t._s(t.dateTime.date))]),a("span",[t._v(t._s(t.dateTime.week))]),a("span",[t._v(t._s(t.dateTime.time))])])])},n=[],o={data:function(){return{dateTime:{time:"",date:"",week:""}}},mounted:function(){this.initTodayStr()},methods:{initTodayStr:function(){var t=this,e=this.$moment();this.dateTime={date:e.format("YYYY/MM/DD"),week:e.format("dddd"),time:e.format("HH:mm:ss")},setTimeout((function(){t.initTodayStr()}),1e3)}}},r=o,l=(a("5e2f"),a("2877")),s=Object(l["a"])(r,i,n,!1,null,"c872b0bc",null);e["default"]=s.exports},"95f6":function(t,e,a){t.exports=a.p+"static/img/co2_build.45aafdb4.png"},a524:function(t,e,a){var i,n,o;(function(r,l){n=[e,a("313e")],i=l,o="function"===typeof i?i.apply(e,n):i,void 0===o||(t.exports=o)})(0,(function(t,e){var a=function(t){"undefined"!==typeof console&&console&&console.error&&console.error(t)};if(e){var i="#B9B8CE",n="#100C2A",o=function(){return{axisLine:{lineStyle:{color:i}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},r=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],l={darkMode:!0,color:r,backgroundColor:n,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:i}},textStyle:{color:i},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:i}},dataZoom:{borderColor:"#71708A",textStyle:{color:i},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:i}},timeline:{lineStyle:{color:i},label:{color:i},controlStyle:{color:i,borderColor:i}},calendar:{itemStyle:{color:n},dayLabel:{color:i},monthLabel:{color:i},yearLabel:{color:i}},timeAxis:o(),logAxis:o(),valueAxis:o(),categoryAxis:o(),line:{symbol:"circle"},graph:{color:r},gauge:{title:{color:i}},candlestick:{itemStyle:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}};l.categoryAxis.splitLine.show=!1,e.registerTheme("dark",l)}else a("ECharts is not Loaded")}))},a956:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAUCAYAAAEPcKYrAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAGaADAAQAAAABAAAAFAAAAACgY1PvAAADiklEQVQ4EZ1VTUhUURQ+5zmOmGWOM5bOWFhZEWlBkva3qKBaFUXLFlFG24IWRURKYQsDW7TIjRCkG6OCglpEIkFZLYIipVkVpWPUjKNW6ozOO33nNXd8w1iUF57n3O985+eee+5INNcKjUhLRVQkY9NNFjCnhRXNoSkQjEl3oH9wxLhZzSJWKCoXHEAo6a0IdeV4psO9Ml6OrIxKvWEG+oc+K+hRIAWFLTqg+nCAnWI8jstvw4TlpY3G07Giqg4c8riSwH9JQp3GYU5ZFZeqapGC4LgE3IRMmmBUptC1AmOc9JNvZpSqPjJ/BJYwuEoOxWSLLdTnbJgaIdtQzmLdu1c+0/pPfh5QzKnbGDOHYdqO+munifryhBpg34ezHFaedsIyDgZQkIVW2za15xMtV9xiuqm4aZ3bJ0tH1/ZoI7LAv23qRPJFhEOjsvuPPERN6HnMmZRY1h/5uXTgS41xctqM+2/AVb8w04mOtOpM2F4aixRzkSGrtHAfXUo2oKeQQuhhI7CXkqT3FTFpMzaVbNIj6oRl0daUTW/cBIdE1EN+2h9hnvCg789RyjZEbVUyHMPQE+i/F+RJBGkDfoti9LMyLrs8ET9v1yhI/U4lxqUUTg/hVA293yaqxH4SeiFmegf032uniCc8Qkl4ZDBjSzvM4PKKDZYlke0DzjYcFFkA/XKWcT4b3NdFbZZOw7/455Tsdlo2JptT03RemDbgeHHY7qz109Ve5hnDK//84xFeSy0CPSspLDoRLuPvxmZk5h0aIDgqm9DEI3A6NjNNPgdHZ9OrLhyj02jJCr1KxZLDg91k26vE4m/h+nU5CZTjnETnORijg9i341uiBvfCWCTxwi6B/BjD9gS2hRk7053iUjoW5twTGA7rC0uk6IMB3BJP9SLKeI2568RX4ra5dSRPkEVnvD7qwC/QlNumOofGZY2dxMCmF6qOI/hO9PkUunQUd5HnmJieImEXAtYCOwnsNU53Q2cV+jl8ixwe/oAzge/KUIBbFINOpGMnI8491Hgtupaw6QECZV42Et8GMaKJ0049kPdQxHXYplMFVGdN0Vu1uRd+2s5GSrnVSeI2qI7RbEJ7mt24JkHQ+wi6Era9sH0FdhtlokY6BPw4CmsBp8L4oSPbhvzcN2cSQ6qOSXEij8rw1ssRqBdBZqeRycY+Cu7soDDd9TA1NfpooJkZnZzn0v+k/+v6C0GOgOM6m+SwAAAAAElFTkSuQmCC"},b115:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[!t.cgBuildingFlag||"2D"!=t.settings.dashboard_display&&"2DCO2"!=t.settings.dashboard_display?t._e():a("div",{staticClass:"ibg",style:{backgroundImage:"url("+t.settings.dashboard_bg_img+")"}}),a("div",{staticClass:"co2View co2_container"},[a("div",{staticClass:"layout_header_img",attrs:{src:"@/assets/image/co2_header_img.png"}}),a("div",{staticClass:"layout_bottom_img",attrs:{src:"@/assets/image/co2_bottom_img.png"}}),a("transition",{attrs:{mode:"out-in"}},[a(t.viewType,{tag:"component",staticClass:"co2_main"},[t._t("default")],2)],1)],1)])},n=[],o=(a("a9e3"),a("823d")),r=a("0c6a"),l={components:{BuildingGroup:r["a"],EnergyView:o["default"]},props:{_cityId:{type:Number,default:0}},mixins:[],data:function(){return{cgBuildingFlag:!0,viewType:"EnergyView",buildingList:this.gf.getBuildingList(),settings:this.gf.projectBaseSettings()}},methods:{openWindow:function(t){window.open(this.gf.getFullPath()+t.path,"innerPage")},changeMenu:function(t){this.viewType=t},handleBimIconClick:function(t){console.log("handleBimIconClick",t)}}},s=l,c=a("2877"),d=Object(c["a"])(s,i,n,!1,null,null,null);e["default"]=d.exports},b6ee:function(t,e,a){"use strict";a.r(e),a.d(e,"electricityPieOption",(function(){return r})),a.d(e,"electricityPieOption2",(function(){return l})),a.d(e,"electricityPieOption3",(function(){return s})),a.d(e,"electricityPieOption4",(function(){return c})),a.d(e,"electricityLineOption",(function(){return d})),a.d(e,"waterPieOption",(function(){return p})),a.d(e,"waterTreemapOption",(function(){return u})),a.d(e,"waterOptOption",(function(){return m})),a.d(e,"waterBarOption",(function(){return f})),a.d(e,"carbonLineOption",(function(){return g})),a.d(e,"carbonPieOption",(function(){return h})),a.d(e,"reducePieOption",(function(){return b})),a.d(e,"devicePieOption",(function(){return y})),a.d(e,"SecurityBar",(function(){return v})),a.d(e,"radiationCurveOption",(function(){return A})),a.d(e,"deviceFaultOption",(function(){return _})),a.d(e,"repairOption",(function(){return x})),a.d(e,"gaugeOption",(function(){return C})),a.d(e,"electricityWanterLineOption",(function(){return w}));var i=a("2909"),n=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("b0c0"),a("b680"),a("d3b7"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("313e")),o=function(t,e){t||(t="#ededed");var a="rgba("+parseInt("0x"+t.slice(1,3))+","+parseInt("0x"+t.slice(3,5))+","+parseInt("0x"+t.slice(5,7))+","+(e||"1")+")";return a};function r(t){return{legend:{show:!1},color:["#79FFFF","#67C23A","#FD6F4A"],graphic:{elements:[{type:"image",left:"center",top:"center",style:{image:"/images/co2/co2_icon_5.png",width:28,height:30}}]},series:[{name:"用电分析",type:"pie",startAngle:180,center:["50%","50%"],radius:["40%","50%"],selectedMode:"single",selectedOffset:4,avoidLabelOverlap:!1,label:{normal:{alignTo:"labelLine",bleedMargin:5,color:"#fff",fontSize:11,formatter:"{b}\n{c|{c}} {b|kwh}\n{a|占比: {d}}%",rich:{a:{padding:[8,0]},b:{color:"#B1C6D5",fontSize:10},c:{color:"#00C2FF",fontWeight:"bold"}}}},labelLine:{normal:{show:!0}},data:t}]}}function l(t){var e=0,a=[];return t=t.map((function(t){return t.per=0,t.unit="kwh",e+=t.value,a.push(t.name),t})),0!=e&&(t=t.map((function(t){return t.per=parseFloat((100*t.value/e).toFixed(2)),t}))),console.log(t),{title:{text:"{name| 共计}\n{value|"+e.toFixed(2)+"}\n{unit|tce}",top:"30%",left:"26.5%",textAlign:"center",textStyle:{rich:{name:{color:"#D0DEEE",fontSize:14,lineHeight:25},value:{color:"#FFFFFF",fontSize:16,fontWeight:"bold"},unit:{color:"#D0DEEE",fontSize:16,fontWeight:"bold"}}}},legend:{orient:"vertical",top:"center",right:10,icon:"rect",itemWidth:8,itemHeight:15,itemGap:15,position:"center",data:a,formatter:function(e){for(var a,i,n,o=0;o<t.length;o++)t[o].name===e&&(t[o].value,n=t[o].unit,a=t[o].value,i=t[o].per);return"{a|"+e+"}\n{b|"+a+" "+n+"}\n{c|"+i+" %}"},textStyle:{padding:[0,0,0,5],rich:{a:{lineHeight:20,fontSize:12,color:"#D0DEEE"},b:{fontSize:14,color:"#ffffff"},c:{fontSize:12,color:"#D0DEEE"}}}},xAxis:{show:!1},yAxis:{show:!1},color:["#79FFFF","#67C23A","#FD6F4A"],series:[{name:"总量",type:"pie",radius:["52%","75%"],center:["28%","50%"],itemStyle:{borderRadius:2,borderColor:"rgba(0,0,0,0.5)",borderWidth:2},label:{show:!1},data:t}]}}function s(t){var e=0,a=[];return t=t.map((function(t){return t.per=0,t.unit=t.unit||"kwh",e+=t.value,a.push(t.name),t})),0!=e&&(t=t.map((function(t){return t.per=(100*t.value/e).toFixed(2),t.value=t.value.toFixed(2),t}))),console.log(t),{title:{text:"{name| 共计}\n{value|"+e.toFixed(2)+"}\n{unit|m³}",top:"30%",left:"26.5%",textAlign:"center",textStyle:{rich:{name:{color:"#D0DEEE",fontSize:14,lineHeight:25},value:{color:"#FFFFFF",fontSize:16,fontWeight:"bold"},unit:{color:"#D0DEEE",fontSize:16,fontWeight:"bold"}}}},legend:{type:"scroll",orient:"vertical",top:"center",right:10,icon:"rect",itemWidth:8,itemHeight:15,itemGap:10,position:"center",data:a,formatter:function(e){for(var a,i,n,o=0;o<t.length;o++)t[o].name===e&&(t[o].value,n=t[o].unit,a=t[o].value,i=t[o].per);return"{a|"+e+"}\n{b|"+a+" "+n+"} {c|"+i+" %}"},textStyle:{padding:[0,0,0,5],rich:{a:{lineHeight:20,fontSize:12,color:"#D0DEEE"},b:{fontSize:14,width:100,color:"#ffffff"},c:{fontSize:12,color:"#D0DEEE"}}}},xAxis:{show:!1},yAxis:{show:!1},series:[{name:"总量",type:"pie",radius:["52%","75%"],center:["28%","50%"],itemStyle:{borderRadius:2,borderColor:"rgba(0,0,0,0.5)",borderWidth:2},label:{show:!1},data:t}]}}function c(t){var e=0,a=[];return t=t.map((function(t){return t.per=0,t.unit="kWh",e+=t.value,a.push(t.name),t})),0!=e&&(t=t.map((function(t){return t.per=parseFloat((100*t.value/e).toFixed(2)),t}))),console.log(t),{title:{text:"{name| 共计}\n{value|"+e.toFixed(2)+"}\n{unit|kWh}",top:"30%",left:"26.5%",textAlign:"center",textStyle:{rich:{name:{color:"#D0DEEE",fontSize:14,lineHeight:25},value:{color:"#FFFFFF",fontSize:16,fontWeight:"bold"},unit:{color:"#D0DEEE",fontSize:16,fontWeight:"bold"}}}},legend:{orient:"vertical",top:"center",right:10,icon:"rect",itemWidth:8,itemHeight:15,itemGap:15,position:"center",data:a,formatter:function(e){for(var a,i,n,o=0;o<t.length;o++)t[o].name===e&&(t[o].value,n=t[o].unit,a=t[o].value,i=t[o].per);return"{a|"+e+"}\n{b|"+a+" "+n+"}\n{c|"+i+" %}"},textStyle:{padding:[0,0,0,5],rich:{a:{lineHeight:20,fontSize:12,color:"#D0DEEE"},b:{fontSize:14,color:"#ffffff"},c:{fontSize:12,color:"#D0DEEE"}}}},xAxis:{show:!1},yAxis:{show:!1},color:["#79FFFF","#67C23A","#FD6F4A"],series:[{name:"总量",type:"pie",radius:["52%","75%"],center:["28%","50%"],itemStyle:{borderRadius:2,borderColor:"rgba(0,0,0,0.5)",borderWidth:2},label:{show:!1},data:t}]}}function d(t){return{color:["#F9B30A","#0DC9E5","#0DD3A3","#CA334E"],legend:{top:"88%",left:"center",data:[t[2].name,t[3].name]},grid:{left:40,right:20,bottom:45},xAxis:{splitLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:.5}},axisLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.31)"}},data:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24"]},yAxis:[{scale:!0,name:"实时用电 单位（kWh）",nameTextStyle:{padding:[0,0,0,50]},splitLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:.5}},axisLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.31)"}},formformatter:function(t){return t.toFixed(1)}},{show:!1,scale:!0,name:"峰谷电价",splitLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:.5}},formformatter:function(t){return t.toFixed(1)}}],series:[,{name:t[2].name,type:"line",showSymbol:!1,smooth:!0,yAxisIndex:0,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#F9B30A"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(118, 81, 8, 0.59)"},{offset:1,color:"transparent"}])}},data:t[2].data},{name:t[3].name,type:"line",showSymbol:!1,smooth:!0,yAxisIndex:0,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#0DC9E5"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(4, 144, 186, 0.48)"},{offset:1,color:"transparent"}])}},data:t[3].data},{name:t[4].name,type:"bar",barWidth:"40%",showSymbol:!1,smooth:!0,yAxisIndex:0,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#0DD3A3"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(13, 211, 163, 0.31)"},{offset:1,color:"transparent"}])}},data:t[4].data},{name:t[5].name,type:"bar",barWidth:"40%",showSymbol:!1,smooth:!0,yAxisIndex:0,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#0DD3A3"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(13, 211, 163, 0.31)"},{offset:1,color:"transparent"}])}},data:t[5].data},{name:t[1].name,type:"line",step:"start",showSymbol:!1,yAxisIndex:1,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(10, 178, 216, 0.31)"},{offset:1,color:"transparent"}])}},data:t[1].data}]}}function p(t){return{legend:{show:!1},tooltip:{trigger:"item"},graphic:{elements:[{type:"text",left:"center",top:"48%",style:{text:"用水\n结构",fill:"#19ECFF",fontSize:12,fontWeight:"bold",textAlign:"center"}}]},series:[{name:"用水结构",type:"pie",center:["50%","55%"],radius:["30%","45%"],startAngle:150,avoidLabelOverlap:!1,itemStyle:{borderRadius:2,borderColor:"transparent",borderWidth:2},label:{normal:{color:"#fff",fontSize:10,fontWeight:"bold",lineHeight:14,formatter:"{b}：\n{c|{c}} {b|吨}\n{a|{d}%}",rich:{b:{color:"#B1C6D5",fontSize:10},c:{color:"#00C2FF",fontWeight:"bold"}}}},labelLine:{show:!0,length2:1,minTurnAngle:0},data:t}]}}function u(t){return{legend:{show:!1},tooltip:{trigger:"item"},series:t}}function m(t){return{color:["#006EE5","#457DE3","#F07353"],tooltip:{trigger:"axis"},legend:{padding:[0,0,0,20],top:"center",left:"left",orient:"vertical",itemHeight:15,itemGap:10,data:t.legend},grid:{left:"70%",right:"5%",bottom:"3%",containLabel:!0},xAxis:{show:!1,data:["用水分项"],type:"category"},yAxis:{show:!1,type:"value"},series:t.data}}function f(t){return{grid:{top:"25%",right:10,left:50,bottom:45},legend:{show:!1},title:[{text:"用水",top:"5%",left:"15%",textStyle:{fontSize:14,color:"#B1C6D5"}},{text:"单位：(吨)",top:"5%",right:10,textStyle:{fontSize:12,color:"#B1C6D5"}}],xAxis:{boundaryGap:!0,axisLabel:{formatter:function(t){return t.split("").join("\n")}},data:t.xAxis},yAxis:{splitLine:{show:!0,lineStyle:{color:"#979797",width:.5}}},series:[{name:"用水",type:"bar",barWidth:8,barGap:0,itemStyle:{normal:{color:new n["graphic"].LinearGradient(0,1,0,0,[{offset:0,color:"#0B4EC3"},{offset:.6,color:"#138CEB"},{offset:1,color:"#4FCBCB"}],!1)}},data:t.data},{name:"用水",type:"bar",barWidth:8,tooltip:{show:!1},label:{normal:{show:!1,position:"insideRight"}},itemStyle:{normal:{color:new n["graphic"].LinearGradient(0,1,0,0,[{offset:0,color:"#09337C"},{offset:.6,color:"#0761C0"},{offset:1,color:"#4FCBCB"}],!1)}},data:t.data},{name:"用水",type:"pictorialBar",symbol:"diamond",symbolRotate:0,symbolSize:["16","10"],symbolOffset:["0","-5"],symbolPosition:"end",z:3,tooltip:{show:!1},label:{normal:{show:!1,position:"insideRight"}},itemStyle:{normal:{color:"#79FFFF"}},data:t.data}]}}function g(t){return{grid:{top:25,left:50,right:40,bottom:30},legend:{icon:"rect",itemWidth:12,itemHeight:10},xAxis:[{type:"time",show:!1,splitLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:.5}},axisLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.31)"}},axisLabel:{rotate:0,textStyle:{color:"#39CBD0"}}},{type:"category",show:!0,position:"bottom",data:t.xAxis,axisLabel:{textStyle:{color:"#39CBD0"}}}],yAxis:{name:"碳排放 单位(tco2)",nameTextStyle:{padding:[0,0,0,30]},splitLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:.5}},axisLine:{show:!0,lineStyle:{color:"rgba(10, 178, 216, 0.31)"}},axisLabel:{textStyle:{color:"#39CBD0"},formatter:function(t){return(t/1e4).toFixed(2)}}},series:[{name:t.data[0].name,type:"bar",barWidth:"30%",smooth:!0,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#F9B30A"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(118, 81, 8, 0.59)"},{offset:1,color:"transparent"}])}},data:t.data[0].data},{name:t.data[1].name,type:"line",barWidth:"30%",smooth:!0,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#E8738E"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(221, 120, 135, 0.22)"},{offset:1,color:"transparent"}])}},data:t.data[1].data},{name:t.data[2].name,type:"line",barWidth:"30%",smooth:!0,label:{normal:{show:!1}},itemStyle:{},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(4, 144, 186, 0.48)"},{offset:1,color:"transparent"}])}},data:t.data[2].data}]}}function h(t){var e=0;return t.data.forEach((function(t){e+=parseFloat(t.value)})),e=parseFloat(e.toFixed(2)),{grid:{left:0,right:0,top:0,bottom:0},legend:{show:!1},tooltip:{trigger:"item"},color:["#003DFF","#494949"],graphic:{elements:[{type:"text",left:"center",top:"43%",style:{text:"年度累计",fill:"#F9FFFE",fontSize:12,textAlign:"center"}},{type:"text",left:"center",top:"50%",style:{text:e,fill:"#19ECFF",fontSize:12,fontWeight:"bold",textAlign:"center"}},{type:"text",left:"center",top:"56%",style:{text:"tco2",fill:"#B1C6D5",fontSize:10,fontWeight:"bold",textAlign:"center"}}]},series:[{name:"年碳排放",type:"pie",center:["50%","50%"],radius:["26%","40%"],startAngle:150,avoidLabelOverlap:!1,label:{normal:{position:"inner",color:"#F9FFFE",fontSize:10,fontWeight:"bold",lineHeight:13,formatter:"{b}\n{d}%",rich:{a:{color:"#19ECFF"},b:{color:"#B1C6D5",fontSize:10}}}},labelLine:{show:!0},data:t.data},{name:"其他",type:"pie",center:["50%","50%"],radius:["50%","62%"],roseType:"area",avoidLabelOverlap:!1,itemStyle:{borderRadius:1,borderColor:"transparent",borderWidth:2},labelLayout:{hideOverlap:!1},label:{normal:{color:"#fff",fontSize:10,fontWeight:"bold",lineHeight:20,padding:[0,-55],formatter:"{a|{d}%}\n{b}",rich:{a:{color:"#19ECFF",fontSize:12}}}},labelLine:{show:!0,length:10,length2:60,lineStyle:{color:"#0464D3",type:"dashed"}},data:t.other}]}}function b(t){return{legend:{show:!1},color:["#69ECFC","#457DE3","#FFFFFF","#67C23A"],tooltip:{trigger:"item"},graphic:{elements:[{type:"text",left:"center",top:"center",style:{text:"降碳贡献",fill:"#FFFFFF",fontSize:14,fontWeight:"bold",textAlign:"center"}}]},series:[{name:"降碳贡献",type:"pie",center:["50%","50%"],radius:["80%","90%"],startAngle:150,avoidLabelOverlap:!1,itemStyle:{borderRadius:20,borderColor:"transparent",borderWidth:10},label:{normal:{show:!1,position:"inner"}},labelLine:{show:!1},data:t}]}}function y(t){var e=t.color||[],a=[];if(0===t.graphics.type){var i=0;t.data.forEach((function(t){i+=t.value})),i=parseInt(i),a=[{type:"text",left:"center",top:"36%",style:{text:"年度累计\n"+i+"\nkWh",fill:"#ffffff",fontSize:12,fontWeight:"bold",textAlign:"center"}}]}else{var n=0;t.data.forEach((function(t){n+=t.value})),n=parseInt(n),a=[{type:"text",left:"center",top:"40%",style:{text:n,fill:"#ffffff",fontSize:18,fontWeight:"bold",textAlign:"center"}},{type:"text",left:"center",top:"60%",style:{text:t.graphics.text,fill:"#ffffff",fontSize:14,textAlign:"center"}}]}return{legend:{show:!1},color:e,graphic:{elements:a},series:[{type:"pie",center:["50%","50%"],radius:["70%","80%"],startAngle:150,avoidLabelOverlap:!1,itemStyle:{borderRadius:20,borderColor:"transparent",borderWidth:10},label:{normal:{show:!1}},labelLine:{show:!1},data:t.data}]}}function v(t){return{grid:{right:20},legend:{show:!1},xAxis:{boundaryGap:!0,axisLine:{show:!0,lineStyle:{color:"#3489BF",width:2}},axisTick:{show:!0,inside:!0,length:3,lineStyle:{color:"#B1E1FF"}},axisLabel:{textStyle:{color:"#D9D9D9"}},data:t.xAxis},yAxis:{name:t.yAxisName,nameGap:12,nameTextStyle:{color:"#D9D9D9"},splitLine:{show:!0,lineStyle:{color:"#112851",width:.5}},axisLabel:{textStyle:{color:"#D9D9D9"}}},series:[{name:t.data[0].name,type:"bar",barWidth:6,label:{normal:{show:!1}},itemStyle:{normal:{color:new n["graphic"].LinearGradient(0,1,0,0,[{offset:0,color:"transparent"},{offset:1,color:t.colors[0]}])}},data:t.data[0].data},{name:t.data[1].name,type:"line",showSymbol:!1,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:t.colors[1]}}},data:t.data[1].data}]}}function A(t){return{grid:{top:20,left:40,right:20,bottom:30},legend:{show:!1,icon:"rect",itemWidth:12,itemHeight:10},xAxis:{boundaryGap:!0,splitLine:{show:!1,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:.5}},axisLine:{show:!0,lineStyle:{color:"#3489BF",width:2}},axisLabel:{rotate:0,textStyle:{color:"#D9D9D9"}},axisTick:{show:!0,inside:!0,lineStyle:{color:"#D9D9D9"}},data:t.xAxis},yAxis:{scale:!0,nameTextStyle:{padding:[0,0,0,30]},splitLine:{show:!0,lineStyle:{color:"rgba(0, 83, 209, 0.2)",type:"solid",width:.5}},axisLine:{show:!1,lineStyle:{color:"rgba(10, 178, 216, 0.31)"}},axisLabel:{textStyle:{color:"#D9D9D9"}}},series:[{name:"功率辐射曲线",type:"line",smooth:!0,showSymbol:!1,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#0DD3A3"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(13, 211, 163, 0.59)"},{offset:1,color:"transparent"}])}},data:t.data}]}}function _(t){return{grid:{top:20,left:40,right:20,bottom:30},legend:{show:!1,icon:"rect",itemWidth:12,itemHeight:10},xAxis:{boundaryGap:!0,splitLine:{show:!1,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:.5}},axisLine:{show:!0,lineStyle:{color:"#3489BF",width:2}},axisLabel:{rotate:0,textStyle:{color:"#D9D9D9"}},axisTick:{show:!0,inside:!0,lineStyle:{color:"#D9D9D9"}},data:t.xAxis},yAxis:{scale:!0,nameTextStyle:{padding:[0,0,0,30]},splitLine:{show:!0,lineStyle:{color:"rgba(0, 83, 209, 0.2)",type:"solid",width:.5}},axisLine:{show:!1,lineStyle:{color:"rgba(10, 178, 216, 0.31)"}},axisLabel:{textStyle:{color:"#D9D9D9"}}},series:[{name:"设备故障趋势分析",type:"line",smooth:!0,label:{normal:{show:!1}},itemStyle:{normal:{lineStyle:{width:1,color:"#2294FE"}}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:"rgba(34, 148, 254, 0.59)"},{offset:1,color:"transparent"}])}},data:t.data}]}}function x(t){return{legend:{show:!1},series:[{name:"报警事件",type:"sunburst",levels:[{},{r0:"0%",r:"60%",itemStyle:{borderWidth:0},label:{rotate:0,color:"#ffffff",show:!0,fontSize:10,formatter:function(t){var e=t.treePathInfo[0].value;return"".concat((100*t.value/e).toFixed(2),"% \n ").concat(t.name)}}},{r0:"75%",r:"88%",label:{show:!1},emphasis:{label:{show:!1}},itemStyle:{borderWidth:1,borderColor:"#0D2C3B"}}],data:t}]}}function C(t){return{legend:{show:!1},series:[{name:t.data.name,type:"gauge",max:t.max,radius:"80%",progress:{show:!0,width:4,itemStyle:{color:new n["graphic"].LinearGradient(0,0,1,0,[{offset:0,color:"#3CCCF9"},{offset:.5,color:"#2294FE"},{offset:1,color:"#FCD869"}])}},axisLine:{lineStyle:{width:4,color:[[1,"rgba(25, 57, 57, 0.3)"]]}},pointer:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{show:!1},anchor:{show:!1},title:{show:!1},detail:{valueAnimation:!0,formatter:"{a|{value}}\n{b|kw}",rich:{a:{color:"#3CCCF9",fontSize:22,fontWeight:"bold",lineHeight:20},b:{fontSize:12,color:"#ffffff",lineHeight:20}},offsetCenter:[0,"24%"]},data:[{value:t.data.value}]}]}}function w(t){var e=[].concat(Object(i["a"])(t.color),["#0042a8","#0bb39c"]),a=t.series.map((function(t,a){return{type:"line",name:t.name,showSymbol:!1,smooth:!0,yAxisIndex:0,label:{normal:{show:!1}},areaStyle:{normal:{color:new n["graphic"].LinearGradient(0,0,0,1,[{offset:.1,color:o(e[a],.5)},{offset:1,color:"transparent"}])}},data:t.data}}));return{color:e,legend:{icon:"circle",itemWidth:8,itemHeight:8,textStyle:{color:"#ffffff"}},grid:{left:"15%"},tooltip:{trigger:"axis",formatter:function(e){for(var a=e[0].name,i=0,n=e.length;i<n;i++)a+="<br/>"+e[i].marker+e[i].seriesName+" "+e[i].value+t.unit;return a}},xAxis:{splitLine:{show:!1,lineStyle:{color:"rgba(10, 178, 216, 0.2)",type:"dashed",width:1}},axisLine:{show:!0,lineStyle:{color:"#226993",width:2}},axisTick:{show:!0,inside:!0,lineStyle:{color:"#8c9da4",width:1.5}},axisLabel:{textStyle:{color:"#6c8097"}},data:t.xAxisData},yAxis:{splitLine:{show:!0,lineStyle:{color:"#011d48",width:1}},axisLabel:{textStyle:{color:"#6c8097"}}},series:a}}}}]);