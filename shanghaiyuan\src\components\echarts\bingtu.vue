<template>
  <div>
    <div class="echart" ref="echart">
    </div>


  </div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",

  data() {

    return {};
  },
  props: ["echartData"],

  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {

    initData() {

    },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const data = [{
        value: 100,
        name: '空调'
      },
      {
        value: 100,
        name: '空压'
      },
      {
        value: 100,
        name: '氮气'
      },
      {
        value: 100,
        name: '真空'
      },
      {
        value: 100,
        name: '照明'
      },
      {
        value: 100,
        name: '生产'
      },
      {
        value: 100,
        name: '其它'
      },]
      const total = data.reduce((total, item) => total + item.value, 0);

      // 修改每个数据项的名称，添加百分比
      const processedData = data.map(item => ({
        value: item.value,
        name: `${item.name} ${(item.value / total * 100).toFixed(0)}%`
      }));

      const option = {

        tooltip: {
          trigger: 'item',
          formatter: "{a} <br/>{b}: {c} ({d}%)",

        },

        title: {

          left: '20%',
          top: '55%',
          padding: [24, 0],
          textStyle: {
            color: '#fff',
            fontSize: 20,
            align: 'center'
          }
        },
        legend: {
          orient: 'vertical',
          icon: 'circle',
          top: 50,
          left: 300,
          x: 'center',
          data: processedData.map(item => item.name),
          textStyle: {

            fontSize: 14,
            color: '#fff'
          }
        },
        series: [
          {

            right: '20%',
            name: '类型',
            type: 'pie',
            radius: ['45%', '79%'],
            color: ['#00EC9D', '#01C8AE', '#0394B5', '#046CBB', '#2DF8FF', '#9AFFF0', '#2599F1',],
            label: {
              normal: {
                show: false  // 不显示标签文本
              }
            },
            labelLine: {
              normal: {
                show: false  // 不显示连接线
              }
            },

            data: processedData
          }
        ]
      };;


      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 400px;
  height: 280px;
}

@media (max-height: 1080px) {
  .echart {
    width: 400px;
    height: 280px !important;
  }
}
</style>