<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>three sdk</title>
    <style>
        html,
        body {
            padding: 0;
            margin: 0;
            width: 100%;
            height: 100%;
            background: transparent;
        }

        .container {
            width: 100%;
            height: 100%;
        }

        .btns-ctn {
            position: fixed;
            left: 10px;
            top: 20px;
            z-index: 99;
        }

        #labelEx1 {
            background: url(./textures/label-bg11111.png) no-repeat center center;
            background-size: 100% 100%;
            width: 120px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            color: #fff;
            cursor: pointer;
        }

        #labelEx1:hover {
            background-color: #1368fc;
        }

        #labelEx2 .label-inner {
            background: url(./textures/label-bg222222.png) no-repeat center center;
            background-size: 100% 100%;
            width: 358px;
            height: 150px;
            line-height: 60px;
            text-align: center;
            cursor: pointer;
            transform: translate(calc(50% - 30px), -50%);

            display: flex;
            color: #fff;
            padding-left: 90px;
        }

        /* 加载动画样式 */
        .loading_page {
            position: fixed;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 99999;
            display: flex;
            flex-direction: column;
            /* Stack items vertically */
            justify-content: center;
            /* Center items vertically */
            align-items: center;
            /* Center items horizontally */

            /* background-color: rgb(33, 33, 33); */
            margin: 0;
        }

        .inner-box {
            margin-left: 32.5px;
            position: relative;
            width: 36px;
            height: 36px;
            transform-style: preserve-3d;
            transform-origin: center;
            animation: 3s ctn infinite;
            transform-origin: 0 0;
            transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
        }

        .inner {
            position: absolute;
            width: 36px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            color: #fff;
            border-radius: 6px;
            background: rgba(7, 127, 240, 0.1);
            border: 2px solid rgba(19, 108, 241, 0.986);
            transform-origin: center;
        }

        .inner:nth-child(1) {
            transform: rotateX(90deg) translateZ(18px);
            animation: 3s top infinite;
        }

        .inner:nth-child(2) {
            transform: rotateX(-90deg) translateZ(18px);
            animation: 3s bottom infinite;
        }

        .inner:nth-child(3) {
            transform: rotateY(90deg) translateZ(18px);
            animation: 3s left infinite;
        }

        .inner:nth-child(4) {
            transform: rotateY(-90deg) translateZ(18px);
            animation: 3s right infinite;
        }

        .inner:nth-child(5) {
            transform: translateZ(18px);
            animation: 3s front infinite;
        }

        .inner:nth-child(6) {
            transform: rotateY(180deg) translateZ(18px);
            animation: 3s back infinite;
        }

        @keyframes ctn {
            from {
                transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
            }

            50% {
                transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
            }

            to {
                transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
            }
        }

        @keyframes top {
            from {
                transform: rotateX(90deg) translateZ(18px);
            }

            50% {
                transform: rotateX(90deg) translateZ(18px);
            }

            75% {
                transform: rotateX(90deg) translateZ(36px);
            }

            to {
                transform: rotateX(90deg) translateZ(18px);
            }
        }

        @keyframes bottom {
            from {
                transform: rotateX(-90deg) translateZ(18px);
            }

            50% {
                transform: rotateX(-90deg) translateZ(18px);
            }

            75% {
                transform: rotateX(-90deg) translateZ(36px);
            }

            to {
                transform: rotateX(-90deg) translateZ(18px);
            }
        }

        @keyframes left {
            from {
                transform: rotateY(90deg) translateZ(18px);
            }

            50% {
                transform: rotateY(90deg) translateZ(18px);
            }

            75% {
                transform: rotateY(90deg) translateZ(36px);
            }

            to {
                transform: rotateY(90deg) translateZ(18px);
            }
        }

        @keyframes right {
            from {
                transform: rotateY(-90deg) translateZ(18px);
            }

            50% {
                transform: rotateY(-90deg) translateZ(18px);
            }

            75% {
                transform: rotateY(-90deg) translateZ(36px);
            }

            to {
                transform: rotateY(-90deg) translateZ(18px);
            }
        }

        @keyframes front {
            from {
                transform: translateZ(18px);
            }

            50% {
                transform: translateZ(18px);
            }

            75% {
                transform: translateZ(36px);
            }

            to {
                transform: translateZ(18px);
            }
        }

        @keyframes back {
            from {
                transform: rotateY(180deg) translateZ(18px);
            }

            50% {
                transform: rotateY(180deg) translateZ(18px);
            }

            75% {
                transform: rotateY(180deg) translateZ(36px);
            }

            to {
                transform: rotateY(180deg) translateZ(18px);
            }
        }

        .loading-text {
            z-index: 9999;
            color: #fff;
            /* Text color */
            margin-top: 25px;
            /* Space between the cube and text */
            font-size: 16px;
            /* Text size */
            letter-spacing: 1px;
            /* Letter spacing */
            text-align: center;
        }
    </style>
</head>

<body>
    <!-- 加载动画 -->

    <div id="loading-page" class="loading_page">
        <div class="inner-box">
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
        </div>
        <div class="loading-text">正在加载中,请耐心等候</div>
        <!-- 添加文本 -->
    </div>

    <div class="label-ctn" style="display: none;">
        <div id="labelEx1">
            3号电梯口
        </div>

        <div id="labelEx2">
            <div class="label-inner">
                <div class="css3dHomeLabelTitle">空压系统</div>
                <div class="css3dHomeLabelStatus">暂无异常</div>
                <div class="css3dHomeLabelDetailText">当前功率</div>
                <div class="css3dHomeLabelDetailMh">
                    :
                </div>
                <div class="css3dHomeLabelDetail">19.2</div>
            </div>
        </div>
    </div>

    <div class="container" id="container"></div>
    <div class="btns-ctn" style="display: none;">
        <button class="btn" onclick="startRoam()">开始漫游</button>
        <button class="btn" onclick="stopRoam()">结束漫游</button>
        <button class="btn" onclick="turnOff()">关灯</button>
        <button class="btn" onclick="turnOn()">开灯</button>
    </div>
    <!-- <script src="./build/sdk.js"></script> -->
    <script src="./build/sdk1.js"></script>
    <!-- <script src="./js/msg-execute3d.js"></script> -->

    <script>
        /**
         * 3D场景配置和初始化脚本
         * 基于URL中的id参数加载不同的3D场景
         */
        var id = 1
        const urlParams = new URLSearchParams(window.location.search);
        var urltype = urlParams.get('id') || 'QRXFJZ'; // 默认为1
        if (urltype == "QRXFJZ") {
            id = '1'
        } else if (urltype == "RGXFJZ") {
            id = '2'
        } else if (urltype == "ZPXFKT") {
            id = '3'
        } else if (urltype == "BPXF") {
            id = '4'
        }
        else if (urltype == "KT") {
            id = '5'
        } else {
            id = '1'
        }
        console.log(urlParams, id, 225);

        /**
         * 场景配置映射对象
         * 每个ID对应一个完整的场景配置，包括相机位置、模型路径、光照参数等
         */
        const sceneConfigs = {
            // 大华校区
            '1': {
                name: "全热回收机组",
                description: "全热回收机组",
                camera: {
                    position: [10.75130990603799, -0.33144859681340333, 2.3160630086831784],
                    target: [-0.182872578139986, -2.090261548760215, 2.7566977091496754]
                },
                model: {
                    path: "./models/dhxgb.glb",
                    position: [0, -1.34, 0],
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0]
                },
                lighting: {
                    ambient: {
                        color: "#ffffff",
                        intensity: 0.3
                    }
                },
                // 透明对象列表
                transparentObjects: [
                    {
                        names: ["gz", "gzpCube16", "gzpCube10"],
                        opacity: 0.2,
                        color: "#557FCA"
                    }
                ],
                // 隐藏的对象列表
                hiddenObjects: ["gzuvgzpCube9", "gzuvgzpCube10", "pfjq", "polySurface245", "polySurface246", "polySurface247", "polySurface248"],
                // 高亮轮廓对象
                outlineObjects: [
                    {
                        name: "gz",
                        color: "#557FCA"
                    }
                ],
                // 标签数据
                labels: [
                    {
                        objectNames: [{ modelname: 'tfk001', devicename: '新风频率,送风湿度,送风频率,送风压力' }, { modelname: 'tfk5', devicename: '排风频率,排风温度,排风湿度,排风压力' }, { modelname: 'tfk6', devicename: '送风温度,送风湿度,送风频率,送风压力' }, { modelname: 'tfk7', devicename: '回风频率,回风温度,回风湿度,回风压力' }],
                        type: 1
                    }
                ],
                // 自定义设置（可选）   
                customSettings: {
                    // 添加场景特定的额外配置
                }
            },
            // 模型2
            '2': {
                name: "热管热回收机组",
                description: "热管热回收机组",
                camera: {
                    position: [0.01695907173351013, -1.3007675026396868, 0.5219695476716615],
                    target: [-0.01832211668091224, -1.3551872945780825, -0.011592184655675]
                },
                model: {
                    path: "./models/0325(4).glb",
                    position: [0, -1.34, 0],
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0]
                },
                lighting: {
                    ambient: {
                        color: "#ffffff",
                        intensity: 4
                    }
                },
                // 透明对象列表
                transparentObjects: [
                    {
                        names: ["pasted__polySurface375"],
                        opacity: 0.2,
                        color: "#557FCA"
                    },
                    {
                        names: ["平面"],
                        opacity: 0.1,
                        color: "#557FCA"
                    }
                ],
                hiddenObjects: ["qdfj001", "qdfj", "qdfj002", "qdfj003", "柱体", "柱体001"],
                // 高亮轮廓对象
                outlineObjects: [
                    {
                        name: "pasted__polySurface375",
                        color: "#557FCA"
                    }
                ],
                // 标签数据
                labels: [
                    // {
                    //     objectNames: ['qdfj', 'qdfj001'],
                    //     type: 2
                    // }
                    {
                        objectNames: [{ modelname: 'pasted__polySurface375002', devicename: '排风频率,排风温度,排风湿度,排风压力' }, { modelname: 'group3001', devicename: '新风频率,新风温度,新风湿度,新风压力' }, { modelname: 'pasted__polySurface375001', devicename: '回风频率,回风温度,回风湿度,回风压力' }, { modelname: 'group3', devicename: '送风频率,送风温度,送风湿度,送风压力' },],
                        type: 2
                    }
                ],
                // 自定义设置（可选）
                customSettings: {
                    // 添加场景特定的额外配置
                }
            },
            // 模型3
            '3': {
                name: "直膨式空调箱",
                description: "直膨式空调箱",
                camera: {
                    position: [
                        10.333115686580827, 0.17388747039419172, 1.8651365250838094
                    ],
                    target: [0.054445758643921094, -2.1010773734675063, 2.0654487211766686]
                },
                model: {
                    path: "./models/DEBytj.glb",
                    position: [0, -1.34, 0],
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0]
                },
                lighting: {
                    ambient: {
                        color: "#ffffff",
                        intensity: 0.3
                    }
                },
                // 透明对象列表
                transparentObjects: [
                    {
                        names: ["gz", "Box003", 'Line008',],
                        opacity: 0.3,
                        color: "#557FCA"
                    },
                    {
                        names: ['对象001015', '对象002015'],
                        opacity: 0.5,
                        color: "#557FCA"
                    },
                    {
                        names: ["gz001"],
                        opacity: 0.1,
                        color: "#557FCA"
                    }
                ],
                // 隐藏的对象列表
                hiddenObjects: ["group3"],
                // 高亮轮廓对象
                outlineObjects: [
                    {
                        name: "gz",
                        color: "#557FCA"
                    },
                    {
                        name: "Box003",
                        color: "#557FCA"
                    }
                ],
                // 标签数据
                labels: [
                    {
                        objectNames: [{ modelname: 'chuxiaoguolvwang', devicename: '初效' }, { modelname: 'group1group3pCube23', devicename: '送风' }],
                        type: 1
                    }
                ],

                // 自定义设置（可选）
                customSettings: {}
            },
            // 模型4 - 默认
            '4': {
                name: "变频机组",
                description: "变频机组",
                camera: {
                    position: [8.344456452053977, -0.19677773155571066, 2.1783077156069175],
                    target: [0.0707015100754735, -2.4542487217099667, 2.179597931615951]
                },
                model: {
                    path: "./models/DEBsnj.glb",
                    position: [0, -1.34, 0],
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0]
                },
                lighting: {
                    ambient: {
                        color: "#ffffff",
                        intensity: 0.3
                    }
                },
                // 透明对象列表
                transparentObjects: [
                    {
                        names: ["gz"],
                        opacity: 0.2,
                        color: "#557FCA"
                    },
                    {
                        names: ["gz001"],
                        opacity: 0.1,
                        color: "#557FCA"
                    }
                ],
                // 隐藏的对象列表
                hiddenObjects: ["group3"],
                // 高亮轮廓对象
                outlineObjects: [
                    {
                        name: "gz",
                        color: "#557FCA"
                    }
                ],
                // 标签数据
                labels: [
                    {
                        objectNames: [{ modelname: 'chuxiaoguolvwang', devicename: '初效滤网压差' }, { modelname: 'group1group3pCube23', devicename: '送风温度,送风湿度,送风频率,送风压力' },],
                        type: 1
                    }
                ],

                // 自定义设置（可选）
                customSettings: {}
            },
            // 模型4 - 默认
            '5': {
                name: "空调箱",
                description: "空调箱",
                camera: {

                    position: [8.684610899745293, 0.4750171292653298, 2.1596384909449813],
                    target: [-0.1658107309622756, -1.2816477430140052, 2.4448358544101003]
                },
                model: {
                    path: "./models/xgdeb(3).glb",
                    position: [0, -1.34, 0],
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0]
                },
                lighting: {
                    ambient: {
                        color: "#ffffff",
                        intensity: 0.3
                    }
                },
                // 透明对象列表
                transparentObjects: [
                    {
                        names: ["gz"],
                        opacity: 0.2,
                        color: "#557FCA"
                    },
                    // {
                    //     names: ["gz001"],
                    //     opacity: 0.1,
                    //     color: "#557FCA"
                    // }
                ],
                // 隐藏的对象列表
                hiddenObjects: ["group10", "group8"],
                // 高亮轮廓对象
                outlineObjects: [
                    {
                        name: "gz",
                        color: "#557FCA"
                    }
                ],
                // 标签数据
                labels: [
                    {
                        objectNames: [{ modelname: 'tfk7', devicename: '回风阀开度反馈' }, { modelname: 'tfk6', devicename: '送风温度,送风湿度' }, { modelname: 'tfk5', devicename: '新风阀开度反馈' },
                        { modelname: 'pasted__ddd', devicename: '回风CO2,回风温度,回风湿度' }, { modelname: 'pasted__网格039_1', devicename: '初效滤网压差' }, { modelname: 'pasted__gl', devicename: '中效滤网压差' }, { modelname: 'pasted__sg', devicename: '水阀开度反馈' }],
                        type: 1
                    }
                ],

                // 自定义设置（可选）
                customSettings: {}
            }
        };

        // 确保id存在于配置中，否则使用默认配置
        if (!sceneConfigs[id]) {
            id = '1';
        }

        // 获取当前场景配置
        const currentConfig = sceneConfigs[id];

        // 初始化3D视图对象
        const view = new app3d({
            dom: 'container',
            dracoPath: "./build/draco/gltf/"
        })

        // 创建场景配置
        const config = {
            lightConfig: [
                {
                    type: "AmbientLight",
                    color: currentConfig.lighting.ambient.color,
                    intensity: currentConfig.lighting.ambient.intensity
                },
                {
                    intensity: 5,
                    type: "DirectionalLight",
                    color: "#ffffff",
                    position: [1, 3, 2],
                }
            ],
            dracoPath: "./build/draco/gltf/",
            hdrPath: "./textures/equirectangular/venice_sunset_1k.hdr",
            camera: {
                position: currentConfig.camera.position,
                target: currentConfig.camera.target,
                near: 0.01, // 近截面
                far: 3000,
            },
            css2d: {
                use: true,
            },
            css3d: {
                use: true,
            },
            useEffectComposer: true,

            models: [{
                path: currentConfig.model.path,
                position: currentConfig.model.position,
                name: "readyRoom",
                scale: currentConfig.model.scale,
                rotation: currentConfig.model.rotation,
                id: 1,
                visible: true,
                isGlow: true, // 是否使用辉光
                glowNames: ["lamp1"], // 辉光的部位
            }]
        }
        console.log(config, 225);

        // 更新加载文本
        function updateLoadingText(percent) {
            document.querySelector('.loading-text').textContent = `正在加载中，请耐心等候 (${percent}%)`;
        }

        // 初始化场景
        view.init(config, () => {
            setTimeout(() => {
                // 额外模型加载可以在这里添加
                // addAirConditioner();

                // addLight();
            }, 1.5);

            view.toggleShowStyle(true);
            view.setBloomParams({
                threshold: 0.0,
                strength: 4,
                radius: .2,
            });

            view.setGroundMirrorVisible1(false);
        });

        // 设置回调函数
        view.setCallBack({
            mouseclick: mouseclick,
            progress: progress,
            onSiteInspectionCallback: onSiteInspectionCallback
        })

        /**
         * 加载进度回调函数
         * @param {Number} load - 加载进度(0-100)
         * @param {Boolean} isload - 是否加载完成
         */
        function progress(load, isload) {
            // 更新加载进度
            updateLoadingText(Math.round(load * 100));

            if (isload) {
                console.log(view.searchByName("qdfj"), "加载完成");
                window.parent.postMessage(
                    {
                        type: "isload",
                        value: true
                    },
                    "*"
                );
                // view.LoadJsonDevice.setAnimateByModelIds(["200001"], true);
                // setTimeout(() => {
                //     view.LoadJsonDevice.setAnimateByModelIds(["200001"], false);
                // }, 2000);
                view.clearSkyBox();
                view.renderer.setClearColor(0x000000, 0); // 设置清屏颜色为透明（alpha = 0）

                // 隐藏加载动画
                document.getElementById("loading-page").style.display = "none";




                // 根据场景ID执行特定操作（如果有需要）
                if (id === '1') {
                    let poijson = {
                        "models": [
                            {
                                "id": "xf1",
                                "path": "./models/FDJ.glb"
                            },

                        ],
                        "devices": [
                            {
                                "json": {
                                    "modelId": "xf1",
                                    "position": [0.0, -1.92, 0.5950557223535992],
                                    "ue_position": [
                                        272.66775068274706,
                                        419.4952586526717,
                                        -106.32257716223062
                                    ],
                                    "scale": [
                                        1,
                                        1,
                                        1
                                    ],
                                    "rotation": [
                                        0,
                                        0,
                                        0
                                    ],
                                    "id": 6175,
                                    "floorNum": 2,
                                    "name": "电机"
                                },
                                "id": 547641,
                                "title": "电机",
                                "name": "电机",
                                "parkId": "0",
                                "floorId": "0",
                                "type": 2
                            }, {
                                "json": {
                                    "modelId": "xf1",
                                    "position": [0.0, -1.93, 4.7950557223535992],
                                    "ue_position": [
                                        272.66775068274706,
                                        419.4952586526717,
                                        -106.32257716223062
                                    ],
                                    "scale": [
                                        1,
                                        1,
                                        1
                                    ],
                                    "rotation": [
                                        0,
                                        3.14,
                                        0
                                    ],
                                    "id": 6176,
                                    "floorNum": 2,
                                    "name": "电机"
                                },
                                "id": 547642,
                                "title": "电机",
                                "name": "电机",
                                "parkId": "0",
                                "floorId": "0",
                                "type": 2
                            }
                        ]
                    }
                    view.LoadJsonDevice.setScene(poijson, (obj) => {

                        // view.LoadJsonDevice.setAnimateByModelIds([547641], false);
                    })

                    // ID 1特定操作

                    console.log(`已加载${currentConfig.name}场景`);
                } else if (id === '2') {
                    let poijson = {
                        "models": [
                            {
                                "id": "xf112",
                                "path": "./models/dj.glb"
                            },

                        ],
                        "devices": [
                            {
                                "json": {
                                    "modelId": "xf112",
                                    "position": [-0.00, -1.34, 0.000],
                                    "ue_position": [
                                        272.66775068274706,
                                        419.4952586526717,
                                        -106.32257716223062
                                    ],
                                    "scale": [
                                        1,
                                        1,
                                        1
                                    ],
                                    "rotation": [
                                        0,
                                        0,
                                        0
                                    ],
                                    "id": 6175,
                                    "floorNum": 2,
                                    "name": "电机"
                                },
                                "id": 113100401,
                                "title": "电机",
                                "name": "电机",
                                "parkId": "0",
                                "floorId": "0",
                                "type": 2
                            }, {
                                "json": {
                                    "modelId": "xf112",
                                    "position": [-0.00, -1.43, 0.000],
                                    "ue_position": [
                                        272.66775068274706,
                                        419.4952586526717,
                                        -106.32257716223062
                                    ],
                                    "scale": [
                                        1,
                                        1,
                                        1
                                    ],
                                    "rotation": [
                                        0,
                                        0,
                                        0
                                    ],
                                    "id": 6176,
                                    "floorNum": 2,
                                    "name": "电机"
                                },
                                "id": 113100402,
                                "title": "电机",
                                "name": "电机",
                                "parkId": "0",
                                "floorId": "0",
                                "type": 2
                            }
                        ]
                    }
                    view.LoadJsonDevice.setScene(poijson, (obj) => {

                        // view.LoadJsonDevice.setAnimateByModelIds([547641], false);
                    })
                    console.log(poijson, '设置场景');
                    // view.addModel({
                    //     path: "./models/dj.glb",
                    //     position: [-0.11443663228274875, -1.294849065888053, 0.04050990234521616],
                    //     name: "shebei",
                    //     scale: [1, 1, 1],
                    //     rotation: [0, 0, 0],
                    //     id: 1,
                    //     visible: true,
                    //     isGlow: true, // 是否使用辉光
                    //     // glowNames: ["d3f1_ksfjpg19"], // 辉光的部位
                    // });
                    // ID 2特定操作
                    console.log(`已加载${currentConfig.name}场景`);
                } else if (id === '3' || id === '4' ) {
                    let poijson = {
                        "models": [
                            {
                                "id": "xf1",
                                "path": "./models/FDJ.glb"
                            },

                        ],
                        "devices": [
                            {
                                "json": {
                                    "modelId": "xf1",
                                    "position": [0.1, -1.95, 0.5950557223535992],
                                    "ue_position": [
                                        272.66775068274706,
                                        419.4952586526717,
                                        -106.32257716223062
                                    ],
                                    "scale": [
                                        1,
                                        1,
                                        1
                                    ],
                                    "rotation": [
                                        0,
                                        0,
                                        0
                                    ],
                                    "id": 6175,
                                    "floorNum": 2,
                                    "name": "电机"
                                },
                                "id": 547641,
                                "title": "电机",
                                "name": "电机",
                                "parkId": "0",
                                "floorId": "0",
                                "type": 2
                            },
                        ]
                    }
                    view.LoadJsonDevice.setScene(poijson, (obj) => {
                        // view.LoadJsonDevice.setAnimateByModelIds([547641], false);
                    })

                }else if ( id === '5') {
                    let poijson = {
                        "models": [
                            {
                                "id": "xf1",
                                "path": "./models/FDJ.glb"
                            },

                        ],
                        "devices": [
                            {
                                "json": {
                                    "modelId": "xf1",
                                    "position": [0.1, -1.85, -1.0950557223535992],
                                    "ue_position": [
                                        272.66775068274706,
                                        419.4952586526717,
                                        -106.32257716223062
                                    ],
                                    "scale": [
                                        1,
                                        1,
                                        1
                                    ],
                                    "rotation": [
                                        0,
                                        0,
                                        0
                                    ],
                                    "id": 6175,
                                    "floorNum": 2,
                                    "name": "电机"
                                },
                                "id": 547641,
                                "title": "电机",
                                "name": "电机",
                                "parkId": "0",
                                "floorId": "0",
                                "type": 2
                            },
                        ]
                    }
                    view.LoadJsonDevice.setScene(poijson, (obj) => {
                        // view.LoadJsonDevice.setAnimateByModelIds([547641], false);
                    })

                }

                applySceneSetup(currentConfig, view);
            }
        }

        // 添加全局变量存储已创建的标签
        var createdLabels = {};

        /**
         * 应用场景基础设置函数
         * 根据配置对象应用透明度、隐藏对象、轮廓和标签
         * @param {Object} config - 场景配置对象
         * @param {Object} view - 3D视图对象
         */
        function applySceneSetup(config, view) {
            // 1. 设置透明对象
            if (config.transparentObjects && config.transparentObjects.length > 0) {
                config.transparentObjects.forEach(item => {
                    view.setOpa(item.names, item.opacity, item.color);
                });
            }

            // 2. 隐藏指定对象
            if (config.hiddenObjects && config.hiddenObjects.length > 0) {
                config.hiddenObjects.forEach(objName => {
                    view.nameVisible(objName, false);
                });
            }

            // 3. 添加对象轮廓
            if (config.outlineObjects && config.outlineObjects.length > 0) {
                config.outlineObjects.forEach(item => {
                    view.addOutlineByName(item.name, item.color);
                });
            }

            // 4. 添加标签
            if (config.labels && config.labels.length > 0) {
                config.labels.forEach(labelConfig => {
                    // 提取modelname数组用于获取位置
                    const modelNames = labelConfig.objectNames.map(item => item.modelname);
                    // 获取位置信息
                    const positions = view.getObjCenterByNames(modelNames);

                    if (positions && positions.length > 0) {
                        // 将devicename信息添加到positions中
                        positions.forEach((position, index) => {
                            if (index < labelConfig.objectNames.length) {
                                // 处理可能包含逗号的devicename
                                const deviceNames = labelConfig.objectNames[index].devicename.split(',');
                                position.deviceName = labelConfig.objectNames[index].devicename;
                                position.deviceNames = deviceNames; // 存储拆分后的设备名称数组
                            }
                        });

                        // 只添加一次事件监听器
                        if (!window.deviceDataListenerAdded) {
                            window.deviceDataListenerAdded = true;

                            window.addEventListener("message", function (event) {
                                console.log(event.data.data, "收到的数据")
                                // event.data获取传过来的数据
                                if (event.data.type == "devicedata") {
                                    // if (id == '1') {
                                    // } else if (id == '2') {
                                    //     console.log(event.data.data.find(item => item.dId == 113100402).valStr, "收到的数据");
                                    //     let status1 = (event.data.data ? event.data.data.find(item => item.dId == 113100401).valStr : '') == '运行' ? true : false
                                    //     let status2 = (event.data.data ? event.data.data.find(item => item.dId == 113100402).valStr : '') == '运行' ? true : false
                                    //     console.log(status1, status2, "收到的数据");
                                    //     setTimeout(() => {
                                    //         view.LoadJsonDevice.setAnimateByModelIds([113100401], status1);
                                    //         view.LoadJsonDevice.setAnimateByModelIds([113100402], status2);
                                    //     }, 100);
                                    var devicedata = event.data.data;

                                    if (devicedata && devicedata.length > 0) {
                                        // 检查标签是否已存在
                                        if (Object.keys(createdLabels).length === 0) {
                                            // 首次创建标签
                                            console.log("首次创建标签");
                                            // 创建新标签并存储引用
                                            createdLabels = createLabels(positions, labelConfig.type, devicedata);
                                        } else {
                                            // 更新已存在的标签
                                            console.log("更新现有标签");
                                            updateLabels(createdLabels, devicedata);
                                        }
                                    }
                                } else if (event.data.type == "activeDevice") {
                                    var devicedata = event.data.data.deviceDataBase.filter(
                                        (item) => item.dmTag.includes("chart"));
                                    var statusdata = event.data.data.deviceDataBase.filter(
                                        (item) => item.dmTag.includes("status"));
                                    console.log(devicedata, statusdata, 'devicedata');
                                    //武汉智元
                                    view.LoadJsonDevice.setAnimateByModelIds([547641], statusdata[0].valStr == '运行');
                                    if (devicedata && devicedata.length > 0) {
                                        // 检查标签是否已存在
                                        if (Object.keys(createdLabels).length === 0) {
                                            // 首次创建标签
                                            console.log("首次创建标签");
                                            // 创建新标签并存储引用
                                            createdLabels = createLabels(positions, labelConfig.type, devicedata);
                                        } else {
                                            // 更新已存在的标签
                                            console.log("更新现有标签");
                                            updateLabels(createdLabels, devicedata);
                                        }
                                    }
                                }
                            });
                        }
                    }
                });
            }

            // 5. 应用其他自定义设置（如果有）
            // 可以在这里扩展更多功能
        }

        /**
         * 鼠标点击事件回调
         * @param {Object} obj - 点击对象信息
         */
        function mouseclick(obj) {
            const { model, point, center } = obj;
            // view.setOutlineModel([model]);
            console.log(obj.point.toArray());
        }

        function addAirQualityData() {
            let position = { x: -5.691270549245441, y: 0.8205613250310237, z: 7.2690083367943465 }; //获取单个模型的顶部中心点
            // let earthMassDiv = document.createElement("div");
            // img = document.createElement("img");
            // img.src = "./images/Frame.png";
            // img.style.width = "100px";
            // earthMassDiv.appendChild(img);
            // earthMassDiv.className = "label";
            // 空气质量数据数组
            const airQualityData = [
                { label: "温度", value: "21", unit: "°C" },
                { label: "湿度", value: "21", unit: "%" },
                { label: "CO2", value: "500", unit: "ppm" },
                { label: "PM2.5", value: "32", unit: "mg/m³" },
                { label: "HCHO", value: "0.09", unit: "mg/m³" },
                { label: "TVOC", value: "0.16", unit: "mg/m³" }
            ];

            // 创建一个包含背景图的 div 容器
            let airQualityDiv = document.createElement("div");
            airQualityDiv.className = "air-quality-container";
            airQualityDiv.style.position = "relative"; // 相对定位，以便数据覆盖在背景图上
            airQualityDiv.style.width = "300px"; // 容器宽度（可根据背景图调整）

            airQualityDiv.style.height = "100px"; // 容器高度（可根据背景图调整）
            airQualityDiv.style.backgroundImage = "url('./images/Frame.png')"; // 背景图路径
            airQualityDiv.style.backgroundSize = "cover"; // 背景图覆盖整个容器
            airQualityDiv.style.backgroundPosition = "center"; // 背景图居中

            // 创建数据容器
            let dataDiv = document.createElement("div");
            dataDiv.style.position = "absolute"; // 绝对定位，覆盖在背景图上
            dataDiv.style.top = "6px"; // 从顶部开始
            dataDiv.style.left = "49px"; // 从左侧开始
            dataDiv.style.width = "85%"; // 占满容器宽度
            dataDiv.style.height = "85%"; // 占满容器高度
            dataDiv.style.display = "grid"; // 使用 grid 布局
            dataDiv.style.gridTemplateColumns = "1fr 1fr"; // 两列
            dataDiv.style.gap = "5px"; // 网格间距
            dataDiv.style.padding = "5px"; // 内边距
            dataDiv.style.fontFamily = "Arial, sans-serif"; // 字体

            // 使用循环生成数据项
            for (let i = 0; i < airQualityData.length; i++) {
                let dataItemDiv = document.createElement("div");
                dataItemDiv.style.display = "flex";
                dataItemDiv.style.alignItems = "center";

                // 创建标签元素
                let labelElement = document.createElement("span");
                labelElement.textContent = airQualityData[i].label;
                labelElement.style.color = "#D5DFED"; // 标签使用浅蓝色
                labelElement.style.fontSize = "12px";
                labelElement.style.marginRight = "12px";

                // 创建数值元素
                let valueElement = document.createElement("span");
                valueElement.textContent = airQualityData[i].value;
                valueElement.style.color = "#ffffff"; // 数值使用白色
                valueElement.style.fontSize = "14px";


                // 创建单位元素
                let unitElement = document.createElement("span");
                unitElement.textContent = airQualityData[i].unit;
                unitElement.style.color = "#D5DFED"; // 单位使用灰色
                unitElement.style.fontSize = "12px";
                unitElement.style.marginLeft = "3px";

                // 将三个元素添加到数据项中
                dataItemDiv.appendChild(labelElement);
                dataItemDiv.appendChild(valueElement);
                dataItemDiv.appendChild(unitElement);

                dataDiv.appendChild(dataItemDiv);
            }

            // 将数据容器添加到主容器
            airQualityDiv.appendChild(dataDiv);

            // 将整个 airQualityDiv 添加到页面中（假设添加到 body）
            // document.body.appendChild(airQualityDiv);
            view.add3dSprite(airQualityDiv, {
                scale: 0.01, //标签尺寸
                position, //标签位置
                name: "D3",
            });

        }
        // 巡检路线数据
        const onSiteInspectionData = [
            // { // 第一段路线
            //     positions: [ // 路线点位
            //         [2.573756697671823, 0, 12.037283990187387],
            //         [2.5075716334768505, 0, 5.829250176976521]
            //     ],
            //     checkObj: { // 巡检的对象
            //         position: [3.9187071545122527, 1.0095932498704998, 5.9108942446632],
            //         id: "",
            //         name: "",
            //     },
            //     totalTime: 1, // 漫游时间
            //     delay: 1, // 停顿时间
            // },
            { // 第二段路线
                positions: [ // 路线点位
                    [2.573756697671823, 0, 12.037283990187387],
                    [2.5075716334768505, 0, 5.829250176976521],
                    [
                        2.445338625740245,
                        0,
                        1.397617079497992
                    ]
                ],
                checkObj: {
                    position: [
                        2.330452390824419, 0.9423001362791258, 0.7690112981126882
                    ],
                    id: "light1",
                    name: "灯",
                },
                totalTime: 5, // 漫游时间
                delay: 1, // 停顿时间

            },
            { // 第三段路线
                positions: [ // 路线点位
                    [
                        2.445338625740245,
                        0,
                        1.397617079497992
                    ],
                    [2.8596099903040275, 0, -6.076886775455652],
                    [-3.7475358811340858, 0, -7.315830354412762],
                    // [2.22381693429758e-13, 0, 0],
                    // [-3.5121438692957327, 0, -4.8739816529802615],
                    [-1.4485026306881679, 0, 0.5441035217501339],
                    [0.8792542400234312, -0.1387484940502251, 5.992017608163784]
                ],
                checkObj: {
                    position: [
                        -5.069640001822959, 0.880404884856482, 7.320091389189232
                    ],
                    id: "Cube105",
                    name: "传感器",
                },
                totalTime: 10, // 漫游时间
                delay: 5, // 停顿时间
            }
        ];

        function startRoam() {
            view.onSiteInspection({
                data: onSiteInspectionData,
                type: "end"
            });
            // 显示屋顶
            view.nameVisible("ceiling", true);
            setTimeout(() => {
                view.resetLayer();
                view.animateCamera(
                    {
                        "x": -12.503554845873484,
                        "y": 18.901970314827643,
                        "z": -0.5912673122797542
                    },
                    {
                        "x": -0.8566964145413888,
                        "y": -0.6141021588834138,
                        "z": -0.6077478142687041
                    },
                    1000 //飞入动画的时间
                );
                view.nameVisible("ceiling", false);
            }, 28000);
        }

        function stopRoam() {
            view.stopRoam();
            view.nameVisible("ceiling", false);
        }

        // 巡检的回调
        // {
        //      "data": {
        //         "position": [
        //             3.9187071545122527,
        //             1.0095932498704998,
        //             5.9108942446632
        //         ],
        //         "id": "bookshelf1",
        //         "name": "书架1"
        //     },
        //     "type": "checkObj"
        // }
        // type: end 结束巡检
        // type: checkObj 检测到巡检的对象, 返回对应配置里面的数据， 用于添加弹框等操作

        function onSiteInspectionCallback(data) {
            console.log("---data--", data);
            if (data.data.name == "传感器") {
                console.log("ddd");
                addAirQualityData();
            }
        }


        // 添加空调设备
        function addAirConditioner() {
            const airConditionerData = [
                {
                    position: [-0.11443663228274875, -1.294849065888053, 0.04050990234521616],
                },
                // {
                //     position: [0, 2.5, 0],
                // },
                // {
                //     position: [0, 2.5, -5],
                // },
            ];

            airConditionerData.forEach(item => {
                view.addModel({
                    path: "./models/dj.glb",
                    position: item.position,
                    name: "shebei",
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0],
                    id: 200001,
                    visible: true,
                    isGlow: true, // 是否使用辉光
                    // glowNames: ["d3f1_ksfjpg19"], // 辉光的部位
                });

            });

            // view.addAirConditioner(airConditionerData, "green");

        }

        // 关灯
        function turnOff() {
            view.toggleNight(true);
        }

        // 开灯
        function turnOn() {

            view.toggleNight(false);
        }


        function addLight() {

        }

        /**
         * 创建标签并返回引用
         * @param {Array} positions 标签位置数组
         * @param {String} labelType 标签类型
         * @param {Array} deviceData 设备数据
         * @return {Object} 创建的标签引用对象
         */
        function createLabels(positions, labelType, deviceData) {
            // 存储创建的标签引用
            const labelRefs = {};

            // 调用原有的addTempLabels方法创建标签
            const createdElements = addTempLabels(positions, labelType, deviceData);
            console.log(positions, createdElements, 'createdElements');
            // 将创建的标签元素与设备ID关联并存储
            if (createdElements && createdElements.length > 0) {
                createdElements.forEach((element, index) => {
                    if (index < positions.length && positions[index].deviceName) {
                        labelRefs[positions[index].deviceName] = {
                            element: element,
                            position: positions[index]
                        };
                    }
                });
            }
            console.log(labelRefs, 'labelRefs');


            return labelRefs;
        }

        /**
         * 更新已存在的标签内容
         * @param {Object} labelRefs 标签引用对象
         * @param {Array} deviceData 新的设备数据
         */
        function updateLabels(labelRefs, deviceData) {
            console.log("开始更新标签", deviceData);

            // 1. 收集所有deviceData里的dmName
            const dmNames = deviceData.map(d => d.dmName);
            console.log("当前数据集中的dmName:", dmNames);

            // 遍历每个标签
            for (const key in labelRefs) {
                console.log("处理标签:", key);
                const labelElement = labelRefs[key].element;

                // 找到标签内的数据容器
                const dataPanel = labelElement.querySelector('div'); // 第一层div是dataPanel
                if (!dataPanel) {
                    console.log("未找到dataPanel");
                    continue;
                }

                const dataContent = dataPanel.querySelector('div'); // 第二层div是dataContent
                if (!dataContent) {
                    console.log("未找到dataContent");
                    continue;
                }

                // 获取标签内所有数据行
                const dataRows = dataContent.children;
                console.log(`标签 ${key} 有 ${dataRows.length} 行数据`);

                // 1. 更新已有数据行
                deviceData.forEach(data => {
                    const dmName = data.dmName;

                    // 检查该标签是否应该包含这个dmName
                    const names = key.split(',').map(s => s.trim());
                    if (names.includes(dmName)) {
                        let rowFound = false;

                        // 查找已有的行
                        for (let i = 0; i < dataRows.length; i++) {
                            const row = dataRows[i];
                            const labelSpan = row.querySelector('span:first-child');

                            if (labelSpan && labelSpan.textContent === dmName) {
                                // 找到了对应的行，更新值
                                const valueSpan = row.querySelector('.value');
                                if (valueSpan) {
                                    valueSpan.textContent = data.valStr || data.indication || '0';
                                    console.log(`更新行: ${dmName} = ${valueSpan.textContent}`);
                                }
                                rowFound = true;
                                break;
                            }
                        }

                        // 如果没找到对应的行，创建一个新行
                        if (!rowFound) {
                            console.log(`为 ${dmName} 创建新行`);
                            // 创建新行的代码保持不变...
                            const dataRow = document.createElement("div");
                            dataRow.style.display = "flex";
                            dataRow.style.justifyContent = "space-between";
                            dataRow.style.marginBottom = "5px";

                            const labelSpan = document.createElement("span");
                            labelSpan.textContent = dmName;

                            const valueSpan = document.createElement("span");
                            valueSpan.textContent = data.valStr || data.indication || '0';
                            valueSpan.style.color = "#81C2FF";
                            valueSpan.className = "value";

                            dataRow.appendChild(labelSpan);
                            dataRow.appendChild(valueSpan);
                            dataContent.appendChild(dataRow);
                        }
                    }
                });

                // 2. 删除不再需要的行
                // 先收集需要删除的行索引
                const rowsToRemove = [];

                for (let i = 0; i < dataRows.length; i++) {
                    const row = dataRows[i];
                    const labelSpan = row.querySelector('span:first-child');

                    if (labelSpan) {
                        const rowDmName = labelSpan.textContent;
                        console.log(`检查行 ${i}: ${rowDmName}`);

                        // 检查这个dmName是否在当前数据集中
                        let found = false;
                        for (const data of deviceData) {
                            if (data.dmName === rowDmName) {
                                found = true;
                                break;
                            }
                        }

                        // 如果不在，标记为需要删除
                        if (!found) {
                            console.log(`行 ${i}: ${rowDmName} 需要删除`);
                            rowsToRemove.push(i);
                        }
                    }
                }

                // 从后往前删除行（这样不会影响索引）
                for (let i = rowsToRemove.length - 1; i >= 0; i--) {
                    const index = rowsToRemove[i];
                    if (index < dataRows.length) {
                        try {
                            console.log(`尝试删除索引 ${index} 的行`);
                            dataContent.removeChild(dataRows[index]);
                            console.log(`成功删除索引 ${index} 的行`);
                        } catch (e) {
                            console.error(`删除行时出错:`, e);
                        }
                    }
                }

                // 如果标签内没有任何数据行了，可以考虑删除整个标签
                if (dataContent.children.length === 0) {
                    console.log(`标签 ${key} 没有数据行，删除整个标签`);
                    // 从页面移除
                    if (labelElement.parentNode) {
                        labelElement.parentNode.removeChild(labelElement);
                    }
                    // 从 labelRefs 删除
                    delete labelRefs[key];
                }
            }
        }

        /**
         * 修改现有的addTempLabels函数，使其返回创建的标签元素数组
         */
        function addTempLabels(positions, labelType, deviceData) {
            // 创建标签元素
            const createdElements = [];

            // 遍历每个位置创建标签 
            positions.forEach((posData, index) => {
                // 根据对象名称自动判断方向(top/bottom)
                const objName = posData.name || '';
                // 判断标签方向：tfk5、tfk6、qdfj001为bottom方向，其他为top方向
                const direction = ['tfk5', 'tfk6', 'qdfj001', 'zxglw', 'group3001', 'group3', 'pasted__polySurface375002', 'pasted__sg','pasted__网格039_1'].includes(objName) ? 'bottom' : 'top';

                // 创建主容器
                const labelContainer = document.createElement("div");
                labelContainer.style.position = "relative";

                // 创建数据面板
                const dataPanel = document.createElement("div");
                dataPanel.style.width = "180px";
                // dataPanel.style.height = "240px";
                dataPanel.style.background = direction === 'bottom' ? "url('./images/bottom.png')" : "url('./images/top.png')";
                dataPanel.style.backgroundSize = "100% 100%";
                dataPanel.style.color = "#fff";
                // dataPanel.style.padding = "10px 15px";
                dataPanel.style.fontFamily = "Arial, sans-serif";
                dataPanel.style.fontSize = "16px";
                dataPanel.style.paddingBottom = direction === 'bottom' ? "5px" : "70px";
                dataPanel.style.paddingTop = direction === 'bottom' ? "45px" : "5px";
                dataPanel.style.paddingLeft = "10px";
                dataPanel.style.paddingRight = "10px";
                // 创建数据内容
                const dataContent = document.createElement("div");
                dataContent.style.display = "flex";
                dataContent.style.flexDirection = "column";
                dataContent.style.height = "100%";
                dataContent.style.marginTop = direction === 'bottom' ? "8px" : "0px";

                // 设备名称标题
                const titleDiv = document.createElement("div");
                titleDiv.style.fontSize = "20px";
                titleDiv.style.fontWeight = "bold";
                titleDiv.style.marginBottom = "10px";
                titleDiv.style.color = "#2294FE";
                // 使用deviceName显示设备名称，如果没有则使用对象名
                // titleDiv.textContent = posData.deviceName || objName;
                // dataContent.appendChild(titleDiv);
                console.log(posData.deviceName, 'posData.deviceName');
                console.log(deviceData, 'deviceData');

                // 获取所有相关设备数据
                let relevantData = [];

                // 如果存在拆分后的设备名称数组
                if (posData.deviceNames && posData.deviceNames.length > 0) {
                    // 遍历每个设备名称，找出对应的数据
                    posData.deviceNames.forEach(deviceName => {
                        // 查找匹配此设备名称的所有数据
                        const matchedData = deviceData ? deviceData.filter(item =>
                            item.dmName && item.dmName.includes(deviceName.trim())
                        ) : [];

                        // 将找到的数据加入相关数据数组
                        if (matchedData.length > 0) {
                            relevantData = relevantData.concat(matchedData);
                        }
                    });
                } else {
                    // 如果没有拆分的名称，则按原来的方式查找数据
                    relevantData = deviceData ? deviceData.filter(item =>
                        item.dmName && item.dmName.includes(posData.deviceName)
                    ) : [];
                }

                // 如果没有找到匹配的数据，使用默认数据
                if (relevantData.length === 0) {
                    relevantData = [
                        // { dmName: "送风温度:", valStr: "12°C" },
                        // { dmName: "送风温度:", valStr: "12°C" },
                        // { dmName: "送风进风温度:", valStr: "12°C" }
                    ];
                }

                // 添加数据行
                relevantData.forEach(row => {
                    const dataRow = document.createElement("div");
                    dataRow.style.display = "flex";
                    dataRow.style.justifyContent = "space-between";
                    dataRow.style.marginBottom = "5px";

                    const labelSpan = document.createElement("span");
                    labelSpan.textContent = row.dmName;

                    const valueSpan = document.createElement("span");
                    valueSpan.textContent = row.valStr;
                    valueSpan.style.color = "#81C2FF";
                    valueSpan.className = "value"
                    dataRow.appendChild(labelSpan);
                    dataRow.appendChild(valueSpan);
                    dataContent.appendChild(dataRow);
                });

                // 组装元素
                dataPanel.appendChild(dataContent);
                labelContainer.appendChild(dataPanel);



                // 根据方向和类型调整位置
                if (direction === 'bottom') {
                    if (labelType == 2) {
                        if (posData.name == 'pasted__polySurface375002') {
                            posData.center.y -= 0.053;
                        } else {
                            posData.center.y -= 0.083;
                        }
                    } else {
                        if (posData.name == 'ddejfglb') {
                            posData.center.y -= 0.73;
                        } else {
                            posData.center.y -= 1.3;
                        }
                    }
                } else {
                    if (labelType == 2) {
                        posData.center.y += 0.023;
                    } else {
                        posData.center.y += 0.35;
                    }
                }

                // // 添加到3D场景
                // view.add3dSprite(labelContainer, {
                //     scale: labelType == 2 ? 0.0003 : 0.005, // 标签尺寸
                //     position: posData.center, // 使用获取到的中心点位置
                //     name: `tempLabel_${posData.deviceName || index}`, // 使用deviceName作为标签名称
                // });
                view.add2d(labelContainer, {
                    position: posData.center,
                    name: `tempLabel_${posData.deviceName || index}`,
                });
                createdElements.push(labelContainer);

            });

            // 返回创建的元素数组
            return createdElements;
        }

        // 使用示例
        function createLabelsForObjects() {
            // 获取对象中心点
            const positions = view.getObjCenterByNames(['tfk001', 'tfk5', 'tfk6', 'tfk7']);
            console.log(positions, 520); // 打印位置信息

            // 创建标签，type=1为普通尺寸，type=2为小尺寸
            addTempLabels(positions, 1);
        }

        // 可以在需要的地方调用
        // createLabelsForObjects();

    </script>

</body>

</html>