let resultArray1 = [];
for (let i = 1; i <= 12; i++) {
  let result = `b3_rsxt_flrb_${i.toString().padStart(3, "0")}`;
  resultArray1.push(result);
}
let resultArray2 = [];
for (let i = 1; i <= 10; i++) {
  let result = `dw_shuibeng_${i.toString().padStart(3, "0")}`;
  resultArray2.push(result);
}
let resultArray3 = [];
for (let i = 1; i <= 12; i++) {
  let result = `zw_shuibeng_${i.toString().padStart(3, "0")}`;
  resultArray3.push(result);
}
// 批量获取中心点
const centerresult = view.getObjCenterByNames(
  [
    "zw_lengqueta_001",
    "zw_lengqueta_002",
    "zw_lengqueta_003",
    "zw_lengqueta_004",
    "dw_lengqueta_001",
    "dw_lengqueta_002",
    "dw_lengqueta_003",
    "dw_lengqueta_004",
    "dw_lengqueta_005",
  ]
);
console.log(centerresult, "模型中心点");
