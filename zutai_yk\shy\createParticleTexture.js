// 这个脚本可以用来生成粒子纹理
const canvas = document.createElement('canvas');
canvas.width = 128;
canvas.height = 128;
const context = canvas.getContext('2d');

// 创建径向渐变作为粒子纹理
const gradient = context.createRadialGradient(
    canvas.width / 2,
    canvas.height / 2,
    0,
    canvas.width / 2,
    canvas.height / 2,
    canvas.width / 2
);

gradient.addColorStop(0, 'rgba(255,255,255,1)');
gradient.addColorStop(0.2, 'rgba(200,200,255,1)');
gradient.addColorStop(0.4, 'rgba(100,100,255,0.8)');
gradient.addColorStop(1, 'rgba(0,0,100,0)');

context.fillStyle = gradient;
context.fillRect(0, 0, canvas.width, canvas.height);

// 导出为图片
const link = document.createElement('a');
link.href = canvas.toDataURL('image/png');
link.download = 'particle.png';
link.click(); 