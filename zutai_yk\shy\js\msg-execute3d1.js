var comdata;
var buildnum;
window.addEventListener("message", function (event) {
  //event.data获取传过来的数据
  if (event.data.type == "APdata") {
    console.log(event.data.data, poijson.devices, "event.data.data");

    // 根据poijson.devices生成comdata数组
    comdata = poijson.devices.map(device => {
      // 尝试找到匹配的AP数据
      // 1. 首先尝试通过deviceId与manageIp精确匹配
      // 2. 如果没有找到，则默认使用索引对应的数据（如果存在）

      let matchedData = event.data.data.find(item => item.manageIp === device.deviceId);

      // 如果没有通过IP匹配，尝试通过楼层号或名称进行匹配
      if (!matchedData && device.floorId && device.json && device.json.floorNum) {
        // 尝试匹配楼层和名称中的数字
        const floorPattern = new RegExp(`${device.floorId}F-AP\\d+`, 'i');
        matchedData = event.data.data.find(item =>
          floorPattern.test(item.name) ||
          (item.name && device.name && (
            item.name.includes(device.name) ||
            device.name.includes(item.name)
          ))
        );
      }

      // 如果仍然没有匹配，则使用索引对应的数据（如果存在）
      if (!matchedData) {
        const index = poijson.devices.indexOf(device);
        if (index < event.data.data.length) {
          matchedData = event.data.data[index];
        }
      }

      // 构建结果对象
      if (matchedData) {
        return {
          ...matchedData,
          deviceId: device.deviceId,
          title: device.title,
          name: device.name || matchedData.name,
          id: device.id,
          floorId: device.floorId,
          type: device.type,
          parkId: device.parkId,
          json: device.json
        };
      } else {
        return {
          deviceId: device.deviceId,
          title: device.title,
          name: device.name,
          id: device.id,
          floorId: device.floorId,
          type: device.type,
          parkId: device.parkId,
          json: device.json
        };
      }
    });

    console.log("生成的comdata数组:", comdata);

    // 根据comdata生成radarData
    const radarData = comdata.map(item => {
      // 确保json和position存在
      console.log(item.userCount, 'item2e');

      if (item && item.json && item.json.position) {
        return {
          id: 'wave' + item.id,
          count: item.userCount > 200 && item.userCount < 500 ? 3 : item.userCount > 499 && item.userCount < 1000 ? 4 : item.userCount > 999 && item.userCount < 50000 ? 6 : 2, // 波纹条数
          radius: item.userCount > 200 && item.userCount < 500 ? 4 : item.userCount > 499 && item.userCount < 1000 ? 6 : item.userCount > 999 && item.userCount < 50000 ? 8 : 3,
          position: {
            x: item.json.position[0],
            y: item.json.position[1],
            z: item.json.position[2]
          },
          color: '#15D36A',
          opacity: 0.5,
          speed: item.userCount > 200 && item.userCount < 500 ? 0.075 : item.userCount > 499 && item.userCount < 1000 ? 0.1 : item.userCount > 999 && item.userCount < 50000 ? 0.06 : 0.2,
        };
      }
      // 如果没有有效的position数据，返回null（后续可以过滤掉）
      return null;
    }).filter(item => item !== null); // 过滤掉无效的项

    console.log("生成的radarData数组:", radarData);

    // 使用radarData添加波纹效果
    if (view && typeof view.addWave === 'function' && radarData.length > 0) {
      view.addWave(radarData);
    }

  } else if (event.data.type == "light") {

  }
});
