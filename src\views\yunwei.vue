<template>
  <div>

    <SlidingPanel v-if="show" :showLeftPanel="true" :showRightPanel="true">
      <!-- 左侧内容 -->
      <template #left>
        <!-- <iframe class="item" :src="`${iframeUrl}/#/card/new/BimWorkOrder`" frameborder="0"></iframe> -->
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimEventStatistics?type=1`" frameborder="0"></iframe>
      </template>

      <!-- 右侧内容
      <template #right>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimMaintenanceInformation`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimPlannedTasks`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimWorkforceManagement`" frameborder="0"></iframe>
      </template> -->
    </SlidingPanel>
    <transition name="expand" mode="out-in">
      <iframe v-if="componentTag" :key="componentTag" class="componentTag" :src="iframeSrc" frameborder="0"></iframe>
    </transition>

    <!-- {{ iframeSrc }} -->
    <img class="close" @click="close" v-if="isclose" src="../assets/close.png" alt="">
  </div>
</template>

<script>
import SlidingPanel from "@/components/common/SlidingPanel.vue";
export default {
  components: {
    SlidingPanel,
  },
  data() {
    return {
      isclose: false,
      iframeUrl,
      componentTag: '',
      show: true,
    }
  },
  computed: {
    iframeSrc() {
      return this.iframeUrl + `/#/${this.componentTag}`;
    }
  },
  methods: {
    yunwei(index) {
      console.log(index, '运维');
      this.show = false

      if (index == 0) {

        this.isclose = true
        this.componentTag = 'card/maintananceMgt/assets/list'
      } else if (index == 1) {
        this.componentTag = 'card/bimAlarmManagement'

        this.isclose = true
      } else if (index == 2) {
        this.componentTag = 'card/bimRepairManagement'

        this.isclose = true
      } else if (index == 3) {
        this.componentTag = 'card/bimMaintenanceManagement'

        this.isclose = true
      }
      else if (index == 4) {
        this.componentTag = 'card/bimInspection'

        this.isclose = true
      }
      else if (index == 5) {
        this.componentTag = 'card/bimShifts'

        this.isclose = true
      } else if (index == 6) {
        this.componentTag = 'card/documentList'

        this.isclose = true
      }
      else {
        this.componentTag = ''
        this.show = true
      }
      console.log(this.iframeSrc);

    },
    close() {
      this.componentTag = ''
      this.isclose = false
    },
  }
};
</script>

<style scoped lang="less">
.item {
  width: 336px;
  height: 340px;
  overflow: hidden;
  margin-bottom: 5px;
}

.left-panel .item:nth-child(1) {
  height: 890px;
}

// .left-panel .item:nth-child(2) {
//   height: 285px;
// }

// .left-panel .item:nth-child(3) {
//   height: 200px;
// }

.right-panel .item:nth-child(1) {
  height: 236px;
}

.right-panel .item:nth-child(2) {
  height: 308px;
}

.right-panel .item:nth-child(3) {
  height: 420px;
}

.close {
  position: fixed;
  z-index: 100;
  top: 85px;
  right: 25px;
  font-size: 55px;
  color: #fff;
  cursor: pointer;
  width: 30px;
  height: 30px;
}


.expand-leave-active {
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-duration: 0.8s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-name: expandIn;
}

.expand-leave-active {
  animation-name: shrinkAndFade;
}

@keyframes expandIn {
  0% {
    transform: scale(0.5);
    opacity: 0;
    transform-origin: center;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shrinkAndFade {
  0% {
    transform: scale(1);
    opacity: 1;
    transform-origin: center;
  }

  100% {
    transform: scale(0.4);
    opacity: 0;
  }
}


.componentTag {
  position: fixed;
  z-index: 18;
  top: 80px;
  left: 1%;
  width: 98%;
  height: 890px;
  opacity: 1;
  transform-origin: center;
  /* 动画从中间展开 */
}



.left-panel {
  position: fixed;
  z-index: 1;
  width: 336px;
  top: 70px;
  left: 15px;
  height: 980px;
  // background: url("../assets/image/left.png");
  // background-size: 100% 100%;
}

.lefti1 {
  width: 336px;
  height: 300px;
}

.lefti2 {
  width: 336px;
  height: 436px;
}

.right-panel {
  position: fixed;
  z-index: 1;
  right: 15px;
  width: 336px;
  top: 70px;
  height: 980px;
}
</style>