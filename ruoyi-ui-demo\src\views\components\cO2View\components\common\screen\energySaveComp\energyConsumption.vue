<template>
  <custCard :title="title" autoHeight>
    <div class="cont">
      <div class="types">
        <div
          v-for="item in energyConsumption.chartTypes"
          :key="item.value"
          :class="{ active: item.value === currentType }"
          @click="currentType = item.value"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="filter">
        <el-radio-group
          v-model="tabPosition"
          size="mini"
          style="margin-right: 12px"
          @change="tabPositionChange"
        >
          <el-radio-button :label="item.value" v-for="item in energyConsumption.dateOptions" :key="item.value">{{item.label}}</el-radio-button>
        </el-radio-group>
        <el-date-picker
          v-model="dateSet"
          type="date"
          size="mini"
          placeholder="选择日期"
          @change="dateSetChange"
        >
        </el-date-picker>
      </div>
      <BaseChart
        height="230px"
        autoResize
        :optionData="getOptionData"
      ></BaseChart>
      <!--  节能分析-->
      <analysis v-if="energyConsumption.analysis" :data="energyConsumption.analysis || {}"></analysis>
    </div>
  </custCard>
</template>

<script>
import custCard from "../components/custCard.vue";
import analysis from "../components/analysis.vue";
import BaseChart from "../../BaseChart.vue";
export default {
  components: {
    custCard,
    BaseChart,
    analysis
  },
  props: {
    "title": {
      type: String,
      default: "系统总能耗",
    },
    "energyConsumption": {
      type: Object,
      default: () => {},
    }
  },
  computed: {
    getXAxisList () {
      let arr = []
      if(this.tabPosition === 'today'){
        const date = this.$moment().format('MM-DD');
        arr.push(date)
      } else {
        for (let i = 0; i < 7; i++) {
          arr.unshift(this.$moment().subtract(i, 'days').format('MM-DD'));
        }
      }
      return arr
    },
    getOptionData() {
      const dataList = (this.energyConsumption.chartDataSum?.chartData || []).map(item => {
              return  {
                  name: item.name,
                  data: this.tabPosition === 'today' ? [item.data[6]] : item.data,
                  type: this.currentType,
                  symbol: "none",
                  smooth: 0.6,
                  stack: "save",
                  lineStyle: {
                    color: `rgba(${item.rgbColor}, 1)`,
                    width: 1,
                  },
                  itemStyle: {
                    color: `rgba(${item.rgbColor}, 1)`,
                  },
                  barWidth: "10",
                  areaStyle: {
                    color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: `rgba(${item.rgbColor}, .5)`,
                      },
                      {
                        offset: 1,
                        color: `rgba(${item.rgbColor}, 0)`,
                      },
                    ]),
                  },
                }
            })
      return {
        xAxis: {
          show: true,
          type: "category",
          boundaryGap: true,
          data: this.getXAxisList,
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: "rgba(52, 137, 191, 1)",
            },
            onZero: false
          },
          axisTick: {
            show: true,
            inside: false,
            length: 5,
            lineStyle: {
              width: 1,
              color: "rgba(217, 217, 217, 1)",
            },
          },
          axisLabel: {
            show: true,
            fontSize: 12, // 根据需要调整字体大小
            // interval: 0, // 设置为0以显示所有坐标点
            align: 'center' //设置标签居中
          },
        },
        yAxis: [
          {
            type: "value",
            name:  `单位：${this.energyConsumption.chartDataSum?.unit || ''}`,
            position: "left",
            axisLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(52, 137, 191, 1)'
                }
              },
              axisLabel: {
                textStyle: {
                  color: "rgba(217, 217, 217, 1)"
                }
              },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(0, 83, 209, 0.3)",
              },
            },
            nameTextStyle: {
              align: "center",
              color: "rgb(255, 255, 255)",
              fontSize: 12,
              fontFamily: "Source Han Sans CN",
            },
          },
        ],
        legend: {
          icon: "circle",
          itemHeight: 10,
          itemWidth: 10,
          padding: [2, 10]
        },
        grid: {
          left: "3%",
          right: "5%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          ...dataList
        ],
      };
    },
  },
  data() {
    return {
      dateSet: '',
      tabPosition: "realTime",
      currentType: "bar",
    };
  },
  mounted() {
    this.dateSet = this.$moment().format("YYYY-MM-DD");
  },
  methods: {
    tabPositionChange () {
      this.dateSet = ''
    },
    dateSetChange () {
      this.tabPosition = ''
    }
  },
};
</script>

<style scoped lang="scss">
.cont {
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 10px 16px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    &:nth-child(odd) {
      border-top: 1px solid #0e3b6c;
      background: #333c4780;
    }
    .val {
      width: 50px;
    }
  }
  .types {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 12px 0;
    & > div {
      margin-right: 16px;
      background: url("/image/screen/airConditioning_tabs.png") no-repeat 0;
      background-size: 100% 100%;
      font-family: Source Han Sans CN;
      font-size: 12px;
      width: 100px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      cursor: pointer;
      &.active {
        background: url("/image/screen/airConditioning_tabs_active.png")
          no-repeat 0;
        background-size: 100% 100%;
      }
    }
  }
  .filter {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
    .el-input {
      width: 130px;
    }
  }
}
</style>
