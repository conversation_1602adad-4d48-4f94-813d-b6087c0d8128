var parkId = "0",
  buildId = "0",
  floorId = "0";
async function getmodeltableData(type, projectId, parkId, buildId, floorId) {
  // console.log(type, projectId);
  // try {
  //   const response = await axios.get(`${LocalUrl}/projectSet/all`, {
  //     params: {
  //       type: type,
  //       projectId: projectId,
  //       deviceId: "",
  //       parkId: parkId,
  //       buildId: buildId,
  //       floorId: floorId,
  //       roomId: "",
  //     },
  //   });

  if (type === 1) {
    // alreadymodels = response.data.data;
    // alreadymodels.forEach((item, index) => {
    //   alreadymodels[index].id = item.id;
    // });
    console.log(alreadymodels, "模型数据111");

    upaddmodel();
  } else if (type === 2) {
    // let lidata = response.data.data;
    // console.log(response, "管道数据");
    // lidata.forEach((obj) => {
    //   // 提取最外层的 id 值
    //   let outerId = obj.id;
    //   // 解析 pzjson 字符串为对象
    //   let pzjsonObj = JSON.parse(obj.pzjson);
    //   // 将最外层的 id 值赋给 pzjson 对象
    //   pzjsonObj.id = outerId;

    //   // 将修改后的 pzjson 对象重新转化为 JSON 字符串
    //   obj.pzjson = JSON.stringify(pzjsonObj);
    // });

    // alreadylines = lidata.map((item) => {
    //   return JSON.parse(item.pzjson);
    // });
    // // alreadylines.forEach(item => {
    // //     item.json
    // // });
    console.log(alreadylines, "管道数据111");
    upaddline();
  }
  // } catch (error) {
  //   console.error("Error:", error);
  // }
  // JSONData.devices = response.data.data;
}
async function execute() {
  await getmodeltableData(1, urlid, 0, 0, 0);
  await getmodeltableData(2, urlid, 0, 0, 0);
  console.log(alreadymodels, alreadylines);
  // let mjson = [];
  // alreadymodels.forEach((item) => {
  //   mjson.push({
  //     json: JSON.parse(item.pzjson),
  //     id: item.id,
  //     title: item.name,
  //     name: item.name,
  //     parkId: item.parkId,
  //     floorId: item.floorId,
  //     type: 2,
  //   });
  // });
  // poijson = { models: pzjson, devices: mjson };
  // console.log(poijson, 222);
  // //添加模型
  // view.LoadJsonDevice.setScene(poijson, (obj) => {
  //   console.log(obj, 222);

  //   const models = view.getObjByModelIds(["500333", "500334", "500335"]); // 通过modelIds批量获取模型
  //   console.log(models);
  //   view.setOutlineModel(models); // 可以传多个， 模型数组
  //   // setTimeout(() => {
  //   //   const info = view.getObjViewInfoByModelId(500333);
  //   //   const {position, target} = info;
  //   //   view.animateCamera(position, target, 500);
  //   // }, 5000);
  // });
  // linejson = alreadylines;
  // //添加管道
  // console.log(linejson, 33);
  // // 转换后的JSON对象
  // // 转换后的数组
  // var convertedArray = [];

  // // 遍历原始数据中的每个设备对象
  // // linejson.forEach(function (item) {
  // //   var points = item.points;
  // //   // 创建一个新的对象，包含设备的信息
  // //   var convertedDevice = {
  // //     useShader: true,
  // //     color: item.color,
  // //     bgColor: item.bgColor,
  // //     name: "tube-" + item.id,
  // //     points: item.points,
  // //     direction: item.direction,
  // //     // texturePath: item.texturePath,
  // //     repeatX: item.repeatX,
  // //     size: item.size,
  // //     isFlyline: true,
  // //     range: 180, // 飞线长度
  // //     speed: 3,
  // //   };
  // //   // 将新对象添加到数组中
  // //   convertedArray.push(convertedDevice);
  // // });
  // if (linejson.length) {
  //   let lljson = [
  //     {
  //       useShader: true,
  //       color: "#aa8921",
  //       bgColor: "#313B43",
  //       name: "Cylinder099",
  //       points: [
  //         [-1.1662564277648926, -0.6123451590538025, 0.19119834899902344],
  //         [-1.1662564277648926, -0.6123462915420532, 4.086634635925293],
  //         [-1.177513599395752, -0.6123462915420532, 4.116909027099609],
  //         [-1.206953525543213, -0.612346351146698, 4.142604827880859],
  //         [-1.2399696111679077, -0.612346351146698, 4.149908542633057],
  //         [-2.5850882530212402, -0.612346351146698, 4.149908542633057],
  //         [-2.629512310028076, -0.612346351146698, 4.140296459197998],
  //         [-2.667074203491211, -0.6123462915420532, 4.118006706237793],
  //         [-2.6931142807006836, -0.6123462915420532, 4.085423469543457],
  //         [-2.7046985626220703, -0.6123462915420532, 4.047277450561523],
  //         [-2.7046985626220703, -0.6123450994491577, 0.06968307495117188],
  //       ],
  //       direction: 1,
  //       repeatX: 50,
  //       size: 0.07,
  //       isFlyline: true,
  //       range: 180,
  //       speed: 3,
  //     },
  //     {
  //       useShader: true,
  //       color: "#aa8921",
  //       bgColor: "#313B43",
  //       name: "Cylinder103",
  //       points: [
  //         [-3.045335531234741, -0.6123490929603577, 0.10705757141113281],
  //         [-3.045335531234741, -0.612350344657898, 4.308966159820557],
  //         [-3.0566020011901855, -0.612350344657898, 4.339199542999268],
  //         [-3.0860352516174316, -0.6123504042625427, 4.36484956741333],
  //         [-3.1190338134765625, -0.6123504042625427, 4.372267723083496],
  //         [-4.169950485229492, -0.6123504042625427, 4.372267723083496],
  //         [-4.214358329772949, -0.6123504042625427, 4.362607955932617],
  //         [-4.251931190490723, -0.612350344657898, 4.340254306793213],
  //         [-4.277952671051025, -0.612350344657898, 4.307746410369873],
  //         [-4.289526462554932, -0.612350344657898, 4.269633769989014],
  //         [-4.289570331573486, -0.6123501658439636, 3.72200870513916],
  //       ],
  //       direction: 1,
  //       repeatX: 50,
  //       size: 0.07,
  //       isFlyline: true,
  //       range: 180,
  //       speed: 3,
  //     },
  //   ];
  //   console.log(linejson);
  //   // 遍历原始数据中的每个设备对象
  //   linejson.forEach(function (device) {
  //     var points = device.points;

  //     // 创建一个新的对象，包含设备的信息
  //     var convertedDevice = {
  //       color: device.color,
  //       bgColor: device.bgColor,
  //       useShader: true,
  //       name: "tube-" + device.id,
  //       points: points,
  //       direction: 1,
  //       // texturePath: device.texturePath,
  //       repeatX: 1000,
  //       size: device.size,
  //       range: 180,
  //       speed: 3,
  //       isFlyline: true,
  //     };

  //     // 将新对象添加到数组中
  //     convertedArray.push(convertedDevice);
  //   });

  //   // 打印转换后的JSON数据
  //   console.log(convertedArray);
  //   setTimeout(() => {
  //     view.DrawLine1.setTubeData(convertedArray);
  //   }, 800);
  // }
  // console.log("This will run after the data is received.");
}

//离线本地部署 过滤模型
function processModelPaths(modelConfigs, pathConfigs) {
  // 1. 收集所有 modelId
  const modelIds = modelConfigs.map((config) => config.json.modelId);

  // 2. 只保留匹配的项并修改路径
  const updatedPathConfigs = pathConfigs
    .filter((pathConfig) => modelIds.includes(pathConfig.id)) // 过滤掉不匹配的项
    .map((pathConfig) => {
      // 从原始路径中提取文件名
      const fileName = pathConfig.path.split("/").pop();
      // 修改为新的路径格式
      return {
        id: pathConfig.id,
        path: `./product/${fileName}`,
      };
    });

  return updatedPathConfigs;
}

//刷新添加的模型
function upaddmodel() {
  let mjson = [];
  console.log(alreadymodels, "模型数据111");

  alreadymodels.forEach((item) => {
    mjson.push({
      json: JSON.parse(item.pzjson),
      id: item.id,
      title: item.name,
      name: item.name,
      parkId: item.parkId,
      floorId: item.floorId,
      type: 2,
    });
  });
  console.log(
    mjson,
    allresponse.data.poijson
      ? JSON.parse(allresponse.data.poijson)
      : { models: [], devices: [] },
    "测试啊啊啊"
  );

  poijson = {
    models: processModelPaths(
      mjson,
      allresponse.data.poijson
        ? JSON.parse(allresponse.data.poijson)
        : { models: [], devices: [] }
    ),
    devices: mjson,
  };
  console.log(poijson, 222);
  //添加模型
  setTimeout(() => {
    view.LoadJsonDevice.setScene(poijson, (obj) => {
      console.log(obj, 222);
      const models = view.getObjByModelIds(["500333", "500334", "500335"]); // 通过modelIds批量获取模型
      console.log(models);
      view.setOutlineModel(models); // 可以传多个， 模型数组
      if (projectname == "ao组态") {
        view.clearAllLight(); // 清除所有灯光
        let lightConfig = [
          {
            type: "AmbientLight",
            color: "#aaaaff",
            intensity: 1.5,
          },
          {
            intensity: 0.5,
            type: "DirectionalLight",
            color: "#ffffff",
            position: [30, 190, 20],
          },
        ];
        view.setLight(lightConfig);
        view.Fan.initFan(
          [
            "sb_fs_501009",
            "sb_fs_501067",
            // "sb_fs_501068",
            "sb_fs_501069",
            "sb_fs_501070",
            // "sb_fs_501071",
            // "sb_fs_501072",
            // "sb_fs_501073",
            "b3_rs_flrbfs_004008_501074",
            "b3_rs_flrbfs_004008_501075",
            "b3_rs_flrbfs_004008_501076",
            "b3_rs_flrbfs_004008_501077",
            "b3_rs_flrbfs_004008_501078",
            // "b3_rs_flrbfs_004008_501079",
            // "b3_rs_flrbfs_004008_501080",
            "b3_rs_flrbfs_004008_501081",
            // "b3_rs_flrbfs_004008_501082",
          ],
          {
            sb_fs_501009: "normal",
            sb_fs_501067: "normal",
            sb_fs_501068: "normal",
            sb_fs_501069: "normal",
            sb_fs_501070: "normal",
            sb_fs_501071: "normal",
            sb_fs_501072: "normal",
            sb_fs_501073: "normal",
            b3_rs_flrbfs_004008_501074: "normal",
            b3_rs_flrbfs_004008_501075: "normal",
            b3_rs_flrbfs_004008_501076: "normal",
            b3_rs_flrbfs_004008_501077: "normal",
            b3_rs_flrbfs_004008_501078: "normal",
            b3_rs_flrbfs_004008_501079: "normal",
            b3_rs_flrbfs_004008_501080: "normal",
            b3_rs_flrbfs_004008_501081: "normal",
            b3_rs_flrbfs_004008_501082: "normal",

            // b3_rs_flrbfs_004008_45824: "normal",
          },
          {
            sb_fs_501009: ["b1001_501009", "sb_fs_501009"],
            sb_fs_501067: ["b1001_501067", "sb_fs_501067"],
            sb_fs_501068: ["b1001_501068", "sb_fs_501068"],
            sb_fs_501069: ["b1001_501069", "sb_fs_501069"],

            sb_fs_501070: ["b1001_501070", "sb_fs_501070"],
            sb_fs_501071: ["b1001_501071", "sb_fs_501071"],
            sb_fs_501072: ["b1001_501072", "sb_fs_501072"],
            sb_fs_501073: ["b1001_501073", "sb_fs_501073"],
            b3_rs_flrbfs_004008_501074: [
              "b3_rs_flrbfs_004008_501074",
              "b3_rs_flrbfs_004008_501074",
            ],
            b3_rs_flrbfs_004008_501075: [
              "b3_rs_flrbfs_004008_501075",
              "b3_rs_flrbfs_004008_501075",
            ],
            b3_rs_flrbfs_004008_501076: [
              "b3_rs_flrbfs_004008_501076",
              "b3_rs_flrbfs_004008_501076",
            ],
            b3_rs_flrbfs_004008_501077: [
              "b3_rs_flrbfs_004008_501077",
              "b3_rs_flrbfs_004008_501077",
            ],
            b3_rs_flrbfs_004008_501078: [
              "b3_rs_flrbfs_004008_501078",
              "b3_rs_flrbfs_004008_501078",
            ],
            b3_rs_flrbfs_004008_501079: [
              "b3_rs_flrbfs_004008_501079",
              "b3_rs_flrbfs_004008_501079",
            ],
            b3_rs_flrbfs_004008_501080: [
              "b3_rs_flrbfs_004008_501080",
              "b3_rs_flrbfs_004008_501080",
            ],
            b3_rs_flrbfs_004008_501081: [
              "b3_rs_flrbfs_004008_501081",
              "b3_rs_flrbfs_004008_501081",
            ],
            b3_rs_flrbfs_004008_501082: [
              "b3_rs_flrbfs_004008_501082",
              "b3_rs_flrbfs_004008_501082",
            ],
          }
        );
        // 默认绕着y轴旋转，可以设置names绕x,
        // view.Fan.setRotateXArr([
        //   "sb_fs_501009",
        //   "sb_fs_501067",
        //   "sb_fs_501068",
        //   "sb_fs_501069",
        //   "sb_fs_501070",
        //   "sb_fs_501071",
        //   "sb_fs_501072",
        //   "sb_fs_501073",
        //   // "b3_rs_flrbfs_004008_501074",
        //   // "b3_rs_flrbfs_004008_501075",
        //   // "b3_rs_flrbfs_004008_501076",
        //   // "b3_rs_flrbfs_004008_501077",
        //   // "b3_rs_flrbfs_004008_501078",
        //   // "b3_rs_flrbfs_004008_501079",
        //   // "b3_rs_flrbfs_004008_501080",
        //   // "b3_rs_flrbfs_004008_501081",
        //   // "b3_rs_flrbfs_004008_501082",
        //   // "b3_rs_flrbfs_004008_45824",
        // ]);
        view.setAlwaysOutlineModelByNames(
          [
            "sb_fs_501009",
            "sb_fs_501067",
            // "sb_fs_501068",
            "sb_fs_501069",
            "sb_fs_501070",
            // "sb_fs_501071",
            // "sb_fs_501072",
            // "sb_fs_501073",
            "b3_rs_flrbfs_004008_501074",
            "b3_rs_flrbfs_004008_501075",
            "b3_rs_flrbfs_004008_501076",
            "b3_rs_flrbfs_004008_501077",
            "b3_rs_flrbfs_004008_501078",
            // "b3_rs_flrbfs_004008_501079",
            // "b3_rs_flrbfs_004008_501080",
            "b3_rs_flrbfs_004008_501081",
            // "b3_rs_flrbfs_004008_501082",
            // "b3_rs_flrbfs_004008_45824",
          ],
          100
        ); // 设置永久高亮
        // 根据modelid开启/关闭动画
        view.LoadJsonDevice.setAnimateByModelIds(
          [501083, 501085, 501009, 501069, 501067, 501070],
          // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
          // 501070, 501072, 501073,
          true
        );
        view.LoadJsonDevice.setAnimateByModelIds(
          [501084, 501068, 501071, 501072, 501073],
          // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
          // 501070, 501072, 501073,
          false
        );
        addlablenewall(
          view.getObjCenterByModelIds([
            501009, 501067, 501068, 501069, 501070, 501071, 501072, 501073,
            501074, 501075, 501076, 501077, 501078, 501079, 501080, 501081,
            501082, 501083, 501084, 501085,
            //  501086, 501087,
          ])
        );
      } else if (projectname == "内部组态") {
        view.clearAllLight(); // 清除所有灯光
        let lightConfig = [
          {
            type: "AmbientLight",
            color: "#fff",
            intensity: 1.5,
          },
          {
            intensity: 1.5,
            type: "DirectionalLight",
            color: "#ffffff",
            position: [30, 190, 20],
          },
        ];
        view.setLight(lightConfig);
        view.Fan.initFan(
          [
            "sb_fs_501356",
            "sb_fs_501357",
            "sb_fs_501358",
            "sb_fs_501359",
            "sb_fs_501360",
            "sb_fs_501361",
            "sb_fs_501362",
            "sb_fs_501363",
            "b3_rs_flrbfs_004_501342",
            "b3_rs_flrbfs_004_501343",
            "b3_rs_flrbfs_004_501344",
            "b3_rs_flrbfs_004_501345",
            "b3_rs_flrbfs_004_501346",
            "b3_rs_flrbfs_004_501347",
            "b3_rs_flrbfs_004_501348",
            "b3_rs_flrbfs_004_501349",
            "b3_rs_flrbfs_004_501350",
          ],
          {
            sb_fs_501356: "normal",
            sb_fs_501357: "normal",
            sb_fs_501358: "normal",
            sb_fs_501359: "normal",
            sb_fs_501360: "normal",
            sb_fs_501361: "normal",
            sb_fs_501362: "normal",
            sb_fs_501363: "normal",
            b3_rs_flrbfs_004_501342: "normal",
            b3_rs_flrbfs_004_501343: "normal",
            b3_rs_flrbfs_004_501344: "normal",
            b3_rs_flrbfs_004_501345: "normal",
            b3_rs_flrbfs_004_501346: "normal",
            b3_rs_flrbfs_004_501347: "normal",
            b3_rs_flrbfs_004_501348: "normal",
            b3_rs_flrbfs_004_501349: "normal",
            b3_rs_flrbfs_004_501350: "normal",

            // b3_rs_flrbfs_004_45824: "normal",
          },
          {
            sb_fs_501356: ["b1001_501356", "sb_fs_501356"],
            sb_fs_501357: ["b1001_501357", "sb_fs_501357"],
            sb_fs_501358: ["b1001_501358", "sb_fs_501358"],
            sb_fs_501359: ["b1001_501359", "sb_fs_501359"],

            sb_fs_501360: ["b1001_501360", "sb_fs_501360"],
            sb_fs_501361: ["b1001_501361", "sb_fs_501361"],
            sb_fs_501362: ["b1001_501362", "sb_fs_501362"],
            sb_fs_501363: ["b1001_501363", "sb_fs_501363"],
            b3_rs_flrbfs_004_501342: [
              "b3_rs_flrbfs_004_501342",
              "b3_rs_flrbfs_004_501342",
            ],
            b3_rs_flrbfs_004_501343: [
              "b3_rs_flrbfs_004_501343",
              "b3_rs_flrbfs_004_501343",
            ],
            b3_rs_flrbfs_004_501344: [
              "b3_rs_flrbfs_004_501344",
              "b3_rs_flrbfs_004_501344",
            ],
            b3_rs_flrbfs_004_501345: [
              "b3_rs_flrbfs_004_501345",
              "b3_rs_flrbfs_004_501345",
            ],
            b3_rs_flrbfs_004_501346: [
              "b3_rs_flrbfs_004_501346",
              "b3_rs_flrbfs_004_501346",
            ],
            b3_rs_flrbfs_004_501347: [
              "b3_rs_flrbfs_004_501347",
              "b3_rs_flrbfs_004_501347",
            ],
            b3_rs_flrbfs_004_501348: [
              "b3_rs_flrbfs_004_501348",
              "b3_rs_flrbfs_004_501348",
            ],
            b3_rs_flrbfs_004_501349: [
              "b3_rs_flrbfs_004_501349",
              "b3_rs_flrbfs_004_501349",
            ],
            b3_rs_flrbfs_004_501350: [
              "b3_rs_flrbfs_004_501350",
              "b3_rs_flrbfs_004_501350",
            ],
          }
        );
        // 默认绕着y轴旋转，可以设置names绕x,
        // view.Fan.setRotateXArr([
        //   "sb_fs_501009",
        //   "sb_fs_501067",
        //   "sb_fs_501068",
        //   "sb_fs_501069",
        //   "sb_fs_501070",
        //   "sb_fs_501071",
        //   "sb_fs_501072",
        //   "sb_fs_501073",
        //   // "b3_rs_flrbfs_004008_501074",
        //   // "b3_rs_flrbfs_004008_501075",
        //   // "b3_rs_flrbfs_004008_501076",
        //   // "b3_rs_flrbfs_004008_501077",
        //   // "b3_rs_flrbfs_004008_501078",
        //   // "b3_rs_flrbfs_004008_501079",
        //   // "b3_rs_flrbfs_004008_501080",
        //   // "b3_rs_flrbfs_004008_501081",
        //   // "b3_rs_flrbfs_004008_501082",
        //   // "b3_rs_flrbfs_004008_45824",
        // ]);
        view.setAlwaysOutlineModelByNames(
          [
            "sb_fs_501356",
            "sb_fs_501357",
            "sb_fs_501358",
            "sb_fs_501359",
            "sb_fs_501360",
            "sb_fs_501361",
            "sb_fs_501362",
            "sb_fs_501363",
            "b3_rs_flrbfs_004_501342",
            "b3_rs_flrbfs_004_501343",
            "b3_rs_flrbfs_004_501344",
            "b3_rs_flrbfs_004_501345",
            "b3_rs_flrbfs_004_501346",
            "b3_rs_flrbfs_004_501347",
            "b3_rs_flrbfs_004_501348",
            "b3_rs_flrbfs_004_501349",
            "b3_rs_flrbfs_004_501350",
          ],
          1
        ); // 设置永久高亮
        // 根据modelid开启/关闭动画
        // view.LoadJsonDevice.setAnimateByModelIds(
        //   [501083, 501085, 501009, 501069, 501067, 501070],
        //   // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
        //   // 501070, 501072, 501073,
        //   true
        // );
        // view.LoadJsonDevice.setAnimateByModelIds(
        //   [501084, 501068, 501071, 501072, 501073],
        //   // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
        //   // 501070, 501072, 501073,
        //   false
        // );
        addlablenewall(
          view.getObjCenterByModelIds([
            501342, 501343, 501344, 501345, 501346, 501347, 501348, 501349,
            501350, 501351, 501352, 501353, 501354, 501355, 501356, 501357,
            501358, 501359, 501360, 501361, 501362, 501363,
          ])
        );
      } else if (projectname == "内部冷热源组态") {
        view.clearAllLight(); // 清除所有灯光
        let lightConfig = [
          {
            type: "AmbientLight",
            color: "#fff",
            intensity: 1.5,
          },
          {
            intensity: 1.5,
            type: "DirectionalLight",
            color: "#ffffff",
            position: [30, 190, 20],
          },
        ];
        view.setLight(lightConfig);

        view.setColorByModelIds(
          [
            // "sb_fs_500120",
            "sb_fs_500121",
            // "sb_fs_500122",
            // "sb_fs_500123",
            // "sb_fs_500124",
            "sb_fs_500125",
            // "sb_fs_500126",
            // "sb_fs_500127",
            // "b3_rs_flrbfs_004_500128",
            // "b3_rs_flrbfs_004_500129",
            // "b3_rs_flrbfs_004_500130",
            // "b3_rs_flrbfs_004_500131",
            // "b3_rs_flrbfs_004_500132",
            // "b3_rs_flrbfs_004_500133",
            "b3_rs_flrbfs_004_500134",
            "b3_rs_flrbfs_004_500135",
            "b3_rs_flrbfs_004_500136",
            "c_wq_500119",
            "网格101_500119",
            "f_500121",
            "f_500125",
          ],
          "#FF0041"
        );

        // 设置永久高亮

        // 根据modelid开启/关闭动画
        // view.LoadJsonDevice.setAnimateByModelIds(
        //   [501083, 501085, 501009, 501069, 501067, 501070],
        //   // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
        //   // 501070, 501072, 501073,
        //   true
        // );
        view.LoadJsonDevice.setAnimateByModelIds(
          [
            500130, 500128, 500133, 500131, 500129, 500132, 500120, 500122,
            500123, 500118, 500117, 500124, 500126, 500127,
          ],
          // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
          // 501070, 501072, 501073,
          false
        );
        console.log(
          view.getObjCenterByModelIds([
            500128, 500129, 500130, 500131, 500132, 500133, 500134, 500135,
            500136,
          ]),
          "getObjCenterByModelIds"
        );

        addlablenewall(
          view.getObjCenterByModelIds([
            500117, 500118, 500119, 500120, 500121, 500122, 500123, 500124,
            500125, 500126, 500127, 500128, 500130, 500133, 500137, 500138,
            500131, 500129, 500132, 500134, 500135, 500136, 500129,
          ])
        );
        //加烟雾
        // let yanwudata = view.getObjCenterByModelIds([
        //   500128,
        //    500129, 500130, 500131, 500132, 500133, 500134, 500135, 500136,
        // ]);

        // yanwudata.forEach((item) => {
        //   console.log(item.center);
        //   view.Wind.createSmoke(
        //     {x:item.center.x,y:item.center.y-0.35,z:item.center.z},
        //     "./textures/smokeparticle.png",
        //     "name1",
        //     1.0, // 颜色调整， 0-1
        //   2.2 // size，粒子大小
        //   );
        // });
      } else {
        view.clearAllLight(); // 清除所有灯光
        let lightConfig = [
          {
            type: "AmbientLight",
            color: "#fff",
            intensity: 1.5,
          },
          {
            intensity: 1,
            type: "DirectionalLight",
            color: "#ffffff",
            position: [30, 190, 20],
          },
        ];
        view.setLight(lightConfig);
      }

      //加烟雾
      // let yanwudata = view.getObjCenterByModelIds([
      //   500128,
      //    500129, 500130, 500131, 500132, 500133, 500134, 500135, 500136,
      // ]);

      // yanwudata.forEach((item) => {
      //   console.log(item.center);
      //   view.Wind.createSmoke(
      //     {x:item.center.x,y:item.center.y-0.35,z:item.center.z},
      //     "./textures/smokeparticle.png",
      //     "name1",
      //     1.0, // 颜色调整， 0-1
      //   2.2 // size，粒子大小
      //   );
      // });

      // console.log(
      //   view.getObjCenterByModelIds([
      //     501009, 501067, 501068, 501069, 501070, 501071, 501072, 501073,
      //     501074, 501075, 501076, 501077, 501078, 501079, 501080, 501081,
      //     501082, 501083, 501084, 501085, 501086, 501087,
      //   ]),

      //   "getObjCenterByModelIds"
      // );

      // setTimeout(() => {
      //   const info = view.getObjViewInfoByModelId(500333);
      //   const {position, target} = info;
      //   view.animateCamera(position, target, 500);
      // }, 5000);
    });
  }, 100);
}
//刷新添加的管道
function upaddline() {
  linejson = alreadylines;
  //添加管道
  console.log(linejson, 33);
  // 转换后的JSON对象
  // 转换后的数组
  var convertedArray = [];

  // 遍历原始数据中的每个设备对象
  // linejson.forEach(function (item) {
  //   var points = item.points;
  //   // 创建一个新的对象，包含设备的信息
  //   var convertedDevice = {
  //     useShader: true,
  //     color: item.color,
  //     bgColor: item.bgColor,
  //     name: "tube-" + item.id,
  //     points: item.points,
  //     direction: item.direction,
  //     // texturePath: item.texturePath,
  //     repeatX: item.repeatX,
  //     size: item.size,
  //     isFlyline: true,
  //     range: 180, // 飞线长度
  //     speed: 3,
  //   };
  //   // 将新对象添加到数组中
  //   convertedArray.push(convertedDevice);
  // });
  if (linejson.length) {
    let lljson = [
      {
        useShader: true,
        color: "#aa8921",
        bgColor: "#313B43",
        name: "Cylinder099",
        points: [
          [-1.1662564277648926, -0.6123451590538025, 0.19119834899902344],
          [-1.1662564277648926, -0.6123462915420532, 4.086634635925293],
          [-1.177513599395752, -0.6123462915420532, 4.116909027099609],
          [-1.206953525543213, -0.612346351146698, 4.142604827880859],
          [-1.2399696111679077, -0.612346351146698, 4.149908542633057],
          [-2.5850882530212402, -0.612346351146698, 4.149908542633057],
          [-2.629512310028076, -0.612346351146698, 4.140296459197998],
          [-2.667074203491211, -0.6123462915420532, 4.118006706237793],
          [-2.6931142807006836, -0.6123462915420532, 4.085423469543457],
          [-2.7046985626220703, -0.6123462915420532, 4.047277450561523],
          [-2.7046985626220703, -0.6123450994491577, 0.06968307495117188],
        ],
        direction: 1,
        repeatX: 50,
        size: 0.07,
        isFlyline: true,
        range: 80,
        speed: 3,
      },
      {
        useShader: true,
        color: "#aa8921",
        bgColor: "#313B43",
        name: "Cylinder103",
        points: [
          [-3.045335531234741, -0.6123490929603577, 0.10705757141113281],
          [-3.045335531234741, -0.612350344657898, 4.308966159820557],
          [-3.0566020011901855, -0.612350344657898, 4.339199542999268],
          [-3.0860352516174316, -0.6123504042625427, 4.36484956741333],
          [-3.1190338134765625, -0.6123504042625427, 4.372267723083496],
          [-4.169950485229492, -0.6123504042625427, 4.372267723083496],
          [-4.214358329772949, -0.6123504042625427, 4.362607955932617],
          [-4.251931190490723, -0.612350344657898, 4.340254306793213],
          [-4.277952671051025, -0.612350344657898, 4.307746410369873],
          [-4.289526462554932, -0.612350344657898, 4.269633769989014],
          [-4.289570331573486, -0.6123501658439636, 3.72200870513916],
        ],
        direction: 1,
        repeatX: 50,
        size: 0.07,
        isFlyline: true,
        range: 180,
        speed: 3,
      },
    ];
    console.log(linejson);
    // 遍历原始数据中的每个设备对象
    linejson.forEach(function (device) {
      console.log(device, "linedev");
      var points = device.points;
      // 创建一个新的对象，包含设备的信息
      var convertedDevice = {
        color: "#FFFF00",
        bgColor: device.title.includes("green") ? "#1AE5EF" : "#4B92AC",

        name: "tube-" + device.id,
        points: points,
        direction: [
          501053, 501054, 501052, 501047, 501040, 501033, 501036, 501051,
          501056, 501020, 501021, 501022, 501023,
        ].includes(device.id)
          ? 0
          : 1,
        // texturePath: device.texturePath,
        repeatX: 50,
        size: device.title.includes("cu") ? "0.04" : "0.045",
        range: 40,
        speed: 1.1,
        isFlyline: true,
        useShader: true,
        showTube: true,
        opacity: 0.12,
      };

      // 将新对象添加到数组中
      convertedArray.push(convertedDevice);
    });
    // view.setBloomParams({
    //   threshold: 1.9,
    //   strength: 1.2,
    //   radius: 0.9,
    // });
    // 打印转换后的JSON数据
    console.log(convertedArray, "linedev");
    setTimeout(() => {
      view.DrawLine.setTubeData(convertedArray);
      const ids = [];
      const startId = 500089;
      const endId = 500108;
      for (let i = startId; i <= endId; i++) {
        ids.push(`tube-${i}`);
      }
      view.DrawLine.setTubeAniFlyline(
        [
          //冷却泵2
          "tube-500091",
          "tube-500106",
          //冷机1
          "tube-500074",
          "tube-500075",
          "tube-500100",
          "tube-500102",
          //冷冻泵3
          "tube-500068",
          "tube-500076",
          //冷却塔6
          "tube-500112",
        ],

        false
      );
    }, 800);
  }
}
