import * as THREE from 'three';
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';

// 初始化场景、相机、渲染器等...
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ antialias: true });
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

// 存储模型部件和它们的爆炸参数
let modelParts = [];
const explosionParams = {
    maxDistance: 5,    // 最大爆炸距离
    speed: 0.5,        // 爆炸速度
    isExploded: false  // 当前状态
};

// 加载GLTF模型
const loader = new GLTFLoader();
loader.load('model.gltf', (gltf) => {
    const model = gltf.scene;
    
    // 遍历模型子对象进行分组（根据你的实际分组名称修改）
    model.traverse((child) => {
        if (child.isMesh) {
            // 示例分组逻辑 - 根据名称包含的字符串分组
            const partInfo = {
                object: child,
                originalPosition: child.position.clone(),
                direction: new THREE.Vector3(0, 0, 1) // 默认Z轴方向
            };

            if (child.name.includes('polySurface296')) {
                partInfo.direction.set(1, 0, 0); // X轴方向
            } else if (child.name.includes('group2')) {
                partInfo.direction.set(0, 1, 0); // Y轴方向
            } else if (child.name.includes('group3')) {
                partInfo.direction.set(-1, 0, 0); // 反方向X轴
            }
            
            modelParts.push(partInfo);
        }
    });
    
    scene.add(model);
});

// 爆炸动画函数
function explodeModel(delta) {
    modelParts.forEach(part => {
        const currentPos = part.object.position;
        const targetPos = part.originalPosition.clone()
            .add(part.direction.clone().multiplyScalar(explosionParams.maxDistance));
        
        // 使用缓动函数实现平滑移动
        part.object.position.lerpVectors(
            explosionParams.isExploded ? part.originalPosition : currentPos,
            explosionParams.isExploded ? part.originalPosition : targetPos,
            delta * explosionParams.speed
        );
    });
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);
    const delta = Math.min(0.1, clock.getDelta());
    
    if (explosionParams.isExploded) {
        explodeModel(delta);
    }
    
    renderer.render(scene, camera);
}
animate();

// 触发爆炸/恢复的函数
function toggleExplosion() {
    explosionParams.isExploded = !explosionParams.isExploded;
}

// 示例：绑定按钮点击事件
document.getElementById('explodeBtn').addEventListener('click', toggleExplosion); 