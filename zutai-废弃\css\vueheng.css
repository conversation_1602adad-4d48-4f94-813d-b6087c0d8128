/* 设置滚动条的样式 */
::-webkit-scrollbar {
  height: 14px;

}

* {
  font-family: "微软雅黑";
}

.divtit111 {
  padding-top: 1px;
  width      : 100%;
  text-align : center;

}

.divtit1 {
  margin-top : -12px;
  margin-left: 10px;
}


.divtit2 {

  margin-left: 10px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 3px rgba(77, 77, 77, 0.3);
  border-radius     : 5px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius        : 5px;
  /* background        : rgba(0, 0, 0, 0.8); */
  -webkit-box-shadow   : inset 0 0 3px RGB(243, 243, 244);
}

.ant-modal-wrap {
  position: relative !important;
}

.ant-select {
  width: 100% !important;
}

.toplist .rad {
  margin-top: 17.65px;
  width     : 2px;
  height    : 18px;
  background: #36C9D1;
}

.rightBtn .list .content .rad {
  margin-top: 0px;
  width     : 2px;
  height    : 18px;
  background: #36C9D1;
}

.rightBtn .list .content .rad2 {
  background   : transparent;
  width        : 4px;
  height       : 4px;
  border-radius: 4px;
  margin       : 0 .8px;
}

.ant-form-item {
  margin: 0 !important;
}

.color {
  color        : red;
  text-indent  : 20px;
  margin-bottom: 20px;
}

.rightBtn .list .content {
  display           : flex;
  align-items       : center;
  text-align        : center;
  margin-left       : 10px;
  /* font-size      : 15px; */
  /* color          : #fff; */
  justify-content   : space-around;
  width             : 50px;
}

.title {
  position: absolute;
  top     : 54px;
  left    : 150px;
}

#date {
  width          : 100%;
  position       : fixed;
  top            : 0;
  z-index        : 5;
  color          : #fff;
  display        : flex;
  justify-content: space-between;
}

#date>div {
  margin-top: 30px;
}

#date>div:first-of-type {
  margin-left: 30px;
}

#date>div:last-of-type {
  margin-right: 30px;
  margin-left : 10px;
}

#temperature {
  font-size: 20px;
}

#excellent {
  position: fixed;
  left    : 30px;
  top     : 30px;
  width   : 380px;
}

#home {
  width         : 16px;
  margin-left   : 10px;
  vertical-align: middle;
  position      : fixed;
  right         : 64px;
  top           : 153px;
}

.ant-modal {
  width         : 355px !important;
  position      : fixed !important;
  right         : 59px;
  display       : flex;
  bottom        : 14px;
  flex-direction: column-reverse;

}

.ifram {
  border: 0;
  width : 100%;
  height: 100%;
}

#ifram {
  display  : none;
  position : fixed;
  z-index  : 9;
  left     : 50%;
  top      : 50%;
  transform: translate(-50%, -50%);
  width    : 60%;
  height   : 65%;
}

.i-header {
  width           : 100%;
  height          : 40px;
  position        : relative;
  background-color: #fff;
}

#ifram .close {
  position: absolute;
  right   : 10px;
  top     : 7px;
  width   : 25px;
}

#videoIframe {
  width : 100%;
  height: 100%;
  border: 0;
}

#videoUrl {
  position : fixed;
  z-index  : 9;
  left     : 50%;
  top      : 50%;
  transform: translate(-50%, -50%);
  width    : 60%;
  height   : 65%;
}

#videoUrl .close {
  position: absolute;
  right   : 10px;
  top     : 7px;
  width   : 25px;
}

.i-title {
  position : absolute;
  left     : 50%;
  color    : #000;
  font-size: 20px;
  top      : 50%;
  transform: translate(-50%, -50%);
}

.dialog {
  position : fixed;
  left     : 50%;
  top      : 50%;
  transform: translate(-50%, -50%);
}

.dialog .b1 {
  width            : 144px;
  background-size  : 100% 100%;
  background-repeat: no-repeat;
  position         : relative;
}

.b2 {
  /* background: url("../iocn/b_03.png"); */
}

.b3 {
  background: rgba(25, 28, 47, 0.3);
}

.dialog .b3 .header .d-title {
  justify-content: flex-start;
  line-height    : 40px;
  width          : 80%;
  margin         : 0 auto;
}

.dialog .b3 .header .d-title .key {
  font-size: 9.6px;
  display  : block;
  width    : 80px;
}

.line {
  width     : 100%;
  height    : 1.6px;
  background: #00ffff;
}

.dialog .b2 .header .d-title {
  justify-content: flex-start;
  line-height    : 40px;
  width          : 80%;
  margin         : 0 auto;
  border-bottom  : 1.6px solid #00ffff;
}

.dialog .b2 .header .d-title .key {
  font-size  : 9.6px;
  font-weight: 500;
  color      : #4ce4ff;
  display    : block;
  width      : 80px;
}

.d-title .val {
  font-size  : 9.6px;
  font-weight: 500;
  color      : #fff;
}

.dialog .content {
  padding: 8px 0;
}

.content .d-title {
  display        : flex;
  width          : 80%;
  margin         : 0 auto;
  padding        : 4px 0;
  justify-content: flex-start;
}

.key2 {
  font-size  : 9.6px;
  font-weight: 500;
  color      : #fff;
  display    : block;
  width      : 80px;
  text-align : left;
}

.content .d-title .key {
  font-size  : 9.6px;
  font-weight: 500;
  color      : #4ce4ff;
  display    : block;
  width      : 80px;
  text-align : left;
}

.content .d-title .val {
  font-size  : 9.6px;
  font-weight: 500;
  color      : #fff;
}

.d-title {
  margin-bottom: 0;
  margin-top   : 0;
  display      : flex;
}

.dialog .foot {
  display        : flex;
  width          : 80%;
  margin         : 0 auto;
  justify-content: center;
  padding-bottom : 16px;
}

.dialog .foot button {
  width        : 51.2px;
  line-height  : 16px;
  color        : #fff;
  border       : 0;
  border-radius: 2.4px;
  outline      : none;
  background   : #008fef;
}

[v-cloak] {
  display: none;
}

.leftBtn .item {
  display        : flex;
  justify-content: space-between;
  align-items    : center;
  margin-top     : 16px;
}

.leftBtn .item img {
  width: 30.4px;
}

.leftBtn .item img span {
  margin-left: 9.6px;
  font-size  : 12px;
  padding    : 0 16px;
  cursor     : pointer;
}

.leftBtn {
  position: absolute;
  left    : 8px;
  bottom  : 16px;
}

.l-title {
  font-size  : 12px;
  cursor     : pointer;
  color      : #fff;
  margin-left: 12px;
}

.leftBtnIndex {
  color          : #fff;
  /* background  : url("./iocn/zongxuanxiangtiao.png"); */
  font-size      : 12px;
  background-size: 100% 100%;
  cursor         : pointer;
}

.close {
  width   : 12px;
  right   : 8px;
  top     : 8px;
  position: absolute;
}

.floorlist {
  background-size    : 100%;
  /* background      : url("../iocn/zixuanxiangtiao.png"); */
  background-repeat  : no-repeat;
  background-position: -8px;
  color              : #36C9D1 !important;
  font-size          : 17px !important;
}

#rightBtn {
  width   : 800px;
  position: fixed;
  left    : 0%;
  right   : 0;
  margin  : 0 auto;
  bottom  : 3%;

}


.rightBtn .list {
  display        : flex;
  justify-content: center;
  align-items    : center;
  padding        : 8px 0;
  cursor         : pointer;
  color          : #fff;
}

.rightBtn {
  background     : url('../textures/bg.png');
  background-size: 100% 100%;
  /* height      : 400px; */
  overflow       : hidden;
  transform      : translate(0, 0);
  display        : flex;

  flex-direction   : row;
  align-items      : center;
  border-radius    : 2.4px;
  /* box-shadow    : 0rem 0rem 8px #3755da inset; */
}

.allweidianji {
  margin-top: 16px;
}

.rightBtn .top .rTitle {
  display        : flex;
  justify-content: center;
  color          : #fff;
  width          : 50px;
}

/* .label {
  /* cursor: pointer; */
padding-bottom: 15px;

}

*/ .labellabel {
  /* cursor: pointer; */
  padding-top: 10px;
}

.divdetail {
  display           : flex;
  justify-content   : space-between;
  width             : auto;

  font-size         : 10px;
  /* justify-content: space-between;
  width             : 90%;
  margin-left       : 5%; */
}

.rightBtn .top .rTitle .labelTitle {
  /* border-bottom: .8px solid #fff; */
  font-size  : 15px;
  line-height: 24px;
  cursor     : pointer;
}

.activelabel {
  /* border-bottom: .8px solid #fff; */
  font-size  : 15px;
  line-height: 24px;
  cursor     : pointer;
  color      : #36C9D1 !important;
}

.scroll_left {
  margin-left: 10px;
  cursor     : pointer;
}

.scroll_right {
  margin-left: 16px;
  cursor     : pointer;
}

.rightBtn .top {
  margin-left      : 10px;
  scroll-behavior  : smooth;
  width            : 90%;
  display          : flex;
  flex-direction   : row;
  min-width        : 74px;
  height           : 100%;
  /* background    : rgba(29, 35, 58, 0.2);
  border           : 1px solid rgba(55, 85, 218, 0.3); */
  font-size        : 14px;
  overflow         : auto;
  color            : #7d8394;
  border-radius    : 6.4px;
  box-sizing       : border-box;
}

.toplist {
  display        : flex;
  flex-direction : row;
  width          : 100%;
  justify-content: center
}

#edit {
  width    : 80%;
  height   : 80%;
  position : fixed;
  z-index  : 999999;
  transform: translate(-50%, -50%);
}

#hotspot {
  max-height: 70%;
  overflow  : auto;
  position  : fixed;
  left      : 50%;
  top       : 50%;
  transform : translate(-50%, -50%);
}

/* .ant-modal-root{
  width: 100%;  position: fixed;
  height: 100%;
} */
@media only screen and (max-width: 580px) {
  #rightBtn {
    width   : 92%;
    position: fixed;
    left    : 0%;
    right   : 0;
    margin  : 0 auto;
    bottom  : 1%;

  }

  .rightBtn .top {
    margin-left      : 0px;
    scroll-behavior  : smooth;
    width            : 90%;
    display          : flex;
    flex-direction   : row;
    min-width        : 74px;
    height           : 100%;
    /* background    : rgba(29, 35, 58, 0.2);
    border           : 1px solid rgba(55, 85, 218, 0.3); */
    font-size        : 13px;
    overflow         : auto;
    color            : #7d8394;
    border-radius    : 6.4px;
    box-sizing       : border-box;
  }

  .toplist {
    width: auto;
  }

  .rightBtn .list .content {
    display        : flex;
    align-items    : center;
    text-align     : center;
    margin-left    : 4px;
    /* font-size   : 15px; */
    /* color       : #fff; */
    justify-content: space-around;
    width          : 50px;
  }
}