(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-83a069f0"],{"56d8":function(e,t,o){"use strict";o("cdca")},cdca:function(e,t,o){},dd7b:function(e,t,o){"use strict";o.r(t);var i=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"login"},["formDisplayIsAvaliabled"==e.formDisplay||"lanxing121"==e.$route.query.lf?o("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules}},[o("h3",{staticClass:"title"},[e._v(e._s(e.project.title))]),o("el-form-item",{attrs:{prop:"username"}},[o("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"请输入账号"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}},[o("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"user"},slot:"prefix"})],1)],1),o("el-form-item",{attrs:{prop:"password"}},[o("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"请输入密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[o("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),o("el-form-item",{attrs:{prop:"code"}},[o("el-input",{staticStyle:{width:"63%"},attrs:{"auto-complete":"off",placeholder:"验证码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.code,callback:function(t){e.$set(e.loginForm,"code",t)},expression:"loginForm.code"}},[o("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"validCode"},slot:"prefix"})],1),o("div",{staticClass:"login-code"},[o("img",{staticClass:"login-code-img",attrs:{src:e.codeUrl},on:{click:e.getCode}})])],1),o("el-checkbox",{staticStyle:{margin:"0px 0px 25px 0px"},model:{value:e.loginForm.rememberMe,callback:function(t){e.$set(e.loginForm,"rememberMe",t)},expression:"loginForm.rememberMe"}},[e._v("记住密码")]),o("el-popover",{staticClass:"pull-right",attrs:{placement:"top-end",width:"200",trigger:"click",content:"请联系管理员重置密码。"}},[o("span",{staticStyle:{color:"#2294FE","text-decoration":"none","font-size":"14px",cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"},[e._v("忘记密码")])]),o("el-form-item",{staticStyle:{width:"100%"}},[o("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,size:"medium",type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e.loading?o("span",[e._v("登 录 中...")]):o("span",[e._v("登 录")])])],1)],1):e._e(),e._m(0)],1)},n=[function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"el-login-footer"},[o("span",[e._v("Copyright © 2018-2024 lanxing.tech All Rights Reserved.")])])}],r=(o("4de4"),o("14d9"),o("d3b7"),o("ac1f"),o("5319"),o("0643"),o("2382"),o("21f2")),s=o("7ded"),a=o("c0c7"),c=o("00b2"),l=o("a78e"),u=o.n(l),d=o("4360"),g=(o("5f87"),{name:"Login",data:function(){return{formDisplay:!1,formDisplaySettings:!1,project:this.gf.projectInfo(),codeUrl:"",cookiePassword:"",loginForm:{username:"",password:"",rememberMe:!1,code:"",uuid:""},loginRules:{username:[{required:!0,trigger:"blur",message:"用户名不能为空"}],password:[{required:!0,trigger:"blur",message:"密码不能为空"},{validator:this.gf.validatePass,trigger:"blur"}],code:[{required:!0,trigger:"change",message:"验证码不能为空"}]},loading:!1,redirect:void 0,checkLogin:null,urlAutoLogin:this.$route.query.autoLogin||"",urlUsername:this.$route.query.username||"",urlPassword:this.$route.query.password||"",autoLogin:0,loginUsername:"",loginPassword:""}},watch:{},created:function(){var e=this;this.updatePublicKey(),this.getDicts("base_configs").then((function(t){t&&t.data&&t.data.length>0&&t.data.filter((function(t){"ssologin"==t.dictLabel?e.ssologin=t.dictValue:"ssoRedirect"==t.dictLabel?e.ssoRedirect=t.dictValue:"loginRedirect"==t.dictLabel?e.redirect=t.dictValue:"formDisplaySettings"==t.dictLabel?e.formDisplaySettings=t.dictValue:"autoLogin"==t.dictLabel?e.autoLogin=t.dictValue:"loginUsername"==t.dictLabel?e.loginUsername=t.dictValue:"loginPassword"==t.dictLabel&&(e.loginPassword=t.dictValue)}))})).then((function(){e.redirectLoginPage()})).then((function(){e.doAutoLogin()}))},destroyed:function(){clearInterval(this.checkLogin),this.checkLogin=null},methods:{redirectLoginPage:function(){this.ssologin&&"Y"==this.ssologin&&this.ssoRedirect&&""!=this.ssoRedirect&&"lanxing121"!=this.$route.query.lf?window.location.href=this.ssoRedirect:this.formDisplay=this.formDisplaySettings,console.log(this.formDisplay)},updatePublicKey:function(){var e=this;d["a"].dispatch("getPublicKey").then((function(e){})).then((function(){e.getCode(),e.getCookie(),Object(s["h"])().then((function(t){var o=t.data;console.log(o),1==o.autoLoginCheck&&e.checkAutoLogin()}))}))},doAutoLogin:function(){1==this.autoLogin?this.handleAutoLogin(this.loginUsername,this.loginPassword):1==this.urlAutoLogin&&this.handleAutoLogin(this.urlUsername,this.urlPassword)},handleAutoLogin:function(e,t){var o=this;this.loginForm={username:e,password:t,rememberMe:!1,code:"123",uuid:"123"},this.$store.dispatch("ApiLogin",this.loginForm).then((function(){return Object(a["g"])()})).then((function(){return Object(a["j"])()})).then((function(){o.$router.push({path:o.redirect||"/"}).catch((function(){})),console.log("autoLogin................finish"),window.parent.postMessage({type:"autoLogin",status:1,message:{name:"finish"}},"*")})).catch((function(){o.loading=!1,o.getCode(),o.updatePublicKey(),d["a"].dispatch("FedLogOut")}))},getCode:function(){var e=this;Object(s["c"])().then((function(t){e.codeUrl="data:image/gif;base64,"+t.img,e.loginForm.uuid=t.uuid}))},getCookie:function(){var e=u.a.get("username"),t=u.a.get("password"),o=u.a.get("rememberMe");this.loginForm={username:void 0===e?this.loginForm.username:e,password:void 0===t?this.loginForm.password:Object(r["a"])(t),rememberMe:void 0!==o&&Boolean(o)}},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){t&&(e.loading=!0,e.loginForm.rememberMe?(u.a.set("username",e.loginForm.username,{expires:3e3}),u.a.set("password",Object(r["b"])(e.loginForm.password),{expires:3e3}),u.a.set("rememberMe",e.loginForm.rememberMe,{expires:3e3})):(u.a.remove("username"),u.a.remove("password"),u.a.remove("rememberMe")),e.$store.dispatch("Login",e.loginForm).then((function(){return Object(a["g"])()})).then((function(){return Object(a["j"])()})).then((function(){e.$router.push({path:e.redirect||"/"}).catch((function(){}))})).catch((function(){e.loading=!1,e.getCode(),e.updatePublicKey(),d["a"].dispatch("FedLogOut")})))}))},handleApiLogin:function(){var e=this;this.$store.dispatch("ApiLogin",this.loginForm).then((function(){return Object(a["g"])()})).then((function(){return Object(a["j"])()})).then((function(){e.$router.push({path:e.redirect||"/"}).catch((function(){}))})).catch((function(){e.loading=!1,e.getCode(),e.updatePublicKey(),d["a"].dispatch("FedLogOut")}))},checkAutoLogin:function(){var e=this;this.checkLogin=setInterval((function(){clearInterval(e.checkLogin),e.checkLogin=null,Object(c["a"])().then((function(t){t.data&&1==t.data.logined?(e.loginForm={username:t.data.user,password:t.data.pwd,rememberMe:!1,code:"123",uuid:"123"},e.handleApiLogin()):e.checkAutoLogin()})).catch((function(t){e.checkAutoLogin()}))}),1e3)},redirectHomePage:function(){this.redirect&&""!=this.redirect?this.$router.replace({path:"/"+this.redirect}):this.loading=!1}}}),h=g,m=(o("56d8"),o("2877")),p=Object(m["a"])(h,i,n,!1,null,null,null);t["default"]=p.exports}}]);