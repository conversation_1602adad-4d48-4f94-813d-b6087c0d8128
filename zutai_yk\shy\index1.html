<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>three sdk</title>

    <style>
        html,
        body {
            padding: 0;
            margin: 0;
            width: 100%;
            height: 100%;
            background: transparent;
        }

        .container {
            width: 100%;
            height: 100%;
        }

        .btns-ctn {
            position: fixed;
            left: 10px;
            top: 20px;
            z-index: 99;
        }

        #labelEx1 {
            background: url(./textures/label-bg11111.png) no-repeat center center;
            background-size: 100% 100%;
            width: 120px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            color: #fff;
            cursor: pointer;
        }

        #labelEx1:hover {
            background-color: #1368fc;
        }

        #labelEx2 .label-inner {
            background: url(./textures/label-bg222222.png) no-repeat center center;
            background-size: 100% 100%;
            width: 358px;
            height: 150px;
            line-height: 60px;
            text-align: center;
            cursor: pointer;
            transform: translate(calc(50% - 30px), -50%);

            display: flex;
            color: #fff;
            padding-left: 90px;
        }

        /* 加载动画样式 */
        .loading_page {
            position: fixed;
            top: 0;
            width: 100%;
            height: 100%;
            z-index: 99999;
            display: flex;
            flex-direction: column;
            /* Stack items vertically */
            justify-content: center;
            /* Center items vertically */
            align-items: center;
            /* Center items horizontally */

            background-color: #13294D;
            margin: 0;
        }

        .inner-box {
            margin-left: 32.5px;
            position: relative;
            width: 36px;
            height: 36px;
            transform-style: preserve-3d;
            transform-origin: center;
            animation: 3s ctn infinite;
            transform-origin: 0 0;
            transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
        }

        .inner {
            position: absolute;
            width: 36px;
            height: 36px;
            text-align: center;
            line-height: 36px;
            color: #fff;
            border-radius: 6px;
            background: rgba(7, 127, 240, 0.1);
            border: 2px solid rgba(19, 108, 241, 0.986);
            transform-origin: center;
        }

        .inner:nth-child(1) {
            transform: rotateX(90deg) translateZ(18px);
            animation: 3s top infinite;
        }

        .inner:nth-child(2) {
            transform: rotateX(-90deg) translateZ(18px);
            animation: 3s bottom infinite;
        }

        .inner:nth-child(3) {
            transform: rotateY(90deg) translateZ(18px);
            animation: 3s left infinite;
        }

        .inner:nth-child(4) {
            transform: rotateY(-90deg) translateZ(18px);
            animation: 3s right infinite;
        }

        .inner:nth-child(5) {
            transform: translateZ(18px);
            animation: 3s front infinite;
        }

        .inner:nth-child(6) {
            transform: rotateY(180deg) translateZ(18px);
            animation: 3s back infinite;
        }

        @keyframes ctn {
            from {
                transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
            }

            50% {
                transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
            }

            to {
                transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
            }
        }

        @keyframes top {
            from {
                transform: rotateX(90deg) translateZ(18px);
            }

            50% {
                transform: rotateX(90deg) translateZ(18px);
            }

            75% {
                transform: rotateX(90deg) translateZ(36px);
            }

            to {
                transform: rotateX(90deg) translateZ(18px);
            }
        }

        @keyframes bottom {
            from {
                transform: rotateX(-90deg) translateZ(18px);
            }

            50% {
                transform: rotateX(-90deg) translateZ(18px);
            }

            75% {
                transform: rotateX(-90deg) translateZ(36px);
            }

            to {
                transform: rotateX(-90deg) translateZ(18px);
            }
        }

        @keyframes left {
            from {
                transform: rotateY(90deg) translateZ(18px);
            }

            50% {
                transform: rotateY(90deg) translateZ(18px);
            }

            75% {
                transform: rotateY(90deg) translateZ(36px);
            }

            to {
                transform: rotateY(90deg) translateZ(18px);
            }
        }

        @keyframes right {
            from {
                transform: rotateY(-90deg) translateZ(18px);
            }

            50% {
                transform: rotateY(-90deg) translateZ(18px);
            }

            75% {
                transform: rotateY(-90deg) translateZ(36px);
            }

            to {
                transform: rotateY(-90deg) translateZ(18px);
            }
        }

        @keyframes front {
            from {
                transform: translateZ(18px);
            }

            50% {
                transform: translateZ(18px);
            }

            75% {
                transform: translateZ(36px);
            }

            to {
                transform: translateZ(18px);
            }
        }

        @keyframes back {
            from {
                transform: rotateY(180deg) translateZ(18px);
            }

            50% {
                transform: rotateY(180deg) translateZ(18px);
            }

            75% {
                transform: rotateY(180deg) translateZ(36px);
            }

            to {
                transform: rotateY(180deg) translateZ(18px);
            }
        }

        .loading-text {
            z-index: 9999;
            color: #fff;
            /* Text color */
            margin-top: 25px;
            /* Space between the cube and text */
            font-size: 16px;
            /* Text size */
            letter-spacing: 1px;
            /* Letter spacing */
            text-align: center;
        }
    </style>
</head>

<body>
    <!-- 加载动画 -->

    <div id="loading-page" class="loading_page">
        <div class="inner-box">
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
            <div class="inner"></div>
        </div>
        <div class="loading-text">正在加载中,请耐心等候</div>
        <!-- 添加文本 -->
    </div>

    <div class="label-ctn" style="display: none;">
        <div id="labelEx1">
            3号电梯口
        </div>

        <div id="labelEx2">
            <div class="label-inner">
                <div class="css3dHomeLabelTitle">空压系统</div>
                <div class="css3dHomeLabelStatus">暂无异常</div>
                <div class="css3dHomeLabelDetailText">当前功率</div>
                <div class="css3dHomeLabelDetailMh">
                    :
                </div>
                <div class="css3dHomeLabelDetail">19.2</div>
            </div>
        </div>
    </div>

    <div class="container" id="container"></div>
    <div class="btns-ctn" style="display: none;">
        <button class="btn" onclick="startRoam()">开始漫游</button>
        <button class="btn" onclick="stopRoam()">结束漫游</button>
        <button class="btn" onclick="turnOff()">关灯</button>
        <button class="btn" onclick="turnOn()">开灯</button>
    </div>
    <script src="./build/sdk.js"></script>

    <script src="./js/msg-execute3d1.js"></script>
    <script src="./js/lable.js"></script>
    <script>


        const view = new app3d({
            dom: 'container',
            dracoPath: "./build/draco/gltf/"
        })

        const config = {
            lightConfig: [
                {
                    type: "AmbientLight",
                    color: "#ffffff",
                    intensity: 0.3
                },
                {
                    intensity: 5,
                    type: "DirectionalLight",
                    color: "#ffffff",
                    position: [1, 3, 2],
                }
            ],
            dracoPath: "./build/draco/gltf/",
            hdrPath: "./textures/equirectangular/venice_sunset_1k.hdr",
            // hdrPath: "./textures/equirectangular/autoshop_01_1k.hdr",
            camera: {

                position: [0.6512120286276961, 81.65003251655797, 51.23290630810624],
                target: [0.4738069472962142, -1.8250583556109514, 1.4166650678797221],
                near: 0.01, // 近截面
                far: 3000,
            },
            css2d: {
                use: true,
            },
            css3d: {
                use: true,
            },
            useEffectComposer: true,

            models: [{

                path: "./models/shy(2).glb",
                position: [0, -1.34, 0],
                name: "readyRoom",
                scale: [1, 1, 1],
                rotation: [0, 0, 0],
                id: 1,
                visible: true,
                isGlow: true, // 是否使用辉光
                glowNames: ["lamp1"], // 辉光的部位
            }]
        }

        // 初始话场景
        view.init(config, () => {
            setTimeout(() => {
                // 加载空调设备
                // addAirConditioner();
                // // 加载灯
                // addLight();
                // 添加科技感背景
                // addTechBackground();
            }, 1.5);

            view.toggleShowStyle(true);
            view.setBloomParams({
                threshold: 0.0,
                strength: 0.8,
                radius: .8,
            });
            // view.renderer.toneMappingExposure = 1.2;
            // view.renderer.toneMappingExposure = .08;


            view.setGroundMirrorVisible1(false);
        });




        view.setCallBack({
            mouseclick: mouseclick,
            progress: progress,
            onSiteInspectionCallback: onSiteInspectionCallback
        })
        let poijson = {
            "models": [
                {
                    "id": "wifi",
                    "path": "./models/wifi2.glb"
                },
            ],
            "devices": [{
                "json": {
                    "modelId": "wifi",
                    "position": [
                        14.065424209822538,
                        5.733508446123359,
                        10.250391354600747
                    ],
                    "ue_position": [
                        1406.5424209822538,
                        1025.0391354600747,
                        573.3508446123359
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 543,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 548661,
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "deviceId": "10.100.23.103",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -11.507913635383323,
                        5.733508446123359,
                        9.702887149179961
                    ],
                    "ue_position": [
                        -1150.7913635383322,
                        970.2887149179961,
                        573.3508446123359
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 6638,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 548662,
                "deviceId": "10.100.23.169",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        4.5821796835028294,
                        5.733508446123359,
                        15.263049696599733
                    ],
                    "ue_position": [
                        458.21796835028294,
                        1526.3049696599733,
                        573.3508446123359
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 4421,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549403,
                "deviceId": "10.100.23.217",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        8.473486862103737,
                        5.733508446123359,
                        -1.0009548815029987
                    ],
                    "ue_position": [
                        847.3486862103737,
                        -100.09548815029987,
                        573.3508446123359
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 973,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549404,
                "deviceId": "10.100.23.212",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -3.931281264261443,
                        5.877527622568628,
                        -1.5991744809222146
                    ],
                    "ue_position": [
                        -393.1281264261443,
                        -159.91744809222146,
                        587.7527622568629
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 9550,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549405,
                "deviceId": "10.100.23.193",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        26.211910069062093,
                        5.733508446123359,
                        -4.915686994624181
                    ],
                    "ue_position": [
                        2621.191006906209,
                        -491.5686994624181,
                        573.3508446123359
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 4518,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549406,
                "deviceId": "10.100.23.185",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -4.3779073778165465,
                        5.856921189274573,
                        -17.397914238031117
                    ],
                    "ue_position": [
                        -437.7907377816546,
                        -1739.7914238031117,
                        585.6921189274573
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 3822,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549407,
                "deviceId": "10.100.23.205",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        12.421763401071338,
                        5.733508446123359,
                        -17.95508017261695
                    ],
                    "ue_position": [
                        1242.176340107134,
                        -1795.508017261695,
                        573.3508446123359
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 4647,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549408,
                "deviceId": "10.100.23.140",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -18.722678130339613,
                        5.8307458856764285,
                        -2.344042282462693
                    ],
                    "ue_position": [
                        -1872.2678130339614,
                        -234.40422824626933,
                        583.0745885676429
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 3534,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549409,
                "deviceId": "10.100.23.161",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "1F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -46.17163050898315,
                        11.015069742867777,
                        -11.34155770242938
                    ],
                    "ue_position": [
                        -4617.163050898315,
                        -1134.155770242938,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 308,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549410,
                "deviceId": "10.100.23.114",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "192.168.0.0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -46.24187844572608,
                        11.015069742867777,
                        8.89947669013861
                    ],
                    "ue_position": [
                        -4624.187844572608,
                        889.947669013861,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 4841,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549411,
                "deviceId": "10.100.23.178",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -21.656099154655895,
                        11.015069742867777,
                        -4.170965402535935
                    ],
                    "ue_position": [
                        -2165.6099154655894,
                        -417.0965402535935,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 1904,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549412,
                "deviceId": "10.100.23.192",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -34.07441789513499,
                        11.015069742867777,
                        -0.05327677782673579
                    ],
                    "ue_position": [
                        -3407.4417895134993,
                        -5.327677782673579,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 2915,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549413,
                "deviceId": "10.100.23.216",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -28.675517917419132,
                        11.015069742867777,
                        6.8474471848932055
                    ],
                    "ue_position": [
                        -2867.5517917419133,
                        684.7447184893206,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 2711,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549414,
                "deviceId": "10.100.23.174",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -12.428808996522426,
                        11.015069742867777,
                        4.8423470896025655
                    ],
                    "ue_position": [
                        -1242.8808996522425,
                        484.23470896025657,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 9184,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549415,
                "deviceId": "10.100.23.110",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -17.92535307704338,
                        11.015069742867777,
                        18.17829871223022
                    ],
                    "ue_position": [
                        -1792.5353077043378,
                        1817.8298712230219,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 6978,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549416,
                "deviceId": "10.100.23.120",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -8.864744451743839,
                        11.015069742867777,
                        -3.2113345697730287
                    ],
                    "ue_position": [
                        -886.4744451743838,
                        -321.13345697730284,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 5248,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549417,
                "deviceId": "10.100.23.236",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        -5.950053014955557,
                        11.015069742867777,
                        -11.702169245580986
                    ],
                    "ue_position": [
                        -595.0053014955557,
                        -1170.2169245580985,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 2844,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549418,
                "deviceId": "10.100.23.170",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        9.91518861160511,
                        11.015069742867777,
                        -20.038712995400964
                    ],
                    "ue_position": [
                        991.518861160511,
                        -2003.8712995400963,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 8932,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549419,
                "deviceId": "10.100.23.201",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        10.038262551809005,
                        11.015069742867777,
                        -8.511016246372739
                    ],
                    "ue_position": [
                        1003.8262551809005,
                        -851.1016246372739,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 3310,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549420,
                "deviceId": "10.100.23.168",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        27.22385067056205,
                        11.015069742867777,
                        -10.535847686254318
                    ],
                    "ue_position": [
                        2722.385067056205,
                        -1053.5847686254317,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 5721,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549421,
                "deviceId": "10.100.23.179",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        19.036024699864953,
                        11.015069742867777,
                        4.713460090141677
                    ],
                    "ue_position": [
                        1903.6024699864952,
                        471.3460090141677,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 2802,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549422,
                "deviceId": "10.100.23.112",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        1.697001814075847,
                        11.015069742867777,
                        13.060684077802478
                    ],
                    "ue_position": [
                        169.7001814075847,
                        1306.0684077802478,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 7334,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549423,
                "deviceId": "10.100.23.190",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "0",
                "floorId": "2F",
                "type": 1
            },
            {
                "json": {
                    "modelId": "wifi",
                    "position": [
                        33.53286633843989,
                        11.015069742867777,
                        15.189646919569267
                    ],
                    "ue_position": [
                        3353.286633843989,
                        1518.9646919569266,
                        1101.5069742867777
                    ],
                    "scale": [
                        1,
                        1,
                        1
                    ],
                    "rotation": [
                        0,
                        0,
                        0
                    ],
                    "id": 9592,
                    "floorNum": 2,
                    "name": "华为路由器"
                },
                "id": 549424,
                "deviceId": "10.100.23.153",
                "title": "华为路由器",
                "name": "华为路由器",
                "parkId": "192.168.10.1",
                "floorId": "2F",
                "type": 1
            }]
        }
        function progress(load, isload) {
            if (isload) {
                console.log("加载完成");

                // 隐藏加载动画
                document.getElementById("loading-page").style.display = "none";
                view.addOutlineByName("立方体004", "#4086F2");
                // view.setMaterialLinearGradient(["立方体004"]);
                view.addOutlineByName("立方体", "#4086F2");
                // view.addOutlineByName("平面", "#4086F2");
                // view.addOutlineByName("wall001", "#4086F2");
                // view.setMaterialLinearGradient(["立方体","立方体004"]);

                // view.setMaterialLinearGradient(["立方体004"]);

                // 隐藏屋顶
                //  view.nameVisible("F2", false);

                view.LoadJsonDevice.setScene(poijson, (obj) => {
                    // view.LoadJsonDevice.setAnimateByModelIds([547641], false);
                })
            }
        }
        function isPureNumber(str) {
            return /^\d+$/.test(str);
        }
        function mouseclick(obj) {
            const { model, point, center } = obj;
            console.log(obj, '点击打印的信息');
            view.removeObjByNames(['wave1']);
            if (isPureNumber(model.name)) {
                console.log(comdata,model.modelId, '点击打印的信息');
                console.log(comdata.find(item => item.id == model.modelId), '点击打印的信息');
                view.setOutlineModel([model]);
                let activedata = comdata.find(item => item.id == model.modelId)
                createlable(activedata)
                let radarData = [{
                    "id": "wave1",
                    "count": 3,
                    "radius": 5,
                    "position": center,
                    "color": "#15D36A",
                    "opacity": 0.5,
                    "speed": 0.2
                }]
                // view.addWave(radarData);
            } else {
                view.removeObjByNames([currentLabelId])
                view.removeObjByNames(['wave1']);
            }

            console.log(obj.point.toArray());
        }
        
        function addAirQualityData() {
            let position = { x: -5.691270549245441, y: 0.8205613250310237, z: 7.2690083367943465 }; //获取单个模型的顶部中心点
            // let earthMassDiv = document.createElement("div");
            // img = document.createElement("img");
            // img.src = "./images/Frame.png";
            // img.style.width = "100px";
            // earthMassDiv.appendChild(img);
            // earthMassDiv.className = "label";
            // 空气质量数据数组
            const airQualityData = [
                { label: "温度", value: "21", unit: "°C" },
                { label: "湿度", value: "21", unit: "%" },
                { label: "CO2", value: "500", unit: "ppm" },
                { label: "PM2.5", value: "32", unit: "mg/m³" },
                { label: "HCHO", value: "0.09", unit: "mg/m³" },
                { label: "TVOC", value: "0.16", unit: "mg/m³" }
            ];

            // 创建一个包含背景图的 div 容器
            let airQualityDiv = document.createElement("div");
            airQualityDiv.className = "air-quality-container";
            airQualityDiv.style.position = "relative"; // 相对定位，以便数据覆盖在背景图上
            airQualityDiv.style.width = "300px"; // 容器宽度（可根据背景图调整）

            airQualityDiv.style.height = "100px"; // 容器高度（可根据背景图调整）
            airQualityDiv.style.backgroundImage = "url('./images/Frame.png')"; // 背景图路径
            airQualityDiv.style.backgroundSize = "cover"; // 背景图覆盖整个容器
            airQualityDiv.style.backgroundPosition = "center"; // 背景图居中

            // 创建数据容器
            let dataDiv = document.createElement("div");
            dataDiv.style.position = "absolute"; // 绝对定位，覆盖在背景图上
            dataDiv.style.top = "6px"; // 从顶部开始
            dataDiv.style.left = "49px"; // 从左侧开始
            dataDiv.style.width = "85%"; // 占满容器宽度
            dataDiv.style.height = "85%"; // 占满容器高度
            dataDiv.style.display = "grid"; // 使用 grid 布局
            dataDiv.style.gridTemplateColumns = "1fr 1fr"; // 两列
            dataDiv.style.gap = "5px"; // 网格间距
            dataDiv.style.padding = "5px"; // 内边距
            dataDiv.style.fontFamily = "Arial, sans-serif"; // 字体

            // 使用循环生成数据项
            for (let i = 0; i < airQualityData.length; i++) {
                let dataItemDiv = document.createElement("div");
                dataItemDiv.style.display = "flex";
                dataItemDiv.style.alignItems = "center";

                // 创建标签元素
                let labelElement = document.createElement("span");
                labelElement.textContent = airQualityData[i].label;
                labelElement.style.color = "#D5DFED"; // 标签使用浅蓝色
                labelElement.style.fontSize = "12px";
                labelElement.style.marginRight = "12px";

                // 创建数值元素
                let valueElement = document.createElement("span");
                valueElement.textContent = airQualityData[i].value;
                valueElement.style.color = "#ffffff"; // 数值使用白色
                valueElement.style.fontSize = "14px";


                // 创建单位元素
                let unitElement = document.createElement("span");
                unitElement.textContent = airQualityData[i].unit;
                unitElement.style.color = "#D5DFED"; // 单位使用灰色
                unitElement.style.fontSize = "12px";
                unitElement.style.marginLeft = "3px";

                // 将三个元素添加到数据项中
                dataItemDiv.appendChild(labelElement);
                dataItemDiv.appendChild(valueElement);
                dataItemDiv.appendChild(unitElement);

                dataDiv.appendChild(dataItemDiv);
            }

            // 将数据容器添加到主容器
            airQualityDiv.appendChild(dataDiv);

            // 将整个 airQualityDiv 添加到页面中（假设添加到 body）
            // document.body.appendChild(airQualityDiv);
            view.add3dSprite(airQualityDiv, {
                scale: 0.01, //标签尺寸
                position, //标签位置
                name: "D3",
            });

        }
        // 巡检路线数据
        const onSiteInspectionData = [
            // { // 第一段路线
            //     positions: [ // 路线点位
            //         [2.573756697671823, 0, 12.037283990187387],
            //         [2.5075716334768505, 0, 5.829250176976521]
            //     ],
            //     checkObj: { // 巡检的对象
            //         position: [3.9187071545122527, 1.0095932498704998, 5.9108942446632],
            //         id: "",
            //         name: "",
            //     },
            //     totalTime: 1, // 漫游时间
            //     delay: 1, // 停顿时间
            // },
            { // 第二段路线
                positions: [ // 路线点位
                    [2.573756697671823, 0, 12.037283990187387],
                    [2.5075716334768505, 0, 5.829250176976521],
                    [
                        2.445338625740245,
                        0,
                        1.397617079497992
                    ]
                ],
                checkObj: {
                    position: [
                        2.330452390824419, 0.9423001362791258, 0.7690112981126882
                    ],
                    id: "light1",
                    name: "灯",
                },
                totalTime: 5, // 漫游时间
                delay: 1, // 停顿时间

            },
            { // 第三段路线
                positions: [ // 路线点位
                    [
                        2.445338625740245,
                        0,
                        1.397617079497992
                    ],
                    [2.8596099903040275, 0, -6.076886775455652],
                    [-3.7475358811340858, 0, -7.315830354412762],
                    // [2.22381693429758e-13, 0, 0],
                    // [-3.5121438692957327, 0, -4.8739816529802615],
                    [-1.4485026306881679, 0, 0.5441035217501339],
                    [0.8792542400234312, -0.1387484940502251, 5.992017608163784]
                ],
                checkObj: {
                    position: [
                        -5.069640001822959, 0.880404884856482, 7.320091389189232
                    ],
                    id: "Cube105",
                    name: "传感器",
                },
                totalTime: 10, // 漫游时间
                delay: 5, // 停顿时间
            }
        ];

        function startRoam() {
            view.onSiteInspection({
                data: onSiteInspectionData,
                type: "end"
            });
            // 显示屋顶
            view.nameVisible("ceiling", true);
            setTimeout(() => {
                view.resetLayer();
                view.animateCamera(
                    {
                        "x": -12.503554845873484,
                        "y": 18.901970314827643,
                        "z": -0.5912673122797542
                    },
                    {
                        "x": -0.8566964145413888,
                        "y": -0.6141021588834138,
                        "z": -0.6077478142687041
                    },
                    1000 //飞入动画的时间
                );
                view.nameVisible("ceiling", false);
            }, 28000);
        }

        function stopRoam() {
            view.stopRoam();

            view.resetLayer();
            view.animateCamera(
                {
                    "x": -12.503554845873484,
                    "y": 18.901970314827643,
                    "z": -0.5912673122797542
                },
                {
                    "x": -0.8566964145413888,
                    "y": -0.6141021588834138,
                    "z": -0.6077478142687041
                },
                1000 //飞入动画的时间
            );
            view.nameVisible("ceiling", false);
        }

        // 巡检的回调
        // {
        //      "data": {
        //         "position": [
        //             3.9187071545122527,
        //             1.0095932498704998,
        //             5.9108942446632
        //         ],
        //         "id": "bookshelf1",
        //         "name": "书架1"
        //     },
        //     "type": "checkObj"
        // }
        // type: end 结束巡检
        // type: checkObj 检测到巡检的对象, 返回对应配置里面的数据， 用于添加弹框等操作

        function onSiteInspectionCallback(data) {
            console.log("---data--", data);
            if (data.data.name == "传感器") {
                console.log("ddd");
                addAirQualityData();
            }
        }


        // 添加空调设备
        function addAirConditioner() {
            const airConditionerData = [
                {
                    position: [0, 2.5, 5],
                },
                {
                    position: [0, 2.5, 0],
                },
                {
                    position: [0, 2.5, -5],
                },
            ];

            airConditionerData.forEach(item => {
                view.addModel({
                    path: "./models/ksfjpg.glb",
                    position: item.position,
                    name: "shebei",
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0],
                    id: 1,
                    visible: true,
                    isGlow: true, // 是否使用辉光
                    // glowNames: ["d3f1_ksfjpg19"], // 辉光的部位
                });

            });

            view.addAirConditioner(airConditionerData, "green");

        }


        // 关灯
        function turnOff() {
            view.toggleNight(true, .12);
        }

        // 开灯
        function turnOn() {

            view.toggleNight(false, .71);
        }



        function addLight() {
            const lightData = [
                {
                    position: [2.5, 2.5, 5],
                },
                {
                    position: [2.5, 2.5, 0],
                },
                {
                    position: [2.5, 2.5, -5],
                },

                {
                    position: [-2.5, 2.5, 5],
                },
                {
                    position: [-2.5, 2.5, 0],
                },
                {
                    position: [-2.5, 2.5, -5],
                },
            ];

            lightData.forEach((item, index) => {
                view.addModel({
                    path: "./models/deng.glb",
                    position: item.position,
                    name: `light${index}`,
                    scale: [1, 1, 1],
                    rotation: [0, 0, 0],
                    id: 1,
                    visible: true,
                    isGlow: true, // 是否使用辉光
                    glowNames: [`light${index}`], // 辉光的部位
                });

            });
        }

        // 修改addTechBackground函数来增强视觉效果
        function addTechBackground() {
            // 清除旧的背景
            const oldBackground = view.scene.getObjectByName("techBackground");
            if (oldBackground) view.scene.remove(oldBackground);

            // 创建背景容器
            const techBackgroundContainer = new THREE.Object3D();
            techBackgroundContainer.name = "techBackground";
            view.scene.add(techBackgroundContainer);

            // 创建增强的动态网格 - 更淡的颜色
            createEnhancedFlowingGrid(techBackgroundContainer);

            // 添加发光粒子效果 - 更淡的颜色
            addGlowingParticles(techBackgroundContainer);

            // 添加全息投影扫描效果 (新增)
            // addHolographicScanEffect(techBackgroundContainer);

            // 添加全息数据流动效果 (新增)
            // addDataFlowEffect(techBackgroundContainer);

            // 设置动画循环
            setupAnimationLoop(techBackgroundContainer);
        }

        // 优化流动网格 - 更淡的颜色和更强的动态效果
        function createEnhancedFlowingGrid(parent) {
            // 创建地面网格
            const gridSize = 120;
            const resolution = 60;
            const geometry = new THREE.PlaneGeometry(gridSize, gridSize, resolution, resolution);

            // 网格材质 - 使用更淡的颜色
            const material = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0.0 },
                    color1: { value: new THREE.Color(0x478AE2) }, // 淡蓝色
                    color2: { value: new THREE.Color(0x478AE2) }  // 粉蓝色
                },
                vertexShader: `
                    uniform float time;
                    varying vec2 vUv;
                    varying float vElevation;
                    
                    // 扰动函数，创造更自然的波动
                    float turbulence(vec2 p) {
                        float t = -0.5;
                        for (float f = 1.0; f <= 10.0; f++) {
                            float power = pow(2.0, f);
                            t += abs(sin(power * p.x * 0.05 + time) + sin(power * p.y * 0.05 + time)) / power;
                        }
                        return t;
                    }
                    
                    void main() {
                        vUv = uv;
                        
                        // 复杂的波浪效果
                        float freq = 0.05;
                        float elevation = sin(position.x * freq + time * 0.4) * 
                                         sin(position.y * freq + time * 0.4) * 5.0;
                        
                        // 添加多层波动，增加复杂度
                        elevation += sin(position.x * 0.1 + time * 0.7) * 
                                    cos(position.y * 0.1 - time * 0.5) * 2.0;
                            
                            // 添加湍流扰动
                            elevation += turbulence(vec2(position.x, position.y)) * 1.5;
                         
                        vElevation = elevation;
                        
                        // 应用高度变化
                        vec3 pos = position;
                        pos.z = elevation;
                        
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform vec3 color1;
                    uniform vec3 color2;
                    uniform float time;
                    
                    varying vec2 vUv;
                    varying float vElevation;
                    
                    void main() {
                        // 网格线 - 更细致的网格
                        float gridX = step(0.98, mod(vUv.x * 60.0, 1.0));
                        float gridY = step(0.98, mod(vUv.y * 60.0, 1.0));
                        float grid = gridX + gridY;
                        
                        // 添加扫描线效果
                        float scanLine = step(0.98, mod(vUv.y * 100.0 + time * 10.0, 1.0)) * 0.3;
                        
                        // 颜色渐变 - 使用更淡的颜色
                        vec3 color = mix(color1, color2, vUv.y + vElevation * 0.15);
                        
                        // 添加动态闪烁和脉冲
                        float pulse = sin(time * 2.0) * 0.5 + 0.5;
                        float flash = sin(time * 3.0 + vUv.x * 10.0 + vUv.y * 10.0) * 0.5 + 0.5;
                        
                        // 全息投影特有的失真效果
                        float distortion = sin(vUv.y * 100.0 + time * 5.0) * 0.03;
                        color += vec3(0.1, 0.2, 0.3) * distortion;
                        
                        // 应用网格线，扫描线和闪烁
                        color += grid * 0.3 * vec3(0.5, 0.8, 1.0);
                        color += scanLine * vec3(0.5, 0.8, 1.0);
                        color *= 0.7 + flash * 0.3; // 降低整体亮度，增加闪烁
                        
                        // 调整透明度 - 更透明的效果
                        float alpha = (grid * 0.4 + 0.03) * (0.7 + pulse * 0.3);
                        
                        gl_FragColor = vec4(color, alpha);
                    }
                `,
                transparent: true,
                wireframe: false,
                side: THREE.DoubleSide,
                blending: THREE.AdditiveBlending,
                depthWrite: false
            });

            // 创建网格并添加到场景
            const groundGrid = new THREE.Mesh(geometry, material);
            groundGrid.rotation.x = -Math.PI / 2;
            groundGrid.position.y = -10;
            groundGrid.name = "groundGrid";
            parent.add(groundGrid);
        }

        // 优化粒子系统 - 更淡的颜色和更动态的效果
        function addGlowingParticles(parent) {
            const particleGroup = new THREE.Group();
            particleGroup.name = "particleGroup";
            parent.add(particleGroup);

            // 增加粒子数量
            const particleCount = 80;
            const particleDistance = 50;
            const maxConnections = 4;

            const particlesGeometry = new THREE.BufferGeometry();
            const particlePositions = new Float32Array(particleCount * 3);
            const particleSizes = new Float32Array(particleCount);
            const particleColors = new Float32Array(particleCount * 3);
            const velocities = [];

            const linesGeometry = new THREE.BufferGeometry();
            const linePositions = new Float32Array(particleCount * maxConnections * 2 * 3);
            const lineColors = new Float32Array(particleCount * maxConnections * 2 * 3);

            // 初始化粒子
            for (let i = 0; i < particleCount; i++) {
                const x = Math.random() * 100 - 50;
                const y = Math.random() * 20 + 5;
                const z = Math.random() * 100 - 50;

                particlePositions[i * 3] = x;
                particlePositions[i * 3 + 1] = y;
                particlePositions[i * 3 + 2] = z;

                // 粒子大小变化更大
                particleSizes[i] = Math.random() * 3 + 1.5;

                // 更淡的科技蓝色调
                const intensity = 0.6 + Math.random() * 0.4;
                particleColors[i * 3] = 0.3 * intensity; // 增加红色分量使其更淡
                particleColors[i * 3 + 1] = 0.7 * intensity; // 增加绿色分量
                particleColors[i * 3 + 2] = 1.0 * intensity; // 保持蓝色

                // 更动态的速度
                velocities.push({
                    x: (Math.random() - 0.5) * 0.08,
                    y: (Math.random() - 0.5) * 0.04,
                    z: (Math.random() - 0.5) * 0.08
                });
            }

            particlesGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
            particlesGeometry.setAttribute('customColor', new THREE.BufferAttribute(particleColors, 3));
            particlesGeometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

            // 更高级的粒子着色器
            const particleMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 }
                },
                vertexShader: `
                    attribute float size;
                    attribute vec3 customColor;
                    varying vec3 vColor;
                    uniform float time;
                    
                    // 噪声函数
                    float noise(float x, float y, float z) {
                        return fract(sin(dot(vec3(x, y, z), vec3(12.9898, 78.233, 45.164))) * 43758.5453);
                    }
                    
                    void main() {
                        vColor = customColor;
                        
                        // 获取当前位置并添加复杂的波动
                        vec3 pos = position;
                        pos.y += sin(time * 0.7 + position.x * 0.05 + position.z * 0.05) * 0.5;
                        pos.x += cos(time * 0.5 + position.z * 0.05) * 0.5;
                        pos.z += sin(time * 0.3 + position.x * 0.05) * 0.5;
                        
                        // 添加一些随机性
                        float noise = noise(position.x * 0.01, position.y * 0.01, time * 0.5);
                        
                        vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
                        // 更复杂的粒子大小动画
                        gl_PointSize = size * (300.0 / -mvPosition.z) * (0.7 + 0.3 * sin(time * 2.0 + position.x + position.z));
                        gl_Position = projectionMatrix * mvPosition;
                    }
                `,
                fragmentShader: `
                    varying vec3 vColor;
                    uniform float time;
                    
                    void main() {
                        // 计算距离中心的距离
                        vec2 center = vec2(0.5, 0.5);
                        float dist = length(gl_PointCoord - center);
                        
                        // 创建柔和的发光粒子
                        float strength = 1.0 - smoothstep(0.0, 0.5, dist);
                        
                        // 添加脉动发光效果
                        float pulse = 0.5 + 0.5 * sin(time * 3.0);
                        strength *= mix(0.8, 1.2, pulse);
                        
                        // 创建柔和的发光颜色
                        vec3 glow = vColor * strength;
                        
                        // 添加全息投影的闪烁
                        float flicker = 0.95 + 0.05 * sin(time * 10.0 + gl_PointCoord.x * 30.0);
                        glow *= flicker;
                        
                        // 设置透明度随距离衰减
                        float alpha = strength * 0.8;
                        
                        gl_FragColor = vec4(glow, alpha);
                        
                        // 丢弃低alpha值
                        if (alpha < 0.1) discard;
                    }
                `,
                blending: THREE.AdditiveBlending,
                depthTest: false,
                transparent: true,
                vertexColors: true
            });

            const particles = new THREE.Points(particlesGeometry, particleMaterial);
            particles.name = "glowingParticles";
            particleGroup.add(particles);

            // 使用更淡的颜色的连接线
            const lineMaterial = new THREE.LineBasicMaterial({
                color: 0x87CEFA, // 淡蓝色
                transparent: true,
                opacity: 0.2,
                blending: THREE.AdditiveBlending
            });

            const lines = new THREE.LineSegments(linesGeometry, lineMaterial);
            lines.name = "particleConnections";
            particleGroup.add(lines);

            // 保存粒子数据
            window.particleSystemData = {
                positions: particlePositions,
                sizes: particleSizes,
                colors: particleColors,
                linePositions: linePositions,
                lineColors: lineColors,
                velocities: velocities,
                particles: particles,
                lines: lines,
                particleCount: particleCount,
                maxConnections: maxConnections,
                particleDistance: particleDistance,
                update: updateParticles
            };

            // 更新粒子函数
            function updateParticles(time) {
                const data = window.particleSystemData;
                if (!data) return;

                let connectionsCount = 0;

                // 更新粒子位置
                for (let i = 0; i < data.particleCount; i++) {
                    let x = data.positions[i * 3];
                    let y = data.positions[i * 3 + 1];
                    let z = data.positions[i * 3 + 2];

                    // 使用更复杂的运动模式
                    x += data.velocities[i].x * (1.0 + 0.2 * Math.sin(time + i));
                    y += data.velocities[i].y * (1.0 + 0.2 * Math.cos(time * 0.7 + i));
                    z += data.velocities[i].z * (1.0 + 0.2 * Math.sin(time * 0.5 + i * 0.3));

                    // 边界检查和反弹
                    if (x < -50 || x > 50) data.velocities[i].x = -data.velocities[i].x;
                    if (y < 5 || y > 25) data.velocities[i].y = -data.velocities[i].y;
                    if (z < -50 || z > 50) data.velocities[i].z = -data.velocities[i].z;

                    // 保存更新后的位置
                    data.positions[i * 3] = x;
                    data.positions[i * 3 + 1] = y;
                    data.positions[i * 3 + 2] = z;

                    // 连接粒子
                    const connections = [];

                    for (let j = 0; j < data.particleCount; j++) {
                        if (i === j) continue;

                        const dx = data.positions[j * 3] - x;
                        const dy = data.positions[j * 3 + 1] - y;
                        const dz = data.positions[j * 3 + 2] - z;
                        const dist = Math.sqrt(dx * dx + dy * dy + dz * dz);

                        if (dist < data.particleDistance) {
                            connections.push({
                                index: j,
                                distance: dist
                            });

                            if (connections.length >= data.maxConnections) break;
                        }
                    }

                    // 创建连接线
                    for (let k = 0; k < connections.length; k++) {
                        if (connectionsCount >= data.particleCount * data.maxConnections) break;

                        const j = connections[k].index;
                        const dist = connections[k].distance;
                        const alpha = 1.0 - dist / data.particleDistance;

                        // 线的第一个点
                        data.linePositions[connectionsCount * 6] = x;
                        data.linePositions[connectionsCount * 6 + 1] = y;
                        data.linePositions[connectionsCount * 6 + 2] = z;

                        // 线的第二个点
                        data.linePositions[connectionsCount * 6 + 3] = data.positions[j * 3];
                        data.linePositions[connectionsCount * 6 + 4] = data.positions[j * 3 + 1];
                        data.linePositions[connectionsCount * 6 + 5] = data.positions[j * 3 + 2];

                        // 设置线的颜色 - 更淡的颜色
                        const pulseEffect = 0.7 + 0.3 * Math.sin(time * 2.0 + i * 0.1);
                        data.lineColors[connectionsCount * 6] = 0.3 * alpha * pulseEffect;
                        data.lineColors[connectionsCount * 6 + 1] = 0.7 * alpha * pulseEffect;
                        data.lineColors[connectionsCount * 6 + 2] = 1.0 * alpha * pulseEffect;
                        data.lineColors[connectionsCount * 6 + 3] = 0.3 * alpha * pulseEffect;
                        data.lineColors[connectionsCount * 6 + 4] = 0.7 * alpha * pulseEffect;
                        data.lineColors[connectionsCount * 6 + 5] = 1.0 * alpha * pulseEffect;

                        connectionsCount++;
                    }
                }

                // 更新几何体
                data.particles.geometry.attributes.position.needsUpdate = true;

                // 更新线段几何体
                data.lines.geometry.setAttribute('position', new THREE.BufferAttribute(data.linePositions, 3));
                data.lines.geometry.attributes.position.needsUpdate = true;

                // 只使用实际创建的连接数量
                data.lines.geometry.setDrawRange(0, connectionsCount * 2);
            }
        }

        // 新增：添加全息投影扫描效果
        function addHolographicScanEffect(parent) {
            // 创建扫描平面
            const scanPlaneSize = 100;
            const scanGeometry = new THREE.PlaneGeometry(scanPlaneSize, scanPlaneSize, 1, 1);

            // 扫描材质
            const scanMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0.0 },
                    scanColor: { value: new THREE.Color(0x478AE2) } // 淡蓝色
                },
                vertexShader: `
                    varying vec2 vUv;
                    
                    void main() {
                        vUv = uv;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 scanColor;
                    varying vec2 vUv;
                    
                    void main() {
                        // 扫描线效果
                        float scanLine = mod(vUv.y - time * 0.2, 1.0);
                        float scanIntensity = exp(-pow(scanLine * 10.0, 2.0)) * 0.5;
                        
                        // 增加圆形扩散波效果
                        float centerDist = length(vUv - vec2(0.5, 0.5));
                        float ripple = sin((centerDist * 20.0 - time * 2.0) * 3.14159);
                        float rippleIntensity = exp(-pow(ripple * 2.0, 2.0)) * 0.3;
                        
                        // 结合效果
                        float intensity = scanIntensity + rippleIntensity;
                        
                        // 全息效果的颜色和透明度
                        vec3 color = scanColor * intensity;
                        float alpha = intensity * 0.4; // 较低的透明度
                        
                        gl_FragColor = vec4(color, alpha);
                    }
                `,
                transparent: true,
                blending: THREE.AdditiveBlending,
                side: THREE.DoubleSide,
                depthWrite: false
            });

            // 创建扫描平面
            const scanPlane = new THREE.Mesh(scanGeometry, scanMaterial);
            scanPlane.rotation.x = -Math.PI / 2;
            scanPlane.position.y = -5; // 稍高于网格
            scanPlane.name = "holographicScan";
            parent.add(scanPlane);

            // 创建垂直扫描效果
            const verticalScanGeometry = new THREE.PlaneGeometry(scanPlaneSize, 40, 1, 1);

            const verticalScanMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0.0 },
                    scanColor: { value: new THREE.Color(0x478AE2) } // 非常淡的蓝色
                },
                vertexShader: `
                    varying vec2 vUv;
                    
                    void main() {
                        vUv = uv;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform vec3 scanColor;
                    varying vec2 vUv;
                    
                    void main() {
                        // 创建垂直扫描效果
                        float scanPos = mod(time * 0.1, 2.0) - 1.0; // -1到1循环
                        float dist = abs(vUv.x - scanPos);
                        float scanIntensity = smoothstep(0.1, 0.0, dist) * 0.3;
                        
                        // 添加数据线效果
                        float dataLines = step(0.97, mod(vUv.y * 50.0, 1.0)) * 0.2;
                        
                        // 添加失真效果
                        float distortion = sin(vUv.y * 100.0 + time * 10.0) * 0.02;
                        
                        // 组合效果
                        float alpha = scanIntensity + dataLines * (0.1 + distortion);
                        vec3 color = scanColor;
                        
                        // 添加边缘发光
                        float edge = smoothstep(0.5, 0.0, abs(vUv.y - 0.5)) * 0.2;
                        color += edge * vec3(0.8, 0.9, 1.0);
                        
                        gl_FragColor = vec4(color, alpha);
                    }
                `,
                transparent: true,
                blending: THREE.AdditiveBlending,
                side: THREE.DoubleSide,
                depthWrite: false
            });

            // 创建多个垂直扫描平面，形成3D效果
            for (let i = 0; i < 4; i++) {
                const angle = i * Math.PI / 2;
                const verticalScan = new THREE.Mesh(verticalScanGeometry, verticalScanMaterial.clone());
                verticalScan.position.set(Math.sin(angle) * 30, 10, Math.cos(angle) * 30);
                verticalScan.rotation.y = angle;
                verticalScan.name = `verticalScan_${i}`;
                parent.add(verticalScan);
            }
        }

        // 新增：数据流动效果
        function addDataFlowEffect(parent) {
            // 创建数据线路径
            const curve = new THREE.CurvePath();

            // 创建几个贝塞尔曲线形成复杂路径
            const startPoint = new THREE.Vector3(-40, 0, -40);
            const endPoint = new THREE.Vector3(40, 20, 40);

            // 随机生成几条控制点不同的曲线
            for (let i = 0; i < 5; i++) {
                const startOffset = new THREE.Vector3(
                    (Math.random() - 0.5) * 20,
                    (Math.random() - 0.5) * 10 + 5,
                    (Math.random() - 0.5) * 20
                );

                const controlPoint1 = new THREE.Vector3(
                    (Math.random() - 0.5) * 40,
                    (Math.random() - 0.5) * 10 + 15,
                    (Math.random() - 0.5) * 40
                );

                const controlPoint2 = new THREE.Vector3(
                    (Math.random() - 0.5) * 40,
                    (Math.random() - 0.5) * 10 + 15,
                    (Math.random() - 0.5) * 40
                );

                const endOffset = new THREE.Vector3(
                    (Math.random() - 0.5) * 20,
                    (Math.random() - 0.5) * 10 + 5,
                    (Math.random() - 0.5) * 20
                );

                const actualStart = startPoint.clone().add(startOffset);
                const actualEnd = endPoint.clone().add(endOffset);

                const cubicBezier = new THREE.CubicBezierCurve3(
                    actualStart,
                    actualStart.clone().add(controlPoint1),
                    actualEnd.clone().add(controlPoint2),
                    actualEnd
                );

                // 创建数据流tubular几何体
                const tubeGeometry = new THREE.TubeGeometry(
                    cubicBezier,
                    100,  // 分段数
                    0.2,  // 管道半径
                    8,    // 径向分段
                    false // 是否闭合
                );

                // 创建数据流材质
                const dataFlowMaterial = new THREE.ShaderMaterial({
                    uniforms: {
                        time: { value: 0.0 },
                        color: { value: new THREE.Color(0xB0E2FF) } // 淡蓝色
                    },
                    vertexShader: `
                        uniform float time;
                        varying vec2 vUv;
                        
                        void main() {
                            vUv = uv;
                            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                        }
                    `,
                    fragmentShader: `
                        uniform float time;
                        uniform vec3 color;
                        varying vec2 vUv;
                        
                        void main() {
                            // 流动效果
                            float flow = mod(vUv.x - time * 0.5, 1.0);
                            float intensity = smoothstep(0.0, 0.2, flow) * smoothstep(1.0, 0.8, flow);
                            
                            // 脉冲效果
                            float pulse = 0.5 + 0.5 * sin(time * 3.0 + vUv.x * 10.0);
                            
                            // 调整颜色亮度
                            vec3 finalColor = color * intensity * (0.6 + 0.4 * pulse);
                            
                            // 增加一些闪烁
                            float flicker = 0.95 + 0.05 * sin(time * 20.0 + vUv.x * 50.0);
                            finalColor *= flicker;
                            
                            // 调整透明度
                            float alpha = intensity * 0.7;
                            
                            gl_FragColor = vec4(finalColor, alpha);
                        }
                    `,
                    transparent: true,
                    blending: THREE.AdditiveBlending,
                    side: THREE.FrontSide,
                    depthWrite: false
                });

                // 创建数据流对象
                const dataFlow = new THREE.Mesh(tubeGeometry, dataFlowMaterial);
                dataFlow.name = `dataFlow_${i}`;
                parent.add(dataFlow);
            }
        }

        // 更新动画循环
        function setupAnimationLoop(container) {
            const clock = new THREE.Clock();
            window.techBackgroundData = {
                time: 0,
                container: container,
                clock: clock
            };

            function animateTechBackground() {
                requestAnimationFrame(animateTechBackground);

                const data = window.techBackgroundData;
                if (!data) return;

                const delta = data.clock.getDelta();
                data.time += delta;

                // 更新着色器材质的时间
                if (data.container) {
                    data.container.traverse(function (object) {
                        if (object.material && object.material.uniforms && object.material.uniforms.time !== undefined) {
                            object.material.uniforms.time.value = data.time;
                        }
                    });

                    // 非常缓慢的旋转整个容器
                    data.container.rotation.y = data.time * 0.02;
                }

                // 更新粒子系统
                if (window.particleSystemData) {
                    window.particleSystemData.update(data.time);
                }
            }

            // 启动动画循环
            animateTechBackground();
        }

    </script>



</body>

</html>