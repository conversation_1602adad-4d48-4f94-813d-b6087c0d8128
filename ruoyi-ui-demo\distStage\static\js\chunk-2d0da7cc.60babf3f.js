(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0da7cc"],{"6c8a":function(t,s,e){"use strict";e.r(s);var i=function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("div",[t._v("sso login")])},o=[],n=(e("4de4"),e("d3b7"),e("0643"),e("2382"),e("4360")),c=e("5f87"),u={created:function(){var t=this;this.getDicts("base_configs").then((function(s){t.conf=s.data.filter((function(s){"ssologin"==s.dictLabel?t.ssologin=s.dictValue:"ssoRedirect"==s.dictLabel?t.ssoRedirect=s.dictValue:"ssologinSuccess"==s.dictLabel?t.ssologinSuccess=s.dictValue:"ssologinFailed"==s.dictLabel&&(t.ssologinFailed=s.dictValue)}))})),n["a"].dispatch("getPublicKey").then((function(t){var s=t.publicKey;Object(c["e"])(s),n["a"].commit("SET_PUBLICKEY",s);var e=t.sPublicKey;Object(c["f"])(e),n["a"].commit("SET_PUBLICKEY2",e)})).then((function(){}))},props:{},data:function(){return{token:this.$route.query.token,code:this.$route.query.code,tusn:this.$route.query.tusn,ssologin:null,ssoRedirect:null,ssologinSuccess:null,ssologinFailed:null}},mounted:function(){this.code||(this.code=this.gf.request("code")),this.token||(this.token=this.gf.request("token")),this.tusn||(this.tusn=this.gf.request("tusn"));var t=this;setTimeout((function(){t.getData()}),500)},methods:{getData:function(){var t=this;console.log(this.token,this.code,this.tusn),this.$store.dispatch("ssologin",{token:this.token,code:this.code,tusn:this.tusn}).then((function(){window.location.href=t.ssologinSuccess})).catch((function(s){window.location.href=t.ssologinFailed}))}}},l=u,a=e("2877"),d=Object(a["a"])(l,i,o,!1,null,null,null);s["default"]=d.exports}}]);