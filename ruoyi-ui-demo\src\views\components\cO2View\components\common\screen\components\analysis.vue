<template>
    <div class="analysis">
        <div class="title">
            {{ data.title }}
        </div>
        <div class="cont">
            <BaseChart height="150px" autoResize :optionData="getProgressOption" width="50%"></BaseChart>
            <BaseChart height="150px" class="proportion" autoResize :optionData="getProportionOption" width="50%">
            </BaseChart>
        </div>
    </div>
</template>

<script>
import BaseChart from "../../BaseChart.vue";
import {
    getDeviceDataListByIds,
    deviceDataSummary
} from "@/api/device/apis";
export default {
    props: {
        "height": {
            type: String,
            default: '194px'
        },
        "data": {
            type: Object,
            default: () => ({}),
        },
        "dataOverview": {
            type: Array,
            default() {
                return []
            },
        }
    },
    watch: {
        dataOverview: {
            handler(val) {
                if (val && val.length > 0) {
                    this.getApiData('currentTotalElectricalPower');
                }
            },
            deep: true,
            immediate: true
        },
        data: {
            handler(val) {
               

                // 当data变化且包含itemDataIds时，重新获取数据
                if (val && val.itemDataIds) {
                    console.log('检测到itemDataIds变化，重新获取数据');
                    this.$nextTick(() => {
                        this.getData();
                    });
                }
            },
            deep: true,
            immediate: true
        }
    },
    components: {
        BaseChart
    },
    computed: {
        effectiveItemDataIds() {
          
            // 确保 this.data 是对象且有 itemDataIds 属性
            if (this.data && typeof this.data === 'object' && this.data.itemDataIds) {
                console.log('使用配置的itemDataIds:', this.data.itemDataIds);
                return this.data.itemDataIds;
            }

            return "";
        },
        getProgressOption() {
            return {
                title: {
                    text: `总功率(kW)`,
                    left: "center",
                    top: "55%",
                    textStyle: {
                        color: "#999999",
                        fontSize: 13,
                        fontWeight: "400",
                    },
                },
                //饼图中间显示文字
                graphic: {
                    type: "text",
                    left: "center",
                    top: "34%",
                    style: {
                        text: `${this.totalPower}`, //文字内容
                        fontSize: 22, //文字字号
                        fontWeight: "800",
                        fill: "rgb(208, 222, 238)",
                    },
                },
                tooltip: {
                    trigger: "item",
                },
                series: [
                    {
                        //第一张圆环
                        type: "pie",
                        radius: ["85%", "95%"],
                        center: ["50%", "50%"],
                        // 隐藏指示线
                        labelLine: { show: false },
                        //隐藏圆环上文字
                        label: {
                            show: false,
                        },
                        data: [
                            //value当前进度 + 颜色
                            {
                                value: this.data.progress?.percentage || 0,
                                itemStyle: {
                                    normal: {
                                        color: new this.$echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [{ offset: 0, color: "rgb(34, 148, 254)" }]
                                        ),
                                    },
                                },
                            },
                            {
                                value: 100 - (this.data.progress?.percentage || 0),
                                itemStyle: {
                                    normal: { color: "#162944" },
                                },
                                // barWidth: 10,
                            },
                        ],
                    },
                ],
            }
        },
        getProportionOption() {
            let that = this;
            return {
                title: {
                    text: this.data.proportion?.title || '',
                    left: "17%",
                    top: "35%",
                    textStyle: {
                        color: "#999999",
                        fontSize: 13,
                        fontWeight: "400",
                    },
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function (params) {
                        console.log(params, 'params')
                        return `<div style="color: ${params.color};">● ` + (params.name ? params.name + ' : ' : '') + params.value + '%</div>'
                    }
                },
                legend: {
                    top: "center",
                    left: "68%",
                    orient: 'vertical',
                    icon: "circle",
                    itemHeight: 10,
                    itemWidth: 10,
                    formatter: function (name) {
                        const dataList = that.list || []
                        let value = 0;
                        for (var i = 0; i < dataList.length; i++) {
                            if (dataList[i].name === name) {
                                value = dataList[i].value;
                            }
                        };
                        return `${name}\n${value}%`;
                    }
                },
                color: [...this.data.proportion?.color || []],
                series: [
                    {
                        name: "占比",
                        type: "pie",
                        radius: ["85%", "95%"],
                        center: ["32%", "50%"],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: "center",
                        },
                        labelLine: {
                            show: false,
                        },
                        data: [
                            ...this.list || []
                        ],
                    },
                ],
            }
        }
    },
    data() {
        return {
            curBuilding: this.gf.getCurBuilding(),
            itemDataIds: this.data.itemDataIds || "",
            list: [],
            totalPower: 0,
        };
    },
    mounted() {

        this.getData();
    },
    methods: {
        getData() {
            console.log('=== getData方法执行 ===');
            console.log('当前effectiveItemDataIds:', this.effectiveItemDataIds);

            // 如果没有配置itemDataIds，则不调用API
            if (!this.effectiveItemDataIds) {
                console.log('itemDataIds为空，跳过API调用');
                return;
            }

            const map = [
                {
                    code: "LD1",
                    name: "冷冻泵"
                },
                {
                    code: "LQ1",
                    name: "冷却泵"
                },
                {
                    code: "LQT1",
                    name: "冷却塔"
                },
                {
                    code: "LRY021",
                    name: "冷水机"
                },
            ]

            console.log('调用API getDeviceDataListByIds，参数:', {
                deviceDataIds: this.effectiveItemDataIds
            });

            getDeviceDataListByIds({
                deviceDataIds: this.effectiveItemDataIds,
            }).then(({ data = [] }) => {
                const arr = [];
                let sum = 0;
                console.log(data, 'data21');
                data.forEach(item => {
                    const val = parseFloat(item.dVal);
                    if (isNaN(val)) return;  // 过滤无效数值

                    const idx = arr.findIndex(it => it.type === item.type);

                    if (idx === -1) {
                        const dt = map.find(d => d.code === item.type);
                        if (!dt) return;  // 保持原有跳过逻辑
                        arr.push({
                            value: val,       // 已解析的数值直接使用
                            name: dt.name,
                            type: dt.code
                        });
                    } else {
                        arr[idx].value += val; // 已解析的数值直接使用
                    }

                    sum += val;  // 保持原有累加逻辑
                });
                arr.forEach(item => {
                    item.value = ((item.value / sum) * 100).toFixed(1)
                })
                this.list = [...arr];
                console.log(this.list, 'this.list')
            });
        },
        getApiData(code) {
            let params = (this.dataOverview.find(item => item.code === code) || {}).params;
            deviceDataSummary(params).then(res => {
                this.totalPower = res.data.val && res.data.val.toFixed(2);
            })
        },
    }
};
</script>

<style scoped lang="scss">
.analysis {
    margin: 18px 0 0;

    .title {
        height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: url('/image/screen/airConditioning_powerOn_bg.png') no-repeat center center;
        background-size: 100% 100%;
        padding: 2px 0 2px 12px;
        font-size: 16px;
        line-height: 16px;
        color: #FFFFFF;
        margin-bottom: 18px;

        ::v-deep .el-input {
            width: 70px;
            margin-left: 12px;
        }
    }

    .cont {
        padding: 12px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .proportion {
            left: -7%;
        }
    }
}
</style>
