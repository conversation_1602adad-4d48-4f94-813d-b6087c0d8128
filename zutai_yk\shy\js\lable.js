// 用于跟踪当前显示的标签
var currentLabelId = null;

function createlable(params) {
  console.log(params, 'params');

  // 移除之前的标签
  removeExistingLabels();

  let earthMassDiv = document.createElement("div");
  earthMassDiv.className = "label";
  earthMassDiv.id = params.id;
  let infoDiv = document.createElement("div");
  infoDiv.style.backgroundImage = "url('./images/pop.png')";
  // infoDiv.style.pointerEvents = "none";
  infoDiv.style.backgroundSize = "100% 100%"; // 背景图充满整个div
  infoDiv.style.width = "170px"; // 调整宽度
  infoDiv.style.height = "130px"; // 调整高度
  infoDiv.style.color = "white"; // 文本颜色
  infoDiv.style.fontSize = "11px"; // 字体大小
  // infoDiv.style.display = "flex"; // 使用Flex布局使内容居中
  infoDiv.style.flexDirection = "column"; // 垂直排列内容
  infoDiv.style.justifyContent = "center"; // 在div中垂直居中内容
  infoDiv.style.paddingLeft = "10px"; // 左侧内边距
  infoDiv.style.paddingRight = "10px"; // 左侧内边距
  infoDiv.style.paddingTop = "5px"; // 顶部内边距
  infoDiv.style.paddingBottom = "5px"; // 顶部内边距
  // infoDiv.style.alignItems = "center"; // 在div中水平居中内容
  //infoDiv.style.padding = "-2px 5px"; // 调整内边距，左右各10px
  // infoDiv.style.paddingTop = "-60px";

  // 获取设备IP、状态等信息
  const deviceIp = params.manageIp || "--";
  const deviceStatus = params.status === "0" ? "正常" : "异常";
  const deviceManageStatus = params.manageStatus === "NORMAL" ? "正常" : "异常";
  const performance = params.performance || "--";
  const uptime = params.uptime || "--";
  const userCount = params.userCount || "--";

  // 使用表格布局使左右对齐更整齐
  infoDiv.innerHTML = `
        <table style="width:100%;  text-align: left;">
          <tr>
            <td style="width:45%;">设备IP：</td>
            <td style="color:#42b9eb;">${deviceIp}</td>
          </tr>
          <tr>
            <td>设备状态：</td>
            <td style="color:#42b9eb;">${deviceStatus}</td>
          </tr>
          <tr>
            <td>管理状态：</td>
            <td style="color:#42b9eb;">${deviceManageStatus}</td>
          </tr>
       
          <tr>
            <td>在线时长：</td>
            <td style="color:#42b9eb;">${uptime}</td>
          </tr>
           <tr>
            <td>接入用户数：</td>
            <td style="color:#42b9eb;">${userCount}</td>
          </tr>
        </table>
      `;

  // 将infoDiv添加到earthMassDiv中
  earthMassDiv.appendChild(infoDiv);
  console.log({ x: params.json.position[0], y: params.json.position[1], z: params.json.position[2] }, 'params');

  // 生成唯一的标签名称
  const labelName = 'device-label-' + (params.id || '');
  
  // 保存当前标签ID，用于后续清理
  currentLabelId = labelName;

  // 调用3D视图库的方法来添加这个新创建的标签到视图中
  view.add3dSprite(earthMassDiv, {
    position: { x: params.json.position[0], y: params.json.position[1] + 4, z: params.json.position[2] },
    name: labelName,
    scale: 0.05,
  });
  // view.add2d(earthMassDiv, {
  //   position: {x:params.json.position[0],y:params.json.position[1],z:params.json.position[2]},
  //   name: "pupop1",
  // });
}

// 移除所有现有标签的函数
function removeExistingLabels() {
  // 如果有当前显示的标签，则移除它
  if (!currentLabelId) return; // 没有标签需要移除
  
  if (view) {
    try {
      view.removeObjByNames([currentLabelId]);
    } catch (error) {
      console.error('移除标签时出错:', error);
    }
  }
}