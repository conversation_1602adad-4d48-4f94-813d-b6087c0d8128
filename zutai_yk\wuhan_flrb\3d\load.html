<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download JS File</title>
</head>
<body>

<button id="download-btn">Download JS File</button>

<script>
document.getElementById('download-btn').addEventListener('click', () => {
    const config = {
        position: [
            0.08757585217758113,
            16.523905142924555,
            15.392655655955977
        ],
        target: [
            0.037392370742221,
            -1.5625593211559785,
            0.08426981995404643
        ],
        near: 1,
        far: 3000000
    };

    // 将对象转换为 JS 文件格式
    const jsContent = `const config = ${JSON.stringify(config, null, 4)};\n\nexport default config;`;
    
    // 创建一个 Blob 对象
    const blob = new Blob([jsContent], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    
    // 创建一个隐藏的下载链接
    const a = document.createElement('a');
    a.href = url;
    a.download = 'config.js';
    a.style.display = 'none';
    
    // 自动点击下载链接
    document.body.appendChild(a);
    a.click();
    
    // 清理链接对象
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
});
</script>

</body>
</html>
