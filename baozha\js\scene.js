import * as THREE from 'three';
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
import { DRACOLoader } from 'three/addons/loaders/DRACOLoader.js';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

// 初始化场景
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true // 允许透明背景
});
const clock = new THREE.Clock();
renderer.setClearAlpha(0); // 完全透明背景

// 初始化渲染器
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setPixelRatio(window.devicePixelRatio);
renderer.setClearColor(0xeeeeee);
document.body.appendChild(renderer.domElement);

// 添加灯光
const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
scene.add(ambientLight);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
directionalLight.position.set(10, 10, 10);
scene.add(directionalLight);

// 初始化轨道控制器
const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;

// 设置默认相机位置
camera.position.set(8, 5, 8);
controls.target.set(0, 1, 0);
controls.update();

// 添加模型配置
const modelConfig = {
    model1: {
        path: './model/model1.gltf',
        parts: [],
        visible: true
    },
    model2: {
        path: './model/model2.gltf', 
        parts: [],
        visible: false
    }
};

// 初始化加载器
const dracoLoader = new DRACOLoader();
dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');

const gltfLoader = new GLTFLoader();
gltfLoader.setDRACOLoader(dracoLoader);

// 修改模型加载逻辑
function loadAllModels() {
    const loader = new GLTFLoader();
    
    // 加载模型1
    loader.load(modelConfig.model1.path, (gltf) => {
        const model = gltf.scene;
        model.name = 'model1';
        model.visible = modelConfig.model1.visible;
        processModelParts(model, 'model1');
        scene.add(model);
    });

    // 加载模型2
    loader.load(modelConfig.model2.path, (gltf) => {
        const model = gltf.scene;
        model.name = 'model2';
        model.visible = modelConfig.model2.visible;
        processModelParts(model, 'model2');
        scene.add(model);
    });
}

// 模型处理函数
function processModelParts(model, modelName) {
    model.traverse((child) => {
        if (child.isMesh) {
            const partInfo = {
                object: child,
                originalPosition: child.position.clone(),
                direction: new THREE.Vector3(1, 0, 0) // 默认X轴方向
            };
            
            // 添加模型专属处理逻辑
            modelConfig[modelName].parts.push(partInfo);
        }
    });
}

// 模型切换函数
function switchModel(targetModel) {
    Object.keys(modelConfig).forEach(modelKey => {
        const model = scene.getObjectByName(modelKey);
        if (model) {
            model.visible = (modelKey === targetModel);
            modelConfig[modelKey].visible = (modelKey === targetModel);
        }
    });
    
    // 更新当前模型部件引用
    modelParts = modelConfig[targetModel].parts;
}

// 事件绑定
document.getElementById('showModel1').addEventListener('click', () => {
    switchModel('model1');
    resetCameraPosition('model1');
});

document.getElementById('showModel2').addEventListener('click', () => {
    switchModel('model2');
    resetCameraPosition('model2');
});

// 相机复位函数
function resetCameraPosition(modelName) {
    const model = scene.getObjectByName(modelName);
    if (model) {
        const box = new THREE.Box3().setFromObject(model);
        const center = box.getCenter(new THREE.Vector3());
        controls.target.copy(center);
        camera.position.copy(center).add(new THREE.Vector3(5, 5, 5));
        controls.update();
    }
}

// 初始化时加载所有模型
loadAllModels();

// 爆炸动画
function updateExplosion(delta) {
    modelParts.forEach(part => {
        const target = part.originalPosition.clone().add(
            part.direction.multiplyScalar(
                explosionParams.isExploded ? explosionParams.maxDistance : 0
            )
        );
        part.object.position.lerp(target, delta * explosionParams.speed);
    });
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);
    const delta = clock.getDelta();
    
    updateExplosion(delta);
    controls.update();
    renderer.render(scene, camera);
}
animate();

// 事件监听
document.getElementById('explodeBtn').addEventListener('click', () => {
    explosionParams.isExploded = !explosionParams.isExploded;
});

// 窗口响应
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});

// 在初始化场景后添加
const axesHelper = new THREE.AxesHelper(5);  // 添加坐标系
scene.add(axesHelper);

const gridHelper = new THREE.GridHelper(10, 10); // 添加网格地面
scene.add(gridHelper); 