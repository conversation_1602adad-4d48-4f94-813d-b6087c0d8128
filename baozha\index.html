<!DOCTYPE html>
<html>
<head>
    <title>模型查看器</title>
    <style>
        body { margin: 0; overflow: hidden; }
        #ui {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 100;
        }
        button {
            padding: 10px 20px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        #loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            padding: 20px 40px;
            border-radius: 10px;
            color: white;
            text-align: center;
        }
        #progressBar {
            width: 200px;
            height: 10px;
            background: #444;
            margin: 10px 0;
            border-radius: 5px;
        }
        #progressFill {
            width: 0%;
            height: 100%;
            background: #4CAF50;
            border-radius: 5px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="ui">
        <button id="showModel1">显示模型1</button>
        <button id="showModel2">显示模型2</button>
        <button id="explodeBtn">爆炸展开</button>
    </div>
    
    <div id="loading">
        <div>模型加载中...</div>
        <div id="progressBar">
            <div id="progressFill"></div>
        </div>
        <div id="progressText">0%</div>
    </div>

    <script async src="https://unpkg.com/es-module-shims@1.6.3/dist/es-module-shims.js"></script>
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.150.1/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.150.1/examples/jsm/",
            "three/draco/": "https://www.gstatic.com/draco/versioned/decoders/1.5.6/"
        }
    }
    </script>
    <script type="module" src="./js/scene.js"></script>
</body>
</html> 