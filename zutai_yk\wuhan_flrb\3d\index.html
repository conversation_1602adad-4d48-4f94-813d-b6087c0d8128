<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title id="dynamicTitle">三维预览</title>
    <link rel="stylesheet" type="text/css" href="./css/vueheng.css" />
    <style>
      html,
      body {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100vh;
        overflow: hidden;
        background: url("./images/expl.png");
        background-size: 100% 100%;
      }
      .pipeexplain {
        position: fixed;
        top: 10%;
        right: 15%;
        display: inline-block;
        width: 178px;
        /* height: 205px; */
        z-index: 2222;
        background: url("./images/expl.png");
        background-size: 100% 100%;
        padding-bottom: 10px;
        /* display: none; */
      }
      .extit {
        margin-left: 20px;
        margin-top: 20px;
      }
      .explaincl {
        height: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }
      .ex1 {
        width: 53px;
        height: 19px;
        background-color: #4ff810;
      }
      .ex2 {
        width: 53px;
        height: 19px;
        background-color: #1ae5ef;
      }
      .exp {
        margin-left: 15px;
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
      .switch {
        /* display: none!important; */
        position: fixed;
        bottom: 3%;
        right: 5%;
        display: inline-block;
        width: 60px;
        height: 34px;
        z-index: 2222;
        display: none;
      }

      .switch input {
        opacity: 0;
        width: 0;
        height: 0;
        pointer-events: none;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
        border-radius: 34px;
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
      }

      input:checked + .slider {
        background-color: #2196f3;
      }

      input:checked + .slider:before {
        transform: translateX(26px);
      }

      .bgbg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: url("./images/3dbg2.jpg");
        background-size: 100% 100%;
      }
      .menu-container,
      .submenu-container {
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 10px;
        margin: 10px;
      }

      .menu-container {
        position: fixed;
        top: 6vh;
        right: 5%;
        height: 40vh;
        overflow-y: auto;
        z-index: 10000;
      }

      .resfw {
        position: fixed;
        top: 4.2vh;

        right: 10.55%;
        cursor: pointer;
        z-index: 110000;
        background-color: #f0f0f0;
        width: 58px;
        height: 26px;
        border-radius: 3px;
        line-height: 26px;
        text-align: center;
      }

      .touming {
        position: fixed;
        top: 1.5vh;
        right: 10.55%;
        cursor: pointer;
        z-index: 110000;
        background-color: #f0f0f0;
        width: 58px;
        height: 26px;
        border-radius: 3px;
        line-height: 26px;
        text-align: center;
      }
      .biaoti {
        position: fixed;
        top: 3.95vh;
        right: 3.2%;
        cursor: pointer;
        z-index: 10000;
        color: #f0f0f0;
        width: 166px;
        height: 26px;
        font-size: 20px;
        border-radius: 3px;
        line-height: 26px;
        text-align: center;
      }
      /* 设置滚动条的样式 */
      .menu-container::-webkit-scrollbar {
        width: 10px; /* 设置滚动条的宽度 */
      }

      /* 设置滚动条轨道的样式 */
      .menu-container::-webkit-scrollbar-track {
        background-color: #f1f1f1; /* 设置滚动条轨道的背景色 */
      }

      /* 设置滚动条滑块的样式 */
      .menu-container::-webkit-scrollbar-thumb {
        background-color: #888; /* 设置滚动条滑块的背景色 */
      }

      .menu {
        list-style-type: none;
        padding: 0;
        margin: 0;
      }

      .menu > li,
      .submenu > li {
        margin-bottom: 10px;
      }

      .menu > li > a,
      .submenu > li > a,
      .menu > li,
      .submenu > li {
        display: block;
        padding: 5px 10px;
        /* background-color: #333; */
        color: rgb(0, 0, 0);
        text-decoration: none;
        cursor: pointer;
        border-radius: 3px;
      }
      .submenu {
        margin-top: 0;
      }

      .submenu-container {
        position: fixed;
        top: 6vh;
        right: 10%; /* 控制子菜单的位置 */
        z-index: 1000;
      }
      .loading_page {
        position: fixed;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 11199999;
        display: flex;
        flex-direction: column;
        /* Stack items vertically */
        justify-content: center;
        /* Center items vertically */
        align-items: center;
        /* Center items horizontally */
        background-color: rgb(33, 33, 33);
        margin: 0;
      }
      .inner-box {
        margin-left: 32.5px;
        position: relative;
        width: 36px;
        height: 36px;
        transform-style: preserve-3d;
        transform-origin: center;
        animation: 3s ctn infinite;
        transform-origin: 0 0;
        transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
      }

      .inner {
        position: absolute;
        width: 36px;
        height: 36px;
        text-align: center;
        line-height: 36px;
        color: #fff;
        border-radius: 6px;
        background: hwb(0 69% 31% / 0.1);
        border: 2px solid #fff;
        transform-origin: center;
      }

      .inner:nth-child(1) {
        transform: rotateX(90deg) translateZ(18px);
        animation: 3s top infinite;
      }

      .inner:nth-child(2) {
        transform: rotateX(-90deg) translateZ(18px);
        animation: 3s bottom infinite;
      }

      .inner:nth-child(3) {
        transform: rotateY(90deg) translateZ(18px);
        animation: 3s left infinite;
      }

      .inner:nth-child(4) {
        transform: rotateY(-90deg) translateZ(18px);
        animation: 3s right infinite;
      }

      .inner:nth-child(5) {
        transform: translateZ(18px);
        animation: 3s front infinite;
      }

      .inner:nth-child(6) {
        transform: rotateY(180deg) translateZ(18px);
        animation: 3s back infinite;
      }

      @keyframes ctn {
        from {
          transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
        }

        50% {
          transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
        }

        to {
          transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
        }
      }

      @keyframes top {
        from {
          transform: rotateX(90deg) translateZ(18px);
        }

        50% {
          transform: rotateX(90deg) translateZ(18px);
        }

        75% {
          transform: rotateX(90deg) translateZ(36px);
        }

        to {
          transform: rotateX(90deg) translateZ(18px);
        }
      }

      @keyframes bottom {
        from {
          transform: rotateX(-90deg) translateZ(18px);
        }

        50% {
          transform: rotateX(-90deg) translateZ(18px);
        }

        75% {
          transform: rotateX(-90deg) translateZ(36px);
        }

        to {
          transform: rotateX(-90deg) translateZ(18px);
        }
      }

      @keyframes left {
        from {
          transform: rotateY(90deg) translateZ(18px);
        }

        50% {
          transform: rotateY(90deg) translateZ(18px);
        }

        75% {
          transform: rotateY(90deg) translateZ(36px);
        }

        to {
          transform: rotateY(90deg) translateZ(18px);
        }
      }

      @keyframes right {
        from {
          transform: rotateY(-90deg) translateZ(18px);
        }

        50% {
          transform: rotateY(-90deg) translateZ(18px);
        }

        75% {
          transform: rotateY(-90deg) translateZ(36px);
        }

        to {
          transform: rotateY(-90deg) translateZ(18px);
        }
      }

      @keyframes front {
        from {
          transform: translateZ(18px);
        }

        50% {
          transform: translateZ(18px);
        }

        75% {
          transform: translateZ(36px);
        }

        to {
          transform: translateZ(18px);
        }
      }

      @keyframes back {
        from {
          transform: rotateY(180deg) translateZ(18px);
        }

        50% {
          transform: rotateY(180deg) translateZ(18px);
        }

        75% {
          transform: rotateY(180deg) translateZ(36px);
        }

        to {
          transform: rotateY(180deg) translateZ(18px);
        }
      }

      .loading-text {
        z-index: 9999;
        color: #fff;
        /* Text color */
        margin-top: 25px;
        /* Space between the cube and text */
        font-size: 16px;
        /* Text size */
        letter-spacing: 1px;
        /* Letter spacing */
        text-align: center;
      }
      .container {
        width: 100%;
        height: 100%;
        background: url("./images/3dbg2.jpg");
        background-size: 100% 100%;
      }
      .icon1 {
        margin-top: 0px;
        margin-left: 2px;
        width: 2px;
        height: 2px;
        background-color: aqua;
        border-radius: 50%;
      }
      .icon {
        height: 4px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }
      #myList1 {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 10;
        display: none;
      }
      #myList2 {
        position: fixed;
        left: 50px;
        top: 0;
        z-index: 10;
      }
      ul {
        font-size: 12px;
        list-style-type: none;
        padding: 0px 2px;
        text-align: center;
      }
      li {
        padding: 6px;
        background-color: #f0f0f0;
        margin-bottom: 5px;
        cursor: pointer;
      }
      /* li:hover {
        background-color: #ccc;
      } */
      li.selected {
        background-color: rgb(97, 94, 94);
        color: #f0f0f0;
      }
      .anniu {
        width: 200px;
        position: fixed;
        bottom: 10.2vh;
        left: 0;
        right: 0;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
      }
      .butt {
        cursor: pointer;
        color: #f0f0f0;
        text-align: center;
        line-height: 42px;
        width: 90px;
        height: 42px;
        background-image: url(./images/butbg.png);
        background-size: 100% 100%;
      }
    </style>
    <!-- 统计代码 -->
    <script>
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?46274a4877030f8a0b194f5eb94ceefd";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
    </script>
  </head>
  <body>
    <div id="app">
      <div id="loading-page" class="loading_page">
        <div class="inner-box">
          <div class="inner"></div>
          <div class="inner"></div>
          <div class="inner"></div>
          <div class="inner"></div>
          <div class="inner"></div>
          <div class="inner"></div>
        </div>
        <div class="loading-text">正在加载中,请耐心等候...</div>
        <!-- 添加文本 -->
      </div>
      <ul id="myList1">
        <li data-name="1">B1栋</li>
        <li data-name="2">B2栋</li>
        <li data-name="3">B3栋</li>
        <li data-name="4">B4栋</li>
        <li data-name="5">W1栋</li>
        <li data-name="6">W2栋</li>
      </ul>
      <label class="switch" id="switch">
        <input
          type="checkbox"
          id="toggleSwitch"
          checked
          onchange="switchLayer()"
        />
        <span class="slider"></span>
      </label>
      <div class="pipeexplain" v-if="iscolorshiyi">
        <img class="extit" src="./images/extit.png" alt="" />
        <div class="explaincl">
          <div class="ex1"></div>
          <p class="exp">冷却系统</p>
        </div>
        <div class="explaincl">
          <div class="ex2"></div>
          <p class="exp">冷冻系统</p>
        </div>
      </div>
      <ul id="myList2"></ul>
      <div class="anniu" id="anniu">
        <div
          class="butt"
          id="zhankai"
          onclick="expandFloor(true,false)"
          v-if="isshowchange=='bottom'&&iszk"
        >
          展开
        </div>
        <div
          class="butt"
          id="fuwei"
          onclick="resetLayer()"
          v-if="isshowchange=='bottom'&&isfw"
        >
          复位
        </div>
        <!-- <button id="open" ></button> -->
      </div>
      <div v-cloak id="rightBtn" v-if="rightBtn.length&&isshowchange=='bottom'">
        <div class="rightBtn" id="rightSlide">
          <img
            class="scroll_left"
            src="./textures/left.png"
            @click="scrollLeft"
            alt=""
          />
          <div class="top">
            <div class="toplist" v-for="(item, index) in rightBtn" :key="index">
              <p class="rTitle" @click="labelTitleFun(item,index)">
                <label
                  :class="rightBtnIndex2 == index?'activelabel':'labelTitle'"
                  >{{ item.title }}</label
                >
              </p>
              <div class="rad" v-show="true"></div>
              <div
                class="list"
                v-for="(item2, index2) in item.floor"
                :key="index2"
                :class="
        rightBtnIndex == item2.name
          ? 'floorlist'
          : ''
        "
                @click="rightBtnFun(item2.name,item2.pos,item2.tar,item2.title,index, index2,item)"
              >
                <div class="content">
                  {{ item2.title }}
                  <div class="rad" v-show="true"></div>
                  <!-- <div
                  class="rad2"
                  v-show="rightBtnIndex != item2.name || rightBtnIndex1 != index"
                ></div> -->
                </div>
              </div>
            </div>
          </div>
          <img
            class="scroll_right"
            src="./textures/right.png"
            @click="scrollRight"
            alt=""
          />
        </div>
      </div>
      <div class="touming" id="touming" onclick="setop()">半透明</div>
      <div class="resfw" id="resfw" onclick="resetLayer()">复位</div>
      <div class="biaoti" id="biaoti"></div>
      <div class="menu-container" v-if="rightBtn.length&&isshowchange=='right'">
        <ul class="menu">
          <li
            v-for="(items, parentMenu,index) in rightBtn"
            :key="parentMenu"
            :class="{ selected: selectedMenu === parentMenu }"
            @click="changeSubmenu(parentMenu, items),labelTitleFun(items,index)"
          >
            {{ items.title }}
          </li>
        </ul>
      </div>

      <div class="submenu-container" v-if="submenuItems.length > 0">
        <ul class="submenu">
          <li
            v-for="( item,index) in submenuItems"
            :key="item"
            :class="{ selected: selectedSubmenuItem === item }"
            @click="selectSubmenuItem(item),rightBtnFun(item.name,item.pos,item.tar,item.title,index)"
          >
            {{ item.title }}
          </li>
        </ul>
      </div>
    </div>
    <div class="container" id="container"></div>
    <script src="./js/local.js"></script>
    <script src="./build/sdk.js"></script>

    <script src="./mockData/road.js"></script>
    <script src="./mockData/config.js"></script>
    <script src="./js/xz.js"></script>
    <script src="./js/way.js"></script>
    <script src="./js/lable.js"></script>
    <script src="./js/msg-execute3d.js"></script>
    <script src="./js/json.js"></script>
    <script src="./js/vue.js"></script>
    <script src="./js/config.js"></script>
    <script src="./js/axios.min.js"></script>

    <script src="./js/carDate.js"></script>
    <script>
      //更新地址栏
      function updateUrlParameter(key, value) {
        var url = new URL(window.location.href);
        url.searchParams.set(key, value);
        window.history.replaceState({}, "", url);
      }
      var buildings = [];
      var myList2 = document.getElementById("myList2");

      function updateList2(selectedName) {
        // 清空第二个列表
        myList2.innerHTML = "";

        // 根据第一个列表中选定的项来动态更新第二个列表
        var index = parseInt(selectedName) - 1; // 将选定的项转换为索引
        var items = buildings[index];
        items.forEach(function (item) {
          var li = document.createElement("li");
          li.textContent = item;
          li.setAttribute("data-name", item); // 设置 data-name 属性
          myList2.appendChild(li);
        });
      }
      var layerMap = [];
      let maplist;
      var listItems1 = document.querySelectorAll("#myList1 li");
      listItems1.forEach(function (item) {
        item.addEventListener("click", function () {
          let selectedName = this.getAttribute("data-name");
          console.log(selectedName);
          listItems1.forEach(function (li) {
            li.classList.remove("selected");
          });
          this.classList.add("selected");
          updateUrlParameter("id", selectedName);
          location.reload();

          // 在这里可以将点击的结果保存到任何你想要的地方
        });
      });

      // 将点击事件绑定到第二个列表的父元素上，并使用事件委托来处理点击事件
      // document
      //   .getElementById("myList2")
      //   .addEventListener("click", function (event) {
      //     if (event.target.tagName === "LI") {
      //       var selectedName = event.target.getAttribute("data-name");
      //       console.log(selectedName);
      //       var listItems = document.querySelectorAll("#myList2 li");
      //       listItems.forEach(function (li) {
      //         li.classList.remove("selected");
      //       });
      //       event.target.classList.add("selected");
      //       var layers = maplist[selectedName];
      //       if (layers) {
      //         view.setLayer(layers);
      //       }
      //       // 在这里可以将点击的结果保存到任何你想要的地方
      //     }
      //   });

      //动态的场景json数据
      let pos;
      let tar;
      let path;
      let uid;
      let modelurl; //初始场景链接
      let scenejson;
      let linejson;
      let poijson; //设备的json
      var pzjson;
      var tagidarr; //设备标签的id
      let projectname; //项目名字
      var floorlist;
      var light;
      var isfw;
      var iscolorshiyi;
      var toggletag;
      var skybox;
      var addlable_size; //点击标签的大小
      var addlable_y; //点击标签的位置高度
      var iskeji; //是否科技感
      var ismousemove; //是否移动触发标签
      var ismouseclick; //是否移动触发标签
      const urlid = new URL(location.href).searchParams.get("ids");
      var alllist;
      var response;
      var groupNames;

      const view = new app3d({
        dom: "container",
        dracoPath: "./build/draco/gltf/",
      });

      function setfloor(params, p1, p2) {
        console.log(params, p1, p2, 185);
        view.resetLayer();
        view.setLayer(params, true);
        // expandFloor(true, true, alllist, params, p1, p2);
        view.clearAllLight(); // 清除所有灯光
        view.setLight(light);
        view.animateCamera(p1, p2, 1000);
      }
      console.log(urlid);
      // axios
      //   .get(`${LocalUrl}/project/getProjectByIds?id=${urlid}`)
      //   .then((response) => {
      //     console.log(response, 121);
      //   })
      //   .catch((error) => {
      //     console.error(
      //       "There was a problem with your fetch operation:",
      //       error
      //     );
      //   });

   

      var newTitle = allresponse ? allresponse.data.name : "三维预览";
      document.getElementById("dynamicTitle").innerText = newTitle; // 这里可以处理返回的数据
      const multiplefiles = JSON.parse(allresponse.data.multiplefiles);

      let modelFile = null;

      for (const iterator of multiplefiles) {
        if (iterator.indexOf(".glb")) {
          modelFile = iterator;
          modelurl = "./project/" + modelFile.match(/[^/]+$/)[0];
        }
        console.log(modelurl, 121);
      }

      scenejson = allresponse.data.scenejson
        ? JSON.parse(allresponse.data.scenejson)
        : {
            config: {
              isfw: false, //是否显示复位按钮
              iszk: false, //是否显示展开按钮
              iskeji: true, //是否科技感  显示外轮廓要在科技感下
              projectname: "",
              isshowchange: "bottom", //是否显示楼层按钮   不显示false  下方显示bottom   右边显示 right
              ismouseclick: true, //是否可以点击显示标签
              ismousemove: true, //是否触碰显示标签
              isscale: true, //是否限制 3D 缩放
              minDistance: 15, // 设置相机与目标之间的最小距离
              maxDistance: 25, // 设置相机与目标之间的最大距离
              addlable_y: 0.5, //显示的标签的位置高度
              addlable_size: 0.01, //显示的标签的尺寸
              lightConfig: [], //灯光
              skybox: "black", //天空盒背景  夜晚night  黑色 black   默认白天
              dracoPath: "",
              hdrPath: "./textures/equirectangular/autoshop_01_1k.hdr",
              camera: {
                position: [
                  -0.4461485074383461, 9.351136644827179, -4.086642341360172,
                ],
                target: [
                  -0.5016038185903354, -2.544023974776512, 2.25025448290627,
                ],
                near: 1,
                far: 3000000,
              },
              css2d: {
                use: true,
              },
              css3d: {
                use: true,
              },
              floorlist: [],
              useEffectComposer: true,
              models: [
                {
                  path: "",
                  name: "shebei",
                  scale: [1, 1, 1],
                  rotation: [0, 0, 0],
                  id: 1,
                  visible: true,
                  groupNames: [],
                  isGlow: true,
                  glowNames: [],
                  transparentConf: [
                    // {
                    //   names: ["dm"],
                    //   opacity: 0.38,
                    // },
                    // {
                    //   names: ["rf_sb"],
                    //   opacity: 1,
                    // },
                  ],
                },
              ],
            },
          };
      console.log(scenejson.config, " ");
      isfw = "isfw" in scenejson.config ? scenejson.config.isfw : true; //复位
      tagidarr =
      "tagidarr" in scenejson.config ? scenejson.config.tagidarr : [];
      threshold =
        "threshold" in scenejson.config ? scenejson.config.threshold : 0.05;
      strength =
        "strength" in scenejson.config ? scenejson.config.strength : 0.01;
      radius = "radius" in scenejson.config ? scenejson.config.radius : 0.05;
      iscolorshiyi =
        "iscolorshiyi" in scenejson.config
          ? scenejson.config.iscolorshiyi
          : false; //是否颜色示意
      isGroundreflection =
        "isGroundreflection" in scenejson.config
          ? scenejson.config.isGroundreflection
          : false; //是否地面反射  默认反射
      toggletag =
        "toggletag" in scenejson.config ? scenejson.config.toggletag : false; //是否tag标签
      ismousemove =
        "ismousemove" in scenejson.config
          ? scenejson.config.ismousemove
          : false;
      ismouseclick =
        "ismouseclick" in scenejson.config
          ? scenejson.config.ismouseclick
          : false;
      groupNames =
        "groupNames" in scenejson.config.models
          ? scenejson.config.models[0].groupNames
          : [];
      rightBtn.isfw = "isfw" in scenejson.config ? scenejson.config.isfw : true;
      iszk = "iszk" in scenejson.config ? scenejson.config.iszk : false;
      rightBtn.iszk =
        "iszk" in scenejson.config ? scenejson.config.iszk : false;
      isshowchange =
        "isshowchange" in scenejson.config
          ? scenejson.config.isshowchange
          : false;
      istouming =
        "istouming" in scenejson.config ? scenejson.config.istouming : false;
      rightBtn.isshowchange =
        "isshowchange" in scenejson.config
          ? scenejson.config.isshowchange
          : false;
      skybox =
        "skybox" in scenejson.config ? scenejson.config.skybox : "bright";
      floorlist =
        scenejson.config && scenejson.config.floorlist
          ? scenejson.config.floorlist
          : [];
      addlable_size =
        scenejson.config && scenejson.config.addlable_size
          ? scenejson.config.addlable_size
          : 0.1;
      addlable_y =
        scenejson.config && scenejson.config.addlable_y
          ? scenejson.config.addlable_y
          : 1;
      alllist = floorlist.find((item) => item.title == "整体")?.name;
      // maplist = layerMap[uid - 1];
      // updateList2(uid);
      console.log(isfw);
      // document.getElementById("fuwei").style.display = isfw
      //   ? "block"
      //   : "none";
      // document.getElementById("zhankai").style.display = iszk
      //   ? "block"
      //   : "none";

      document.getElementById("resfw").style.display =
        isfw && isshowchange == "right" ? "block" : "none";
      document.getElementById("switch").style.display = toggletag
        ? "block"
        : "none";
      document.getElementById("touming").style.display =
        istouming == true ? "block" : "none";
      const config = {
        lightConfig: [
          // {
          //   type: "AmbientLight",
          //   color: "#ffffff",
          //   intensity: 1.5,
          // },
        ],
        dracoPath: "./build/draco/gltf/",
        hdrPath: "./textures/equirectangular/environment_1.hdr",
        camera: {
          position: [450.5499120771089, 458.6294256655836, 670.3708491162595],
          target: [-42.35672902764709, -79.53724385392881, -13.31012836746597],
          near: 0.01, // 近截面
          far: 3000000,
        },
        css2d: {
          use: true,
        },
        css3d: {
          use: true,
        },
        useEffectComposer: true,

        models: [
          {
            path: modelurl,
            position: [0, 0, 0],
            name: "shebei",
            scale: [1, 1, 1],
            rotation: [0, 0, 0],
            id: 1,
            visible: true,
            groupNames: [], // 用来配置点击选中是可以选择个组，例如一栋楼，一个电机。而不是只能选中某一部件
            isGlow: true, // 是否使用辉光
            glowNames: [], //如果设置就是局部辉光，如果不设置就是整体辉光
            transparentConf: [
              //配置哪些组需要设置透明度
              // {
              //   names: ["dm"],
              //   opacity: 0.8,
              // },
              // {
              //   names: ["rf_sb"],
              //   opacity: 1,
              // },
            ],
            // industryNames: ["B3"],
          },
        ],
      };
      // 深拷贝 config
      config.hdrPath = scenejson.config.hdrPath
        ? scenejson.config.hdrPath
        : "./textures/equirectangular/environment_1.hdr";
      iskeji = scenejson.config.iskeji ? scenejson.config.iskeji : false;
      config.lightConfig = scenejson.config.lightConfig
        ? scenejson.config.lightConfig
        : config.lightConfig;
      light = scenejson.config.lightConfig
        ? scenejson.config.lightConfig
        : config.lightConfig;
      //用来配置点击选中是可以选择个组，例如一栋楼，一个电机。而不是只能选中某一部件
      config.models[0].groupNames = scenejson.config.models[0].groupNames;

      //请求模型管道的数据
      execute();
      pzjson = allresponse.data.poijson
        ? JSON.parse(allresponse.data.poijson)
        : { models: [], devices: [] };
      projectname = scenejson.config.projectname
        ? scenejson.config.projectname
        : "";
      console.log(config);
      // 初始话场景
      console.log(config);

      view.init(config);
      view.removeTransformControls();
      // 添加天空盒
      if (skybox == "shenlan") {
        view.setSkyBox([
          "./textures/sky/px.jpg",
          "./textures/sky/nx.jpg",
          "./textures/sky/py.jpg",
          "./textures/sky/ny.jpg",
          "./textures/sky/pz.jpg",
          "./textures/sky/nz.jpg",
        ]);
      } else if (skybox == "night") {
        view.setSkyBox([
          "./textures/sky1/panoright.jpg",
          "./textures/sky1/panoleft.jpg",
          "./textures/sky1/panotop.jpg",
          "./textures/sky1/panobottom.jpg",
          "./textures/sky1/panofront.jpg",
          "./textures/sky1/panoback.jpg",
        ]);
      } else if (skybox == "black") {
        view.setSkyBox([
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
          "./textures/blacksky/sky.png",
        ]);
      } else {
        // view.setSkyBox([
        //   "./textures/skyboxlight/px.jpg",
        //   "./textures/skyboxlight/nx.jpg",
        //   "./textures/skyboxlight/py.jpg",
        //   "./textures/skyboxlight/ny.jpg",
        //   "./textures/skyboxlight/pz.jpg",
        //   "./textures/skyboxlight/nz.jpg",
        // ]);
      }

      // view.setSkyBox([
      //   "./textures/baitian/panoright.jpg",
      //   "./textures/baitian/panoleft.jpg",
      //   "./textures/baitian/panotop.jpg",
      //   "./textures/baitian/panobottom.jpg",
      //   "./textures/baitian/panofront.jpg",
      //   "./textures/baitian/panoback.jpg",
      // ]);

      // view.setNotAllowSelect([]);
      // view.addRoad(roadData);

      // view.addWall(wallData, "./textures/wall.png", 0.43);

      view.setCallBack({
        mousemove: mousemove,
        mouseclick: mouseclick,
        progress: progress,
        mouseDbClick: mouseDbClick,
      });
      view.needDoubleClickSetLayer(true);

      autoRoate = function (flag, speed) {
        view.controls.autoRotate = flag;
        view.controls.autoRotateSpeed = speed;
      };
      // 设置一个变量来存储 setTimeout 的返回值
      // 设置一个变量来存储 setTimeout 的返回值
      let timeoutId = null;

      // 这是您想要在鼠标10秒内没有任何操作时执行的函数
      function onInactive() {
        //console.log("鼠标已经10秒没有任何操作");
        // 在这里执行您的逻辑
        recordxz();
        autoRoate(true, 1); // 假设这是一个示例函数，根据您的需要调整
      }

      // 这是您想要在鼠标有操作时执行的函数
      function onActive() {
        console.log("鼠标有操作");
        // 在这里执行您的逻辑
        autoRoate(false, 3); // 假设这是一个示例函数，根据您的需要调整
      }
      const recode = 60; //未操作页面开始旋转时间 单位s
      // 假设deviceLabels已经在外部定义，用于跟踪所有已创建的标签
      function resetTimer() {
        // 首先，调用活动函数
        onActive(); // 每次用户有操作时调用

        // 如果已经有一个计时器在运行，则先清除它
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        // 设置一个新的计时器
        timeoutId = setTimeout(onInactive, recode * 1000); // 10000ms = 10s
      }
      // view.addDirectionalLight({
      //   color: "#fff",
      //   intensity: 1.2,
      //   position: [25.48246372423977, 0.958647042239285, -23.898312155269824],
      // });
      function mousedown() {
        console.log(111);
        // resetTimer();
      }
      function mousemove(obj) {
        const { model, point, center } = obj;
        if (ismousemove) {
          // console.log(obj, 55555);
          view.setOutlineModel([model]);
          addlable(view.getObjCenterByNames([model.name]));
        }
      }

      /**
       * 车流路线 点位数组
       * 车辆模型地址 url 数组
       * 当前道路 id（支持多条道路各自控制）
       */
      showCarFlow = function () {
        view.startCarFlow(road1, carModelUrls, "road1", 3);
        view.startCarFlow(road2, carModelUrls, "road2", 3);
      };
      showCarFlow1 = function () {
        view.startCarFlow(road3, carModelUrls, "road3", 2.5);
        view.startCarFlow(road4, carModelUrls, "road4", 2.5);
        view.startCarFlow(road5, carModelUrls, "road5", 2.5);
      };
      /**
       * 支持传入道路 id 关闭特定道路的
       * 如果不传则关闭所有
       */
      hideCarFlow = function () {
        view.stopCarFlow();
      };

      function progress(load, isload) {
        console.log("progress:", load);
        if (isload) {
          isload = isload;
          view.removeTransformControls();
          view.setGroundMirrorVisible(isGroundreflection); //关闭地面反射
          // view.setBloomParams({
          //   threshold: threshold,
          //   strength: strength,
          //   radius: radius,
          // });
          view.setBloomParams({
                threshold: 0.8, // 中等亮度阈值
                strength: 0.86, // 正常强度
                radius: 0.5,
              });
          // view.setGroundMirrorVisible(true);
          view.setGroundMirrorVisible1(false);
          
          let ppzzdata = [
            {
              modelname: "XZL",
              bqname: "1#行政楼",
            },
            {
              modelname: "YYG",
              bqname: "2#游泳馆",
            },
            {
              modelname: "TL",
              bqname: "塔楼",
            }, // 修复这里的错误，添加缺失的括号
            {
              modelname: "TYG",
              bqname: "3#体育馆",
            },
            {
              modelname: "LT",
              bqname: "4#礼堂",
            },
            {
              modelname: "JXL3",
              bqname: "5#教学楼A",
            },
            {
              modelname: "JXL2",
              bqname: "5#教学楼B",
            },
            {
              modelname: "JXL1",
              bqname: "5#教学楼C",
            },
            {
              modelname: "SSL",
              bqname: "6#宿舍楼",
            },
          ];
          //标签得详细信息
          let bqdetails = {
            imgurl: "https://diy.3dzhanting.cn/engineer-xf/bqimg/jiashan.png",
            fontsize: "11px",
            width: "86px",
            height: "27px",
            color: "#fff",
            lineheight: "21px",
          };
          // 创建两个数组来存储 modelname 和 bqname
          let modelnames = [];
          let bqnames = [];

          // 遍历 ppzzdata 并将 modelname 和 bqname 分别添加到对应的数组中
          for (let i = 0; i < ppzzdata.length; i++) {
            modelnames.push(ppzzdata[i].modelname);
            bqnames.push(ppzzdata[i].bqname);
          }
          console.log(Array.isArray(ppzzdata)); // 应该返回 true
          // 添加在config配置的标签

          let url = new URL(window.location.href);
          if (
            url &&
            url.searchParams.get("bid") &&
            url.searchParams.get("fid")
          ) {
            const result = rightBtn.rightBtn.find((item) =>
              item.title.includes(url.searchParams.get("bid"))
            );
            console.log(result, 185);
            let pos = result.floor[url.searchParams.get("fid")].pos;
            let tar = result.floor[url.searchParams.get("fid")].tar;
            let zuname = result.floor[url.searchParams.get("fid")].name;

            setTimeout(() => {
              setfloor(zuname, pos, tar);
              getmodeltableData(
                1,
                urlid,
                0,
                result.title,
                result.floor[url.searchParams.get("fid")].title
              );
            }, 0);

            rightBtn.isshowchange = false;
          } else {
            console.log(scenejson);
            if (scenejson.config.camera && scenejson.config.camera.position) {
              view.animateCamera(
                {
                  x: scenejson.config.camera.position[0],
                  y: scenejson.config.camera.position[1],
                  z: scenejson.config.camera.position[2],
                },
                {
                  x: scenejson.config.camera.target[0],
                  y: scenejson.config.camera.target[1],
                  z: scenejson.config.camera.target[2],
                },
                3000
              );
            }
          }
          // console.log(view.searchAllByName("500787"));
          // console.log(view.searchAllByName("智能化设备网桥架"));
          //开启车流
          // view.startCarFlow(road1, carModelUrls, "road1");
          // // view.startCarFlow(road2, carModelUrls, "road2");
          //增加金属质感
          // view.setMatalMaterial("Mesh029", "#fff");
          // view.setBloomParams({
          //   threshold: 0.1,
          //   strength: 0.1,
          //   radius: 0.1,
          // });
          //打印需要显示标签的中心点
          // view.getObjCenterByNames(
          //   view.searchAllByName("智能化设备网桥架")
          // );

          // addlable1(
          //   view.getObjCenterByNames(
          //     view.searchAllByName("智能化设备网桥架")
          //   ),
          //   1
          // );

          if (projectname == "筑医台") {
            showCarFlow();
            // 筛选函数
            function filterIdentifiers(identifiers) {
              return identifiers.filter(
                (id) => (id.match(/_/g) || []).length === 2
              );
            }

            // 筛选结果
            const filteredIdentifiers = filterIdentifiers(
              view.searchAllByName("bd_")
            );
            // 输出结果
            console.log(filteredIdentifiers);
            console.log(view.getObjCenterByNames(filteredIdentifiers));
            addlableldbq1(view.getObjCenterByNames(filteredIdentifiers));
          } else if (projectname == "嘉善") {
            addldbq(view.getObjCenterByNames(modelnames), bqnames, bqdetails);
            showCarFlow1();
          } else if (projectname == "嘉定") {
            // view.clearAllLight(); // 清除所有灯光
            // let lightConfig = [
            // {
            //   type: "AmbientLight",
            //   color: "#aaaaff",
            //   intensity: 1.5,
            // },
            // {
            //   intensity: 0.5,
            //   type: "DirectionalLight",
            //   color: "#ffffff",
            //   position: [30, 190, 20],
            // },
            // ];
            // view.setLight(lightConfig);
            // view.setBloomParams({
            //   threshold: 1.9,
            //   strength: 1.2,
            //   radius: 0.9,
            // });
          } else if (projectname == "ao组态") {
            // addlablenewall(view.getObjCenterByModelIds([model.modelId]));

            view.setOpa(["dm_roof"], 0.06, "#0066FF");
          } else if (projectname == "天津组态") {
            view.setOpa(["floor"], 0.05, "#0066FF");

            view.clearAllLight(); // 清除所有灯光
            let lightConfig = [
              {
                type: "AmbientLight",
                color: "#aaaaff",
                intensity: 1,
              },
              {
                intensity: 3,
                type: "DirectionalLight",
                color: "#fff",
                position: [
                  353.1440322709692, 32.118162337619367, 415.14587542705004,
                ],
              },
            ];
            view.setLight(lightConfig);
          }

          // 设置那些对象可以选中

          // view.setAllowSelect(view.searchAllByName("智能化设备网桥架")); // 不设置默认不可选中

          console.log(12121);

          document.getElementById("loading-page").style.display = "none";
          //上下翻转的最大角度
          view.controls.maxPolarAngle = 1.5;
          //上下翻转的最小角度
          view.controls.minPolarAngle = 0.3;
          view.controls.enableDamping = true; //开启阻尼惯性
          view.controls.dampingFactor = 0.08; //阻尼参数

          if (scenejson.config.isscale) {
            // 限制 3D 缩放
            view.controls.minDistance = scenejson.config.minDistance; // 设置相机与目标之间的最小距离
            view.controls.maxDistance = scenejson.config.maxDistance; // 设置相机与目标之间的最大距离
          }

          view.needClickSetObjOutline(true);
          window.parent.postMessage(
            {
              type: "finished",
            },
            "*"
          );
          // 开启关闭mousemove事件
          // 设置为科技风格
          view.toggleShowStyle(iskeji);
          // addLabel();
          // let linelist = [];
          // for (let i = 0; i <= 300; i++) {
          //   let result = `tube${i.toString().padStart(1, "0")}`;
          //   linelist.push(result);
          // }
          // console.log(linelist);
          //  view.setLayer(["B3"]);
          // 开启单机显示当前模型轮廓
          view.needClickSetObjOutline(true);
          // 设置一组模型为一个整体被选中

          // 开启关闭mousemove事件
          view.toggleMousemove(true);
          view.setAllowSelect(config.models[0].groupNames);
          view.setintersectArr(["lqb01", "lqb02", "lqb03"]);

          // view.add2d(earthMassDiv, {
          //   position: {
          //     x: 22.08519033193588,
          //     y: 1.1726030545130566,
          //     z: 6.487071424478986,
          //   },
          //   name: "pupop",
          // });

          // 初始化模型风扇数据，传入风扇和出风效果模型name数据， 以及初始的风扇状态数据
          // const fanNameArrs = Object.keys(modelData);
          // const rotateZArr = [];
          // const rotateXArr = [];
          // let statusMap = {
          //   // 'd1_fs_029': 'normal',
          //   // 'd1_fs_025': 'stop',
          //   // 'd1_fs_026': 'except'
          // };
          // fanNameArrs.forEach((item) => {
          //   statusMap[item] = "normal"; // 默认都设置为正常
          // });
          // view.Fan.initFan(fanNameArrs, statusMap, modelData);
          const floorData = {
            // 按照从一楼到顶楼的顺序配置楼层组名
            floorNames: alllist,
            perHeight: 3,
            aniTime: 800, // 动画时间
          };

          // 设置一组模型为一个整体被选中
          // 设置楼层配置数据
          view.setFloorData(floorData);
          // 配置整层选中
          view.setSelectWholeGroup([
            "1f003",
            "2f003",
            "3f003",
            "4f003",
            "ding003",
          ]);
        }
      }
      function mouseDbClick(name, model) {
        hideCarFlow();
        console.log(name, groupNames);
        // view.setLayer([name]); view.nameTocam(name);
        var divElement = document.getElementById("biaoti");
        // 插入值
        divElement.innerHTML = filterAndConvert(name);
        // 定义筛选和转换函数
        function filterAndConvert(str) {
          // 提取z或cf后面的部分
          var match = str.match(/z(\d+)|cf(\d+)/);
          if (match) {
            // 判断类型并转换
            if (match[1]) {
              return match[1] + "#综合楼 ";
            } else if (match[2]) {
              return match[2] + "#厂房 ";
            }
          }
          return null;
        }

        //  if (name == "B3") {

        //   view.animateCamera(
        //     {
        //       x: 27.031664757887466,
        //       y: 5.762609290224179,
        //       z: 13.589114807899897,
        //     },
        //     {
        //       x: 19.378543379471612,
        //       y: -0.7216638762820583,
        //       z: 6.965869375288216,
        //     },
        //     0
        //   );
        // } else {
        //   view.nameTocam(name);
        // }
        //
      }
      expandFloor = function (flag, action, alllist, dlist, p1, p2) {
        // if (flag) { // 如果是展开则只显示当前楼栋
        console.log(flag, action, alllist, dlist);
        // view.setLayer(alllist, true);
        view.resetLayer();
        // }
        view.animateCamera(
          scenejson.config.unfold_data && scenejson.config.unfold_data.position
            ? scenejson.config.unfold_data.position
            : {},
          scenejson.config.unfold_data && scenejson.config.unfold_data.target
            ? scenejson.config.unfold_data.target
            : {},
          500
        );
        if (action) {
          // 楼层展开或者合上
          view.expandFloor(flag, () => {
            console.log("完成了。。。。。。。。");
            if (flag) {
              setTimeout(() => {
                view.setLayer(dlist, true);
                view.animateCamera(p1, p2, 1000);
              }, 500);
            } else {
            }
          });
        } else {
          // 楼层展开或者合上
          view.expandFloor(flag, () => {
            console.log("完成了。。。。。。。。");
          });
        }
      };
      resetLayer = function () {
        view.resetOpa();
        document.getElementById("biaoti").innerHTML = "";
        hideCarFlow();
        if (projectname == "筑医台") {
          showCarFlow();
        }
        expandFloor(false, false);
        view.resetLayer();
        view.animateCamera(
          {
            x: scenejson.config.camera.position[0],
            y: scenejson.config.camera.position[1],
            z: scenejson.config.camera.position[2],
          },
          {
            x: scenejson.config.camera.target[0],
            y: scenejson.config.camera.target[1],
            z: scenejson.config.camera.target[2],
          },
          1500
        );
        console.log(1212);
      };

      setop = function () {
        view.setOpa(["bd_1_z1"], 0.45, "#bbb");
      };

      function sendRoomMessage(id) {
        let deviceId = getDeviceIdById(id);
        console.log("已发送", id, deviceId);

        window.parent.postMessage(
          {
            type: "clickRoom",
            info: {
              roomId: id, //当前点击房间
              deviceId: deviceId, //当前点击设备
            },
          },
          "*"
        );
      }
      function mouseclick(obj) {
        const { model, point, center } = obj;
        console.log("point:", point);
        console.log("point:", [point.x, point.y, point.z]);
        console.log("点击模型的名字：", model);

        console.log(view.getObjCenterByNames([model.name]));
        sendRoomMessage(model.name);

        console.log(obj, "点击后拿到的所有东西");
        let daying = {
          pos: view.camera.position,
          tar: view.controls.target,
        };
        console.log(daying, "视角");
        // resetTimer();
        // 可以传多个， 模型数组

        if (ismouseclick) {
          model.name = model.name.toString();
          console.log(model.name, "点击的模型名");
          if (
            !model.name.includes("Cylinder") &&
            !model.name.includes("Cube") &&
            !model.name.includes("立方体") &&
            !model.name.includes("网格") &&
            !model.name.includes("ding") &&
            !model.name.includes("tube") &&
            !isNaN(model.name) // 追加条件，判断是否为数字
          ) {
            view.setOutlineModel([model]);
          } else {
            view.setOutlineModel([]);
          }
          if (
            !model.name.includes("Cylinder") &&
            !model.name.includes("Cube")
          ) {
            if (model.modelId) {
              console.log(model, "模型信息");
              if (
                projectname == "ao组态" ||
                projectname == "内部冷热源组态" ||
                projectname == "正大天晴"
              ) {
                // addlablenewall(
                //   view.getObjCenterByModelIds([model.modelId])
                addlablenew(
                  view.getObjCenterByModelIds([model.modelId]),
                  false
                );
                // );
              } else if (projectname == "内部组态") {
                // addlablenewall(
                //   view.getObjCenterByModelIds([model.modelId])
                addlablenew(
                  view.getObjCenterByModelIds([model.modelId]),
                  false
                );
                // );
              } else {
                addlablenew(view.getObjCenterByModelIds([model.modelId]), false);
              }

              postxx(model.modelId);
            } else {
              addlable(view.getObjCenterByNames([model.name]));
              postxx(model.name);
            }
          }
        }
      }

      //给上层发送消息
      function postxx(data) {
        window.parent.postMessage(
          {
            type: "Message",
            name: "Message",
            param: {
              data,
            },
          },
          "*"
        );
      }

      // 你需要替换view.add3dSprite和view.nameVisible方法调用以适应你的3D视图库API。

      // 同样的，确保deviceLabels对象在函数外部正确初始化，并且你的视图库提供了相应的方法来控制3D精灵的可见性。
      window.onload = function () {
        // 禁用文本选择
        document.addEventListener("selectstart", function (e) {
          e.preventDefault();
        });

        // 禁用复制
        document.addEventListener("copy", function (e) {
          e.preventDefault();
        });
      };
      document.addEventListener("contextmenu", function (event) {
        // 取消右击事件的默认行为
        event.preventDefault();
      });
    </script>
    <script src="./js/config-list.js"></script>
    <!-- <script src="./js/center.js"></script> -->
    <!-- <script src="./js/line.js"></script> -->
  </body>
</html>
