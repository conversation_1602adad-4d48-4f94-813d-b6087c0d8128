(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-13f8916c"],{4021:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.loading,expression:"loading",modifiers:{fullscreen:!0,lock:!0}}],staticClass:"energy-summary"},[l("h3",[e._v(" 综合用能 -- (2021年度) "),l("router-link",{staticClass:"menu pull-right",attrs:{to:{path:"/"}}},[l("svg-icon",{attrs:{"icon-class":"back","class-name":"card-panel-icon"}}),l("span",[e._v("返回")])],1)],1),e.tableDatas.length>0?l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tableDatas,border:"",stripe:"","empty-text":"--"},on:{"cell-click":e.handleCellClick,"cell-class-name":e.cellClassName}},e._l(e.tableTitles,(function(e,t){return l("el-table-column",{key:t,attrs:{prop:e.prop,label:e.label,width:e.width}})})),1):e._e(),l("el-dialog",{attrs:{width:"25%",visible:e.infoShow},on:{"update:visible":function(t){e.infoShow=t}}},[l("span",{attrs:{slot:"title"},domProps:{textContent:e._s("单位面积供暖能耗")},slot:"title"}),l("el-form",{attrs:{"label-width":"80px"}},[l("el-form-item",{attrs:{label:"约束值"}},[l("el-tag",{attrs:{effect:"dark",type:"danger"}},[e._v("17.5")])],1),l("el-form-item",{attrs:{label:"基准值"}},[l("el-tag",{attrs:{effect:"dark",type:"warning"}},[e._v("10.6")])],1),l("el-form-item",{attrs:{label:"引导值"}},[l("el-tag",{attrs:{effect:"dark",type:"success"}},[e._v("8.5")])],1)],1),l("el-divider"),l("p",[e._v(" 大于约束值的每年节能降幅需大于4%"),l("br"),e._v(" 大于基准2%,大于引导值维持不变"),l("br"),e._v(" 小于引导值增幅不大于2% ")])],1),l("el-dialog",{attrs:{width:"fit-content",visible:e.detailShow},on:{"update:visible":function(t){e.detailShow=t}}},[l("span",{attrs:{slot:"title"},slot:"title"},[l("h3",{staticClass:"pull-left",staticStyle:{margin:"0px","margin-right":"30px"}},[e._v("嘉兴本部大楼")]),l("el-select",{staticClass:"pull-left",staticStyle:{"vertical-align":"middle"},attrs:{placeholder:"请选择显示方式",clearable:""},model:{value:e.chartType,callback:function(t){e.chartType=t},expression:"chartType"}},e._l(e.tableTitles,(function(e){return l("el-option",{key:e.prop,attrs:{label:e.label,value:e.prop}})})),1),l("div",{staticClass:"clearfix"})],1),l("normal-chart",{staticClass:"chart",attrs:{width:"500px",opts:e.chartOption}})],1)],1)},o=[],r=l("877f"),i={name:"energyDashboard",components:{NormalChart:r["default"]},data:function(){return{loading:!0,buildingId:this.gf.getBuildingId(),displayType:"day",infoShow:!1,detailShow:!1,chartDatas:{legend:[],xAxisData:[],series:[]},tableTitles:[{prop:"name",label:"建筑名称",width:""},{prop:"person",label:"用能人数",width:""},{prop:"avgEnergyPerson",label:"人均综合用能(kgce/p)",width:""},{prop:"avgElecPerson",label:"人均电耗(kWh/p)",width:""},{prop:"avgWaterPerson",label:"人均用水(㎥/p)",width:""},{prop:"effectiveness",label:"数据中心机房能源使用效率(%)",width:""},{prop:"avg1",label:"单位面积供暖能耗(kgce/㎥)",width:""},{prop:"avg2",label:"单位面积非供暖能耗(kgce/㎥)",width:""},{prop:"avg3",label:"单位面积电耗(kWh/㎥)",width:""},{prop:"avg4",label:"单位面积水耗)㎥/㎥)",width:""}],tableDatas:[{name:"嘉兴本部大楼",person:"100",avgEnergyPerson:"11.2",avgElecPerson:"11",avgWaterPerson:"11",effectiveness:"11",avg1:"11",avg2:"11",avg3:"11",avg4:"11",class:""},{name:"融通商务大厦",person:"100",avgEnergyPerson:"11.2",avgElecPerson:"11",avgWaterPerson:"11",effectiveness:"11",avg1:"11",avg2:"11",avg3:"11",avg4:"11",class:""},{name:"滨海大楼",person:"100",avgEnergyPerson:"11.2",avgElecPerson:"11",avgWaterPerson:"11",effectiveness:"11",avg1:"11",avg2:"11",avg3:"11",avg4:"11",class:""}],chartType:"avgEnergyPerson",chartOption:{xAxis:{type:"category",data:["01","02","03","04","05","06","07","08","09","10","11","12"]},yAxis:{type:"value"},visualMap:{top:50,right:0,textStyle:{color:"#eee"},pieces:[{gt:0,lte:8.9,color:"green"},{gt:8.9,lte:10.6,color:"yellow"},{gt:10.6,lte:17.5,color:"orange"}],outOfRange:{color:"red"}},series:[{data:[9.2,9,8.6,8.8,7.9,21,16],type:"bar",barWidth:"40%",showBackground:!0,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)"},markLine:{silent:!0,lineStyle:{color:"#333"},data:[{yAxis:8.9},{yAxis:10.6},{yAxis:17.5}]}}]}}},created:function(){},mounted:function(){this.loading=!1},methods:{handleCellClick:function(e,t,l,a){console.log(arguments),"建筑名称"==t.label?this.detailShow=!this.detailShow:this.infoShow=!this.infoShow},cellClassName:function(e,t,l,a){console.log(arguments)}}},s=i,n=l("2877"),c=Object(n["a"])(s,a,o,!1,null,null,null);t["default"]=c.exports},5403:function(e,t,l){"use strict";var a=l("ed08");t["a"]={data:function(){return{$_sidebarElm:null,$_resizeHandler:null}},mounted:function(){this.initListener()},activated:function(){this.$_resizeHandler||this.initListener(),this.resize()},beforeDestroy:function(){this.destroyListener()},deactivated:function(){this.destroyListener()},methods:{$_sidebarResizeHandler:function(e){"width"===e.propertyName&&this.$_resizeHandler()},initListener:function(){var e=this;this.$_resizeHandler=Object(a["c"])((function(){e.resize()}),100),window.addEventListener("resize",this.$_resizeHandler),this.$_sidebarElm=document.getElementsByClassName("sidebar-container")[0],this.$_sidebarElm&&this.$_sidebarElm.addEventListener("transitionend",this.$_sidebarResizeHandler)},destroyListener:function(){window.removeEventListener("resize",this.$_resizeHandler),this.$_resizeHandler=null,this.$_sidebarElm&&this.$_sidebarElm.removeEventListener("transitionend",this.$_sidebarResizeHandler)},resize:function(){var e=this.chart;e&&e.resize()}}}},"877f":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{class:e.className,style:{height:e.height,width:e.width}})},o=[],r=l("5403");l("a524");var i={mixins:[r["a"]],props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"350px"},autoResize:{type:Boolean,default:!0},opts:{type:Object,required:!0}},data:function(){return{chart:null,defatulOpts:{darkMode:!0,backgroundColor:"transparent",color:this.gf.chartColors(),xAxis:{},grid:{left:10,right:80,bottom:20,top:30,containLabel:!0},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},yAxis:{},legend:{data:[]},series:[]},class2type:{}}},watch:{opts:{deep:!0,handler:function(e){this.setOptions(e)}}},mounted:function(){var e=this;this.$nextTick((function(){e.initChart()}))},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{initChart:function(){this.chart=this.$echarts.init(this.$el,"dark"),this.setOptions(this.opts)},setOptions:function(e){var t=this,l=this.gf.extend(!0,this.defatulOpts,this.opts);console.log("NormalChart -----\x3e ",l),this.chart&&(this.chart.clear(),this.$nextTick((function(){t.chart.setOption(l),t.chart.resize()})))}}},s=i,n=l("2877"),c=Object(n["a"])(s,a,o,!1,null,null,null);t["default"]=c.exports},a524:function(e,t,l){var a,o,r;(function(i,s){o=[t,l("313e")],a=s,r="function"===typeof a?a.apply(t,o):a,void 0===r||(e.exports=r)})(0,(function(e,t){var l=function(e){"undefined"!==typeof console&&console&&console.error&&console.error(e)};if(t){var a="#B9B8CE",o="#100C2A",r=function(){return{axisLine:{lineStyle:{color:a}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},i=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],s={darkMode:!0,color:i,backgroundColor:o,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:a}},textStyle:{color:a},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:a}},dataZoom:{borderColor:"#71708A",textStyle:{color:a},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:a}},timeline:{lineStyle:{color:a},label:{color:a},controlStyle:{color:a,borderColor:a}},calendar:{itemStyle:{color:o},dayLabel:{color:a},monthLabel:{color:a},yearLabel:{color:a}},timeAxis:r(),logAxis:r(),valueAxis:r(),categoryAxis:r(),line:{symbol:"circle"},graph:{color:i},gauge:{title:{color:a}},candlestick:{itemStyle:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}};s.categoryAxis.splitLine.show=!1,t.registerTheme("dark",s)}else l("ECharts is not Loaded")}))}}]);