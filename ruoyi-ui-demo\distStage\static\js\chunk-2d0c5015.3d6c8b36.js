(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c5015"],{"3cf5":function(t,n,o){"use strict";o.r(n);var e=function(){var t=this,n=t.$createElement,o=t._self._c||n;return o("div",{staticClass:"p20"},[o("p",{staticClass:"mb10",domProps:{textContent:t._s(t.message)}}),o("el-button",{attrs:{size:"mini"},on:{click:t.gotoHome}},[t._v("回到首页")])],1)},s=[],i=(o("4de4"),o("d3b7"),o("0643"),o("2382"),{created:function(){var t=this;this.getDicts("base_configs").then((function(n){t.conf=n.data.filter((function(n){"ssologinSuccess"==n.dictLabel?t.ssologinSuccess=n.dictValue:"ssologinFailed"==n.dictLabel&&(t.ssologinFailed=n.dictValue)}))}))},props:{},data:function(){return{message:this.$route.query.msg}},mounted:function(){},methods:{gotoHome:function(){window.location.href=window.location.origin}}}),c=i,a=o("2877"),l=Object(a["a"])(c,e,s,!1,null,null,null);n["default"]=l.exports}}]);