<template>
    <custCard :title="title" autoHeight>
        <div class="cont">
            <!-- <div class="sum">
                <div class="sum_item" v-for="item in energySavingsInformation.subjects" :key="item.code">
                    <img :src="item.icon" />
                    <div>
                        <div class="value"><span>{{ sumData[item.code] }}</span>{{ item.unit }}</div>
                        <div class="title">{{ item.title }}</div>
                    </div>
                </div>
            </div> -->
            <!-- 历史能耗 -->
            <div class="history">
                <div class="title">
                  <span v-if="energySavingsInformation.history">{{ energySavingsInformation.history.title }}</span>
                    <div>
<!--                        <span v-if="useAi == 1" style="padding: 0 20px;font-size: 14px;color: #2294FEFF">AI</span>-->
                        <!-- <el-button size="mini" @click="dialogFlag = true">{{ energySavingsInformation.history.buttonText }}</el-button> -->
                        <el-button size="mini" @click="historyChartTypeChange">{{historyChartType === 'bar' ? "趋势图" : "柱状图"}}</el-button>
                        <el-select size="mini" v-model="monthSelected" placeholder="月份" @change="getHistoryData">
                            <el-option v-for="item in monthList" :key="item" :label="item"
                                :value="item"></el-option>
                        </el-select>

                    </div>
                </div>
                <NormalChart height="336px" autoResize :opts="getOptionData"></NormalChart>
            </div>
            <!--  节能分析-->
            <analysis :data="energySavingsInformation.analysis || {}" :dataOverview="dataOverview"></analysis>
        </div>
        <MeasuredDataDialog
          v-if="dialogFlag"
          :visible.sync="dialogFlag"
          :defaultMonth="monthSelected"
          :curBuildingId="curBuilding.id" />
    </custCard>

</template>

<script>
import custCard from "../components/custCard.vue";
import analysis from "../components/analysis.vue";
import MeasuredDataDialog from "./energySavingsInformationMeasuredData.vue";
import NormalChart from "@/views/components/NormalChart";
import BaseChart from "../../BaseChart.vue";
import {
  buildingSummaryData,
  groupEnergyDataList,
  deviceEnergyDataList
} from "@/api/energy/apis";
import {resourceDevice} from "@/api/device/apis";
export default {
    components: {
        custCard,
        NormalChart,
        MeasuredDataDialog,
        analysis,
        BaseChart
    },
    props: {
        "title": {
            type: String,
            default: "系统总能耗",
        },
        "energySavingsInformation": {
            type: Object,
            default: () => { },
        },
        height: {
            type: String,
            default: "auto"
        },
        "dataOverview": {
            type: Array,
            default () {
                return []
            },
        }
    },
    data() {
        return {
            historyMonthOptions: [],
            monthSelected: 1,
            monthList: [],
            curBuilding: this.gf.getCurBuilding(),
            settings: this.gf.projectSettings(),
            sumData: {
                monthEnergySaving: 8768,
                monthCarbonCuts: 4.364,
                carbonReductionTotal: 4.364,
            },
            dialogFlag: false,
            dataList: {
                unoptimized: [],
                optimize: []
            },
            historyChartType: 'bar',//line bar
            useAi: 0
        };
    },
    computed: {
        getOptionData() {
            const {unoptimized = [],optimize = []} = this.dataList;
            const unoptimizedList = unoptimized.map(item => {
              return {
                value: item.totalVal,
                itemStyle: {color: item.aiSwitch == 1 ? '#2294FE' : '#E45757'}
              }
            });
            const optimizeList = optimize.map(item => item.totalVal);
            const xAxisData = unoptimized.map(item => {
                if(item.recordedAt) {
                    return item.recordedAt.slice(-2)
                } else {
                    return false
                }
            })
            let sColor = this.useAi == 1 ? 'rgba(34, 148, 254, 1)' : '#E45757';
            return {
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow'
                },
                formatter: function(params) {
                  // params 是数组，因为可能多个系列的数据在同一位置
                  const data = params[0];
                  return `
                  <div style="font-weight:bold">${data.name}号</div>
                  <div>数值: ${data.value}</div>
                `;
                }
              },
                dataZoom: [
                    {
                        type: 'inside'
                    },
                ],
                xAxis: {
                    show: true,
                    type: 'category',
                    boundaryGap: ['20%', '20%'],
                    data: xAxisData,
                    axisLine: {
                        show: true,
                        lineStyle: {
                            width: 1,
                            color: "rgba(52, 137, 191, 1)"
                        }
                    },
                    axisTick: {
                        show: true,
                        inside: false,
                        length: 5,
                        lineStyle: {
                            width: 1,
                            color: "rgba(217, 217, 217, 1)"
                        }
                    },
                    axisLabel: {
                        show: true,
                        fontSize: 12, // 根据需要调整字体大小
                    },

                },
                yAxis: [
                    {
                        type: 'value',
                        name: '单位：kWh',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(52, 137, 191, 1)'
                            }
                        },
                        axisLabel: {
                            textStyle: {
                                color: "rgba(217, 217, 217, 1)"
                            }
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgba(0, 83, 209, 0.3)'
                            }
                        },
                        nameTextStyle: {
                            align: 'center',
                            color: 'rgb(255, 255, 255)',
                            fontSize: 12,
                            fontFamily: 'Source Han Sans CN'
                        },
                    }
                ],
                legend: {
                    data: [
                    {
                        name: "AI节能模式",
                        itemStyle: {
                            color: 'rgba(34, 148, 254, 1)'
                        }
                    },
                    {
                        name: "非节能模式",
                        itemStyle: {
                            color: '#E45757'
                        }
                    }
                    ]
                },
                grid: {
                    left: '3%',
                    right: '5%',
                    bottom: '0%',
                    containLabel: true
                },
                series: [
                    {
                        name: "AI节能模式",
                        xAxisIndex: 0,
                        animationDuration: 500,
                        animationEasing: 'quadraticOut',
                        smooth: 0.6,
                        symbol: 'none',
                        data: unoptimizedList,
                        type: this.historyChartType,
                        sampling: 'average',
                        stack: 'dt',
                        // lineStyle: {
                        //     color: sColor,
                        //     width: 1
                        // },
                        // itemStyle: {
                        //     color: sColor,
                        // },
                        areaStyle: {
                            color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: sColor
                                },
                                {
                                    offset: 1,
                                    color: sColor
                                }
                            ])
                        },
                    },
                    {
                        name: '非节能模式',
                        data: optimizeList,
                        type: this.historyChartType,
                        xAxisIndex: 0,
                        animationDuration: 500,
                        animationEasing: 'quadraticOut',
                        smooth: 0.6,
                        symbol: 'none',
                        stack: 'dt',
                        lineStyle: {
                            color: '#E45757',
                            width: 1
                        },
                        itemStyle: {
                            color: `#E45757`,
                        },
                        areaStyle: {
                            color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgba(34, 148, 254, .5)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(34, 148, 254, 0)'
                                }
                            ])
                        },
                    }
                ]
            }
        }
    },
    methods: {
        getSummaryData () {
            // 载入能耗数据
            buildingSummaryData({
                buildingId: this.curBuilding.id,
            }).then(({ data }) => {
                if (data && data.length > 0) {
                    //节电量
                    const electricityReduction = data.find(item => item.type === 'electricityReduction') || {};
                    //减碳量
                    const carbonReduction = data.find(item => item.type === 'carbonReduction') || {};
                    // this.sumData = {
                    //     monthEnergySaving: (electricityReduction.curMonth || 0).toFixed(2),
                    //     monthCarbonCuts: (carbonReduction.curMonth || 0).toFixed(2),
                    //     carbonReductionTotal: (carbonReduction.total || 0).toFixed(2),
                    // }
                }
            });
        },
        getApiData (groupName,field) {
            const valStr = this.settings['screenEnergySave'] || {};
            const data = valStr ? JSON.parse(valStr) : {};
            const energySavingsInformation = data.energySavingsInformation || {};

            const firstDay = this.$moment(this.monthSelected).startOf('months').format('YYYY-MM-DD')
            const lastDay = this.$moment(this.monthSelected).endOf('months').format('YYYY-MM-DD')
            deviceEnergyDataList({
                buildingId: this.curBuilding.id,
                from: firstDay,
                to: lastDay,
                displayType: 'day',
                deviceIds: energySavingsInformation.history?.aiDeviceIds || '',
                aiDataId: energySavingsInformation.history?.aiDataId || ''
            }).then(({data = []}) => {
                this.dataList[field] = [...data]
            })
        },
        getHistoryData() {
            this.getApiData('总表','unoptimized');
            // this.getApiData('AI分组','optimize');
        },
        historyChartTypeChange () {
            this.historyChartType = this.historyChartType === 'line' ? 'bar' : 'line'
        },
        getAiData(){
          const valStr = this.settings['controlCenter_diagram'] || {};
          const data = valStr ? JSON.parse(valStr) : {};
          resourceDevice({ deviceId:data.aiAlgorithmDeviceId }).then(({ data }) => {
            if(data && data.deviceDataBase){
              data.deviceDataBase.forEach(da => {
                if(da.dmTag.includes('useAi')){
                  this.useAi = da.dVal
                  console.log('................data.........',da.dVal)
                }
              })
            }
          });
        }
    },
    mounted() {
        this.monthSelected = this.$moment().format('YYYY-MM');
        const monthList = [];
        // 获取当前日期
        const currentDate = this.$moment();
        for (let i = 0; i < 12; i++) {
            const month = currentDate.clone().subtract(i, 'months').format('YYYY-MM');
            monthList.push(month);
        }
        this.monthList = [...monthList];

        this.getSummaryData();
        this.getHistoryData();
        // this.getAiData();
    }
};
</script>

<style scoped lang="scss">
.cont {
    .sum {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 10px 0;
        &_item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex: 1;

            img {
                width: 44px;
            }

            .value {
                span {
                    font-size: 18px;
                    color: #3CCCF9;
                }

                font-weight: 400;
                font-size: 14px;
                color: #D9D9D9;
            }

            .title {
                font-size: 12px;
                color: #FFFFFF;
            }
        }
    }

    .history{
        margin: 18px 0 0;
        .title {
            height: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: url('/image/screen/airConditioning_powerOn_bg.png') no-repeat center center;
            background-size: 100% 100%;
            padding: 2px 0 2px 12px;
            font-size: 16px;
            line-height: 16px;
            color: #FFFFFF;
            margin-bottom: 18px;
            ::v-deep .el-input {
                width: 90px;
                margin-left: 12px;
            }
        }
    }
}
</style>
