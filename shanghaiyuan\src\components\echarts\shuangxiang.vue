<template>
  <div class="echart" ref="echart"></div>
</template>
    
  <script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      let yAxisData = ["0", "200", "400", "600", "800", "1000", "1200"];
      let data1 = [10, 19, 23, 43, 34, 53, 12, 34];
      let data2 = [5, 12, 10, 7, 32, 40, 28, 34];

      const option = {
        title: {
          text: "宽度(m/m)",
          textStyle: {
            color: "#fff",
            fontSize: 8,
          },
        },

        tooltip: {
          show: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["耗电比例", "运行比例"],
          top: 4,
          right: "34%",
        itemWidth: 6,
        itemHeight:6,
          textStyle: {
            color: "#fff",
            fontSize: 8,
          },
        },

        grid: [
          {
            show: false,
            left: "10%",
            top: "15%",
            width: "40%",
            containLabel: true,
            bottom: 0,
          },
          {
            show: false,
            left: "4%",
            top: 10,
            bottom: 0,
            width: "0%",
          },
          {
            show: false,
            left: "50%",
            top: "15%",
            bottom: 0,
            containLabel: true,
            width: "40%",
          },
        ],
        xAxis: [
          {
            type: "value",
            inverse: true,
            axisLabel: {
              show: true,
              color: "#fff",
              margin: 0,
              fontSize: 8,
              formatter: function (value) {
                return value === 0 ? "0" : "-" + value + "%";
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          {
            gridIndex: 1,
            show: true,
            axisLabel: {
              color: "#fff",
              margin: 0,
              fontSize: 8,
              formatter: function (value) {
                return value === 0 ? "0" : value + "%";
              },
            },
            splitLine: {
              lineStyle: {
                color: "#fff",
                type: "dashed",
              },
            },
          },
          {
            gridIndex: 2,
            type: "value",
            axisLabel: {
              show: true,
              color: "#fff",
              margin: 0,
                fontSize: 8,
              formatter: function (value) {
                return value === 0 ? "0" : value + "%";
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: "category",
            inverse: false,
            position: "right",
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff",
              },
            },
            axisTick: {
              show: false,
            },
            data: yAxisData,
          },
          {
            type: "category",
            inverse: false,
            gridIndex: 1,
            position: "left",
            axisLabel: {
              align: "left",
              padding: [8, 0, 0, 0],
              fontSize: 8,
              fontWeight: 500,
              color: `#fff`,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#fff",
              },
            },
            axisTick: {
              show: false,
            },
            data: yAxisData,
          },
          {
            type: "category",
            inverse: false,
            gridIndex: 2,
            position: "left",
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#979797",
              },
            },
            axisTick: {
              show: false,
            },
            data: yAxisData,
          },
        ],
        series: [
          {
            type: "bar",
            barWidth: 8,
            name: "耗电比例",
            label: {
              normal: {
                show: false,
              },
            },
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(227, 86, 37, 0.78)",
                    },
                    {
                      offset: 1,
                      color: "rgba(227, 86, 37, 0.1)",
                    },
                  ],
                  globalCoord: false,
                },
              },
            },
            data: data1,
          },
          {
            type: "bar",
            barWidth: 8,
            xAxisIndex: 2,
            yAxisIndex: 2,
            name: "运行比例",
            label: {
              normal: {
                show: false,
              },
            },
            itemStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(126,253,225, 0.1)",
                    },
                    {
                      offset: 1,
                      color: "rgba(126,253,225, 0.78)",
                    },
                  ],
                  globalCoord: false,
                },
              },
            },
            data: data2,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
  <style lang="less" scoped>
.echart {
  width: 100%;
  height: 160px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 189px !important;
  }
}
</style>