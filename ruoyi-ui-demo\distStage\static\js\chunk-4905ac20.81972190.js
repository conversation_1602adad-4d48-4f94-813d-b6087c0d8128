(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4905ac20"],{"0f06":function(t,e,a){"use strict";a.r(e);a("b64b"),a("d3b7"),a("25f0"),a("0643"),a("4e3e"),a("159b");e["default"]={data:function(){return{timeouts:{_rotate:null,_move:null,_scale:null,_startHeart:null,_heart:null,_touch:null,_high:null,_process:null,_ws:null,_query:null,_reconnect:null,_loadStatus:null,_loaded:null,_getCtx:null,_absorbPoint:null,_reviseMeasurePoint:null},intervals:{_loadStatus:null},duration:40}},methods:{clearTimeout:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){var e=this;t?(clearTimeout(this.timeouts[t]),this.timeouts[t]=null):Object.keys(this.timeouts).forEach((function(t){e.timeouts[t]&&(clearTimeout(e.timeouts[t]),e.timeouts[t]=null)}))})),clearInterval:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t){var e=this;t?(clearInterval(this.intervals[t]),this.intervals[t]=null):Object.keys(this.intervals).forEach((function(t){e.intervals[t]&&(clearInterval(e.intervals[t]),e.intervals[t]=null)}))})),setTimeout:function(t){function e(e,a){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.duration;this.timeouts[t]=setTimeout(e,a)})),setInterval:function(t){function e(e,a){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:50,i=arguments.length>4?arguments[4]:void 0,n=!!s;this.intervals[t]=setInterval(n?function(){e(),s--,0===s&&i()}:e,a)}))},beforeDestroy:function(){this.clearTimeout(),this.clearInterval()}}},"39f4":function(t,e,a){"use strict";a.r(e);a("d81d"),a("a573");var s,i,n=a("31d7"),r={data:function(){return{buildingId:0,todayStr:"",energyTypeList:[],energyTypes:{},deviceTypeText:"",activeYear:"",curYear:"",compYear:""}},created:function(){var t=this;this.buildingId=this.gf.getBuildingId(),this.todayStr=this.$moment().format("YYYY-MM-DD"),this.curYear=this.$moment().format("YYYY"),this.getEnergyTypeList().then((function(e){t.getDicts("e_device_type").then((function(e){var a=t;e.data.map((function(t){t.dictValue==a.deviceType&&(a.deviceTypeText=t.dictLabel)})),"function"==typeof t.baseLoadedCallback&&t.baseLoadedCallback()}))}))},methods:{getEnergyTypeList:function(){var t=this;return Object(n["i"])({buildingId:this.buildingId}).then((function(e){t.energyTypeList=e.data,t.energyTypes={},t.energyTypeList.map((function(e){t.energyTypes[e.type]=e}))}))}}},o=r,h=a("2877"),c=Object(h["a"])(o,s,i,!1,null,null,null);e["default"]=c.exports},"3ea1":function(t,e,a){"use strict";a.r(e);var s=a("dc56");e["default"]={data:function(){return{}},methods:{handleTouchStart:function(t){console.log("touch事件开始:",t);var e=document.querySelector(".cbim-model-viewer__model"),a=e&&e.offsetLeft||0,i=e&&e.offsetTop||0;this.handlers._isMouseDown=!0,this.handlers._isMouseMove=!1,this.handlers._isRotate=!1,this.startTouches=[],this.touchPosition.x=t.changedTouches[0].pageX,this.touchPosition.y=t.changedTouches[0].pageY,this.startTouches=t.touches;var n=t.touches.length;switch(n){case 1:this.handlers._currentX=this.handleCanvasOrigin(t.changedTouches[0].pageX-a)*this.performance/this.canvas.realWidth,this.handlers._currentY=(t.changedTouches[0].pageY-i)*this.performance/this.canvas.realWidth,this.handlers._currentRealX=this.handleCanvasOrigin(t.changedTouches[0].pageX-a),this.handlers._currentRealY=t.changedTouches[0].pageY-i,this.show.sectionBox&&this.handleSectionBoxStart(this.handleCanvasOrigin(t.changedTouches[0].pageX-a),t.changedTouches[0].pageY-i,s["EVENTS"].DATA_TYPE_SECTION_BOX_START);break;case 2:this.handlers._currentX=this.handleCanvasOrigin((t.changedTouches[0].pageX+t.changedTouches[1].pageX)/2-a)*this.performance/this.canvas.realWidth,this.handlers._currentY=((t.changedTouches[0].pageY+t.changedTouches[1].pageY)/2-i)*this.performance/this.canvas.realWidth,this.handlers._currentRealX=this.handleCanvasOrigin((t.changedTouches[0].pageX+t.changedTouches[1].pageX)/2)-a,this.handlers._currentRealY=(t.changedTouches[0].pageY+t.changedTouches[1].pageY)/2-i,this.handlers._isMouseMove=!0;break}},_distance:function(t,e,a,s){return Math.sqrt(Math.pow(t-a,2)+Math.pow(e-s,2))},handleTouchMove:function(t){var e=this,a=t.touches.length,i=document.querySelector(".cbim-model-viewer__model"),n=i&&i.offsetLeft||0,r=i&&i.offsetTop||0;switch(a){case 1:this.handlers._isMouseMove||(this.show.sectionBox?this.handleSectionBoxChange(this.handleCanvasOrigin(t.touches[0].pageX-n),t.touches[0].pageY-r,s["EVENTS"].DATA_TYPE_SECTION_BOX_CHANGE):(this.handlers._isRotate=!0,this.handleRotate(s["EVENTS"].DATA_TYPE_ROTATE,this.handleCanvasOrigin(t.touches[0].pageX-n),t.touches[0].pageY-r)));break;case 2:if(!this.timeouts._move){var o=this._distance(this.startTouches[0].pageX,this.startTouches[0].pageY,this.startTouches[1].pageX,this.startTouches[1].pageY),h=this._distance(t.touches[0].pageX,t.touches[0].pageY,t.touches[1].pageX,t.touches[1].pageY);if(Math.abs(h-o)>4){h-o<0?this.initData(s["EVENTS"].DATA_TYPE_SMALLER):this.initData(s["EVENTS"].DATA_TYPE_BIGGER);var c=this.handleCanvasOrigin((t.changedTouches[0].pageX+t.changedTouches[1].pageX)/2-n),l=(t.changedTouches[0].pageY+t.changedTouches[1].pageY)/2-r;this._zoom(h-o,c,l),this.startTouches=t.touches}else this.handleMove(this.handleCanvasOrigin((t.changedTouches[0].pageX+t.changedTouches[1].pageX)/2-n),(t.changedTouches[0].pageY+t.changedTouches[1].pageY)/2-r);this.setTimeout("_move",(function(){e.clearTimeout("_move")}))}break}},handleTouchEnd:function(t){var e=this;console.log("touch事件结束:",t);var a=document.querySelector(".cbim-model-viewer__model"),s=a&&a.offsetLeft||0,i=a&&a.offsetTop||0,n=this.handleCanvasOrigin(t.changedTouches[0].pageX-s),r=t.changedTouches[0].pageY-i;(!this.startTouches||this.startTouches.length<=1)&&(this.handlers._isClick?(this.clearTimeout("_touch"),this.touchTimeout=null,this.handlers._isDbClick=!0,this.handlers._isClick=!1,this.handleCanvasDblClick({x:n,y:r})):this.handlers._isMouseMove||this.handlers._isRotate||this.show.sectionBox||(this.handlers._isDbClick=!1,this.handlers._isClick=!0,this.setTimeout("_touch",(function(){e.handlers._isClick=!1,e.handleClick(n,r)}),400)))}}}},"46bc":function(t,e,a){"use strict";a.r(e);a("14d9"),a("c19f"),a("ace4"),a("2c66"),a("249d"),a("40e9"),a("d3b7");e["default"]={data:function(){return{params:{uuid:"",type:"",extraType:0,dx:"",dy:"",posx:"",posy:"",anx:-1,asx:1,any:-1,asy:1,anz:-1,asz:1,messageId:-1,future:-1}}},methods:{stringToByte:function(t){var e,a,s=[];e=t.length;for(var i=0;i<e;i++)a=t.charCodeAt(i),a>=65536&&a<=1114111?(s.push(a>>18&7|240),s.push(a>>12&63|128),s.push(a>>6&63|128),s.push(63&a|128)):a>=2048&&a<=65535?(s.push(a>>12&15|224),s.push(a>>6&63|128),s.push(63&a|128)):a>=128&&a<=2047?(s.push(a>>6&31|192),s.push(63&a|128)):s.push(255&a);return s},transData:function(){var t=this.stringToByte(this.params.uuid||""),e=new ArrayBuffer(60+t.length),a=new DataView(e);a.setInt32(0,this.params.type),a.setInt32(4,this.params.extraType),a.setInt32(8,this.params.dx),a.setInt32(12,this.params.dy),a.setFloat32(16,this.params.posx),a.setFloat32(20,this.params.posy),a.setFloat32(24,this.params.anx),a.setFloat32(28,this.params.asx),a.setFloat32(32,this.params.any),a.setFloat32(36,this.params.asy),a.setFloat32(40,this.params.anz),a.setFloat32(44,this.params.asz),a.setBigInt64(48,BigInt(this.params.messageId)),a.setInt32(56,this.params.future);for(var s=t.length,i=0;i<s;i++)a.setUint8(60+i,t.shift());return e},initData:function(t){this.params.type=t,this.params.uuid="",this.params.extraType=0,this.params.dx=0,this.params.dy=0,this.params.posx=0,this.params.posy=0,this.params.anx=-1,this.params.asx=-1,this.params.any=-1,this.params.asy=-1,this.params.anz=-1,this.params.asz=-1,this.params.messageId=-1,this.params.future=-1}}}},5403:function(t,e,a){"use strict";var s=a("ed08");e["a"]={data:function(){return{$_sidebarElm:null,$_resizeHandler:null}},mounted:function(){this.initListener()},activated:function(){this.$_resizeHandler||this.initListener(),this.resize()},beforeDestroy:function(){this.destroyListener()},deactivated:function(){this.destroyListener()},methods:{$_sidebarResizeHandler:function(t){"width"===t.propertyName&&this.$_resizeHandler()},initListener:function(){var t=this;this.$_resizeHandler=Object(s["c"])((function(){t.resize()}),100),window.addEventListener("resize",this.$_resizeHandler),this.$_sidebarElm=document.getElementsByClassName("sidebar-container")[0],this.$_sidebarElm&&this.$_sidebarElm.addEventListener("transitionend",this.$_sidebarResizeHandler)},destroyListener:function(){window.removeEventListener("resize",this.$_resizeHandler),this.$_resizeHandler=null,this.$_sidebarElm&&this.$_sidebarElm.removeEventListener("transitionend",this.$_sidebarResizeHandler)},resize:function(){var t=this.chart;t&&t.resize()}}}},7737:function(t,e,a){"use strict";a.r(e),e["default"]={data:function(){return{show:{rect:!1},performanceOptions:[{label:"高",value:"HIGH_PERFORMANCE_SEND"},{label:"中",value:"MIDDLE_PERFORMANCE_SEND"},{label:"低",value:"LOW_PERFORMANCE_SEND"}],performanceSelect:"MIDDLE_PERFORMANCE_SEND",isShowProcess:!1,process:0,modelStatus:{firstPersonSpeed:1,firstPersonHeight:1.8,firstPersonOn:!1},routeParams:{disableMouseRightClickMultiChoose:!1},objectTrees:{}}}}},"87c0":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container"},[a("pie-chart",{directives:[{name:"show",rawName:"v-show",value:"all"==t.display||"pie"==t.display,expression:"display == 'all' || display =='pie'"}],ref:"pieChart",attrs:{chartDatas:t.chartPieDatas}}),a("bar-chart",{directives:[{name:"show",rawName:"v-show",value:"all"==t.display||"bar"==t.display,expression:"display == 'all' || display =='bar'"}],ref:"barChart",attrs:{chartDatas:t.chartBarDatas}})],1)},i=[],n=a("5530"),r=(a("4de4"),a("a15b"),a("d81d"),a("14d9"),a("13d5"),a("b0c0"),a("b680"),a("d3b7"),a("3ca3"),a("0643"),a("2382"),a("4e3e"),a("a573"),a("9d4a"),a("159b"),a("ddb0"),a("31d7")),o=a("e120"),h=a("d25c"),c={name:"能耗公共图表组件",components:{PieChart:o["default"],BarChart:h["default"]},props:{display:{type:String,default:"all",note:"显示类型, all|bar|pie"},deviceType:{type:String,default:"electricity",note:"能耗类型"},gName:{type:String,default:"",note:"一级分类名称. eg:分项用电"},from:{type:String,default:""},to:{type:String,default:""}},data:function(){return{loading:!0,buildingId:null,groups:[],groupDeviceIds:{},summaryData:[],chartBarDatas:{legend:[],xAxisData:[],series:[]},chartPieDatas:{legend:[],series:[]},tableTitles:[{prop:"date",label:"日期",width:""}],tableDatas:[]}},created:function(){this.buildingId=this.gf.getBuildingId()},mounted:function(){this.initGroups()},methods:{changetheme:function(t){this.$refs.barChart.changetheme("dark"),this.$refs.pieChart.changetheme("dark")},initGroups:function(){var t=this;Object(r["p"])({buildingId:this.buildingId}).then((function(e){t.groupDevices=e.data,e.data.map((function(e){t.groupDeviceIds[e.id]=e.device.length>0?e.device.map((function(t){return t.id})):[]}))})).then((function(){return Object(r["q"])({buildingId:t.buildingId,deviceType:t.deviceType})})).then((function(e){var a=e.data,s=t.handleTree(a,"id","parent"),i=s.filter((function(e){return e.name+""==t.gName}))[0],n=[];i.children.length<=0?(i.parent=t.gid,n=[i]):n=i.children,t.groups=n})).then((function(){t.getDeviceEnergyDatas()}))},getDeviceEnergyDatas:function(){var t=this;this.resetDatas(),this.timeSpan=this.from+" ~ "+this.to;var e=[];this.groups.map((function(a,s){e.push({deviceIds:t.groupDeviceIds[a.id].join(","),from:t.from,to:t.to,displayType:"month",year:t.$moment(t.to).format("YYYY"),typeName:a.name})})),this.generateTableData(e,this.tableTitles,this.tableDatas)},generateTableData:function(t,e,a,s){var i=this;this.loading=!0;var o=t.map((function(t,e){return Object(r["u"])(t)}));Promise.all(o).then((function(r){console.log(r),i.loading=!1,r.forEach((function(r,o){var h=t[o];h.baseData=r.data,i.gf.fmtTableDatas(e,a,h,o,{key:"recordedAt",val:"totalVal"},[{prop:"val"+o,label:h.typeName,width:null,func:function(t){return t}}]),i.gf.fmtChartDatas(i.chartBarDatas,h,a,o,t.length,s);try{i.$refs.barChart.refreshChart()}catch(c){}i.summaryData[o]=s?Object(n["a"])(Object(n["a"])({},i.summaryData[o]),{},{name:h.typeName,compValue:a.map((function(t){return t["val"+o]})).reduce((function(t,e){return t+e})).toFixed(2)}):Object(n["a"])(Object(n["a"])({},i.summaryData[o]),{},{name:h.typeName,value:a.map((function(t){return t["val"+o]})).reduce((function(t,e){return t+e})).toFixed(2)}),i.chartPieDatas.legend=i.chartBarDatas.legend,i.chartPieDatas.series=[{name:i.groupName,type:"pie",radius:"65%",center:["50%","50%"],selectedMode:"single",data:i.summaryData}];try{i.$refs.pieChart.refreshChart()}catch(c){}}))}))},resetDatas:function(){this.summaryData=[],this.chartBarDatas={legend:[],xAxisData:[],series:[]},this.chartPieDatas={legend:[],series:[]},this.tableTitles=[{prop:"date",label:"日期",width:""}],this.tableDatas=[]}}},l=c,d=a("2877"),u=Object(d["a"])(l,s,i,!1,null,null,null);e["default"]=u.exports},a2d5:function(t,e,a){"use strict";a.r(e);var s=a("2909"),i=(a("99af"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("e9c4"),a("a9e3"),a("b680"),a("b64b"),a("a573"),a("dc56"));a("2ef0");e["default"]={props:{isIFrame:Boolean},data:function(){return{targetOrigin:"*",heartCheckBack:!0,heartTimeout:null,startHeartTimeout:null,drawing:!1,latestDrawData:[],dxfCode:"",annotationOpen:!1,canHandleClick:!1,groupMap:{},objectTreeFinished:!1,objectTree:{},cachePath:[]}},beforeCreate:function(){window.addEventListener("message",this.handleMessageListener)},beforeDestroy:function(){window.removeEventListener("message",this.handleMessageListener)},methods:{handleMessageListener:function(t){this.handleReceiveMessage(t.data)},handlePostMessage:function(t,e){if(this.isIFrame)try{window.parent.postMessage({MSG:t,data:e},this.targetOrigin)}catch(a){console.log("postMessage传递失败")}else this.$emit("postMessage",{data:{MSG:t,data:e}})},handleReceiveMessage:function(t){var e={};try{e=JSON.parse(t||"{}")}catch(a){e={}}switch(this.clearDrawData(e),this.drawing=!1,e.MSG){case"MSG_MODEL_REQUEST_DATA":this.handleModelReceiveData(e.data);break;case"MSG_MODEL_NEW_REQUEST_DATA":this.handleModelNewReceiveData(e.data);break;case"MSG_SET_MODEL_STATUS":this.setModelStatus(e.data);break;case"MSG_BACK_MAIN_SENSE":this.handleBackMainSense();break;case"MSG_INIT_MODEL":this.handleInitModelCtrl({extraType:1});break;case"MSG_FULL_SCREEN":this.handleFullScreen();break;case"MSG_DRAW_CLOSE":break;case"MSG_TYPE_SECTION":this.handleSection(e.data);break;case"MSG_TYPE_SECTION_BOX":this.show.sectionBox=!0,this.handleSectionBox(e.data);break;case"MSG_TYPE_MOBILE_SIZE":this.handleRenderingLevel(e.data);break;case"MSG_OPEN_SUN_LIGHT":this.handleSunLightOpenCtrl(e.data);break;case"MSG_CLIENT_SNAPSHOT_SAVE":this.handleAddSnapshot(e.data);break;case"MSG_ANNOTATION_OPEN":this.annotationOpen=e.data;break;case"MSG_DEMO_ZOOM":this.handleCanvasWheel(e.data);break;default:break}},handleModelReceiveData:function(t){this.initData(t.type),Object.assign(this.params,t),this.sendData()},handleModelNewReceiveData:function(t){t.parameters=t.parameters||[],this.initData(t.type);var e=[t.extraType];"Array"==this.$utils.isClass(t.uuids)?e.push.apply(e,[t.uuids.length,t.parameters.length].concat(Object(s["a"])(t.uuids),Object(s["a"])(t.parameters))):t.uuids?e.push.apply(e,[1,t.parameters.length,t.uuids].concat(Object(s["a"])(t.parameters))):e.push.apply(e,[0,t.parameters.length].concat(Object(s["a"])(t.parameters))),t.uuid=e.join(";"),delete t.parameters,delete t.uuids,Object.assign(this.params,t),this.sendData()},setModelStatus:function(t){this.$set(this.modelStatus,t.key,t.value)},getHighPerformance:function(){var t=this;this.clearTimeout("_high"),this.setTimeout("_high",(function(){t.initData(i["EVENTS"].HIGH_PERFORMANCE),t.sendData(),t.clearTimeout("_high")}),300)},computeCoordinateX:function(t){var e=(t-this.canvas.offsetX)*this.performance/parseInt(this.canvas.realWidth);return e},computeCoordinateY:function(t){var e=(t-this.canvas.offsetY)*this.performance/parseInt(this.canvas.realWidth);return e},handleClick:function(t,e){this.canHandleClick?(this.initData(i["EVENTS"].DATA_TYPE_CLICK),this.params.posx=this.computeCoordinateX(t),this.params.posy=this.computeCoordinateY(e),this.annotationOpen?this.params.extraType=3:17==this.handlers._pressKey?this.params.extraType=2:16==this.handlers._pressKey&&(this.params.extraType=4),this.sendData()):(this.getHighPerformance(),console.log("can not selected object"))},handleRotate:function(t,e,a){var s=this;if(this.timeouts._rotate)return!1;this.initData(t),this.params.dx=e*this.performance/parseInt(this.canvas.realWidth)-this.handlers._currentX,this.params.dy=a*this.performance/parseInt(this.canvas.realWidth)-this.handlers._currentY,this.params.posx=this.computeCoordinateX(e),this.params.posy=this.computeCoordinateY(a),this.params.uuid=(new Date).getTime()+"",this.sendData(),this.setTimeout("_rotate",(function(){s.clearTimeout("_rotate")})),this.handlers._currentX=e*this.performance/parseInt(this.canvas.realWidth),this.handlers._currentY=a*this.performance/parseInt(this.canvas.realWidth)},handleMove:function(t,e){this.initData(i["EVENTS"].DATA_TYPE_MOBILE_MOVE),this.dxfCode&&(this.params.uuid=this.dxfCode),this.params.dx=t*this.performance/parseInt(this.canvas.realWidth)-this.handlers._currentX,this.params.dy=e*this.performance/parseInt(this.canvas.realWidth)-this.handlers._currentY,this.params.posx=this.computeCoordinateX(t),this.params.posy=this.computeCoordinateY(e),this.handlers._isMouseDown&&(this.sendData(),this.handlers._currentX=t*this.performance/parseInt(this.canvas.realWidth),this.handlers._currentY=e*this.performance/parseInt(this.canvas.realWidth))},handleRenderingLevel:function(t){this.initData(i["EVENTS"].DATA_TYPE_MOBILE_SIZE),this.params.extraType=Number(t.extraType),this.params.dx=t.dx||"",this.params.dy=t.dy||"",this.sendData();var e=t.type,a=e?e.slice(0,e.lastIndexOf("_")):"";this.performance=i["EVENTS"]["".concat(a,"_SELECT")]},handleZoomIn:function(t,e){this.initData(i["EVENTS"].DOUBLE_CLICK),this.params.posx=this.computeCoordinateX(t),this.params.posy=this.computeCoordinateY(e),this.sendData()},handleInitModelCtrl:function(t){this.initData(i["EVENTS"].INIT_MODEL_EVENT),this.params.uuid=this.model.itemVersionId,this.params.extraType=t.extraType,this.sendData()},handleModelGroundGridCtrl:function(t){this.initData(i["EVENTS"].MODEL_GROUND_GRID),this.params.extraType=t.extraType,this.sendData()},handleFullScreen:function(){var t=document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen,e=document.body;t?document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitExitFullscreen&&document.webkitExitFullscreen():e.requestFullscreen&&e.requestFullscreen()||e.mozRequestFullScreen&&e.mozRequestFullScreen()||e.webkitRequestFullscreen&&e.webkitRequestFullscreen()||e.msRequestFullscreen&&e.msRequestFullscreen()},handleResetModel:function(){this.initData(i["EVENTS"].MODEL_RESET_ALL),this.sendData()},handleSelectByRectSend:function(t,e,a){this.initData(i["EVENTS"].SELECT_COMPONENT_WITH_RECT),this.params.uuid="Strict",this.params.anx=a||1,this.params.dx=this.computeCoordinateX(t),this.params.dy=this.computeCoordinateY(e),this.params.posx=this.computeCoordinateX(this.handlers._currentRealX),this.params.posy=this.computeCoordinateY(this.handlers._currentRealY),this.sendData()},handleHeartCheck:function(){var t=this;console.log(">>发送心跳检测信息<<"),this.initData(i["EVENTS"].NORMAL_STATE),this.sendData(),this.clearTimeout("_heart"),this.setTimeout("_heart",(function(){t.heartCheckBack?(console.log(">>心跳返回成功，则继续下一次心跳检测<<"),t.handleStartHeartCheck()):(console.log(">>心跳检测失败，重新连接<<"),t.connectSuccess=!1,t.reconnectWS())}),1e4)},handleStartHeartCheck:function(){var t=this;this.clearTimeout("_heart"),this.clearTimeout("_startHeart"),this.heartCheckBack=!1,this.setTimeout("_startHeart",(function(){t.handleHeartCheck()}),18e4)},handleClearInstanceAndUnconnect:function(){this.initData(i["EVENTS"].CLEAR_INSTANCE_AND_UNCONNECT),this.sendData()},handleModelLoadStatusCtrl:function(t){this.initData(i["EVENTS"].MODEL_LOAD_STATUS),this.params.uuid=t.uuid||"",this.sendData()},handleBackMainSense:function(){this.initData(i["EVENTS"].DATA_TYPE_BACK_MAIN),this.sendData()},handleSection:function(t){this.initData(i["EVENTS"].DATA_TYPE_SECTION),this.params.asx=t.dataX.s,this.params.anx=t.dataX.n,this.params.asy=t.dataY.s,this.params.any=t.dataY.n,this.params.asz=t.dataZ.s,this.params.anz=t.dataZ.n,this.sendData()},handleSunLightOpenCtrl:function(t){this.initData(i["EVENTS"].OPEN_SUN_LIGHT),this.params.extraType=t.extraType,this.params.anx=Number(parseFloat(t.red/255*t.sunStrength).toFixed(2)),this.params.any=Number(parseFloat(t.green/255*t.sunStrength).toFixed(2)),this.params.anz=Number(parseFloat(t.blue/255*t.sunStrength).toFixed(2)),(t.x||t.y||t.z)&&(this.params.anx=t.x,this.params.any=t.y,this.params.anz=t.z),this.sendData(),this.handleQueryDataCtrl({extraType:0})},handleQueryDataCtrl:function(t){this.initData(i["EVENTS"].QUERY_DATA_UUID),this.params.extraType=t.extraType,this.sendData()},handleAddSnapshot:function(){this.initData(i["EVENTS"].CLIENT_SNAPSHOT_SAVE),this.params.extraType=-1,this.params.uuid=this.model.itemVersionId,this.sendData()},handleSectionBox:function(){this.initData(i["EVENTS"].DATA_TYPE_SECTION_BOX),this.params.extraType=1,this.sendData()},handleSectionBoxStart:function(t,e,a){this.initData(a),this.params.posx=this.computeCoordinateX(t),this.params.posy=this.computeCoordinateY(e),this.sendData()},handleSectionBoxChange:function(t,e,a,s){this.initData(a),this.params.uuid=s||"",this.params.dx=this.computeCoordinateX(t)-this.handlers._currentX,this.params.dy=this.computeCoordinateY(e)-this.handlers._currentY,this.params.posx=this.computeCoordinateX(t),this.params.posy=this.computeCoordinateY(e),this.handlers._currentX=this.computeCoordinateX(t),this.handlers._currentY=this.computeCoordinateY(e),this.sendData()},handleSectionBoxUp:function(t,e,a){this.initData(a),this.params.posx=this.computeCoordinateX(t),this.params.posy=this.computeCoordinateY(e),this.sendData()},clearDrawData:function(t){this.clientDrawData=[]},updateGroupTree:function(){this.initData(i["EVENTS"].MODEL_GROUP_TREE),this.params.uuid=JSON.stringify({extraType:"getAllGroups",includingUuids:"1"}),this.sendData()},updateGroupTreeCallBack:function(t){var e=this;console.log(t),this.groupMap={},t.map((function(t){e.groupMap[t.groupName]=t.groupUuid}))},hideSkyBox:function(){this.initData(i["EVENTS"].BACKGROUND_IMAGE_CTR),this.params.extraType=0,this.sendData()},hideGround:function(){this.initData(i["EVENTS"].BACKGROUND_IMAGE_CTR),this.params.extraType=5,this.sendData()},modifyBackgroundColor:function(t){this.initData(i["EVENTS"].MODIFY_BACKGROUND_COLOR),(t.x||t.y||t.z)&&(this.params.anx=t.x||.15,this.params.any=t.y||.2,this.params.anz=t.z||.2),this.sendData()},showSkyBox:function(){this.initData(i["EVENTS"].BACKGROUND_IMAGE_CTR),this.params.extraType=1,this.sendData()},showGround:function(){this.initData(i["EVENTS"].BACKGROUND_IMAGE_CTR),this.params.extraType=4,this.sendData()},handleSkyBoxSumTime:function(t){this.initData(i["EVENTS"].BACKGROUND_IMAGE_CTR),this.params.extraType=3,this.params.uuid="SkyBoxSunTime:"+24*t/100,this.sendData()},handleSkyBoxWeather:function(t){this.initData(i["EVENTS"].OPEN_CLOSE_PARTICLE),this.params.extraType=t,this.sendData()},handleScenesLighting:function(t){this.initData(i["EVENTS"].OBJECT_LIGHT),this.params.extraType=t?1:0,this.sendData()},handlePickupLighting:function(t){this.initData(i["EVENTS"].OBJECT_LIGHT),this.params.extraType=t?1:0,this.params.uuid=JSON.stringify({style:"original",rgba:{red:0,green:191,blue:255,alpha:.5},color:{red:0,green:191,blue:255,alpha:null},lineWidth:1,scale:3,strength:.8}),this.sendData()},fbxShow:function(){this.initData(i["EVENTS"].MODIFY_BACKGROUND_COLOR),this.params.uuid="fbx",this.params.type=16,this.params.extraType=1,this.sendData()},fbxHide:function(){this.initData(i["EVENTS"].MODIFY_BACKGROUND_COLOR),this.params.uuid="fbx",this.params.type=16,this.params.extraType=0,this.sendData()},handleReloadScene:function(t){console.log("handleReloadScene"),this.initData(i["EVENTS"].MODEL_SAVE_RECOVER_SCENE),this.params.extraType=1,this.params.uuid=t||'{"type":"CloudRendering","modelVersions":["8140"]}',this.sendData()},handleAutoRotationStart:function(){this.initData(i["EVENTS"].MODEL_SCENE_AUTO_ROTATION),this.sendData()},handleAutoRotationStop:function(){this.initData(i["EVENTS"].MODEL_SCENE_AUTO_ROTATION_STOP),this.params.extraType=3,this.sendData()},handleLinkageModel:function(t){var e=this;t.length>0?(this.initData(i["EVENTS"].LINKAGE_MODEL),t.map((function(t){return Array.isArray(t.objects)&&t.objects.length>0&&(t.objects=t.objects.map((function(t){return e.groupMap[t]||t}))),t})),this.params.uuid=JSON.stringify({ldArr:t}),this.sendData()):console.log(i["EVENTS"].LINKAGE_MODEL,"Invalid 1066 ldArr",t)},handleObjectPickup:function(t){this.initData(i["EVENTS"].MODEL_GROUP_TREE),this.params.uuid=JSON.stringify({extraType:"highlight",groupUuid:t}),this.sendData()},handleGroupTree:function(){this.initData(i["EVENTS"].MODEL_GROUP_TREE),this.params.uuid=JSON.stringify({extraType:"getAllGroups",includingUuids:"1"}),this.sendData()},handleFilterMajor:function(t){this.initData(i["EVENTS"].MODEL_FILTER_MAJOR),this.params.extraType=1,this.params.uuid=JSON.stringify(t),this.sendData()},handleFilterComponent:function(t,e){this.initData(i["EVENTS"].BATCH_CHECK_COMPONENT),this.params.extraType=e?1:0,this.params.uuid=t,this.sendData()},handleInitGroups:function(t){this.objectTreeFinished=!1,this.objectTree=JSON.parse(JSON.stringify(t))},handleInitGroupIds:function(){console.log("handleInitGroupIds ======>",this.objectTree);var t=null;for(var e in this.objectTree["大楼"])""==this.objectTree["大楼"][e]["groupId"]&&(this.cachePath=["大楼",e],t=this.objectTree["大楼"][e]["component"]);for(var a in this.objectTree["楼层"])""==this.objectTree["楼层"][a]["groupId"]&&(this.cachePath=["楼层",a],t=this.objectTree["楼层"][a]["component"]);for(var s in this.objectTree["管线"])""==this.objectTree["管线"][s]["groupId"]&&(this.cachePath=["管线",s],t=this.objectTree["管线"][s]["component"]);return console.log("comp",t),null!=t&&t.length>0&&(this.initData(i["EVENTS"].LINKAGE_MODEL),this.params.uuid=JSON.stringify({ldArr:t}),this.sendData()),this.objectTreeFinished=!0,!1},handleInitGroupIdCallBack:function(t){console.log("handleInitGroupIdCallBack ======>",t);try{2==this.cachePath.length&&(this.objectTree[this.cachePath[0]][this.cachePath[1]].groupId=t.groupId)}catch(e){console.log(e.trace())}this.handleInitGroupIds()},getObjectGroups:function(){return this.objectTreeFinished?this.objectTree:null},resetToSnapshot:function(t){this.initData(i["EVENTS"].OUTPUT_IMAGE),this.params.extraType=5,this.params.uuid=JSON.stringify({data:t.data}),this.sendData()},resetObjectsOnly:function(t){this.initData(i["EVENTS"].OUTPUT_IMAGE),this.params.extraType=8,this.params.uuid=JSON.stringify({data:t.data}),this.sendData()},resetCameraOnly:function(t){this.initData(i["EVENTS"].OUTPUT_IMAGE),this.params.extraType=7,this.params.uuid=JSON.stringify({data:t.data}),this.sendData()},handleGetObjectPos:function(t,e,a){this.cacheObjArr=t,this.cacheObjPosInd=e,this.cacheObjPosCallBack=a,this.initData(i["EVENTS"].LINKAGE_MODEL),this.params.uuid=JSON.stringify({ldArr:t[e]}),this.params.dx=-1,this.params.dy=-1,this.sendData()},handleGetObjectPosCallBack:function(t){this.cacheObjPosCallBack&&this.cacheObjPosCallBack(this.cacheObjArr[this.cacheObjPosInd],t),this.cacheObjPosInd<this.cacheObjArr.length-1&&this.handleGetObjectPos(this.cacheObjArr,this.cacheObjPosInd+1,this.cacheObjPosCallBack)}}}},a9cf:function(t,e,a){"use strict";a.r(e);var s=a("b203");a("83d6");e["default"]={mixins:[s["default"]],data:function(){return{loading:!1,loadingText:"",model:{appId:""},wsServer:{host:"127.0.0.1",port:"9003"},ws:null,timeout:null,apiRetry:20,wsRetry:0,isReload:!1,reloadMsg:""}},methods:{initWS:function(){if(console.log(this.fakeSend),"false"==this.fakeSend){if(window.socket&&window.socket.close(),this.ws=null,!this.wsServer.host||!this.wsServer.port)return!1;var t=this.gf.getBimWsServer();if(!t)return!1;try{try{this.ws=new WebSocket(t)}catch(e){console.log("连接失败")}this.ws.onerror=this.onerror,this.ws.onmessage=this.onmessage,this.ws.onopen=this.onopen,this.ws.onclose=this.onclose,window.ws=this.ws}catch(e){console.log("重连"),this.reconnectWS()}}},reconnectWS:function(){var t=this;if(this.ws&&(this.ws.onerror=null),this.wsRetry>50&&(this.clearTimeout("_ws"),this.$message({showClose:!0,message:"连接模型服务器失败，请重新打开模型",type:"error"})),this.timeouts._ws)return!1;this.wsRetry++,this.setTimeout("_ws",(function(){if(t.clearTimeout("_ws"),t.connectSuccess)return!1;t.ws&&t.ws.close(),t.initWS()}),1500)},initDataWS:function(){this.wsServer.host&&this.wsServer.port,this.initWS()},reload:function(){this.wsRetry=0,this.apiRetry=20,this.isReload=!1,this.initDataWS()}},created:function(){var t=this;window.onbeforeunload=function(){t.ws&&t.ws.onclose()}},mounted:function(){this.initWS()},beforeDestroy:function(){this.ws&&this.ws.onclose()}}},b203:function(t,e,a){"use strict";a.r(e);var s=a("5530"),i=(a("caad"),a("a15b"),a("14d9"),a("fb6a"),a("c19f"),a("ace4"),a("2c66"),a("249d"),a("40e9"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("3ca3"),a("5cc6"),a("907a"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("986a"),a("1d02"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("6ce5"),a("2834"),a("72f7"),a("4ea1"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("dc56"),a("ddce"));e["default"]={mixins:[i["default"]],data:function(){return{connectSuccess:!1,responseNumber:0,firstLoad:!0}},methods:{onopen:function(t){this.connectSuccess=!0,this.loading=!1,this.clearTimeout("_ws"),this.initCanvas(),this.handleStartHeartCheck(),this.handleRenderingLevel({name:"中",value:"7",extraType:"6",type:"MIDDLE_PERFORMANCE_SEND",dx:1280,dy:720}),this.getHighPerformance()},onerror:function(t){var e=this;if(console.log("WS-----------------\x3e>>error",t.message),this.connectSuccess)return!1;this.setTimeout("_reconnect",(function(){e.reconnectWS()}),1e3)},onclose:function(t){this.ws=null},onmessage:function(t){var e=this,a=new Blob([t.data]),i=new FileReader;this.heartCheckBack=!0,this.handleStartHeartCheck(),i.addEventListener("loadend",(function(t){try{var n=new DataView(i.result);if(!n.byteLength||4===n.byteLength)return!1;for(var r=0,o=r+4,h=n.getInt32(r,!0),c=[],l=15e3,d=Math.ceil(h/l),u=1;u<=d;u++){var p=1===u?o:(u-1)*l,f=u===d?h+o:u*l,m=i.result.slice(p,f),_=String.fromCharCode.apply(null,new Uint8Array(m));c.push(_)}var E=c.join("");/^%/.test(E)&&(E=decodeURIComponent(E));var T=E&&JSON.parse(E)||{};/^%/.test(T.header)&&(T.header=decodeURIComponent(T.header));var g=T.header&&JSON.parse(T.header)||{};/^%/.test(T.data)&&(T.data=decodeURIComponent(T.data));var y=T.data&&JSON.parse(T.data)||{};if(y.canvasInfo={offsetX:e.canvas.offsetX,offsetY:e.canvas.offsetY,performance:e.performance,realWidth:e.canvas.realWidth,realHeight:e.canvas.realHeight},e.handleGraphicJsonReply(y),"true"==T.image){e.responseNumber=g.index;var v=URL.createObjectURL(a.slice(h+o));e.drawImage(v,e.responseNumber),e.firstLoad&&e.handleInitGroupIds()}return"screenRotatePos"!=y.type&&"screenMovePos"!=y.type||(e.handlers._mouseRotateCenter=y),"LoadStatus"==y.type&&("Finished"==y.status?(e.clearInterval("_loadStatus"),e.process=100,e.isShowProcess=!1,e.handleReloadScene()):e.process=Number(y.status)),"load scene"==y.type&&(Number(y.progress)>99.9?(e.clearInterval("_loadStatus"),e.process=100,e.isShowProcess=!1,e.handleInitGroupIds()):(e.process=Number(y.progress),e.isShowProcess=!0)),"selectionset_infos"==y.type&&e.updateGroupTreeCallBack(y.selectionSets),"SuccessMessage"!=y.type&&"ErrorMessage"!=y.type||(y=Object(s["a"])(Object(s["a"])({},y.result),{},{groupId:y.result.groupId||"__empty__"}),"make_filterselectionset"==y.type&&e.handleInitGroupIdCallBack(y)),"query_screen_coord"==y.type&&y.hasOwnProperty("data")&&e.handleGetObjectPosCallBack(y.data),e.firstLoad=!1,!1}catch(t){console.log("Invalid Data",t.message,dataView)}})),i.readAsArrayBuffer(a)},sendData:function(t){if("false"!=this.fakeSend)return this.params.messageId=(new Date).getTime(),void console.log(t,"发送到图形的数据",JSON.parse(JSON.stringify(this.params)));[20,10,805,32,40].includes(this.params.type),this.ws&&3!=this.ws.readyState?1==this.ws.readyState&&(this.params.messageId=(new Date).getTime(),console.log("发送到图形的数据",JSON.parse(JSON.stringify(this.params))),this.ws.send(this.transData())):this.reconnectWS()}}}},b85c2:function(t,e,a){"use strict";a("e052")},cd3c:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:t.loading,expression:"loading",modifiers:{fullscreen:!0,lock:!0}}],staticClass:"energy-summary"},[a("h3",[a("div",{staticClass:"pull-left"},[t._v(" 能耗监测 "),a("em",{domProps:{textContent:t._s(t.todayStr)}})]),a("div",{staticClass:"clearfix"})]),a("el-card",{staticClass:"box-card",staticStyle:{"margin-bottom":"20px"},attrs:{shadow:"hover"}},[a("panel-group",{attrs:{summaryData:t.summaryData}})],1),a("el-card",{staticClass:"box-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"time-span"},[a("el-form",{staticClass:"pull-right",attrs:{inline:!0}},[a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{attrs:{placeholder:"请选择日期","suffix-icon":"el-icon-date","value-format":"yyyy-MM-dd","picker-options":t.queryForm.fromPickerOpt},model:{value:t.queryForm.from,callback:function(e){t.$set(t.queryForm,"from",e)},expression:"queryForm.from"}})],1),a("el-form-item",{attrs:{label:"~"}},[a("el-date-picker",{attrs:{placeholder:"请选择日期","suffix-icon":"el-icon-date","value-format":"yyyy-MM-dd","picker-options":t.queryForm.toPickerOpt},model:{value:t.queryForm.to,callback:function(e){t.$set(t.queryForm,"to",e)},expression:"queryForm.to"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.onFreshData}},[t._v("更新数据")])],1),a("div",{staticClass:"clearfix"})],1),a("div",{staticClass:"clearfix"})],1),a("el-divider"),a("bar-chart",{ref:"barChart",staticClass:"chart",attrs:{chartDatas:t.chartDatas}}),a("el-divider"),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.tableDatas,border:"",stripe:""}},t._l(t.tableTitles,(function(t,e){return a("el-table-column",{key:e,attrs:{prop:t.prop,label:t.label,width:t.width}})})),1)],1)],1)},i=[],n=(a("d81d"),a("14d9"),a("b680"),a("d3b7"),a("3ca3"),a("0643"),a("4e3e"),a("a573"),a("159b"),a("ddb0"),a("e309")),r=a("d25c"),o=a("39f4"),h=a("31d7"),c={name:"energyStatistics",props:{},mixins:[o["default"]],components:{PanelGroup:n["default"],BarChart:r["default"]},data:function(){var t=this;return{loading:!0,buildingId:this.gf.getBuildingId(),displayText:"量",queryForm:{from:this.$moment().subtract(1,"years").format("YYYY-MM-DD"),to:this.$moment().format("YYYY-MM-DD"),displayType:"month",fromPickerOpt:{disabledDate:function(e){return t.queryForm.to?t.$moment(e).isAfter(t.$moment(t.queryForm.to)):e.getTime()>Date.now()}},toPickerOpt:{disabledDate:function(e){return t.queryForm.from&&t.$moment(e).isBefore(t.$moment(t.queryForm.from))||e.getTime()>Date.now()}}},summaryData:[{area:0,yesterday:0,unit:"kwh",total:0,lastYear:0,curMonth:0,typeName:"电",lastMonth:0,type:"electricity",curYear:0},{area:0,yesterday:0,unit:"吨",total:0,lastYear:0,curMonth:0,typeName:"水",lastMonth:0,type:"electricity",curYear:0}],chartDatas:{legend:[],xAxisData:[],series:[]},tableTitles:[{prop:"date",label:"日期",width:""}],tableDatas:[]}},mounted:function(){this.loading=!1},methods:{baseLoadedCallback:function(){this.onFreshData()},onFreshData:function(){this.resetDatas(),this.getDatas()},getDatas:function(){var t=this;this.loading=!0,Object(h["r"])({buildingId:this.buildingId}).then((function(e){var a=e.data;return t.loading=!1,a})).then((function(e){return t.fmtSummaryData(e),e})).then((function(e){var a=[];e.map((function(e,s){a.push({buildingId:t.buildingId,deviceType:e.type,displayType:t.queryForm.displayType,from:t.queryForm.from,to:t.queryForm.to,type:e.type})})),t.generateTableData(a,t.tableTitles,t.tableDatas)}))},generateTableData:function(t,e,a){var s=this;this.loading=!0;var i=t.map((function(t,e){return Object(h["d"])(t)}));Promise.all(i).then((function(e){s.loading=!1,e.forEach((function(e,i){var n=t[i];n.typeName=s.energyTypes[n.type].typeName+s.displayText,n.baseData=e.data.datas,n.area=e.data.area,s.gf.fmtTableDatas(s.tableTitles,s.tableDatas,n,i,{key:"recordedAt",val:"totalVal"},s.fmtTableColomn(n,i)),s.gf.fmtChartDatas(s.chartDatas,n,a,i,t.length,0);try{s.$refs.barChart.refreshChart()}catch(r){console.log(r)}}))}))},resetDatas:function(){this.summaryData=[],this.chartDatas={legend:[],series:[],xAxisData:[]},this.tableTitles=[{prop:"date",label:"日期",width:""}],this.tableDatas=[]},fmtSummaryData:function(t){var e=this;this.summaryData=t.map((function(t){return t.curMonth=parseFloat(t.curMonth.toFixed(2)),t.curYear=parseFloat(t.curYear.toFixed(2)),t.lastMonth=parseFloat(t.lastMonth.toFixed(2)),t.lastYear=parseFloat(t.lastYear.toFixed(2)),t.total=parseFloat(t.total.toFixed(2)),t.yesterday=parseFloat(t.yesterday.toFixed(2)),t.compMonth=t.lastMonth>0?100*t.lastMonth/(t.curMonth+t.lastMonth):0,t.compYear=t.lastYear>0?100*t.lastYear/(t.curYear+t.lastYear):0,t.feeUnit=e.energyTypes[t.type].typeUnit,t.icon=t.type,t}))},fmtTableColomn:function(t,e){var a=this;return[{prop:"val"+e,label:this.energyTypes[t.type].typeName+"("+this.energyTypes[t.type].typeUnit+")",width:null,func:function(t){return t}},{prop:"valFee"+e,label:this.energyTypes[t.type].typeName+"费("+this.energyTypes[t.type].feeUnit+")",width:"",func:function(e){return(e*a.energyTypes[t.type].feeRate).toFixed(4)}}]}}},l=c,d=a("2877"),u=Object(d["a"])(l,s,i,!1,null,null,null);e["default"]=u.exports},d25c:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{class:t.className,style:{height:t.height,width:t.width}})},i=[],n=a("5403");a("a524");var r={mixins:[n["a"]],props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"},chartDatas:{type:Object,default:function(){}}},data:function(){return{chart:null}},mounted:function(){var t=this;this.$nextTick((function(){t.chart=t.$echarts.init(t.$el,"dark"),t.refreshChart()}))},watch:{chartDatas:{handler:function(){this.refreshChart()},deep:!0}},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{changetheme:function(t){this.chart&&(this.chart.dispose(),this.chart=null,this.chart=this.$echarts.init(this.$el,t),this.refreshChart())},refreshChart:function(){if(!this.chart)return!1;this.chart.setOption({backgroundColor:"transparent",tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{top:20+this.chartDatas.legend.length/5*30,left:"5%",right:"5%",bottom:20,containLabel:!0},legend:{data:this.chartDatas.legend},dataZoom:[{type:"inside",realtime:!0,start:0,end:100}],xAxis:[{type:"category",data:this.chartDatas.xAxisData,axisTick:{alignWithLabel:!0},axisLabel:{textStyle:{color:"#eee"}}}],yAxis:[{type:"value",axisTick:{show:!1},axisLabel:{textStyle:{color:"#eee"}}}],series:this.chartDatas.series},!0)}}},o=r,h=a("2877"),c=Object(h["a"])(o,s,i,!1,null,null,null);e["default"]=c.exports},d328:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"model-view",attrs:{"element-loading-text":t.loadingText,"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.7)"},on:{contextmenu:function(t){t.preventDefault()}}},[a("div",{staticClass:"model-view-canvas"},[a("canvas",{style:{cursor:86==t.handlers._pressKey&&t.handlers._isMouseDown},attrs:{id:"modelView",width:t.canvas.width+"px",height:t.canvas.height+"px"},on:{contextmenu:function(e){return t.handleCanvasClick(e,!0)},click:function(e){return e.stopPropagation(),e.preventDefault(),t.handleCanvasClick(e)},dblclick:function(e){return e.stopPropagation(),t.handleCanvasDblClick(e)},mousedown:function(e){return e.stopPropagation(),t.handleCanvasMouseDown(e)},mousemove:function(e){return e.preventDefault(),t.handleCanvasMouseMove(e)},mouseup:function(e){return e.stopPropagation(),t.handleCanvasMouseUp(e)},wheel:function(e){return e.stopPropagation(),t.handleCanvasWheel(e)},touchstart:function(e){return e.stopPropagation(),t.handleTouchStart(e)},touchmove:function(e){return e.preventDefault(),t.handleTouchMove(e)},touchend:function(e){return e.preventDefault(),t.handleTouchEnd(e)}}})]),t.isShowProcess?a("div",{staticClass:"model-view-process"},[a("el-progress",{attrs:{"text-inside":!0,"stroke-width":18,percentage:t.process,color:"rgba(55, 187, 161, 0.7)"}})],1):t._e(),!t.loading&&t.isReload?a("div",{staticClass:"cbim-model-reload"},[a("div",{staticClass:"cbim-model-reload__btn",on:{click:t.reload}},[a("i",{staticClass:"el-icon el-icon-refresh"})]),a("div",[t._v(t._s(t.reloadMsg||"模型连接失败"))])]):t._e()])},i=[],n=a("a9cf"),r=a("a2d5"),o=a("7737"),h=a("f3e0"),c=a("46bc"),l=a("0f06"),d={name:"modelView",mixins:[o["default"],n["default"],h["default"],r["default"],c["default"],l["default"]],components:{},props:{fakeSend:{type:String,default:"false",note:"是否开启模拟器（开启状态不连接ws服务，send只做输出控制台）"}},computed:{},watch:{},beforeCreate:function(){},created:function(){var t=this;window.onbeforeunload=function(e){t.handleResetModel()},this.initRouteParams()},mounted:function(){},methods:{initRouteParams:function(){this.routeParams.disableMouseRightClickMultiChoose=!!this.$route.query.disableMouseRightClickMultiChoose}},beforeDestroy:function(){this.handleResetModel()}},u=d,p=(a("b85c2"),a("2877")),f=Object(p["a"])(u,s,i,!1,null,"57406ebf",null);e["default"]=f.exports},dc56:function(t,e,a){"use strict";a.r(e),a.d(e,"EVENTS",(function(){return s}));var s={DATA_TYPE_ROTATE:50,DATA_TYPE_MOBILE_MOVE:100,DATA_TYPE_BACK_MAIN:30,DATA_TYPE_CLICK:40,DATA_TYPE_BIGGER:10,DATA_TYPE_SMALLER:20,DATA_TYPE_SECTION:2,DATA_TYPE_SECTION_BOX:46,DATA_TYPE_SECTION_BOX_START:80,DATA_TYPE_SECTION_BOX_CHANGE:81,DATA_TYPE_SECTION_BOX_UP:82,DATA_TYPE_SECTION_BOX_RESET:83,DATA_TYPE_X_ROTATE:7,DATA_TYPE_Y_ROTATE:8,DATA_TYPE_Z_ROTATE:9,INIT_SEND:1,CLICK_COMPONENT:3,HIGH_PERFORMANCE:4,SHOW_ALL_COMPONENT:11,HIDE_ONE_COMPONENT:86,INSULATE_COMPONENT:85,CLIENT_WANDER_MSG:14,BATCH_CHECK_COMPONENT:16,MULTIPLE_CHOOSE_ACTION:23,MODEL_RESET_ALL:87,BACKGROUND_IMAGE_CTR:33,SHADOW_CTR:41,NORMAL_STATE:666,DATA_TYPE_MOBILE_SIZE:34,USE_FBX_FILE:35,DELETE_FBX_FILE:71,SHOW_TEXTURE:44,USE_TEXTURE:45,MARK_UP_TO_BACKEND:17,MARK_UP_TO_BACKEND_TWO:29,MARK_UP_STATUS:32,CLIENT_SHOW_LINE:25,CLIENT_SHOW_DEG:40,CLIENT_HIDE_LINE:26,CLEAR_MEASURE_LINE:24,CLIENT_SNAPSHOT_SAVE:21,CLIENT_SNAPSHOT_RECOVER:22,BATCH_COLORANT:18,START_BATCH_COLORANT:3,CANCEL_BATCH_COLORANT:2,MULTIPLE_CHOOSE_RECOVER:24,CLIENT_SINGLE_COLORING:27,CANCEL_All_COLORANT:60,BATCH_COLORANT_TWO:23,LOW_PERFORMANCE_SELECT:1024,LOW_PERFORMANCE_SEND:200,MIDDLE_PERFORMANCE_SELECT:1280,MIDDLE_PERFORMANCE_SEND:300,HIGH_PERFORMANCE_SELECT:1920,HIGH_PERFORMANCE_SEND:400,TWOK_PERFORMANCE_SELECT:2560,TWOK_PERFORMANCE_SEND:500,MAX_SOCKET_RETRY:50,FULL_SCREEN:1e5,SHOW_SETTING:100001,SHOW_SECTION:100002,SHOW_COLOR:100003,SHOW_TREE:100004,SHOW_SNAPSHOT:100005,SHOW_REMARK:100006,SHOW_FBX:100007,SHOW_PROPERTY:100008,SELECT_COMPONENT_WITH_RECT:76,DOUBLE_CLICK:47,PRESS_KEY_ACTION:48,SELECT_EFF_CANCEL:5,USE_TEXTURE_FILE:43,USE_TEXTURE_WIREFRAME:42,USE_TEXTURE_EFFECTS:51,MODEL_INVERT_SELECT:88,MODEL_ISOLATION:13,MODEL_HIDE:12,MODEL_RESTORE:11,MODEL_FILTER_MAJOR:102,ADD_START_MODEL:800,ADD_END_MODEL:801,OPEN_SUN_LIGHT:95,OPEN_POINT_LIGHT:97,OPEN_SPOT_LIGHT:98,QUERY_DATA_UUID:99,FRONT_MEASURE:91,USE_VIEWCUBE:49,OUTPUT_IMAGE:124,OUTPUT_VIDEO:123,OUTSIDE_SCENE_CHANGE:74,OPEN_CLOSE_PARTICLE:120,CHANGE_PARTICLE:121,INIT_MODEL_EVENT:500,NAVIGATION_CHART:103,MODEL_COLOR_SCHEMES:101,MODEL_GROUND_GRID:110,MODEL_SAVE_RECOVER_SCENE:92,MODEL_USE_TEXTURE_METAL:52,CLEAR_INSTANCE_AND_UNCONNECT:444,CLEAR_MESSAGE_QUEUE:600,FOURDBIM_TYPE:118,MODEL_ZONE_TYPE:140,MODEL_ZONE_MOUSE_DOWN:141,MODEL_ZONE_MOUSE_MOVE:142,MODEL_ZONE_MOUSE_UP:143,MODEL_FILE_LOAD_FINISHED:84,MAKE_WAY_MOUSE_MOVE:117,DETACHED_REDUCTION_FLOOR:113,ADD_OSGB_SCENE:70,REMOVE_OSGB_SCENE:71,DXF_OPERATE_MODEL:804,MODEL_LOAD_STATUS:805,GET_COORDINATE:1007,NJ_SELF_REVIEW_TABLE:106,LINKAGE_MODEL:1066,MODEL_SCENE_AUTO_ROTATION:1118,MODEL_SCENE_AUTO_ROTATION_STOP:160,MODIFY_BACKGROUND_COLOR:111,MODEL_GROUP_TREE:1115,OBJECT_LIGHT:1114}},ddce:function(t,e,a){"use strict";a.r(e);a("a9e3");e["default"]={methods:{handleGraphicJsonReply:function(t){var e=this;console.warn("图形端返回",t),"LoadStatus"==t.type?"Finished"==t.status?(this.clearInterval("_loadStatus"),this.process=100,this.isShowProcess=!1,this.setTimeout("_loaded",(function(){e.process=0,e.handleModelGroundGridCtrl({extraType:1})}),10)):this.process=Number(t.status):"LayerVisibleDataInfo"!=t.type&&("selectable"in t&&(t={type:"mouse_pos_selectable",data:t}),this.handlePostMessage("MSG_MODEL_RESPONSE_DATA",t))}}}},e052:function(t,e,a){},e120:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{class:t.className,style:{height:t.height,width:t.width}})},i=[],n=a("5403");a("a524");var r={mixins:[n["a"]],props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"},chartDatas:{type:Object,default:{}}},data:function(){return{chart:null}},mounted:function(){var t=this;this.$nextTick((function(){t.chart=t.$echarts.init(t.$el,"dark"),t.refreshChart()}))},watch:{chartDatas:{handler:function(){this.refreshChart()},deep:!0}},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{changetheme:function(t){this.chart&&(this.chart.dispose(),this.chart=null,this.chart=this.$echarts.init(this.$el,t),this.refreshChart())},refreshChart:function(){if(!this.chart)return!1;this.chart.setOption({backgroundColor:"transparent",tooltip:{trigger:"item",formatter:"{b}:<br>{c}<br>({d}%)"},legend:{left:"center",data:this.chartDatas.legend},series:this.chartDatas.series})}}},o=r,h=a("2877"),c=Object(h["a"])(o,s,i,!1,null,null,null);e["default"]=c.exports},e309:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"panel-group-statics"},t._l(t.list,(function(e,s){return a("div",{key:s,staticClass:"card-panel"},[a("div",{staticClass:"card-panel-row"},[a("div",{staticClass:"card-panel-icon-wrapper"},[a("svg-icon",{attrs:{"icon-class":e.type,"class-name":"card-panel-icon"}})],1),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("累计用"+t._s(e.typeName))]),a("count-to",{staticClass:"card-panel-num",attrs:{suffix:e.feeUnit,"start-val":0,"end-val":e.total,duration:1e3,decimals:3}})],1)]),a("el-divider"),a("div",{staticClass:"card-panel-row"},[a("div",{staticClass:"card-row-text"},[t._v(" 本月： "),a("count-to",{attrs:{suffix:e.feeUnit,"start-val":0,"end-val":e.curMonth,duration:1e3,decimals:3}}),e.lastMonth<e.curMonth?a("i",{staticClass:"el-icon-top",style:{color:e.monthColor}}):t._e(),e.lastMonth>e.curMonth?a("i",{staticClass:"el-icon-bottom",style:{color:e.monthColor}}):t._e()],1),a("div",{staticClass:"card-row-text"},[t._v(" 今年： "),a("count-to",{attrs:{suffix:e.feeUnit,"start-val":0,"end-val":e.curYear,duration:1e3,decimals:3}}),e.lastYear<e.curYear?a("i",{staticClass:"el-icon-top",style:{color:e.yearColor}}):t._e(),e.lastYear>e.curYear?a("i",{staticClass:"el-icon-bottom",style:{color:e.yearColor}}):t._e()],1)]),a("div",{staticClass:"card-panel-row"},[a("div",{staticClass:"card-row-text"},[t._v(" 上月： "),a("count-to",{attrs:{suffix:e.feeUnit,"start-val":0,"end-val":e.lastMonth,duration:1e3,decimals:3}})],1),a("div",{staticClass:"card-row-text"},[t._v(" 去年： "),a("count-to",{attrs:{suffix:e.feeUnit,"start-val":0,"end-val":e.lastYear,duration:1e3,decimals:3}})],1)])],1)})),0)},i=[],n=(a("d81d"),a("a573"),a("ec1b")),r=a.n(n),o={props:{summaryData:{type:Array}},data:function(){return{list:[]}},components:{CountTo:r.a},watch:{summaryData:{handler:function(){this.refreshData()},deep:!0}},mounted:function(){this.refreshData()},methods:{refreshData:function(){this.list=this.summaryData.map((function(t){return t.monthColor=parseFloat(t.lastMonth)>parseFloat(t.curMonth)?"#67C23A":"#F56C6C",t.yearColor=parseFloat(t.lastYear)>parseFloat(t.curYear)?"#67C23A":"#F56C6C",t}))}}},h=o,c=a("2877"),l=Object(c["a"])(h,s,i,!1,null,null,null);e["default"]=l.exports},f3e0:function(t,e,a){"use strict";a.r(e);a("cb29"),a("4de4"),a("caad"),a("14d9"),a("a9e3"),a("d3b7"),a("2532"),a("3ca3"),a("0643"),a("2382"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494");var s=a("d8ad"),i=a("dc56"),n=a("3ea1");e["default"]={mixins:[n["default"]],data:function(){return{canvas:{instance:null,ctx:null,width:"",height:"",img:"",realWidth:"",realHeight:"",offsetX:0,offsetY:0,rate:1},handlers:{duration:50,_isMouseDown:!1,_isMouseMove:!1,_isRotate:!1,_isZoom:!1,_isClick:!1,_isDbClick:!1,_currentX:0,_currentY:0,_currentRealX:0,_currentRealY:0,_endX:0,_endY:0,_pressKey:"",_currentImgUrl:null,_mouseKey:"",_mouseTargetPoint:!1,_mouseRotateCenter:{x:0,y:0}},groupPressCode:[],performance:i["EVENTS"].MIDDLE_PERFORMANCE_SELECT,touchPosition:{x:0,y:0},startTouches:[],touchTimeout:null}},beforeCreate:function(){},created:function(){window.addEventListener("resize",this.initCanvas),window.addEventListener("keydown",this.handleKeyPress),window.addEventListener("keyup",this.handleKeyUp)},methods:{initCanvas:function(){var t=this,e=document.getElementsByClassName("model-view-canvas")[0];this.canvas.instance=document.getElementById("modelView"),this.canvas.width=e.clientWidth,this.canvas.height=e.clientHeight,this.canvas.realWidth=this.canvas.width,this.canvas.realHeight=this.canvas.height,this.canvas.rate=1,this.canvas.width/this.canvas.height>16/9?(this.canvas.realHeight=9/16*this.canvas.width,this.canvas.rate=this.canvas.width/this.performance):(this.canvas.realWidth=16/9*this.canvas.height,this.canvas.rate=this.canvas.height/(9*this.performance/16)),this.canvas.offsetX=(this.canvas.width-this.canvas.realWidth)/2,this.canvas.offsetY=(this.canvas.height-this.canvas.realHeight)/2,this.setTimeout("_getCtx",(function(){t.canvas.ctx=t.canvas.instance.getContext("2d"),t.drawImage()}),0),this.canvas.instance.oncontextmenu=function(t){t.returnValue=!1}},resize:function(){this.initCanvas()},drawImage:function(t,e){var a=this;if(t=t||this.handlers._currentImgUrl,!t)return this.initData(i["EVENTS"].INIT_SEND),this.sendData(),!1;var s=new Image;this.canvas.img=t||this.canvas.img,s.src=this.canvas.img,s.onload=function(){if(e<a.responseNumber)return!0;console.log(s,s.width,s.height),a.canvas.ctx&&a.canvas.ctx.clearRect(0,0,a.canvas.width,a.canvas.height),a.canvas.ctx.drawImage(s,a.canvas.offsetX,a.canvas.offsetY,a.canvas.realWidth,a.canvas.realHeight),a.show.rect&&!a.handlers._isClick&&a.handlers._isMouseDown&&a.drawRectOnCanvas(a.canvas.ctx,a.handlers._currentRealX,a.handlers._currentRealY,a.handlers._endX,a.handlers._endY),a.handlers._currentImgUrl&&!a.show.rect&&t!=a.handlers._currentImgUrl&&(window.URL.revokeObjectURL(a.handlers._currentImgUrl),a.handlers._currentImgUrl=t),a.handlers._mouseTargetPoint&&a.drawCenterRotateWeb(a.handlers._mouseRotateCenter)}},drawRect:function(t,e){this.handlers._endX=t||this.handlers._endX,this.handlers._endY=e||this.handlers._endY,this.drawImage(this.handlers._currentImgUrl)},r:function(t,e,a,s,i){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1;t.beginPath(),t.fillStyle="rgba(0,255,0,".concat(n,")"),t.strokeStyle="#1a91ff",t.globalAlpha=.2,t.fillRect(e,a,s-e,i-a),t.globalAlpha=1,t.strokeRect(e,a,s-e,i-a),t.fill(),t.closePath()},handleKeyPress:function(t){var e=window.event?t.keyCode:t.which;if(this.groupPressCode=this.groupPressCode.filter((function(t){return t!==e})),this.groupPressCode.push(e),console.log(e,this.groupPressCode,"-----------------\x3e>>keyCode"),16===e||17===e||82===e)this.handlers._pressKey=e;else if(65===e||83===e||68===e||87===e||81===e||69===e||88===e||90===e||38===e||40===e||37===e||39===e||32===e||67===e){var a=Number(this.modelStatus.firstPersonSpeed);this.initData(i["EVENTS"].PRESS_KEY_ACTION),this.params.dx=0,this.params.dy=0,this.params.posx=0,this.params.posy=0,65==e||37===e?this.params.posx=-1*a:83===e||40===e?this.params.posy=-1*a:68===e||39===e?this.params.posx=1*a:87===e||38===e?this.params.posy=1*a:81===e?this.params.dx=-1:69===e?this.params.dx=1:88===e?this.params.dy=1:90===e&&(this.params.dy=-1),this.params.extraType=32===e?5:67===e?3:0,this.sendData()}},handleKeyUp:function(t){console.log(t);var e=window.event?t.keyCode:t.which;3===this.groupPressCode.length&&this.groupPressCode.includes(16)&&this.groupPressCode.includes(17)&&(this.groupPressCode.includes(53)||this.groupPressCode.includes(101))||16!==e&&17!==e&&82!==e||(console.log("up"),this.handlers._pressKey="",this.params.extraType=0),this.groupPressCode=this.groupPressCode.filter((function(t){return t!==e}))},handleCanvasClick:function(t,e){e&&!this.routeParams.disableMouseRightClickMultiChoose||1==t.ctrlKey?(this.handlers._pressKey=17,this.handleClick(this.handleCanvasOrigin(t.offsetX),t.offsetY)):this.handlers._isClick&&!this.handlers._isDbClick&&this.handleClick(this.handleCanvasOrigin(t.offsetX),t.offsetY),(e||t.ctrlKey)&&(this.handlers._pressKey=""),e?this.handlePostMessage("MSG_MODEL_RESPONSE_DATA",{type:"mouse_right_click_dom",data:{clientX:t.clientX,clientY:t.clientY,offsetX:t.offsetX,offsetY:t.offsetY}}):this.handlePostMessage("MSG_MODEL_RESPONSE_DATA",{type:"mouse_left_click_dom",data:{clientX:t.clientX,clientY:t.clientY,offsetX:t.offsetX,offsetY:t.offsetY}}),s["a"].$emit("drawImageFinished",{})},handleCanvasOrigin:function(t){return t},handleCanvasDblClick:function(t){this.handlers._isDbClick=!0,this.handleZoomIn(t.x,t.y)},handleCanvasMouseDown:function(t){if(this.handlers._mouseKey=t.button,1===t.button){this.handlers._pressKey=1;var e=document.getElementById("modelView");e?e.style.cursor="move":document.body.style.cursor="move"}this.handlers._isClick=!1,this.handlers._isDbClick=!1,this.handlers._isMouseDown=!0,this.handlers._isMouseMove=!1,this.handlers._currentX=this.handleCanvasOrigin(t.offsetX)*this.performance/this.canvas.realWidth,this.handlers._currentY=t.offsetY*this.performance/this.canvas.realWidth,this.handlers._currentRealX=this.handleCanvasOrigin(t.offsetX),this.handlers._currentRealY=t.offsetY,this.show.sectionBox&&this.handleSectionBoxStart(this.handleCanvasOrigin(t.offsetX),t.offsetY,i["EVENTS"].DATA_TYPE_SECTION_BOX_START)},handleCanvasMouseMove:function(t){var e=this;if(this.handlers._isMouseDown&&!this.handlers._isMouseMove){if(!(Math.abs(this.handleCanvasOrigin(t.offsetX)-this.handlers._currentRealX)>4||Math.abs(t.offsetY-this.handlers._currentRealY)>4))return!1;this.handlers._isMouseMove=!0}!this.timeouts._move&&!this.timeouts._reviseMeasurePoint&&this.handlers._isMouseDown&&this.handlers._isMouseMove&&(this.modelStatus.firstPersonOn?this.handleCanvasMouseMovePersonOne(t):16===this.handlers._pressKey||1===this.handlers._pressKey?this.handleMove(this.handleCanvasOrigin(t.offsetX),t.offsetY):82===this.handlers._pressKey?(this.show.rect=!0,this.drawRect(this.handleCanvasOrigin(t.offsetX),t.offsetY),this.handleSelectByRectSend(this.handleCanvasOrigin(t.offsetX),t.offsetY)):this.show.sectionBox?this.handleSectionBoxChange(this.handleCanvasOrigin(t.offsetX),t.offsetY,i["EVENTS"].DATA_TYPE_SECTION_BOX_CHANGE):(console.log("--------\x3e",this.handlers._mouseKey),0===this.handlers._mouseKey||(1===this.handlers._mouseKey?this.handleMove(this.handleCanvasOrigin(t.offsetX),t.offsetY):2===this.handlers._mouseKey&&(this.handleRotate(i["EVENTS"].DATA_TYPE_ROTATE,this.handleCanvasOrigin(t.offsetX),t.offsetY),this.handlers._mouseTargetPoint||this.handleModelReceiveData({type:i["EVENTS"].MODEL_LOAD_STATUS,extraType:1}),this.handlers._mouseTargetPoint=!0))),this.setTimeout("_move",(function(){e.clearTimeout("_move")})))},handleCanvasMouseUp:function(t){if(this.handlers._mouseTargetPoint=!1,1===this.handlers._pressKey){this.handlers._pressKey="";var e=document.getElementById("modelView");e?e.style.cursor="default":document.body.style.cursor="default"}if(this.handlers._isMouseMove){var a=document.getElementById("modelView");a?a.style.cursor="default":document.body.style.cursor="default"}else this.handlers._isClick=!0;this.handlers._isMouseDown=!1,this.handlers._isClick||(this.show.rect?this.handleSelectByRectSend(this.handleCanvasOrigin(t.offsetX),t.offsetY):this.show.sectionBox?this.handleSectionBoxUp(this.handleCanvasOrigin(t.offsetX),t.offsetY,i["EVENTS"].DATA_TYPE_SECTION_BOX_UP):(this.initData(i["EVENTS"].HIGH_PERFORMANCE),this.sendData())),0===this.handlers._mouseKey&&(this.show.rect=!1,this.handlers._mouseKey=""),s["a"].$emit("drawImageFinished",{})},handleCanvasWheel:function(t){var e=this;if(this.timeouts._scale)return!1;this._zoom(t.wheelDelta,t.offsetX,t.offsetY),this.setTimeout("_scale",(function(){e.clearTimeout("_scale")}))},_zoom:function(t,e,a){t<0?this.initData(i["EVENTS"].DATA_TYPE_SMALLER):this.initData(i["EVENTS"].DATA_TYPE_BIGGER),this.dxfCode&&(this.params.uuid=this.dxfCode),this.params.posx=(this.handleCanvasOrigin(e)-this.canvas.offsetX)*this.performance/parseInt(this.canvas.realWidth),this.params.posy=(a-this.canvas.offsetY)*this.performance/parseInt(this.canvas.realWidth),this.sendData(),this.params.uuid="",this.getHighPerformance(),s["a"].$emit("drawImageFinished",{})},handleCanvasMouseMovePersonOne:function(t){var e=.3;this.initData(i["EVENTS"].PRESS_KEY_ACTION),this.params.dx=0,this.params.dy=0,this.params.posx=0,this.params.posy=0,this.params.extraType=0;var a=this.handleCanvasOrigin(t.clientX),s=t.clientY,n=a-this.handlers._currentRealX,r=s-this.handlers._currentRealY,o=Math.abs(n),h=Math.abs(r);this.params.dx=n>0?1*e*o:-1*e*o,this.params.dy=r>0?-1*e*h:1*e*h,this.sendData(),this.handlers._currentRealX=this.handleCanvasOrigin(t.clientX),this.handlers._currentRealY=t.clientY},drawCenterRotateWeb:function(t){var e=this.canvas.ctx,a=t.x*parseInt(this.canvas.realWidth)/this.performance+this.canvas.offsetX,s=t.y*parseInt(this.canvas.realWidth)/this.performance+this.canvas.offsetY;e.save(),e.beginPath(),e.arc(a,s,6,0,2*Math.PI,!1),e.fillStyle="#40E0D0",e.fill(),e.lineWidth=1,e.strokeStyle="#40E0D0",e.stroke(),e.closePath(),e.restore()},handleCanvasOriginPos:function(t){return{x:.99*(parseFloat(t.x)*this.canvas.rate+this.canvas.offsetX),y:.94768*(parseFloat(t.y)*this.canvas.rate+this.canvas.offsetY)}}},beforeDestroy:function(){window.removeEventListener("resize",this.initCanvas),window.removeEventListener("keydown",this.handleKeyPress),window.removeEventListener("keyup",this.handleKeyUp)}}}}]);