// // 获取当前页面的URL
// const url = new URL(window.location.href);

// // 使用URLSearchParams解析查询参数
// const params = new URLSearchParams(url.search);

// // 获取versions参数的值
// const versions = params.get("versions");
// console.log();
// // var LocalUrl = "https://api-dh3d-test.3dzhanting.cn:8080";
// if (versions == "formal") {
//   var LocalUrl = "https://api-dh3d-public.3dzhanting.cn:8081";
// } else {
//   var LocalUrl = "https://api-dh3d-test.3dzhanting.cn:8080";
// }

// 正式版
var LocalUrl = "https://api-dh3d-public.3dzhanting.cn:8081";
var modelUrl = "https://files.3dzhanting.cn";
// 测试版
// var LocalUrl = "https://api-dh3d-test.3dzhanting.cn:8080";
// var modelUrl = "https://test.3dzhanting.cn";

let bqdata = []

function filterData(dataList) {
  console.log(dataList, "dataList");
  return dataList
    .map((data) => {
      let filteredDeviceDataBase = [];

      if (data.name.includes("冷冻泵")) {
        filteredDeviceDataBase = data.deviceDataBase.filter(
          (item) =>
            item.dmTag.includes("chart") ||
            item.dmTag.includes("monitor_freqv")
        );
      } else if (data.name.includes("冷却泵")) {
        filteredDeviceDataBase = data.deviceDataBase.filter(
          (item) =>
            item.dmTag.includes("chart") ||
            item.dmTag.includes("monitor_freqv")
          //  || item.dmTag.includes("monitor_mode")
        );
      } else if (data.name.includes("冷却塔")) {
        filteredDeviceDataBase = data.deviceDataBase.filter(
          (item) =>
            item.dmTag.includes("chart") ||
            item.dmTag.includes("monitor_freqv") ||
            item.dmTag.includes("monitor_mode")
        );
      } else if (data.name.includes("冷水机")) {
        console.log(data, 'd21ata');

        filteredDeviceDataBase = data.deviceDataBase.filter(
          (item) =>
            item.dmTag.includes("chart") ||
            item.dmTag.includes("monitor_ldscswd") ||
            item.dmTag.includes("monitor_load")
        );

        console.log(filteredDeviceDataBase, 'filteredDeviceDataBase');
      } else if (data.name.includes("冷机")) {

        filteredDeviceDataBase = data.deviceDataBase.filter(
          (item) =>
            item.dmTag.includes("chart") ||
            item.dmTag.includes("monitor_ldscswd") ||
            item.dmTag.includes("monitor_load")
        );
        console.log(filteredDeviceDataBase, "filteredDeviceDataBase");
      }
      else if (data.name.includes("循环泵") || data.name.includes("机组")) {

        filteredDeviceDataBase = data.deviceDataBase.filter(
          (item) =>
            item.dmTag.includes("chart")

        );
        console.log(filteredDeviceDataBase, "filteredDeviceDataBase");
      }

      return {
        id: data.id,
        name: data.name,
        filteredDeviceDataBase: filteredDeviceDataBase,
      };
    })
    .filter((item) => item.filteredDeviceDataBase.length > 0);
}

var dataList1 = [];

var dataList = [];
var ssdata = [];
console.log(filterData(dataList), "解析完的数据");
ssdata = transformArray(filterData(dataList));
console.log(ssdata, "处理完的数据");
let runningFrozenPumpIds; //开启的动画设备
let runningFrozenPumpIdsfan; //停止的动画设备
let runningPumpIds; //动画设备id  关闭的
let runningPumpIdsfan; //动画设备id 开启的
//动态动画变化
const checkModelsInterval = setInterval(() => {
  if (alreadymodels) {
    // 当 alreadymodels 存在时，执行处理
    runningFrozenPumpIds = getRunningFrozenPumpDeviceIds(ssdata);
    runningPumpIds = getDeviceIds(ssdata);
    runningFrozenPumpIdsfan = getRunningFrozenPumpDeviceIdsfan(ssdata);
    console.log(runningFrozenPumpIds, runningPumpIds, "处理完的设备动画分组");
    // 清除定时器，防止重复执行
    clearInterval(checkModelsInterval);
  }
}, 1000); // 每秒检查一次
let first = 0;
let previousPumpIdsfan = [];
window.addEventListener("message", function (event) {
  if (event.data.type == "freshBimDeviceData") {
    console.log(event.data.param, "接受到的数据");
    dataList = event.data.param;

    ssdata = transformArray(filterData(dataList));
    console.log(filterData(dataList), "解析完的dataList");
    // console.log(transformArray(filterData(dataList)), tagidarr, "解析完的数据");
    if (!first) {
      // console.log(view.getObjCenterByModelIds(tagidarr), "解析完的数据");
      // addlablenewall(view.getObjCenterByModelIds(tagidarr));
      view.setColorByModelIds(runningFrozenPumpIds, "#00FF17");
      view.setColorByModelIds(runningFrozenPumpIdsfan, "#FF0000");
      view.LoadJsonDevice.setAnimateByModelIds(
        runningPumpIds,
        // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
        // 501070, 501072, 501073,
        false
      );
      first = 1;
      addlablenewall(
        view.getObjCenterByModelIds([
          500117, 500118, 500119, 500120, 500121, 500122, 500123, 500124,
          500125, 500126, 500127, 500128, 500130, 500133, 500137, 500138,
          500131, 500129, 500132, 500134, 500135, 500136, 500129,
        ])
      );
    }
    if (labelNamelist) {
      console.log(runningPumpIds, "更新数据");
      ssdata.forEach((item, index) => {
        console.log(item, "11更新数据");
        let dedata = item.id ? getDeviceIdByitem(item.id) : "";
        updateLabelContent("lable" + getSbId(item.id), dedata);
      });
      const checkModelsInterval = setInterval(() => {
        if (alreadymodels) {
          // 当 alreadymodels 存在时，执行处理
          runningFrozenPumpIds = getRunningFrozenPumpDeviceIds(ssdata);
          runningFrozenPumpIdsfan = getRunningFrozenPumpDeviceIdsfan(ssdata);
          runningPumpIds = getDeviceIds(ssdata);
          runningPumpIdsfan = getDeviceIdsfan(ssdata);

          console.log(
            runningFrozenPumpIds,
            runningPumpIds,
            runningFrozenPumpIdsfan,

            "处理完的设备动画分组"
          );
          // 清除定时器，防止重复执行
          clearInterval(checkModelsInterval);
        }
      }, 1000); // 每秒检查一次
      const { added, removed } = getArrayChanges(
        previousPumpIdsfan,
        runningPumpIdsfan
      );
      // updateLabelContent(labelName, data);
      view.setColorByModelIds(runningFrozenPumpIds, "#00FF17");
      view.setColorByModelIds(runningFrozenPumpIdsfan, "#FF0000");
      view.resetColorByModelIds(removed);
      view.LoadJsonDevice.setAnimateByModelIds(
        runningPumpIds,
        // 501083, 501084, 501085, 501009, 501068, 501069, 501071, 501067,
        // 501070, 501072, 501073,
        false
      );
      // 判断数组是否发生变化
      const isChanged = !arraysAreEqual(previousPumpIdsfan, runningPumpIdsfan);

      if (isChanged) {
        // 如果数组变化了，才执行
        view.LoadJsonDevice.setAnimateByModelIds(runningPumpIdsfan, true);
        // 更新 previousPumpIdsfan 为当前的值
        previousPumpIdsfan = [...runningPumpIdsfan];
      }
    }
    // getDeviceIdById();
  }
});
// 比较两个数组并返回新增和删除的项
function getArrayChanges(oldArray, newArray) {
  const added = newArray.filter((item) => !oldArray.includes(item));
  const removed = oldArray.filter((item) => !newArray.includes(item));
  return { added, removed };
}
// 比较两个数组是否相等
function arraysAreEqual(arr1, arr2) {
  if (arr1.length !== arr2.length) return false;
  for (let i = 0; i < arr1.length; i++) {
    if (arr1[i] !== arr2[i]) return false;
  }
  return true;
}
// 定时更新标签内容
function updateLabelContent(labelName, newData) {
  // 找到标签
  let labelDiv = document.getElementById(labelName);
  if (labelDiv) {
    // 获取标签内的infoDiv
    let infoDiv = labelDiv.querySelector("div");

    // 更新标签内容
    infoDiv.innerHTML = `<div class='divtit111'>
        <span style="color:#fff;">${newData.name}</span></div>`;

    newData.details.forEach((detail) => {
      infoDiv.innerHTML += `<div class='divdetail'>
                                <span style="flex-basis: 66%; text-align: right;">${detail.name}：</span>
                                <span style="font-size: 11px;color: ${detail.value === '运行' ? '#8bfac4' :
          detail.value === '停止' ? '#FF0000' :
            '#ffffff'
        }; flex-basis:40%;">${detail.value}</span>
                              </div>`;
    });
  }
}
function getDValLabel(dVal, dOtherData) {
  const mapping = dOtherData.split(';').reduce((acc, item) => {
    if (item) {
      const [key, value] = item.split(':');
      acc[key] = value;
    }
    return acc;
  }, {});

  return mapping[dVal] || '--';
}
function transformArray(dataArray) {
  return dataArray.map((data) => {
    console.log(data, "默认值data");
    let status = ""; // 默认值
    const transformedDetails = data.filteredDeviceDataBase
      // .filter((item) => !item.dmName.includes("运行状态")) // 过滤掉"运行状态"
      .map((item) => {
        let value = item.valStr ? item.valStr : item.dVal;

        if (item.dDataUnit) {
          value = item.valStr ? item.valStr : `${item.dVal} ${item.dDataUnit}`;
        }

        return {
          name:
            item.dmName == "冷冻水出水温度显示" ||
              item.dmName.includes("冷冻水出水温度")
              ? "出水温度"
              : item.dmName.includes("手自动")
                ? "手自动状态"
                : item.dmName == "冷冻泵频率反馈" ||
                  item.dmName == "冷却泵频率反馈" ||
                  item.dmName.includes("频率反馈")
                  ? "频率反馈"
                  : item.dmName.includes("运行状态") || item.dmName.includes("启停")
                    ? "运行状态"
                    : item.dmName.includes("负载率")
                      ? "负载率"
                      : item.dmName.includes("控制模式")
                        ? " 控制模式"
                        : item.dmName.includes("回水温度")
                          ? "回水温度"
                          : item.dmName.includes("模式设置")
                            ? "模式设置"
                            : item.dmName.includes("故障状态")
                              ? "故障状态"
                              : item.dmName,
          value: item.dmName.includes("手自动")
            ? item.dVal == 1
              ? "自动"
              : "手动"
            : item.dmName.includes("运行状态")
              ? item.dVal == 1
                ? "运行"
                : "停止"
              : item.dmName.includes("控制模式")
                ? getDValLabel(item.dVal, item.dOtherData)
                : value,
        };
      });

    // 查找"运行状态"，并将其值设置为 status
    const statusItem = data.filteredDeviceDataBase.find((item) =>
      item.dmName.includes("运行状态")
    );
    if (statusItem) {
      status = statusItem.dVal === "1" ? "运行" : "停止";
    }

    // // 查找"运行状态"，并将其值设置为 status
    // const statusItem1 = data.filteredDeviceDataBase.find((item) =>
    //   item.dmName.includes("手自动")
    // );
    // if (statusItem1) {
    //   status = statusItem1.dVal === "1" ? "自动" : "手动";
    // }

    return {
      id: data.id,
      name: data.name,
      status: status,
      details: transformedDetails,
    };
  });
}

// 筛选开启动画的设备数组(变色)

function getRunningFrozenPumpDeviceIds(devices) {
  console.log(devices, "处理完的数据11");
  // 存储符合条件的设备 ID
  const resultArray = [];

  // 遍历设备数组
  devices.forEach((device) => {
    // 检查设备名称和状态
    if (
      (device.name.includes("冷冻泵") || device.name.includes("冷却泵") || device.name.includes("循环泵")) &&
      device.status == "运行"
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "sb_fs_" + getSbId(device.id); // 假设该函数已定义
      const deviceId1 = "f_" + getSbId(device.id); // 假设该函数已定义

      resultArray.push(deviceId);
      resultArray.push(deviceId1);
    } else if (device.name.includes("冷却塔") && device.status == "运行") {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "b3_rs_flrbfs_004_" + getSbId(device.id); // 假设该函数已定义
      resultArray.push(deviceId);
    } else if (
      (device.name.includes("冷机") && device.status == "运行") ||
      (device.name.includes("冷水机") && device.status == "运行")||
      (device.name.includes("离心机") && device.status == "运行")
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "c_wq_" + getSbId(device.id);
      const deviceId1 = "网格101_" + getSbId(device.id);
      resultArray.push(deviceId);
      resultArray.push(deviceId1);
    } else if (
      (device.name.includes("机组") && device.status == "运行") ||
      (device.name.includes("机组") && device.status == "运行")
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "fs_" + getSbId(device.id);
      const deviceId1 = "fs1_" + getSbId(device.id);
      console.log(deviceId, deviceId1, 'fenleng');

      resultArray.push(deviceId);
      resultArray.push(deviceId1);
    }

  });

  // 返回符合条件的设备 ID 数组
  return resultArray;
}


// 筛选开启动画的设备数组(恢复色)

function getRunningFrozenPumpDeviceIdsfan(devices) {
  console.log(devices, "处理完的数据11");
  // 存储符合条件的设备 ID
  const resultArray = [];

  // 遍历设备数组
  devices.forEach((device) => {
    // 检查设备名称和状态
    if (
      (device.name.includes("冷冻泵") || device.name.includes("冷却泵") || device.name.includes("循环泵")) &&
      device.status == "停止"
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "sb_fs_" + getSbId(device.id); // 假设该函数已定义
      const deviceId1 = "f_" + getSbId(device.id); // 假设该函数已定义

      resultArray.push(deviceId);
      resultArray.push(deviceId1);
    } else if (device.name.includes("冷却塔") && device.status == "停止") {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "b3_rs_flrbfs_004_" + getSbId(device.id); // 假设该函数已定义
      resultArray.push(deviceId);
    } else if (
      (device.name.includes("冷机") && device.status == "停止") ||
      (device.name.includes("冷水机") && device.status == "停止")||
      (device.name.includes("离心机") && device.status == "停止")
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "c_wq_" + getSbId(device.id);
      const deviceId1 = "网格101_" + getSbId(device.id);

      resultArray.push(deviceId);
      resultArray.push(deviceId1);
    }
    else if (
      (device.name.includes("机组") && device.status == "停止") ||
      (device.name.includes("机组") && device.status == "停止")
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "fs_" + getSbId(device.id);
      const deviceId1 = "fs1_" + getSbId(device.id);
      resultArray.push(deviceId);
      resultArray.push(deviceId1);
    }
  });

  // 返回符合条件的设备 ID 数组
  return resultArray;
}

// 筛选开启动画的设备数组(设备id)

function getDeviceIds(devices) {
  console.log(devices, "处理完的数据11");
  // 存储符合条件的设备 ID
  const resultArray = [];

  // 遍历设备数组
  devices.forEach((device) => {
    // 检查设备名称和状态
    if (
      (device.name.includes("冷冻泵") || device.name.includes("冷却泵") || device.name.includes("循环泵")) &&
      device.status == "运行"
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "sb_fs_" + getSbId(device.id); // 假设该函数已定义
      const deviceId1 = "f_" + getSbId(device.id); // 假设该函数已定义
    } else if (device.name.includes("冷却塔") && device.status == "运行") {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      const deviceId = "b3_rs_flrbfs_004_" + getSbId(device.id); // 假设该函数已定义
    } else if (
      (device.name.includes("冷机") && device.status == "运行") ||
      (device.name.includes("冷水机") && device.status == "运行")
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
    }
    else if (
      (device.name.includes("机组") && device.status == "运行")
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
    } else {
      resultArray.push(getSbId(device.id));
    }
  });

  // 返回符合条件的设备 ID 数组
  return resultArray;
}

function getDeviceIdsfan(devices) {
  console.log(devices, "处理完的数据11");
  // 存储符合条件的设备 ID
  const resultArray = [];

  // 遍历设备数组
  devices.forEach((device) => {
    // 检查设备名称和状态
    if (
      (device.name.includes("冷冻泵") || device.name.includes("冷却泵") || device.name.includes("循环泵")) &&
      device.status == "运行"
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      resultArray.push(getSbId(device.id));
      const deviceId = "sb_fs_" + getSbId(device.id); // 假设该函数已定义
      const deviceId1 = "f_" + getSbId(device.id); // 假设该函数已定义
    } else if (device.name.includes("冷却塔") && device.status == "运行") {
      // 调用 getDeviceIdById 函数并将结果推入新数组
      resultArray.push(getSbId(device.id));
      const deviceId = "b3_rs_flrbfs_004_" + getSbId(device.id); // 假设该函数已定义
    } else if (
      (device.name.includes("冷机") && device.status == "运行") ||
      (device.name.includes("冷水机") && device.status == "运行")
    ) {
      resultArray.push(getSbId(device.id));
      // 调用 getDeviceIdById 函数并将结果推入新数组
    }
    else if (
      (device.name.includes("机组") && device.status == "运行")
    ) {
      // 调用 getDeviceIdById 函数并将结果推入新数组
    } else {
    }
  });

  // 返回符合条件的设备 ID 数组
  return resultArray;
}
