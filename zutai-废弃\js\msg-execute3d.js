var comdata;
var buildnum;
window.addEventListener("message", function (event) {
  //event.data获取传过来的数据

  if (event.data.type == "function") {
    let name = event.data.name;
    let comdata = event.data.param;
    console.log(comdata, "收到了");
    if (comdata) {
      // console.log(labelNamesArray,11222);

      // view.removeObjByNames(labelNamesArray);
      xzrecord = comdata.data;
      // view.setLayer(["灯光"]);
      if (comdata.type == 1) {
        // view.resetLayer();
        if (
          comdata.data == "复位" ||
          comdata.data == "首页" ||
          comdata.data == "整体场景"
        ) {
          resetLayer();
          // view.resetCamera();
        } else if (comdata.data == "标签") {
          addlable1(
            view.getObjCenterByNames(view.searchAllByName("智能化设备网桥架")),
            comdata.value
          );
        }
      }
    }
  } else if (event.data.type == "build") {
    let comdata = event.data.param;

    console.log(comdata);
    if (comdata.data) {
      buildnum = comdata.data;
      console.log(floorlist);
      rightBtn.labelTitleFun(floorlist[comdata.data - 1], comdata.data - 1);
    } else {
      resetLayer();
    }
  } else if (event.data.type == "floor") {
    let comdata = event.data.param;
    console.log(comdata);
    let data = floorlist[buildnum - 1].floor;
    console.log(floorlist);
    if (comdata.data) {
      console.log(data);
      rightBtn.rightBtnFun(data[comdata.data - 1].name,data[comdata.data - 1].pos,data[comdata.data - 1].tar,data[comdata.data - 1].title, comdata.data - 1);
    } else {
      rightBtn.labelTitleFun(floorlist[buildnum - 1], buildnum - 1);
    }
  }
});
