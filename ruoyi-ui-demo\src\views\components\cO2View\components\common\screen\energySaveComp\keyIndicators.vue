<template>
  <custCard :title="title" autoHeight>
    <div class="cont" :style="{'height':getContHeight + 'px'}">
      <div class="item">
        <div class="title" style="padding-left: 30px">数据名称</div>
        <div class="val">数据值</div>
        <div class="time">更新时间</div>
      </div>
      <div class="item" v-for="(item,index) in sortedDeviceDataList" :key="index">
          <div class="title">{{item.dmName}}</div>
          <div class="val">{{item.valStr}}</div>
          <div class="time">{{item.dUpdatedAt}}</div>
      </div>
    </div>
  </custCard>
</template>

<script>
import custCard from "../components/custCard.vue";
import { getDeviceDataListByIds } from "@/api/device/apis";
export default {
   components: {
    custCard
  },
  props: {
    height: {
      type: Number,
      default: 430
    },  
    title: {
      type: String,
      default: "关键数据",
    },
    ids: {
      type: String,
      default: "",
    },
  },
  computed: {
    getContHeight () {
      return this.height - 36
    },
    sortedDeviceDataList() {
      // 创建一个新数组以避免修改原始数据
      return [...this.deviceDataList].sort((a, b) => {
        // 如果a是first，排在前面
        if (a.dmTag === 'first' && b.dmTag !== 'first') {
          return -1;
        }
        // 如果b是first，排在前面
        if (b.dmTag === 'first' && a.dmTag !== 'first') {
          return 1;
        }
        // 其他情况保持原顺序
        return 0;
      });
    }
  },
  data() {
    return {
      curBuilding: this.gf.getCurBuilding(),
      dictKey: 'dashboard_device_data_ids', //
      deviceDataList: []
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
        let conf = this.gf.projectSettings();
        // 标记多个 device_item_data_map.id
        let ids = conf[this.dictKey] || "";
        // 传入的优先级更高
        ids = this.ids ? this.ids : ids;
        if(ids && ids.length > 0) {
            this.getDeviceDataListByIds(ids);
        }
    },

    getDeviceDataListByIds(ids) {
        this.deviceDataList = [];
        getDeviceDataListByIds({
            deviceDataIds: ids,
        }).then(res => {
            this.deviceDataList = res.data.map( d => {
                if(d.dOtherData && d.dOtherData.trim() != "") {
                  // 针对读写 note 不一样的情况，需要分开
                  var ddMap = {}; // 读点
                  var ddrMap = {}; // 写点
                  var kl = d.dOtherData.trim().split(";");
                  var klr = d.drOtherData.trim().split(";");
                  for(var i in kl) {
                    var s = kl[i].trim().split(":");
                    if(s.length == 2) {
                      ddMap[s[0]] = s[1];
                    }
                  }
                  for(var ir in klr) {
                    var sr = klr[ir].trim().split(":");
                    if(sr.length == 2) {
                      ddrMap[sr[0]] = sr[1];
                    }
                  }
                  d.otherDataMap = ddMap;   // 读点
                  d.otherRDataMap = ddrMap; // 写点
                  d.valStr = ddMap[d.dVal] || "";
                } else {
                  d.valStr = d.dVal + ' ' + d.dDataUnit;
                }
                return d;
            });
            
            console.log(res,this.deviceDataList,'deviceDataList');
        });
    }
  },
};
</script>

<style scoped lang="scss">
.cont {
  overflow-y: auto;
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 10px 16px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    &:nth-child(odd) {
      border-top: 1px solid #0e3b6c;
      background: #0b233f80;
    }
    .title {
      width: calc(100% - 240px);
    }
    .val {
      width: 100px;
      text-align: left;
    }
    .time {
      width: 140px;
      text-align: center;
    }
  }
}
</style>
