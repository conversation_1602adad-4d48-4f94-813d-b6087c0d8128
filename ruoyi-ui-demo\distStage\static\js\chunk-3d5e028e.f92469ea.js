(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3d5e028e"],{"23fd":function(e,t,a){"use strict";a("f940")},5895:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container",attrs:{"element-loading-text":"数据正在加载中...","element-loading-background":"rgba(6, 16, 31, 0.2)"}},[e.strategy&&e.strategy.id>0?a("div",{staticClass:"main-card"},[e.hideHeader?e._e():a("h3",[a("span",{domProps:{textContent:e._s(e.pageTitle?e.pageTitle:"策略任务")}}),e._v(" -- "),a("span",{domProps:{textContent:e._s(e.strategy.name)}}),a("span",{staticClass:"pull-right"},[a("em",[a("el-button",{attrs:{type:"default",size:"small",icon:"el-icon-d-arrow-left"},on:{click:e.handleGoBack}},[e._v("返回")]),a("el-button",{attrs:{type:"info",size:"small",icon:"el-icon-refresh"},on:{click:e.handleRefresh}},[e._v("刷新")]),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleTaskAdd}},[e._v("创建任务")])],1)]),a("el-divider")],1),a("el-alert",{attrs:{type:"success",description:"系统运行时，会按照顺序从上往下执行，所以下面的任务会覆盖上面的任务;"}}),a("div",{staticClass:"clearfix mb15"}),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.taskList,stripe:""}},[a("el-table-column",{attrs:{label:"ID",align:"center",fixed:"left",prop:"id"}}),a("el-table-column",{attrs:{label:"序号",align:"center",fixed:"left",prop:"oid"}}),a("el-table-column",{attrs:{label:"任务名",align:"center",prop:"name",fixed:"left"}}),a("el-table-column",{attrs:{label:"备注信息",align:"left",prop:"note"}}),a("el-table-column",{attrs:{label:"创建者",align:"center",prop:"userName"}}),a("el-table-column",{attrs:{label:"任务状态",align:"center",prop:"status"}}),a("el-table-column",{attrs:{label:"操作",fixed:"right",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return e.strategy.userId>0?[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["strategy:*:*"],expression:"['strategy:*:*']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleDetailView(t.row,t.$index)}}},[e._v("编辑")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["strategy:*:*"],expression:"['strategy:*:*']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleDetailRemove(t.row,t.$index)}}},[e._v("删除")])]:void 0}}],null,!0)})],1),a("el-divider"),e.form&&e.taskEditShow?a("el-dialog",{attrs:{modal:!1,width:"650px",visible:e.taskEditShow},on:{"update:visible":function(t){e.taskEditShow=t}}},[a("div",{staticStyle:{cursor:"move"},attrs:{slot:"title"},slot:"title"},[e._v("任务编辑")]),a("el-form",{ref:"form",staticClass:"form",attrs:{model:e.form,"label-width":"90px"}},[a("el-form-item",{staticStyle:{display:"none"},attrs:{label:"策略Id"}},[a("el-input",{model:{value:e.form.groupId,callback:function(t){e.$set(e.form,"groupId",t)},expression:"form.groupId"}})],1),a("el-form-item",{attrs:{label:"序号"}},[a("el-input",{model:{value:e.form.oid,callback:function(t){e.$set(e.form,"oid",t)},expression:"form.oid"}})],1),a("el-form-item",{attrs:{label:"任务名"}},[a("el-input",{model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"备注信息"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.form.note,callback:function(t){e.$set(e.form,"note",t)},expression:"form.note"}})],1),a("el-form-item",{attrs:{label:"任务状态"}},[a("el-switch",{attrs:{size:"mini","active-value":"Y","inactive-value":"N","active-text":"启用","inactive-text":"停用"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}})],1),a("el-alert",{attrs:{type:"warning",title:"执行操作",description:"如果只选择类型, 则该类型下所有设备都会执行操作;"}}),a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"延时执行","label-width":"120px"}},[a("el-input-number",{attrs:{size:"mini"},model:{value:e.form.otherDataObj.delayTime,callback:function(t){e.$set(e.form.otherDataObj,"delayTime",t)},expression:"form.otherDataObj.delayTime"}},[a("template",{slot:"append"},[a("span",[e._v("分钟")])])],2),a("span",{staticClass:"pl15"},[e._v("触发条件达成后，会延迟执行 (降低敏感度)")])],1),a("el-form-item",{attrs:{label:"休息时间","label-width":"120px"}},[a("el-input-number",{attrs:{size:"mini"},model:{value:e.form.otherDataObj.sleepTime,callback:function(t){e.$set(e.form.otherDataObj,"sleepTime",t)},expression:"form.otherDataObj.sleepTime"}},[a("template",{slot:"append"},[a("span",[e._v("分钟")])])],2),a("span",{staticClass:"pl15"},[e._v("每次执行完成后，程序睡眠时间")])],1),a("el-form-item",{attrs:{label:"执行操作","label-width":"120px"}},e._l(e.deviceModel,(function(t,i){return t.drId>0?a("div",{key:i,staticClass:"item flex"},[a("span",{staticClass:"dname",domProps:{textContent:e._s(t.dmName)}}),a("span",{staticClass:"dedit pull-right flex-1"},[t.drId>0?a("span",["input"==t.editType?a("span",[a("el-input",{attrs:{size:"mini",clearable:"",placeholder:"请输入"+t.dmName},model:{value:e.form.otherDataObj.actions[t.dmName],callback:function(a){e.$set(e.form.otherDataObj.actions,t.dmName,a)},expression:"form.otherDataObj.actions[attr.dmName]"}},[a("template",{slot:"append"},[""!=t.drDataUnit?a("span",{domProps:{textContent:e._s(t.drDataUnit)}}):e._e()])],2)],1):e._e(),"inputNum"==t.editType?a("span",[a("el-input-number",{attrs:{size:"mini",clearable:"",step:1,placeholder:"请输入"+t.dmName},model:{value:e.form.otherDataObj.actions[t.dmName],callback:function(a){e.$set(e.form.otherDataObj.actions,t.dmName,a)},expression:"form.otherDataObj.actions[attr.dmName]"}})],1):e._e(),"select"==t.editType||"switch"==t.editType?a("span",[a("el-select",{attrs:{size:"mini",clearable:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.actions[t.dmName],callback:function(a){e.$set(e.form.otherDataObj.actions,t.dmName,a)},expression:"form.otherDataObj.actions[attr.dmName]"}},e._l(t.otherDataMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:t}})})),1)],1):e._e(),""!=t.drDataUnit?a("span",{staticClass:"pl15",domProps:{textContent:e._s(t.drDataUnit)}}):e._e()]):e._e()]),a("div",{staticClass:"clearfix"})]):e._e()})),0),a("el-alert",{attrs:{type:"info",title:"触发时间 -- 满足时间后触发执行",description:"不选择，则代表所有时间"}}),a("el-form-item",{attrs:{label:"","label-width":"30px"}},[a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("节假日")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.holidays,callback:function(t){e.$set(e.form.otherDataObj,"holidays",t)},expression:"form.otherDataObj.holidays"}},e._l(e.holidaysOpts,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("工作日")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.weekend,callback:function(t){e.$set(e.form.otherDataObj,"weekend",t)},expression:"form.otherDataObj.weekend"}},e._l(e.weekendOpts,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("月")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",multiple:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.month,callback:function(t){e.$set(e.form.otherDataObj,"month",t)},expression:"form.otherDataObj.month"}},e._l(e.monthOpts,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("日")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",multiple:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.day,callback:function(t){e.$set(e.form.otherDataObj,"day",t)},expression:"form.otherDataObj.day"}},e._l(e.dayOpts,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("星期")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",multiple:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.weekDay,callback:function(t){e.$set(e.form.otherDataObj,"weekDay",t)},expression:"form.otherDataObj.weekDay"}},e._l(e.weekDayOpts,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("小时")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",multiple:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.hour,callback:function(t){e.$set(e.form.otherDataObj,"hour",t)},expression:"form.otherDataObj.hour"}},e._l(e.hourOpts,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("分钟")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",multiple:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.minute,callback:function(t){e.$set(e.form.otherDataObj,"minute",t)},expression:"form.otherDataObj.minute"}},e._l(e.minuteOpts,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1)])]),a("el-alert",{attrs:{type:"info",title:"联动条件 -- 满足联动后触发执行",description:"不选择，则代表无联动条件"}}),a("el-form-item",{attrs:{label:"","label-width":"30px"}},[a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("设备类型")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",placeholder:"请选择"},on:{change:e.handleConditionDeviceType},model:{value:e.form.otherDataObj.condition.deviceType,callback:function(t){e.$set(e.form.otherDataObj.condition,"deviceType",t)},expression:"form.otherDataObj.condition.deviceType"}},e._l(e.deviceTypeOpts,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("数据点名")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.condition.deviceDataName,callback:function(t){e.$set(e.form.otherDataObj.condition,"deviceDataName",t)},expression:"form.otherDataObj.condition.deviceDataName"}},e._l(e.conditionModel,(function(e){return a("el-option",{key:e.dmName,attrs:{label:e.dmName+" "+e.dmNote,value:e.dmName}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("选择设备")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",multiple:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.condition.deviceId,callback:function(t){e.$set(e.form.otherDataObj.condition,"deviceId",t)},expression:"form.otherDataObj.condition.deviceId"}},e._l(e.conditionDeviceList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name+" "+e.position,value:e.id}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("达成方式")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.condition.isMember,callback:function(t){e.$set(e.form.otherDataObj.condition,"isMember",t)},expression:"form.otherDataObj.condition.isMember"}},e._l(e.conditionIsMemberOpts,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("计算方式")]),a("span",{staticClass:"dedit flex-1"},[a("el-select",{attrs:{size:"mini",clearable:"",placeholder:"请选择"},model:{value:e.form.otherDataObj.condition.compare,callback:function(t){e.$set(e.form.otherDataObj.condition,"compare",t)},expression:"form.otherDataObj.condition.compare"}},e._l(e.conditionCompareOpts,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]),a("div",{staticClass:"item flex"},[a("span",{staticClass:"dname"},[e._v("比较值")]),a("span",{staticClass:"dedit flex-1"},[a("el-input",{attrs:{size:"mini",clearable:"",placeholder:"请输入"},model:{value:e.form.otherDataObj.condition.value,callback:function(t){e.$set(e.form.otherDataObj.condition,"value",t)},expression:"form.otherDataObj.condition.value"}})],1)])]),a("el-form-item",[a("el-button",{on:{click:e.handleFormCancel}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleFormReset}},[e._v("重置")]),a("el-button",{attrs:{type:"info"},on:{click:e.handleFormCopy}},[e._v("复制")]),a("el-button",{attrs:{type:"success"},on:{click:e.handleFormUpdate}},[e._v("保存数据")])],1)],1)],1):e._e()],1):e._e()])},l=[],s=(a("d9e2"),a("d81d"),a("14d9"),a("b0c0"),a("e9c4"),a("b64b"),a("498a"),a("a573"),a("5b61")),n={name:"StrategyConfig",props:{},components:{},data:function(){return{loading:!1,pageTitle:"策略任务",hideHeader:!1,curBuilding:this.gf.getCurBuilding(),user:this.$store.state.user,strategyId:this.$route.params.strategyId||"",strategy:null,taskList:[],deviceList:[],conditionDeviceList:[],deviceModel:[],conditionModel:[],ind:-1,form:null,taskEditShow:!1,holidaysOpts:[{label:"所有日期",value:""},{label:"只有节假日",value:"1"},{label:"非节假日",value:"0"}],weekendOpts:[{label:"所有日期",value:""},{label:"只有工作日",value:"1"},{label:"非工作日",value:"0"}],weekDayOpts:["日","一","二","三","四","五","六"],monthOpts:["01","02","03","04","05","06","07","08","09","10","11","12"],dayOpts:["01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31"],hourOpts:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23"],minuteOpts:["00","01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31","32","33","34","35","36","37","38","39","40","41","42","43","44","45","46","47","48","49","50","51","52","53","54","55","56","57","58","59"],deviceTypeOpts:[],conditionIsMemberOpts:[{label:"单一条件(只用满足一个条件即可触发)",value:1},{label:"全部条件(全部满足条件才可触发)",value:2}],conditionCompareOpts:[{label:"等于(=)",value:"=="},{label:"不等于(!=)",value:"!="},{label:"大于(>)",value:">"},{label:"小于(<)",value:"<"},{label:"大于等于(>=)",value:">="},{label:"小于等于(<=)",value:"<="}],otherDateParams:{holidays:"",weekend:"",year:"",month:"",day:"",weekDay:"",hour:"",minute:"",delayTime:"5",sleepTime:"60",actions:{},condition:{}}}},computed:{},created:function(){this.getStrategyList(),this.getTaskList()},mounted:function(){},methods:{handleGoBack:function(){this.$router.go(-1)},handleRefresh:function(){this.getTaskList(),this.ind=-1,this.form=null},handleTaskAdd:function(){this.taskEditShow=!0,this.ind=-1,this.form={deviceType:this.strategy.deviceType,strategyName:this.strategy.name,userId:this.user.id,id:"",oid:"999",userName:this.user.name,name:"",status:"Y",note:"",otherData:"",otherDataObj:{holidays:"",weekend:"",month:[],day:[],weekDay:[],hour:[],minute:[],delayTime:"5",sleepTime:"30",actions:{},condition:{}}}},handleDetailView:function(e,t){this.ind=t,this.form=JSON.parse(JSON.stringify(e));var a=this.form.otherDataObj.condition.deviceType;a&&this.getConditionDeviceList(a),this.taskEditShow=!0,console.log(this.form)},handleConditionDeviceType:function(){var e=this.form.otherDataObj.condition.deviceType;e&&this.getConditionDeviceList(e)},handleDetailRemove:function(e){var t=this;this.$confirm("如果仅想暂时关闭，可以更新任务为不生效?","确认删除当前任务",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["M"])({id:e.id}).then((function(e){t.$message({showClose:!0,message:"操作成功。",type:"success"}),t.handleRefresh(),t.loading=!1})).catch((function(e){t.$message({showClose:!0,message:"操作失败，请重试。",type:"warning"}),t.loading=!1}))})).catch((function(){}))},handleFormCancel:function(){this.ind=-1,this.form=null,this.taskEditShow=!1},handleFormReset:function(){this.handleDetailView(this.taskList[this.ind],this.ind)},handleFormUpdate:function(){var e=this,t=JSON.parse(JSON.stringify(this.form));if(Object.keys(t.otherDataObj.actions).length<=0)return this.$message({showClose:!0,message:"至少要执行一个动作",type:"warning"}),!1;t.otherData=JSON.stringify(t.otherDataObj),t.groupId=this.strategy.id,delete t.otherDataObj,delete t.recordedContent,console.log(t),this.loading=!0,Object(s["ib"])(t).then((function(t){e.loading=!1,e.form=null,e.getTaskList()})).catch((function(t){e.$message({showClose:!0,message:"操作失败，请重试。",type:"warning"}),e.loading=!1}))},handleFormCopy:function(){var e=this,t=JSON.parse(JSON.stringify(this.form));if(t.otherData=JSON.stringify(t.otherDataObj),Object.keys(t.otherDataObj.actions).length<=0)return this.$message({showClose:!0,message:"至少要执行一个动作",type:"warning"}),!1;t.groupId=this.strategy.id,delete t.id,console.log(t),this.loading=!0,Object(s["ib"])(t).then((function(t){e.loading=!1,e.form=null,e.getTaskList()})).catch((function(t){e.$message({showClose:!0,message:"操作失败，请重试。",type:"warning"}),e.loading=!1}))},getDeviceList:function(){var e=this;this.loading=!0,Object(s["o"])({buildingId:this.curBuilding.id,types:this.strategy.deviceType}).then((function(t){var a=t.data;e.deviceList=a,e.deviceList.length>0&&Object(s["R"])({deviceId:e.deviceList[0].id}).then((function(t){var a=t.data;a&&(e.deviceModel=a.deviceDataBase.map((function(e,t){if("float"==e.drDataType)e.editType="inputNum",e.valStr=e.dVal+" "+e.dDataUnit;else if(""!=e.dOtherData.trim()){var a={},i=e.dOtherData.trim().split(";");for(var l in i){var s=i[l].trim().split(":");2==s.length&&(a[s[0]]=s[1])}e.otherDataMap=a,e.valStr=a[e.dVal],i.length<2?e.editType="input":2==i.length?e.editType="switch":i.length>2&&(e.editType="select")}else e.editType="input",e.valStr=e.dVal+" "+e.dDataUnit;return e})))})).catch((function(e){throw console.log("resourceDevice error",e.message),Error("resourceDevice",e.message)})),e.loading=!1})).catch((function(t){console.log("getDeviceList error",t.message),e.$message({showClose:!0,message:"数据加载失败，请刷新页面重试。getDeviceList",type:"warning"}),e.loading=!1}))},getConditionDeviceTypes:function(){var e=this;this.deviceTypeOpts=[],this.getDicts("d_device_type").then((function(t){var a={};t.data.map((function(e){a[e.dictValue]=e.dictLabel})),console.log(a),e.getDicts("device_strategy_type_map").then((function(t){t.data.map((function(t){if(t.dictLabel==e.deviceType){var i=t.dictValue.split(",");i.map((function(t){e.deviceTypeOpts.push({label:a[t],value:t})}))}console.log("deviceTypeOpts",e.deviceTypeOpts)}))}))}))},getConditionDeviceList:function(e){var t=this;this.loading=!0,Object(s["o"])({buildingId:this.curBuilding.id,types:e}).then((function(e){var a=e.data;t.conditionDeviceList=a,t.conditionDeviceList.length>0&&Object(s["R"])({deviceId:t.conditionDeviceList[0].id}).then((function(e){var a=e.data;a&&(t.conditionModel=a.deviceDataBase.map((function(e,t){if("float"==e.drDataType)e.editType="inputNum",e.valStr=e.dVal+" "+e.dDataUnit;else if(""!=e.dOtherData.trim()){var a={},i=e.dOtherData.trim().split(";");for(var l in i){var s=i[l].trim().split(":");2==s.length&&(a[s[0]]=s[1])}e.otherDataMap=a,e.valStr=a[e.dVal],i.length<2?e.editType="input":2==i.length?e.editType="switch":i.length>2&&(e.editType="select")}else e.editType="input",e.valStr=e.dVal+" "+e.dDataUnit;return e})))})).catch((function(e){throw console.log("resourceDevice error",e.message),Error("resourceDevice",e.message)})),t.loading=!1})).catch((function(e){console.log("getDeviceList error",e.message),t.$message({showClose:!0,message:"数据加载失败，请刷新页面重试。getConditionDeviceList",type:"warning"}),t.loading=!1}))},getStrategyList:function(){var e=this;this.loading=!0,Object(s["V"])({id:this.strategyId}).then((function(t){var a=t.data;a.length>0&&(e.strategy=a[0],e.deviceType=e.strategy.deviceType,e.getDeviceList(),e.getConditionDeviceTypes()),e.loading=!1})).catch((function(t){e.$message({showClose:!0,message:"数据加载失败，请刷新页面重试。getStrategyList",type:"warning"}),e.loading=!1}))},getTaskList:function(){var e=this;this.loading=!0,Object(s["W"])({groupId:this.strategyId}).then((function(t){var a=t.data;e.taskList=a.map((function(e){try{e.otherDataObj=JSON.parse(e.otherData)}catch(t){e.otherDataObj={}}return e})),console.log(e.taskList),e.loading=!1})).catch((function(t){e.$message({showClose:!0,message:"数据加载失败，请刷新页面重试。getTaskList",type:"warning"}),e.loading=!1}))}}},o=n,r=(a("23fd"),a("2877")),c=Object(r["a"])(o,i,l,!1,null,"dbc5f912",null);t["default"]=c.exports},acd9:function(e,t,a){"use strict";a.r(t);var i,l,s=a("5895"),n={name:"策略编辑",mixins:[s["default"]],components:{},data:function(){return{}},created:function(){},mounted:function(){},methods:{handleTabClick:function(){}}},o=n,r=a("2877"),c=Object(r["a"])(o,i,l,!1,null,null,null);t["default"]=c.exports},f940:function(e,t,a){}}]);