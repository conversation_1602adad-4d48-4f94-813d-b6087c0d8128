
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Transformation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        pre {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        #output {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Data Transformation</h1>
    <p>Click the button to transform the data:</p>
    <button onclick="transformData()">Transform Data</button>
    
    <div id="output"></div>

    <script>
        function transformData() {
            // Original input data
            const inputData = [
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "288788",
                    "roomId": "*************",
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[14.065424209822538,5.733508446123359,10.250391354600747],\"ue_position\":[1406.5424209822538,1025.0391354600747,573.3508446123359],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":543,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 548661,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "443655",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-11.507913635383323,5.733508446123359,9.702887149179961],\"ue_position\":[-1150.7913635383322,970.2887149179961,573.3508446123359],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":6638,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 548662,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "478715",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[4.5821796835028294,5.733508446123359,15.263049696599733],\"ue_position\":[458.21796835028294,1526.3049696599733,573.3508446123359],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":4421,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549403,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "321350",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[8.473486862103737,5.733508446123359,-1.0009548815029987],\"ue_position\":[847.3486862103737,-100.09548815029987,573.3508446123359],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":973,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549404,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "867742",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-3.931281264261443,5.877527622568628,-1.5991744809222146],\"ue_position\":[-393.1281264261443,-159.91744809222146,587.7527622568629],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":9550,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549405,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "922524",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[26.211910069062093,5.733508446123359,-4.915686994624181],\"ue_position\":[2621.191006906209,-491.5686994624181,573.3508446123359],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":4518,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549406,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "558671",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-4.3779073778165465,5.856921189274573,-17.397914238031117],\"ue_position\":[-437.7907377816546,-1739.7914238031117,585.6921189274573],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":3822,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549407,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "349340",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[12.421763401071338,5.733508446123359,-17.95508017261695],\"ue_position\":[1242.176340107134,-1795.508017261695,573.3508446123359],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":4647,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549408,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "758030",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "1F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-18.722678130339613,5.8307458856764285,-2.344042282462693],\"ue_position\":[-1872.2678130339614,-234.40422824626933,583.0745885676429],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":3534,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549409,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "87654",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-46.17163050898315,11.015069742867777,-11.34155770242938],\"ue_position\":[-4617.163050898315,-1134.155770242938,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":308,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549410,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "88186",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-46.24187844572608,11.015069742867777,8.89947669013861],\"ue_position\":[-4624.187844572608,889.947669013861,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":4841,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549411,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "476526",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-21.656099154655895,11.015069742867777,-4.170965402535935],\"ue_position\":[-2165.6099154655894,-417.0965402535935,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":1904,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549412,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "422647",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-34.07441789513499,11.015069742867777,-0.05327677782673579],\"ue_position\":[-3407.4417895134993,-5.327677782673579,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":2915,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549413,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "234776",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-28.675517917419132,11.015069742867777,6.8474471848932055],\"ue_position\":[-2867.5517917419133,684.7447184893206,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":2711,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549414,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "59262",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-12.428808996522426,11.015069742867777,4.8423470896025655],\"ue_position\":[-1242.8808996522425,484.23470896025657,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":9184,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549415,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "185453",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-17.92535307704338,11.015069742867777,18.17829871223022],\"ue_position\":[-1792.5353077043378,1817.8298712230219,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":6978,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549416,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "841578",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-8.864744451743839,11.015069742867777,-3.2113345697730287],\"ue_position\":[-886.4744451743838,-321.13345697730284,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":5248,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549417,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "784949",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[-5.950053014955557,11.015069742867777,-11.702169245580986],\"ue_position\":[-595.0053014955557,-1170.2169245580985,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":2844,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549418,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "91022",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[9.91518861160511,11.015069742867777,-20.038712995400964],\"ue_position\":[991.518861160511,-2003.8712995400963,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":8932,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549419,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "610607",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[10.038262551809005,11.015069742867777,-8.511016246372739],\"ue_position\":[1003.8262551809005,-851.1016246372739,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":3310,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549420,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "667719",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[27.22385067056205,11.015069742867777,-10.535847686254318],\"ue_position\":[2722.385067056205,-1053.5847686254317,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":5721,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549421,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "54557",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[19.036024699864953,11.015069742867777,4.713460090141677],\"ue_position\":[1903.6024699864952,471.3460090141677,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":2802,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549422,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "639872",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[1.697001814075847,11.015069742867777,13.060684077802478],\"ue_position\":[169.7001814075847,1306.0684077802478,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":7334,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549423,
                    "sceneJson": null,
                    "projectId": 679
                },
                {
                    "buildId": "整体",
                    "type": 1,
                    "deviceId": "535559",
                    "roomId": null,
                    "parkId": "0",
                    "floorId": "2F",
                    "v3DbuildId": null,
                    "pzjson": "{\"modelId\":\"RNcph4qca6c_7nSvUKs8FA==\",\"position\":[33.53286633843989,11.015069742867777,15.189646919569267],\"ue_position\":[3353.286633843989,1518.9646919569266,1101.5069742867777],\"scale\":[1,1,1],\"rotation\":[0,0,0],\"id\":9592,\"floorNum\":2,\"name\":\"华为路由器\"}",
                    "name": "华为路由器",
                    "json": "",
                    "id": 549424,
                    "sceneJson": null,
                    "projectId": 679
                }
            ];

            // Transform the data
            const transformedData = inputData.map(item => {
                // Parse the pzjson string
                const pzjson = JSON.parse(item.pzjson);
                
                // Modify the required fields
                pzjson.modelId = "wifi";
                // pzjson.position[1] = 1.0425289993677183; // Set Y position
                // pzjson.ue_position[2] = 104.25289993677183; // Set Z position in ue_position
                
                // Create the new object structure
                return {
                    json: pzjson,
                    id: item.id,
                    title: item.name,
                    name: item.name,
                    parkId: item.parkId,
                    floorId: item.floorId,
                    type: item.type
                };
            });

            // Display the transformed data
            const outputElement = document.getElementById('output');
            outputElement.innerHTML = '<h2>Transformed Data:</h2><pre>' + 
                JSON.stringify(transformedData, null, 2) + '</pre>';
            
            // Also log to console for easy copying
            console.log('Transformed Data:', transformedData);
        }
    </script>
</body>
</html>